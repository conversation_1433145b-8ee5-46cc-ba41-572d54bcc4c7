{"level":"INFO","timestamp":"2025-06-20T21:58:43.437-0400","caller":"logger/logger.go:101","msg":"Starting httpsok service..."}
{"level":"FATAL","timestamp":"2025-06-20T21:58:43.438-0400","caller":"logger/logger.go:116","msg":"Failed to connect to database","error":"failed to ping database: dial tcp [::1]:3306: connect: connection refused","stacktrace":"github.com/httpsok/internal/logger.(*zapLogger).Fatal\n\t/home/<USER>/httpsok_improved/internal/logger/logger.go:116\nmain.main\n\t/home/<USER>/httpsok_improved/cmd/main.go:59\nruntime.main\n\t/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/runtime/proc.go:272"}
