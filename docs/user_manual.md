# httpsok系统用户手册

## 1. 系统概述

httpsok是一个基于Go语言和MySQL开发的SSL证书自动化管理系统，旨在简化SSL证书的申请、部署、监控和续期流程。系统提供了直观的Web界面，支持多种CA提供商和加密算法，可以自动检测证书状态，并在证书即将过期时自动续期，大大降低了SSL证书管理的复杂度和人工干预需求。

### 1.1 核心功能

- **证书管理**：申请、查看、更新和删除SSL证书
- **自动部署**：将证书自动部署到各种Web服务器
- **证书监控**：实时监控证书状态和有效期
- **自动续期**：在证书即将过期时自动续期
- **多环境适配**：支持主流Linux发行版和Web服务器
- **集中化管理**：通过Web控制台集中管理多台服务器的证书

### 1.2 系统架构

httpsok系统由以下几个主要组件构成：

- **Web控制台**：提供用户界面，用于管理证书、服务器和监控
- **后端API服务**：处理业务逻辑，与acme.sh交互申请和管理证书
- **客户端脚本**：部署在服务器上，用于收集信息、部署证书和重载服务
- **数据库**：存储证书、服务器和监控信息
- **acme.sh集成**：利用acme.sh与CA提供商交互，申请和续期证书

## 2. 快速入门

### 2.1 系统登录

1. 打开浏览器，访问httpsok系统地址（如：http://your-server-ip:8080）
2. 输入管理员提供的用户名和密码
3. 点击"登录"按钮进入系统

### 2.2 申请证书

1. 在左侧导航栏中选择"证书管理"
2. 点击顶部的"免费申请证书"按钮
3. 填写域名信息（支持通配符域名，如*.example.com）
4. 根据提示配置DNS验证记录
5. 选择证书厂商（Let's Encrypt、ZeroSSL等）
6. 选择加密算法（ECC或RSA）
7. 点击"验证域名"按钮进行域名验证
8. 验证通过后，系统会自动申请证书

### 2.3 部署证书

1. 在左侧导航栏中选择"自动部署"
2. 如果是首次部署，点击"添加服务器"按钮添加目标服务器
3. 在服务器列表中找到目标服务器，打开"自动部署"开关
4. 点击"编辑"按钮，在弹出的详情页面中点击"立即部署"
5. 选择要部署的证书，填写证书路径信息
6. 点击"立即部署"按钮完成部署

### 2.4 监控证书

1. 在左侧导航栏中选择"证书监控"
2. 查看所有证书的状态和有效期
3. 可以按有效期排序，优先关注即将过期的证书
4. 点击"检测"按钮可以立即检查证书状态
5. 打开"检测开关"可以启用自动监控

## 3. 证书管理

### 3.1 证书列表

证书管理页面显示了系统中所有的证书，包括以下信息：

- **域名**：证书所对应的域名
- **域名验证状态**：显示域名是否已通过验证
- **证书品牌**：证书的CA提供商
- **有效期（天）**：证书的剩余有效天数
- **加密方式**：证书使用的加密算法（ECC或RSA）
- **状态**：证书的当前状态（已签发或未签发）
- **备注**：证书的附加说明
- **操作**：可对证书进行的操作（编辑、下载、删除）

### 3.2 申请新证书

申请新证书需要以下步骤：

1. 点击"免费申请证书"按钮
2. 填写域名信息（必填）
3. 配置DNS验证记录：
   - 添加一条类型为TXT的DNS记录
   - 主机记录为：_acme-challenge.your-domain.com
   - 记录值为系统生成的验证字符串
4. 选择证书厂商（必填）：
   - Let's Encrypt：全球最流行的免费CA
   - ZeroSSL：提供更长有效期的免费证书
   - Google：Google Trust Services提供的证书
5. 选择加密算法（必填）：
   - ECC：更短的密钥长度，更高的安全性
   - RSA：更广泛的兼容性
6. 填写备注（可选）
7. 点击"验证域名"按钮进行验证
8. 验证通过后，系统会自动申请证书

### 3.3 管理证书

对于已有的证书，可以进行以下操作：

- **编辑**：修改证书的备注信息
- **下载**：下载证书文件，包括证书文件、私钥和证书链
- **删除**：删除不再需要的证书

系统还提供了"删除失效证书"功能，可以一键清理所有已过期的证书。

### 3.4 证书详情

点击证书的"编辑"按钮可以查看证书的详细信息，包括：

- **域名**：证书所对应的域名
- **证书品牌**：证书的CA提供商
- **生效时间**：证书的生效时间
- **失效时间**：证书的过期时间
- **创建时间**：证书的创建时间
- **备注**：证书的附加说明

在详情页面，可以编辑证书的备注信息。

## 4. 自动部署

### 4.1 服务器管理

自动部署页面显示了系统中所有的服务器，包括以下信息：

- **类型**：服务器上运行的Web服务器类型（如Nginx、Apache）
- **名称**：服务器的名称
- **系统**：服务器的操作系统
- **IP**：服务器的IP地址
- **版本**：Web服务器的版本
- **状态**：服务器的连接状态
- **最后更新**：最后一次与服务器通信的时间
- **自动部署**：是否启用自动部署
- **备注**：服务器的附加说明
- **操作**：可对服务器进行的操作（编辑、删除）

### 4.2 添加服务器

添加服务器需要以下步骤：

1. 点击"添加服务器"按钮
2. 填写服务器信息：
   - 服务器名称（必填）
   - 服务器类型（Nginx或Apache）
   - 主机名/IP地址（必填）
   - 操作系统信息（必填）
   - Web服务器版本
3. 配置连接信息：
   - SSH端口（默认22）
   - 用户名（必填）
   - 认证方式（密码或私钥）
   - 密码或私钥内容
4. 设置自动部署选项
5. 填写备注（可选）
6. 点击"测试连接"按钮测试连接是否成功
7. 点击"保存"按钮添加服务器

### 4.3 部署证书

部署证书到服务器需要以下步骤：

1. 在服务器列表中找到目标服务器，点击"编辑"按钮
2. 在服务器详情页面，点击"立即部署"按钮
3. 选择要部署的证书
4. 填写证书路径信息：
   - 证书文件路径（必填）
   - 私钥文件路径（必填）
   - 证书链文件路径
   - 配置文件路径
5. 设置部署选项：
   - 是否启用自动部署
   - 是否重载服务
6. 点击"立即部署"按钮完成部署

### 4.4 自动部署设置

对于每台服务器，可以设置是否启用自动部署：

1. 在服务器列表中找到目标服务器
2. 切换"自动部署"开关
3. 启用自动部署后，当证书更新时，系统会自动将新证书部署到服务器

## 5. 证书监控

### 5.1 监控列表

证书监控页面显示了系统中所有的监控项，包括以下信息：

- **主机域名**：被监控的域名
- **证书等级**：证书的安全等级
- **加密方式**：证书使用的加密算法
- **端口**：SSL/TLS端口（通常为443）
- **IP类型**：域名或IP地址
- **IP**：服务器的IP地址
- **状态**：证书的当前状态
- **有效期(天)**：证书的剩余有效天数
- **检测开关**：是否启用自动检测
- **备注**：监控项的附加说明
- **操作**：可对监控项进行的操作（检测、编辑、删除）

### 5.2 添加监控

添加监控项需要以下步骤：

1. 点击"添加监控"按钮
2. 填写监控信息：
   - 主机域名（必填）
   - 端口（默认443）
   - IP类型（域名或IP）
   - IP地址（如选择IP类型为IP，则必填）
3. 关联证书（可选）
4. 设置检测间隔（分钟）
5. 设置是否启用监控
6. 填写备注（可选）
7. 点击"保存"按钮添加监控项

### 5.3 监控详情

点击监控项的"编辑"按钮可以查看监控详情，包括：

- **基本信息**：域名、端口、IP地址等
- **证书信息**：证书等级、加密方式、有效期等
- **监控状态**：当前状态、最后检测时间、检测间隔等
- **历史记录**：历史检测结果，包括检测时间、状态、有效期等

在详情页面，可以：

- 编辑监控项的备注信息
- 点击"立即检测"按钮进行手动检测
- 查看历史检测记录

### 5.4 自动检测设置

对于每个监控项，可以设置是否启用自动检测：

1. 在监控列表中找到目标监控项
2. 切换"检测开关"
3. 启用自动检测后，系统会按照设定的间隔自动检测证书状态

## 6. 系统设置

### 6.1 用户管理

管理员可以在系统设置中管理用户：

1. 在左侧导航栏中选择"系统设置"
2. 选择"用户管理"选项卡
3. 可以添加、编辑或删除用户
4. 设置用户权限

### 6.2 系统配置

系统配置包括以下选项：

1. **基本设置**：
   - 系统名称
   - 默认语言
   - 时区设置
2. **证书设置**：
   - 默认CA提供商
   - 默认加密算法
   - 证书续期提前天数
3. **通知设置**：
   - 邮件通知
   - 短信通知
   - 通知触发条件

### 6.3 日志查看

系统日志记录了所有重要操作：

1. 在左侧导航栏中选择"系统设置"
2. 选择"系统日志"选项卡
3. 可以按时间、类型、用户等条件筛选日志
4. 查看详细的操作记录

## 7. 常见问题

### 7.1 域名验证失败

如果域名验证失败，请检查：

1. DNS记录是否正确添加
2. 记录类型是否为TXT
3. 主机记录是否正确（_acme-challenge.your-domain.com）
4. 记录值是否与系统提供的验证字符串一致
5. DNS记录是否已生效（可能需要等待几分钟到几小时）

### 7.2 证书申请失败

如果证书申请失败，可能的原因包括：

1. 域名验证未通过
2. CA服务器暂时不可用
3. 达到CA的速率限制（如Let's Encrypt每周最多申请50个证书）
4. 域名配置问题

解决方法：

1. 确保域名验证已通过
2. 稍后重试
3. 尝试使用其他CA提供商
4. 检查域名配置

### 7.3 证书部署失败

如果证书部署失败，请检查：

1. 服务器连接是否正常
2. 用户权限是否足够
3. 证书路径是否正确
4. Web服务器配置是否正确

### 7.4 自动续期不工作

如果自动续期不工作，可能的原因包括：

1. 系统服务未运行
2. 域名验证方式已变更
3. DNS记录已被删除
4. CA服务器暂时不可用

解决方法：

1. 检查系统服务状态
2. 重新配置域名验证
3. 确保DNS记录存在
4. 手动触发续期

## 8. 附录

### 8.1 命令行工具

httpsok提供了命令行工具，可以在终端中使用：

```bash
# 查看帮助
httpsok-cli --help

# 申请证书
httpsok-cli cert issue --domain example.com --ca letsencrypt --type ecc

# 部署证书
httpsok-cli cert deploy --cert-id 123 --server-id 456

# 检查证书状态
httpsok-cli cert check --domain example.com
```

### 8.2 API接口

httpsok提供了RESTful API，可以与其他系统集成：

```
# 获取认证令牌
POST /api/v1/auth/login

# 获取证书列表
GET /api/v1/certificates

# 申请证书
POST /api/v1/certificates

# 部署证书
POST /api/v1/servers/{server_id}/deploy
```

详细的API文档请参考系统提供的API参考文档。

### 8.3 术语表

- **CA（Certificate Authority）**：证书颁发机构，如Let's Encrypt、ZeroSSL等
- **CSR（Certificate Signing Request）**：证书签名请求
- **DNS验证**：通过添加DNS记录来验证域名所有权
- **ECC（Elliptic Curve Cryptography）**：椭圆曲线加密算法
- **RSA**：一种非对称加密算法
- **SSL/TLS**：安全套接字层/传输层安全协议
- **自动部署**：自动将证书部署到Web服务器
- **自动续期**：在证书即将过期时自动续期
