-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    role ENUM('admin', 'user') NOT NULL DEFAULT 'user',
    status ENUM('active', 'inactive', 'locked') NOT NULL DEFAULT 'active',
    failed_login_attempts INT UNSIGNED NOT NULL DEFAULT 0,
    locked_until TIMESTAMP NULL,
    last_login_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 刷新令牌表
CREATE TABLE IF NOT EXISTS refresh_tokens (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id INT UNSIGNED NOT NULL,
    token_hash VARCHAR(255) NOT NULL UNIQUE,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_expires_at (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 证书表
CREATE TABLE IF NOT EXISTS certificates (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id INT UNSIGNED NOT NULL,
    name VARCHAR(100) NOT NULL,
    domains TEXT NOT NULL, -- JSON格式存储域名列表
    ca_provider ENUM('letsencrypt', 'zerossl', 'google') NOT NULL DEFAULT 'letsencrypt',
    encryption_type ENUM('RSA', 'ECC') NOT NULL DEFAULT 'ECC',
    key_size INT UNSIGNED NOT NULL DEFAULT 256,
    status ENUM('pending', 'validating', 'valid', 'invalid', 'expired', 'revoked') NOT NULL DEFAULT 'pending',
    cert_path VARCHAR(500),
    key_path VARCHAR(500),
    fullchain_path VARCHAR(500),
    issued_at TIMESTAMP NULL,
    expires_at TIMESTAMP NULL,
    auto_renew BOOLEAN NOT NULL DEFAULT TRUE,
    renew_before_days INT UNSIGNED NOT NULL DEFAULT 30,
    last_check_at TIMESTAMP NULL,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_expires_at (expires_at),
    INDEX idx_auto_renew (auto_renew)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 服务器表
CREATE TABLE IF NOT EXISTS servers (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id INT UNSIGNED NOT NULL,
    name VARCHAR(100) NOT NULL,
    host VARCHAR(255) NOT NULL,
    port INT UNSIGNED NOT NULL DEFAULT 22,
    username VARCHAR(50) NOT NULL,
    auth_type ENUM('password', 'key') NOT NULL DEFAULT 'key',
    password VARCHAR(255), -- 加密存储
    private_key_path VARCHAR(500),
    web_server_type ENUM('nginx', 'apache') NOT NULL DEFAULT 'nginx',
    web_server_config_path VARCHAR(500),
    status ENUM('active', 'inactive', 'error') NOT NULL DEFAULT 'active',
    last_connected_at TIMESTAMP NULL,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 证书部署记录表
CREATE TABLE IF NOT EXISTS certificate_deployments (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    certificate_id INT UNSIGNED NOT NULL,
    server_id INT UNSIGNED NOT NULL,
    status ENUM('pending', 'deploying', 'deployed', 'failed') NOT NULL DEFAULT 'pending',
    deployed_at TIMESTAMP NULL,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (certificate_id) REFERENCES certificates(id) ON DELETE CASCADE,
    FOREIGN KEY (server_id) REFERENCES servers(id) ON DELETE CASCADE,
    UNIQUE KEY uk_cert_server (certificate_id, server_id),
    INDEX idx_certificate_id (certificate_id),
    INDEX idx_server_id (server_id),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 监控配置表
CREATE TABLE IF NOT EXISTS monitors (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id INT UNSIGNED NOT NULL,
    name VARCHAR(100) NOT NULL,
    url VARCHAR(500) NOT NULL,
    check_interval INT UNSIGNED NOT NULL DEFAULT 3600, -- 秒
    timeout INT UNSIGNED NOT NULL DEFAULT 30, -- 秒
    alert_before_days JSON NOT NULL, -- 过期前多少天告警
    email_alerts BOOLEAN NOT NULL DEFAULT TRUE,
    webhook_url VARCHAR(500),
    status ENUM('active', 'inactive') NOT NULL DEFAULT 'active',
    last_check_at TIMESTAMP NULL,
    next_check_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_next_check_at (next_check_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 监控结果表
CREATE TABLE IF NOT EXISTS monitor_results (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    monitor_id INT UNSIGNED NOT NULL,
    check_time TIMESTAMP NOT NULL,
    status ENUM('success', 'warning', 'error') NOT NULL,
    response_time INT UNSIGNED, -- 毫秒
    cert_valid BOOLEAN,
    cert_expires_at TIMESTAMP NULL,
    cert_days_until_expiry INT,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (monitor_id) REFERENCES monitors(id) ON DELETE CASCADE,
    INDEX idx_monitor_id (monitor_id),
    INDEX idx_check_time (check_time),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 告警记录表
CREATE TABLE IF NOT EXISTS alerts (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id INT UNSIGNED NOT NULL,
    type ENUM('certificate_expiry', 'monitor_failure', 'deployment_failure') NOT NULL,
    title VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    level ENUM('info', 'warning', 'error', 'critical') NOT NULL DEFAULT 'warning',
    status ENUM('pending', 'sent', 'failed') NOT NULL DEFAULT 'pending',
    related_id INT UNSIGNED, -- 关联的资源ID（证书、监控等）
    sent_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_type (type),
    INDEX idx_status (status),
    INDEX idx_level (level),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 系统设置表
CREATE TABLE IF NOT EXISTS settings (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    key_name VARCHAR(100) NOT NULL UNIQUE,
    value TEXT,
    description VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_key_name (key_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 操作日志表
CREATE TABLE IF NOT EXISTS operation_logs (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id INT UNSIGNED,
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(50) NOT NULL,
    resource_id INT UNSIGNED,
    details JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_resource_type (resource_type),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入默认管理员用户（密码：admin123）
INSERT INTO users (username, email, password_hash, role) VALUES 
('admin', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj8xLs9aTzrC', 'admin')
ON DUPLICATE KEY UPDATE username=username;

-- 插入默认系统设置
INSERT INTO settings (key_name, value, description) VALUES 
('site_name', 'HTTPS OK', '网站名称'),
('site_url', 'https://localhost:8080', '网站URL'),
('admin_email', '<EMAIL>', '管理员邮箱'),
('smtp_enabled', 'false', '是否启用SMTP'),
('default_ca', 'letsencrypt', '默认CA提供商'),
('default_encryption', 'ECC', '默认加密类型'),
('auto_renew_enabled', 'true', '是否启用自动续期'),
('renew_before_days', '30', '续期前天数')
ON DUPLICATE KEY UPDATE key_name=key_name;

