{"server": {"address": "0.0.0.0", "port": 8080, "tls_enabled": false, "tls_cert_file": "", "tls_key_file": "", "read_timeout": 15, "write_timeout": 15, "idle_timeout": 60}, "database": {"host": "localhost", "port": 3306, "user": "root", "password": "", "dbname": "httpsok", "sslmode": "", "max_open_conns": 25, "max_idle_conns": 5, "conn_max_lifetime": 5}, "redis": {"enabled": false, "host": "localhost", "port": 6379, "password": "", "db": 0}, "jwt": {"secret_key": "", "access_token_expiry": 24, "refresh_token_expiry": 7, "issuer": "httpsok", "algorithm": "HS256"}, "log": {"level": "info", "path": "logs", "max_size": 100, "max_backups": 3, "max_age": 28, "compress": true}, "acme": {"default_ca": "letsencrypt", "default_encryption": "ECC", "renew_before_days": 30, "script_path": "/usr/local/bin/acme.sh", "work_dir": "/var/lib/acme", "config_dir": "/etc/acme"}, "alert": {"default_expiry_alert_days": [30, 14, 7, 3, 1], "email_enabled": false, "smtp_host": "", "smtp_port": 587, "smtp_user": "", "smtp_password": "", "from_email": ""}, "rate_limit": {"enabled": true, "requests_per_min": 100, "burst_size": 20, "auth_requests_per_min": 10}, "security": {"password_min_length": 8, "password_require_special": true, "password_require_number": true, "password_require_upper": true, "password_require_lower": true, "max_login_attempts": 5, "lockout_duration": 15, "session_timeout": 30}}