package main

import (
	"fmt"
	"os"
	"testing"

	"github.com/httpsok/internal/config"
	"github.com/httpsok/internal/logger"
)

func TestConfigLoad(t *testing.T) {
	// 测试配置加载
	cfg, err := config.Load()
	if err != nil {
		t.Fatalf("Failed to load config: %v", err)
	}

	if cfg.Server.Port == 0 {
		t.Error("Server port should not be 0")
	}

	if cfg.Database.Host == "" {
		t.Error("Database host should not be empty")
	}
}

func TestLoggerCreation(t *testing.T) {
	// 测试日志创建
	logConfig := logger.LogConfig{
		Level:      "info",
		Path:       "test_logs",
		MaxSize:    10,
		MaxBackups: 3,
		MaxAge:     7,
		Compress:   true,
	}

	log, err := logger.NewLogger(logConfig)
	if err != nil {
		t.Fatalf("Failed to create logger: %v", err)
	}

	log.Info("Test log message")
	log.Sync()

	// 清理测试日志
	os.RemoveAll("test_logs")
}

func TestJWTGeneration(t *testing.T) {
	// 这里应该测试JWT生成和验证
	// 由于需要导入auth包，暂时跳过
	fmt.Println("JWT test placeholder")
}

func TestDatabaseConnection(t *testing.T) {
	// 这里应该测试数据库连接
	// 由于需要实际的数据库，暂时跳过
	fmt.Println("Database test placeholder")
}

