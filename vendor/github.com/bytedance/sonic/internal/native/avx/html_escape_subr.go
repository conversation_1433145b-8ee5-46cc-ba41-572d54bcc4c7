// +build !noasm !appengine
// Code generated by asm2asm, DO NOT EDIT.

package avx

import (
	`github.com/bytedance/sonic/loader`
)

const (
    _entry__html_escape = 64
)

const (
    _stack__html_escape = 72
)

const (
    _size__html_escape = 1248
)

var (
    _pcsp__html_escape = [][2]uint32{
        {1, 0},
        {4, 8},
        {6, 16},
        {8, 24},
        {10, 32},
        {12, 40},
        {13, 48},
        {1224, 72},
        {1228, 48},
        {1229, 40},
        {1231, 32},
        {1233, 24},
        {1235, 16},
        {1237, 8},
        {1239, 0},
    }
)

var _cfunc_html_escape = []loader.CFunc{
    {"_html_escape_entry", 0,  _entry__html_escape, 0, nil},
    {"_html_escape", _entry__html_escape, _size__html_escape, _stack__html_escape, _pcsp__html_escape},
}
