// +build !noasm !appengine
// Code generated by asm2asm, DO NOT EDIT.

package avx

import (
	`github.com/bytedance/sonic/loader`
)

const (
    _entry__skip_one_fast = 128
)

const (
    _stack__skip_one_fast = 176
)

const (
    _size__skip_one_fast = 3016
)

var (
    _pcsp__skip_one_fast = [][2]uint32{
        {1, 0},
        {4, 8},
        {6, 16},
        {8, 24},
        {10, 32},
        {12, 40},
        {13, 48},
        {658, 176},
        {659, 168},
        {661, 160},
        {663, 152},
        {665, 144},
        {667, 136},
        {671, 128},
        {3016, 176},
    }
)

var _cfunc_skip_one_fast = []loader.CFunc{
    {"_skip_one_fast_entry", 0,  _entry__skip_one_fast, 0, nil},
    {"_skip_one_fast", _entry__skip_one_fast, _size__skip_one_fast, _stack__skip_one_fast, _pcsp__skip_one_fast},
}
