// +build amd64
// Code generated by asm2asm, DO NOT EDIT.

package avx

var _text_u64toa = []byte{
	// .p2align 4, 0x00
	// LCPI0_0
	0x59, 0x17, 0xb7, 0xd1, 0x00, 0x00, 0x00, 0x00, // .quad 3518437209
	0x59, 0x17, 0xb7, 0xd1, 0x00, 0x00, 0x00, 0x00, //0x00000008 .quad 3518437209
	//0x00000010 LCPI0_3
	0x0a, 0x00, //0x00000010 .word 10
	0x0a, 0x00, //0x00000012 .word 10
	0x0a, 0x00, //0x00000014 .word 10
	0x0a, 0x00, //0x00000016 .word 10
	0x0a, 0x00, //0x00000018 .word 10
	0x0a, 0x00, //0x0000001a .word 10
	0x0a, 0x00, //0x0000001c .word 10
	0x0a, 0x00, //0x0000001e .word 10
	//0x00000020 LCPI0_4
	0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, //0x00000020 QUAD $0x3030303030303030; QUAD $0x3030303030303030  // .space 16, '0000000000000000'
	//0x00000030 .p2align 3, 0x00
	//0x00000030 LCPI0_1
	0xc5, 0x20, 0x7b, 0x14, 0x34, 0x33, 0x00, 0x80, //0x00000030 .quad -9223315738079846203
	//0x00000038 LCPI0_2
	0x80, 0x00, 0x00, 0x08, 0x00, 0x20, 0x00, 0x80, //0x00000038 .quad -9223336852348469120
	//0x00000040 .p2align 4, 0x90
	//0x00000040 _u64toa
	0x55, //0x00000040 pushq        %rbp
	0x48, 0x89, 0xe5, //0x00000041 movq         %rsp, %rbp
	0x48, 0x81, 0xfe, 0x0f, 0x27, 0x00, 0x00, //0x00000044 cmpq         $9999, %rsi
	0x0f, 0x87, 0xa2, 0x00, 0x00, 0x00, //0x0000004b ja           LBB0_8
	0x0f, 0xb7, 0xc6, //0x00000051 movzwl       %si, %eax
	0xc1, 0xe8, 0x02, //0x00000054 shrl         $2, %eax
	0x69, 0xc0, 0x7b, 0x14, 0x00, 0x00, //0x00000057 imull        $5243, %eax, %eax
	0xc1, 0xe8, 0x11, //0x0000005d shrl         $17, %eax
	0x48, 0x8d, 0x14, 0x00, //0x00000060 leaq         (%rax,%rax), %rdx
	0x6b, 0xc0, 0x64, //0x00000064 imull        $100, %eax, %eax
	0x89, 0xf1, //0x00000067 movl         %esi, %ecx
	0x29, 0xc1, //0x00000069 subl         %eax, %ecx
	0x0f, 0xb7, 0xc1, //0x0000006b movzwl       %cx, %eax
	0x48, 0x01, 0xc0, //0x0000006e addq         %rax, %rax
	0x81, 0xfe, 0xe8, 0x03, 0x00, 0x00, //0x00000071 cmpl         $1000, %esi
	0x0f, 0x82, 0x16, 0x00, 0x00, 0x00, //0x00000077 jb           LBB0_3
	0x48, 0x8d, 0x0d, 0x8c, 0x04, 0x00, 0x00, //0x0000007d leaq         $1164(%rip), %rcx  /* _Digits+0(%rip) */
	0x8a, 0x0c, 0x0a, //0x00000084 movb         (%rdx,%rcx), %cl
	0x88, 0x0f, //0x00000087 movb         %cl, (%rdi)
	0xb9, 0x01, 0x00, 0x00, 0x00, //0x00000089 movl         $1, %ecx
	0xe9, 0x0b, 0x00, 0x00, 0x00, //0x0000008e jmp          LBB0_4
	//0x00000093 LBB0_3
	0x31, 0xc9, //0x00000093 xorl         %ecx, %ecx
	0x83, 0xfe, 0x64, //0x00000095 cmpl         $100, %esi
	0x0f, 0x82, 0x45, 0x00, 0x00, 0x00, //0x00000098 jb           LBB0_5
	//0x0000009e LBB0_4
	0x0f, 0xb7, 0xd2, //0x0000009e movzwl       %dx, %edx
	0x48, 0x83, 0xca, 0x01, //0x000000a1 orq          $1, %rdx
	0x48, 0x8d, 0x35, 0x64, 0x04, 0x00, 0x00, //0x000000a5 leaq         $1124(%rip), %rsi  /* _Digits+0(%rip) */
	0x8a, 0x14, 0x32, //0x000000ac movb         (%rdx,%rsi), %dl
	0x89, 0xce, //0x000000af movl         %ecx, %esi
	0xff, 0xc1, //0x000000b1 incl         %ecx
	0x88, 0x14, 0x37, //0x000000b3 movb         %dl, (%rdi,%rsi)
	//0x000000b6 LBB0_6
	0x48, 0x8d, 0x15, 0x53, 0x04, 0x00, 0x00, //0x000000b6 leaq         $1107(%rip), %rdx  /* _Digits+0(%rip) */
	0x8a, 0x14, 0x10, //0x000000bd movb         (%rax,%rdx), %dl
	0x89, 0xce, //0x000000c0 movl         %ecx, %esi
	0xff, 0xc1, //0x000000c2 incl         %ecx
	0x88, 0x14, 0x37, //0x000000c4 movb         %dl, (%rdi,%rsi)
	//0x000000c7 LBB0_7
	0x0f, 0xb7, 0xc0, //0x000000c7 movzwl       %ax, %eax
	0x48, 0x83, 0xc8, 0x01, //0x000000ca orq          $1, %rax
	0x48, 0x8d, 0x15, 0x3b, 0x04, 0x00, 0x00, //0x000000ce leaq         $1083(%rip), %rdx  /* _Digits+0(%rip) */
	0x8a, 0x04, 0x10, //0x000000d5 movb         (%rax,%rdx), %al
	0x89, 0xca, //0x000000d8 movl         %ecx, %edx
	0xff, 0xc1, //0x000000da incl         %ecx
	0x88, 0x04, 0x17, //0x000000dc movb         %al, (%rdi,%rdx)
	0x89, 0xc8, //0x000000df movl         %ecx, %eax
	0x5d, //0x000000e1 popq         %rbp
	0xc3, //0x000000e2 retq         
	//0x000000e3 LBB0_5
	0x31, 0xc9, //0x000000e3 xorl         %ecx, %ecx
	0x83, 0xfe, 0x0a, //0x000000e5 cmpl         $10, %esi
	0x0f, 0x83, 0xc8, 0xff, 0xff, 0xff, //0x000000e8 jae          LBB0_6
	0xe9, 0xd4, 0xff, 0xff, 0xff, //0x000000ee jmp          LBB0_7
	//0x000000f3 LBB0_8
	0x48, 0x81, 0xfe, 0xff, 0xe0, 0xf5, 0x05, //0x000000f3 cmpq         $99999999, %rsi
	0x0f, 0x87, 0x1e, 0x01, 0x00, 0x00, //0x000000fa ja           LBB0_16
	0x89, 0xf0, //0x00000100 movl         %esi, %eax
	0xba, 0x59, 0x17, 0xb7, 0xd1, //0x00000102 movl         $3518437209, %edx
	0x48, 0x0f, 0xaf, 0xd0, //0x00000107 imulq        %rax, %rdx
	0x48, 0xc1, 0xea, 0x2d, //0x0000010b shrq         $45, %rdx
	0x44, 0x69, 0xc2, 0x10, 0x27, 0x00, 0x00, //0x0000010f imull        $10000, %edx, %r8d
	0x89, 0xf1, //0x00000116 movl         %esi, %ecx
	0x44, 0x29, 0xc1, //0x00000118 subl         %r8d, %ecx
	0x4c, 0x69, 0xd0, 0x83, 0xde, 0x1b, 0x43, //0x0000011b imulq        $1125899907, %rax, %r10
	0x49, 0xc1, 0xea, 0x31, //0x00000122 shrq         $49, %r10
	0x41, 0x83, 0xe2, 0xfe, //0x00000126 andl         $-2, %r10d
	0x0f, 0xb7, 0xc2, //0x0000012a movzwl       %dx, %eax
	0xc1, 0xe8, 0x02, //0x0000012d shrl         $2, %eax
	0x69, 0xc0, 0x7b, 0x14, 0x00, 0x00, //0x00000130 imull        $5243, %eax, %eax
	0xc1, 0xe8, 0x11, //0x00000136 shrl         $17, %eax
	0x6b, 0xc0, 0x64, //0x00000139 imull        $100, %eax, %eax
	0x29, 0xc2, //0x0000013c subl         %eax, %edx
	0x44, 0x0f, 0xb7, 0xca, //0x0000013e movzwl       %dx, %r9d
	0x4d, 0x01, 0xc9, //0x00000142 addq         %r9, %r9
	0x0f, 0xb7, 0xc1, //0x00000145 movzwl       %cx, %eax
	0xc1, 0xe8, 0x02, //0x00000148 shrl         $2, %eax
	0x69, 0xc0, 0x7b, 0x14, 0x00, 0x00, //0x0000014b imull        $5243, %eax, %eax
	0xc1, 0xe8, 0x11, //0x00000151 shrl         $17, %eax
	0x4c, 0x8d, 0x04, 0x00, //0x00000154 leaq         (%rax,%rax), %r8
	0x6b, 0xc0, 0x64, //0x00000158 imull        $100, %eax, %eax
	0x29, 0xc1, //0x0000015b subl         %eax, %ecx
	0x44, 0x0f, 0xb7, 0xd9, //0x0000015d movzwl       %cx, %r11d
	0x4d, 0x01, 0xdb, //0x00000161 addq         %r11, %r11
	0x81, 0xfe, 0x80, 0x96, 0x98, 0x00, //0x00000164 cmpl         $10000000, %esi
	0x0f, 0x82, 0x17, 0x00, 0x00, 0x00, //0x0000016a jb           LBB0_11
	0x48, 0x8d, 0x05, 0x99, 0x03, 0x00, 0x00, //0x00000170 leaq         $921(%rip), %rax  /* _Digits+0(%rip) */
	0x41, 0x8a, 0x04, 0x02, //0x00000177 movb         (%r10,%rax), %al
	0x88, 0x07, //0x0000017b movb         %al, (%rdi)
	0xb9, 0x01, 0x00, 0x00, 0x00, //0x0000017d movl         $1, %ecx
	0xe9, 0x0e, 0x00, 0x00, 0x00, //0x00000182 jmp          LBB0_12
	//0x00000187 LBB0_11
	0x31, 0xc9, //0x00000187 xorl         %ecx, %ecx
	0x81, 0xfe, 0x40, 0x42, 0x0f, 0x00, //0x00000189 cmpl         $1000000, %esi
	0x0f, 0x82, 0x76, 0x00, 0x00, 0x00, //0x0000018f jb           LBB0_13
	//0x00000195 LBB0_12
	0x44, 0x89, 0xd0, //0x00000195 movl         %r10d, %eax
	0x48, 0x83, 0xc8, 0x01, //0x00000198 orq          $1, %rax
	0x48, 0x8d, 0x35, 0x6d, 0x03, 0x00, 0x00, //0x0000019c leaq         $877(%rip), %rsi  /* _Digits+0(%rip) */
	0x8a, 0x04, 0x30, //0x000001a3 movb         (%rax,%rsi), %al
	0x89, 0xce, //0x000001a6 movl         %ecx, %esi
	0xff, 0xc1, //0x000001a8 incl         %ecx
	0x88, 0x04, 0x37, //0x000001aa movb         %al, (%rdi,%rsi)
	//0x000001ad LBB0_14
	0x48, 0x8d, 0x05, 0x5c, 0x03, 0x00, 0x00, //0x000001ad leaq         $860(%rip), %rax  /* _Digits+0(%rip) */
	0x41, 0x8a, 0x04, 0x01, //0x000001b4 movb         (%r9,%rax), %al
	0x89, 0xce, //0x000001b8 movl         %ecx, %esi
	0xff, 0xc1, //0x000001ba incl         %ecx
	0x88, 0x04, 0x37, //0x000001bc movb         %al, (%rdi,%rsi)
	//0x000001bf LBB0_15
	0x41, 0x0f, 0xb7, 0xc1, //0x000001bf movzwl       %r9w, %eax
	0x48, 0x83, 0xc8, 0x01, //0x000001c3 orq          $1, %rax
	0x48, 0x8d, 0x35, 0x42, 0x03, 0x00, 0x00, //0x000001c7 leaq         $834(%rip), %rsi  /* _Digits+0(%rip) */
	0x8a, 0x04, 0x30, //0x000001ce movb         (%rax,%rsi), %al
	0x89, 0xca, //0x000001d1 movl         %ecx, %edx
	0x88, 0x04, 0x3a, //0x000001d3 movb         %al, (%rdx,%rdi)
	0x41, 0x8a, 0x04, 0x30, //0x000001d6 movb         (%r8,%rsi), %al
	0x88, 0x44, 0x3a, 0x01, //0x000001da movb         %al, $1(%rdx,%rdi)
	0x41, 0x0f, 0xb7, 0xc0, //0x000001de movzwl       %r8w, %eax
	0x48, 0x83, 0xc8, 0x01, //0x000001e2 orq          $1, %rax
	0x8a, 0x04, 0x30, //0x000001e6 movb         (%rax,%rsi), %al
	0x88, 0x44, 0x3a, 0x02, //0x000001e9 movb         %al, $2(%rdx,%rdi)
	0x41, 0x8a, 0x04, 0x33, //0x000001ed movb         (%r11,%rsi), %al
	0x88, 0x44, 0x3a, 0x03, //0x000001f1 movb         %al, $3(%rdx,%rdi)
	0x41, 0x0f, 0xb7, 0xc3, //0x000001f5 movzwl       %r11w, %eax
	0x48, 0x83, 0xc8, 0x01, //0x000001f9 orq          $1, %rax
	0x8a, 0x04, 0x30, //0x000001fd movb         (%rax,%rsi), %al
	0x83, 0xc1, 0x05, //0x00000200 addl         $5, %ecx
	0x88, 0x44, 0x3a, 0x04, //0x00000203 movb         %al, $4(%rdx,%rdi)
	0x89, 0xc8, //0x00000207 movl         %ecx, %eax
	0x5d, //0x00000209 popq         %rbp
	0xc3, //0x0000020a retq         
	//0x0000020b LBB0_13
	0x31, 0xc9, //0x0000020b xorl         %ecx, %ecx
	0x81, 0xfe, 0xa0, 0x86, 0x01, 0x00, //0x0000020d cmpl         $100000, %esi
	0x0f, 0x83, 0x94, 0xff, 0xff, 0xff, //0x00000213 jae          LBB0_14
	0xe9, 0xa1, 0xff, 0xff, 0xff, //0x00000219 jmp          LBB0_15
	//0x0000021e LBB0_16
	0x48, 0xb8, 0xff, 0xff, 0xc0, 0x6f, 0xf2, 0x86, 0x23, 0x00, //0x0000021e movabsq      $9999999999999999, %rax
	0x48, 0x39, 0xc6, //0x00000228 cmpq         %rax, %rsi
	0x0f, 0x87, 0x05, 0x01, 0x00, 0x00, //0x0000022b ja           LBB0_18
	0x48, 0xb9, 0xfd, 0xce, 0x61, 0x84, 0x11, 0x77, 0xcc, 0xab, //0x00000231 movabsq      $-6067343680855748867, %rcx
	0x48, 0x89, 0xf0, //0x0000023b movq         %rsi, %rax
	0x48, 0xf7, 0xe1, //0x0000023e mulq         %rcx
	0x48, 0xc1, 0xea, 0x1a, //0x00000241 shrq         $26, %rdx
	0x69, 0xc2, 0x00, 0xe1, 0xf5, 0x05, //0x00000245 imull        $100000000, %edx, %eax
	0x29, 0xc6, //0x0000024b subl         %eax, %esi
	0xc5, 0xf9, 0x6e, 0xc2, //0x0000024d vmovd        %edx, %xmm0
	0xc5, 0xfa, 0x6f, 0x0d, 0xa7, 0xfd, 0xff, 0xff, //0x00000251 vmovdqu      $-601(%rip), %xmm1  /* LCPI0_0+0(%rip) */
	0xc5, 0xf9, 0xf4, 0xd1, //0x00000259 vpmuludq     %xmm1, %xmm0, %xmm2
	0xc5, 0xe9, 0x73, 0xd2, 0x2d, //0x0000025d vpsrlq       $45, %xmm2, %xmm2
	0xb8, 0x10, 0x27, 0x00, 0x00, //0x00000262 movl         $10000, %eax
	0xc4, 0xe1, 0xf9, 0x6e, 0xd8, //0x00000267 vmovq        %rax, %xmm3
	0xc5, 0xe9, 0xf4, 0xe3, //0x0000026c vpmuludq     %xmm3, %xmm2, %xmm4
	0xc5, 0xf9, 0xfa, 0xc4, //0x00000270 vpsubd       %xmm4, %xmm0, %xmm0
	0xc5, 0xe9, 0x61, 0xc0, //0x00000274 vpunpcklwd   %xmm0, %xmm2, %xmm0
	0xc5, 0xf9, 0x73, 0xf0, 0x02, //0x00000278 vpsllq       $2, %xmm0, %xmm0
	0xc5, 0xfb, 0x70, 0xc0, 0x50, //0x0000027d vpshuflw     $80, %xmm0, %xmm0
	0xc5, 0xf9, 0x70, 0xc0, 0x50, //0x00000282 vpshufd      $80, %xmm0, %xmm0
	0xc5, 0xfb, 0x12, 0x15, 0xa1, 0xfd, 0xff, 0xff, //0x00000287 vmovddup     $-607(%rip), %xmm2  /* LCPI0_1+0(%rip) */
	0xc5, 0xf9, 0xe4, 0xc2, //0x0000028f vpmulhuw     %xmm2, %xmm0, %xmm0
	0xc5, 0xfb, 0x12, 0x25, 0x9d, 0xfd, 0xff, 0xff, //0x00000293 vmovddup     $-611(%rip), %xmm4  /* LCPI0_2+0(%rip) */
	0xc5, 0xf9, 0xe4, 0xc4, //0x0000029b vpmulhuw     %xmm4, %xmm0, %xmm0
	0xc5, 0xfa, 0x6f, 0x2d, 0x69, 0xfd, 0xff, 0xff, //0x0000029f vmovdqu      $-663(%rip), %xmm5  /* LCPI0_3+0(%rip) */
	0xc5, 0xf9, 0xd5, 0xf5, //0x000002a7 vpmullw      %xmm5, %xmm0, %xmm6
	0xc5, 0xc9, 0x73, 0xf6, 0x10, //0x000002ab vpsllq       $16, %xmm6, %xmm6
	0xc5, 0xf9, 0xf9, 0xc6, //0x000002b0 vpsubw       %xmm6, %xmm0, %xmm0
	0xc5, 0xf9, 0x6e, 0xf6, //0x000002b4 vmovd        %esi, %xmm6
	0xc5, 0xc9, 0xf4, 0xc9, //0x000002b8 vpmuludq     %xmm1, %xmm6, %xmm1
	0xc5, 0xf1, 0x73, 0xd1, 0x2d, //0x000002bc vpsrlq       $45, %xmm1, %xmm1
	0xc5, 0xf1, 0xf4, 0xdb, //0x000002c1 vpmuludq     %xmm3, %xmm1, %xmm3
	0xc5, 0xc9, 0xfa, 0xdb, //0x000002c5 vpsubd       %xmm3, %xmm6, %xmm3
	0xc5, 0xf1, 0x61, 0xcb, //0x000002c9 vpunpcklwd   %xmm3, %xmm1, %xmm1
	0xc5, 0xf1, 0x73, 0xf1, 0x02, //0x000002cd vpsllq       $2, %xmm1, %xmm1
	0xc5, 0xfb, 0x70, 0xc9, 0x50, //0x000002d2 vpshuflw     $80, %xmm1, %xmm1
	0xc5, 0xf9, 0x70, 0xc9, 0x50, //0x000002d7 vpshufd      $80, %xmm1, %xmm1
	0xc5, 0xf1, 0xe4, 0xca, //0x000002dc vpmulhuw     %xmm2, %xmm1, %xmm1
	0xc5, 0xf1, 0xe4, 0xcc, //0x000002e0 vpmulhuw     %xmm4, %xmm1, %xmm1
	0xc5, 0xf1, 0xd5, 0xd5, //0x000002e4 vpmullw      %xmm5, %xmm1, %xmm2
	0xc5, 0xe9, 0x73, 0xf2, 0x10, //0x000002e8 vpsllq       $16, %xmm2, %xmm2
	0xc5, 0xf1, 0xf9, 0xca, //0x000002ed vpsubw       %xmm2, %xmm1, %xmm1
	0xc5, 0xf9, 0x67, 0xc1, //0x000002f1 vpackuswb    %xmm1, %xmm0, %xmm0
	0xc5, 0xf9, 0xfc, 0x0d, 0x23, 0xfd, 0xff, 0xff, //0x000002f5 vpaddb       $-733(%rip), %xmm0, %xmm1  /* LCPI0_4+0(%rip) */
	0xc5, 0xe9, 0xef, 0xd2, //0x000002fd vpxor        %xmm2, %xmm2, %xmm2
	0xc5, 0xf9, 0x74, 0xc2, //0x00000301 vpcmpeqb     %xmm2, %xmm0, %xmm0
	0xc5, 0xf9, 0xd7, 0xc0, //0x00000305 vpmovmskb    %xmm0, %eax
	0x0d, 0x00, 0x80, 0x00, 0x00, //0x00000309 orl          $32768, %eax
	0x35, 0xff, 0x7f, 0xff, 0xff, //0x0000030e xorl         $-32769, %eax
	0x0f, 0xbc, 0xc0, //0x00000313 bsfl         %eax, %eax
	0xb9, 0x10, 0x00, 0x00, 0x00, //0x00000316 movl         $16, %ecx
	0x29, 0xc1, //0x0000031b subl         %eax, %ecx
	0x48, 0xc1, 0xe0, 0x04, //0x0000031d shlq         $4, %rax
	0x48, 0x8d, 0x15, 0xb8, 0x02, 0x00, 0x00, //0x00000321 leaq         $696(%rip), %rdx  /* _VecShiftShuffles+0(%rip) */
	0xc4, 0xe2, 0x71, 0x00, 0x04, 0x10, //0x00000328 vpshufb      (%rax,%rdx), %xmm1, %xmm0
	0xc5, 0xfa, 0x7f, 0x07, //0x0000032e vmovdqu      %xmm0, (%rdi)
	0x89, 0xc8, //0x00000332 movl         %ecx, %eax
	0x5d, //0x00000334 popq         %rbp
	0xc3, //0x00000335 retq         
	//0x00000336 LBB0_18
	0x48, 0xb9, 0x57, 0x78, 0x13, 0xb1, 0x2f, 0x65, 0xa5, 0x39, //0x00000336 movabsq      $4153837486827862103, %rcx
	0x48, 0x89, 0xf0, //0x00000340 movq         %rsi, %rax
	0x48, 0xf7, 0xe1, //0x00000343 mulq         %rcx
	0x48, 0xc1, 0xea, 0x33, //0x00000346 shrq         $51, %rdx
	0x48, 0xb8, 0x00, 0x00, 0xc1, 0x6f, 0xf2, 0x86, 0x23, 0x00, //0x0000034a movabsq      $10000000000000000, %rax
	0x48, 0x0f, 0xaf, 0xc2, //0x00000354 imulq        %rdx, %rax
	0x48, 0x29, 0xc6, //0x00000358 subq         %rax, %rsi
	0x83, 0xfa, 0x09, //0x0000035b cmpl         $9, %edx
	0x0f, 0x87, 0x0f, 0x00, 0x00, 0x00, //0x0000035e ja           LBB0_20
	0x80, 0xc2, 0x30, //0x00000364 addb         $48, %dl
	0x88, 0x17, //0x00000367 movb         %dl, (%rdi)
	0xb9, 0x01, 0x00, 0x00, 0x00, //0x00000369 movl         $1, %ecx
	0xe9, 0xba, 0x00, 0x00, 0x00, //0x0000036e jmp          LBB0_25
	//0x00000373 LBB0_20
	0x83, 0xfa, 0x63, //0x00000373 cmpl         $99, %edx
	0x0f, 0x87, 0x1f, 0x00, 0x00, 0x00, //0x00000376 ja           LBB0_22
	0x89, 0xd0, //0x0000037c movl         %edx, %eax
	0x48, 0x8d, 0x0d, 0x8b, 0x01, 0x00, 0x00, //0x0000037e leaq         $395(%rip), %rcx  /* _Digits+0(%rip) */
	0x8a, 0x14, 0x41, //0x00000385 movb         (%rcx,%rax,2), %dl
	0x8a, 0x44, 0x41, 0x01, //0x00000388 movb         $1(%rcx,%rax,2), %al
	0x88, 0x17, //0x0000038c movb         %dl, (%rdi)
	0x88, 0x47, 0x01, //0x0000038e movb         %al, $1(%rdi)
	0xb9, 0x02, 0x00, 0x00, 0x00, //0x00000391 movl         $2, %ecx
	0xe9, 0x92, 0x00, 0x00, 0x00, //0x00000396 jmp          LBB0_25
	//0x0000039b LBB0_22
	0x89, 0xd0, //0x0000039b movl         %edx, %eax
	0xc1, 0xe8, 0x02, //0x0000039d shrl         $2, %eax
	0x69, 0xc0, 0x7b, 0x14, 0x00, 0x00, //0x000003a0 imull        $5243, %eax, %eax
	0xc1, 0xe8, 0x11, //0x000003a6 shrl         $17, %eax
	0x81, 0xfa, 0xe7, 0x03, 0x00, 0x00, //0x000003a9 cmpl         $999, %edx
	0x0f, 0x87, 0x3c, 0x00, 0x00, 0x00, //0x000003af ja           LBB0_24
	0x83, 0xc0, 0x30, //0x000003b5 addl         $48, %eax
	0x88, 0x07, //0x000003b8 movb         %al, (%rdi)
	0x0f, 0xb7, 0xc2, //0x000003ba movzwl       %dx, %eax
	0x89, 0xc1, //0x000003bd movl         %eax, %ecx
	0xc1, 0xe9, 0x02, //0x000003bf shrl         $2, %ecx
	0x69, 0xc9, 0x7b, 0x14, 0x00, 0x00, //0x000003c2 imull        $5243, %ecx, %ecx
	0xc1, 0xe9, 0x11, //0x000003c8 shrl         $17, %ecx
	0x6b, 0xc9, 0x64, //0x000003cb imull        $100, %ecx, %ecx
	0x29, 0xc8, //0x000003ce subl         %ecx, %eax
	0x0f, 0xb7, 0xc0, //0x000003d0 movzwl       %ax, %eax
	0x48, 0x8d, 0x0d, 0x36, 0x01, 0x00, 0x00, //0x000003d3 leaq         $310(%rip), %rcx  /* _Digits+0(%rip) */
	0x8a, 0x14, 0x41, //0x000003da movb         (%rcx,%rax,2), %dl
	0x8a, 0x44, 0x41, 0x01, //0x000003dd movb         $1(%rcx,%rax,2), %al
	0x88, 0x57, 0x01, //0x000003e1 movb         %dl, $1(%rdi)
	0x88, 0x47, 0x02, //0x000003e4 movb         %al, $2(%rdi)
	0xb9, 0x03, 0x00, 0x00, 0x00, //0x000003e7 movl         $3, %ecx
	0xe9, 0x3c, 0x00, 0x00, 0x00, //0x000003ec jmp          LBB0_25
	//0x000003f1 LBB0_24
	0x6b, 0xc8, 0x64, //0x000003f1 imull        $100, %eax, %ecx
	0x29, 0xca, //0x000003f4 subl         %ecx, %edx
	0x0f, 0xb7, 0xc0, //0x000003f6 movzwl       %ax, %eax
	0x4c, 0x8d, 0x05, 0x10, 0x01, 0x00, 0x00, //0x000003f9 leaq         $272(%rip), %r8  /* _Digits+0(%rip) */
	0x41, 0x8a, 0x0c, 0x40, //0x00000400 movb         (%r8,%rax,2), %cl
	0x41, 0x8a, 0x44, 0x40, 0x01, //0x00000404 movb         $1(%r8,%rax,2), %al
	0x88, 0x0f, //0x00000409 movb         %cl, (%rdi)
	0x88, 0x47, 0x01, //0x0000040b movb         %al, $1(%rdi)
	0x0f, 0xb7, 0xc2, //0x0000040e movzwl       %dx, %eax
	0x41, 0x8a, 0x0c, 0x40, //0x00000411 movb         (%r8,%rax,2), %cl
	0x48, 0x01, 0xc0, //0x00000415 addq         %rax, %rax
	0x88, 0x4f, 0x02, //0x00000418 movb         %cl, $2(%rdi)
	0x83, 0xc8, 0x01, //0x0000041b orl          $1, %eax
	0x0f, 0xb7, 0xc0, //0x0000041e movzwl       %ax, %eax
	0x42, 0x8a, 0x04, 0x00, //0x00000421 movb         (%rax,%r8), %al
	0x88, 0x47, 0x03, //0x00000425 movb         %al, $3(%rdi)
	0xb9, 0x04, 0x00, 0x00, 0x00, //0x00000428 movl         $4, %ecx
	//0x0000042d LBB0_25
	0x48, 0xba, 0xfd, 0xce, 0x61, 0x84, 0x11, 0x77, 0xcc, 0xab, //0x0000042d movabsq      $-6067343680855748867, %rdx
	0x48, 0x89, 0xf0, //0x00000437 movq         %rsi, %rax
	0x48, 0xf7, 0xe2, //0x0000043a mulq         %rdx
	0x48, 0xc1, 0xea, 0x1a, //0x0000043d shrq         $26, %rdx
	0xc5, 0xf9, 0x6e, 0xc2, //0x00000441 vmovd        %edx, %xmm0
	0xc5, 0xfa, 0x6f, 0x0d, 0xb3, 0xfb, 0xff, 0xff, //0x00000445 vmovdqu      $-1101(%rip), %xmm1  /* LCPI0_0+0(%rip) */
	0xc5, 0xf9, 0xf4, 0xd1, //0x0000044d vpmuludq     %xmm1, %xmm0, %xmm2
	0xc5, 0xe9, 0x73, 0xd2, 0x2d, //0x00000451 vpsrlq       $45, %xmm2, %xmm2
	0xb8, 0x10, 0x27, 0x00, 0x00, //0x00000456 movl         $10000, %eax
	0xc4, 0xe1, 0xf9, 0x6e, 0xd8, //0x0000045b vmovq        %rax, %xmm3
	0xc5, 0xe9, 0xf4, 0xe3, //0x00000460 vpmuludq     %xmm3, %xmm2, %xmm4
	0xc5, 0xf9, 0xfa, 0xc4, //0x00000464 vpsubd       %xmm4, %xmm0, %xmm0
	0xc5, 0xe9, 0x61, 0xc0, //0x00000468 vpunpcklwd   %xmm0, %xmm2, %xmm0
	0xc5, 0xf9, 0x73, 0xf0, 0x02, //0x0000046c vpsllq       $2, %xmm0, %xmm0
	0xc5, 0xfb, 0x70, 0xc0, 0x50, //0x00000471 vpshuflw     $80, %xmm0, %xmm0
	0xc5, 0xf9, 0x70, 0xc0, 0x50, //0x00000476 vpshufd      $80, %xmm0, %xmm0
	0xc5, 0xfb, 0x12, 0x15, 0xad, 0xfb, 0xff, 0xff, //0x0000047b vmovddup     $-1107(%rip), %xmm2  /* LCPI0_1+0(%rip) */
	0xc5, 0xf9, 0xe4, 0xc2, //0x00000483 vpmulhuw     %xmm2, %xmm0, %xmm0
	0xc5, 0xfb, 0x12, 0x25, 0xa9, 0xfb, 0xff, 0xff, //0x00000487 vmovddup     $-1111(%rip), %xmm4  /* LCPI0_2+0(%rip) */
	0xc5, 0xf9, 0xe4, 0xc4, //0x0000048f vpmulhuw     %xmm4, %xmm0, %xmm0
	0xc5, 0xfa, 0x6f, 0x2d, 0x75, 0xfb, 0xff, 0xff, //0x00000493 vmovdqu      $-1163(%rip), %xmm5  /* LCPI0_3+0(%rip) */
	0xc5, 0xf9, 0xd5, 0xf5, //0x0000049b vpmullw      %xmm5, %xmm0, %xmm6
	0xc5, 0xc9, 0x73, 0xf6, 0x10, //0x0000049f vpsllq       $16, %xmm6, %xmm6
	0xc5, 0xf9, 0xf9, 0xc6, //0x000004a4 vpsubw       %xmm6, %xmm0, %xmm0
	0x69, 0xc2, 0x00, 0xe1, 0xf5, 0x05, //0x000004a8 imull        $100000000, %edx, %eax
	0x29, 0xc6, //0x000004ae subl         %eax, %esi
	0xc5, 0xf9, 0x6e, 0xf6, //0x000004b0 vmovd        %esi, %xmm6
	0xc5, 0xc9, 0xf4, 0xc9, //0x000004b4 vpmuludq     %xmm1, %xmm6, %xmm1
	0xc5, 0xf1, 0x73, 0xd1, 0x2d, //0x000004b8 vpsrlq       $45, %xmm1, %xmm1
	0xc5, 0xf1, 0xf4, 0xdb, //0x000004bd vpmuludq     %xmm3, %xmm1, %xmm3
	0xc5, 0xc9, 0xfa, 0xdb, //0x000004c1 vpsubd       %xmm3, %xmm6, %xmm3
	0xc5, 0xf1, 0x61, 0xcb, //0x000004c5 vpunpcklwd   %xmm3, %xmm1, %xmm1
	0xc5, 0xf1, 0x73, 0xf1, 0x02, //0x000004c9 vpsllq       $2, %xmm1, %xmm1
	0xc5, 0xfb, 0x70, 0xc9, 0x50, //0x000004ce vpshuflw     $80, %xmm1, %xmm1
	0xc5, 0xf9, 0x70, 0xc9, 0x50, //0x000004d3 vpshufd      $80, %xmm1, %xmm1
	0xc5, 0xf1, 0xe4, 0xca, //0x000004d8 vpmulhuw     %xmm2, %xmm1, %xmm1
	0xc5, 0xf1, 0xe4, 0xcc, //0x000004dc vpmulhuw     %xmm4, %xmm1, %xmm1
	0xc5, 0xf1, 0xd5, 0xd5, //0x000004e0 vpmullw      %xmm5, %xmm1, %xmm2
	0xc5, 0xe9, 0x73, 0xf2, 0x10, //0x000004e4 vpsllq       $16, %xmm2, %xmm2
	0xc5, 0xf1, 0xf9, 0xca, //0x000004e9 vpsubw       %xmm2, %xmm1, %xmm1
	0xc5, 0xf9, 0x67, 0xc1, //0x000004ed vpackuswb    %xmm1, %xmm0, %xmm0
	0xc5, 0xf9, 0xfc, 0x05, 0x27, 0xfb, 0xff, 0xff, //0x000004f1 vpaddb       $-1241(%rip), %xmm0, %xmm0  /* LCPI0_4+0(%rip) */
	0x89, 0xc8, //0x000004f9 movl         %ecx, %eax
	0xc5, 0xfa, 0x7f, 0x04, 0x07, //0x000004fb vmovdqu      %xmm0, (%rdi,%rax)
	0x83, 0xc9, 0x10, //0x00000500 orl          $16, %ecx
	0x89, 0xc8, //0x00000503 movl         %ecx, %eax
	0x5d, //0x00000505 popq         %rbp
	0xc3, //0x00000506 retq         
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000507 .p2align 4, 0x00
	//0x00000510 _Digits
	0x30, 0x30, 0x30, 0x31, 0x30, 0x32, 0x30, 0x33, 0x30, 0x34, 0x30, 0x35, 0x30, 0x36, 0x30, 0x37, //0x00000510 QUAD $0x3330323031303030; QUAD $0x3730363035303430  // .ascii 16, '0001020304050607'
	0x30, 0x38, 0x30, 0x39, 0x31, 0x30, 0x31, 0x31, 0x31, 0x32, 0x31, 0x33, 0x31, 0x34, 0x31, 0x35, //0x00000520 QUAD $0x3131303139303830; QUAD $0x3531343133313231  // .ascii 16, '0809101112131415'
	0x31, 0x36, 0x31, 0x37, 0x31, 0x38, 0x31, 0x39, 0x32, 0x30, 0x32, 0x31, 0x32, 0x32, 0x32, 0x33, //0x00000530 QUAD $0x3931383137313631; QUAD $0x3332323231323032  // .ascii 16, '1617181920212223'
	0x32, 0x34, 0x32, 0x35, 0x32, 0x36, 0x32, 0x37, 0x32, 0x38, 0x32, 0x39, 0x33, 0x30, 0x33, 0x31, //0x00000540 QUAD $0x3732363235323432; QUAD $0x3133303339323832  // .ascii 16, '2425262728293031'
	0x33, 0x32, 0x33, 0x33, 0x33, 0x34, 0x33, 0x35, 0x33, 0x36, 0x33, 0x37, 0x33, 0x38, 0x33, 0x39, //0x00000550 QUAD $0x3533343333333233; QUAD $0x3933383337333633  // .ascii 16, '3233343536373839'
	0x34, 0x30, 0x34, 0x31, 0x34, 0x32, 0x34, 0x33, 0x34, 0x34, 0x34, 0x35, 0x34, 0x36, 0x34, 0x37, //0x00000560 QUAD $0x3334323431343034; QUAD $0x3734363435343434  // .ascii 16, '4041424344454647'
	0x34, 0x38, 0x34, 0x39, 0x35, 0x30, 0x35, 0x31, 0x35, 0x32, 0x35, 0x33, 0x35, 0x34, 0x35, 0x35, //0x00000570 QUAD $0x3135303539343834; QUAD $0x3535343533353235  // .ascii 16, '4849505152535455'
	0x35, 0x36, 0x35, 0x37, 0x35, 0x38, 0x35, 0x39, 0x36, 0x30, 0x36, 0x31, 0x36, 0x32, 0x36, 0x33, //0x00000580 QUAD $0x3935383537353635; QUAD $0x3336323631363036  // .ascii 16, '5657585960616263'
	0x36, 0x34, 0x36, 0x35, 0x36, 0x36, 0x36, 0x37, 0x36, 0x38, 0x36, 0x39, 0x37, 0x30, 0x37, 0x31, //0x00000590 QUAD $0x3736363635363436; QUAD $0x3137303739363836  // .ascii 16, '6465666768697071'
	0x37, 0x32, 0x37, 0x33, 0x37, 0x34, 0x37, 0x35, 0x37, 0x36, 0x37, 0x37, 0x37, 0x38, 0x37, 0x39, //0x000005a0 QUAD $0x3537343733373237; QUAD $0x3937383737373637  // .ascii 16, '7273747576777879'
	0x38, 0x30, 0x38, 0x31, 0x38, 0x32, 0x38, 0x33, 0x38, 0x34, 0x38, 0x35, 0x38, 0x36, 0x38, 0x37, //0x000005b0 QUAD $0x3338323831383038; QUAD $0x3738363835383438  // .ascii 16, '8081828384858687'
	0x38, 0x38, 0x38, 0x39, 0x39, 0x30, 0x39, 0x31, 0x39, 0x32, 0x39, 0x33, 0x39, 0x34, 0x39, 0x35, //0x000005c0 QUAD $0x3139303939383838; QUAD $0x3539343933393239  // .ascii 16, '8889909192939495'
	0x39, 0x36, 0x39, 0x37, 0x39, 0x38, 0x39, 0x39, //0x000005d0 QUAD $0x3939383937393639  // .ascii 8, '96979899'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000005d8 .p2align 4, 0x00
	//0x000005e0 _VecShiftShuffles
	0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, //0x000005e0 QUAD $0x0706050403020100; QUAD $0x0f0e0d0c0b0a0908  // .ascii 16, '\x00\x01\x02\x03\x04\x05\x06\x07\x08\t\n\x0b\x0c\r\x0e\x0f'
	0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, 0xff, //0x000005f0 QUAD $0x0807060504030201; QUAD $0xff0f0e0d0c0b0a09  // .ascii 16, '\x01\x02\x03\x04\x05\x06\x07\x08\t\n\x0b\x0c\r\x0e\x0f\xff'
	0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, 0xff, 0xff, //0x00000600 QUAD $0x0908070605040302; QUAD $0xffff0f0e0d0c0b0a  // .ascii 16, '\x02\x03\x04\x05\x06\x07\x08\t\n\x0b\x0c\r\x0e\x0f\xff\xff'
	0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, 0xff, 0xff, 0xff, //0x00000610 QUAD $0x0a09080706050403; QUAD $0xffffff0f0e0d0c0b  // .ascii 16, '\x03\x04\x05\x06\x07\x08\t\n\x0b\x0c\r\x0e\x0f\xff\xff\xff'
	0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, 0xff, 0xff, 0xff, 0xff, //0x00000620 QUAD $0x0b0a090807060504; QUAD $0xffffffff0f0e0d0c  // .ascii 16, '\x04\x05\x06\x07\x08\t\n\x0b\x0c\r\x0e\x0f\xff\xff\xff\xff'
	0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00000630 QUAD $0x0c0b0a0908070605; QUAD $0xffffffffff0f0e0d  // .ascii 16, '\x05\x06\x07\x08\t\n\x0b\x0c\r\x0e\x0f\xff\xff\xff\xff\xff'
	0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00000640 QUAD $0x0d0c0b0a09080706; QUAD $0xffffffffffff0f0e  // .ascii 16, '\x06\x07\x08\t\n\x0b\x0c\r\x0e\x0f\xff\xff\xff\xff\xff\xff'
	0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00000650 QUAD $0x0e0d0c0b0a090807; QUAD $0xffffffffffffff0f  // .ascii 16, '\x07\x08\t\n\x0b\x0c\r\x0e\x0f\xff\xff\xff\xff\xff\xff\xff'
	0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00000660 QUAD $0x0f0e0d0c0b0a0908; QUAD $0xffffffffffffffff  // .ascii 16, '\x08\t\n\x0b\x0c\r\x0e\x0f\xff\xff\xff\xff\xff\xff\xff\xff'
}
 
