// +build !noasm !appengine
// Code generated by asm2asm, DO NOT EDIT.

package avx

import (
	`github.com/bytedance/sonic/loader`
)

const (
    _entry__f32toa = 32
)

const (
    _stack__f32toa = 48
)

const (
    _size__f32toa = 3392
)

var (
    _pcsp__f32toa = [][2]uint32{
        {1, 0},
        {4, 8},
        {6, 16},
        {8, 24},
        {10, 32},
        {12, 40},
        {3350, 48},
        {3351, 40},
        {3353, 32},
        {3355, 24},
        {3357, 16},
        {3359, 8},
        {3363, 0},
        {3385, 48},
    }
)

var _cfunc_f32toa = []loader.CFunc{
    {"_f32toa_entry", 0,  _entry__f32toa, 0, nil},
    {"_f32toa", _entry__f32toa, _size__f32toa, _stack__f32toa, _pcsp__f32toa},
}
