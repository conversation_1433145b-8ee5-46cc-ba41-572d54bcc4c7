// +build amd64
// Code generated by asm2asm, DO NOT EDIT.

package avx

var _text_i64toa = []byte{
	// .p2align 4, 0x00
	// LCPI0_0
	0x59, 0x17, 0xb7, 0xd1, 0x00, 0x00, 0x00, 0x00, // .quad 3518437209
	0x59, 0x17, 0xb7, 0xd1, 0x00, 0x00, 0x00, 0x00, //0x00000008 .quad 3518437209
	//0x00000010 LCPI0_3
	0x0a, 0x00, //0x00000010 .word 10
	0x0a, 0x00, //0x00000012 .word 10
	0x0a, 0x00, //0x00000014 .word 10
	0x0a, 0x00, //0x00000016 .word 10
	0x0a, 0x00, //0x00000018 .word 10
	0x0a, 0x00, //0x0000001a .word 10
	0x0a, 0x00, //0x0000001c .word 10
	0x0a, 0x00, //0x0000001e .word 10
	//0x00000020 LCPI0_4
	0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, //0x00000020 QUAD $0x3030303030303030; QUAD $0x3030303030303030  // .space 16, '0000000000000000'
	//0x00000030 .p2align 3, 0x00
	//0x00000030 LCPI0_1
	0xc5, 0x20, 0x7b, 0x14, 0x34, 0x33, 0x00, 0x80, //0x00000030 .quad -9223315738079846203
	//0x00000038 LCPI0_2
	0x80, 0x00, 0x00, 0x08, 0x00, 0x20, 0x00, 0x80, //0x00000038 .quad -9223336852348469120
	//0x00000040 .p2align 4, 0x90
	//0x00000040 _i64toa
	0x55, //0x00000040 pushq        %rbp
	0x48, 0x89, 0xe5, //0x00000041 movq         %rsp, %rbp
	0x48, 0x85, 0xf6, //0x00000044 testq        %rsi, %rsi
	0x0f, 0x88, 0xaf, 0x00, 0x00, 0x00, //0x00000047 js           LBB0_25
	0x48, 0x81, 0xfe, 0x0f, 0x27, 0x00, 0x00, //0x0000004d cmpq         $9999, %rsi
	0x0f, 0x87, 0xf8, 0x00, 0x00, 0x00, //0x00000054 ja           LBB0_9
	0x0f, 0xb7, 0xc6, //0x0000005a movzwl       %si, %eax
	0xc1, 0xe8, 0x02, //0x0000005d shrl         $2, %eax
	0x69, 0xc0, 0x7b, 0x14, 0x00, 0x00, //0x00000060 imull        $5243, %eax, %eax
	0xc1, 0xe8, 0x11, //0x00000066 shrl         $17, %eax
	0x48, 0x8d, 0x14, 0x00, //0x00000069 leaq         (%rax,%rax), %rdx
	0x6b, 0xc0, 0x64, //0x0000006d imull        $100, %eax, %eax
	0x89, 0xf1, //0x00000070 movl         %esi, %ecx
	0x29, 0xc1, //0x00000072 subl         %eax, %ecx
	0x0f, 0xb7, 0xc1, //0x00000074 movzwl       %cx, %eax
	0x48, 0x01, 0xc0, //0x00000077 addq         %rax, %rax
	0x81, 0xfe, 0xe8, 0x03, 0x00, 0x00, //0x0000007a cmpl         $1000, %esi
	0x0f, 0x82, 0x16, 0x00, 0x00, 0x00, //0x00000080 jb           LBB0_4
	0x48, 0x8d, 0x0d, 0x93, 0x08, 0x00, 0x00, //0x00000086 leaq         $2195(%rip), %rcx  /* _Digits+0(%rip) */
	0x8a, 0x0c, 0x0a, //0x0000008d movb         (%rdx,%rcx), %cl
	0x88, 0x0f, //0x00000090 movb         %cl, (%rdi)
	0xb9, 0x01, 0x00, 0x00, 0x00, //0x00000092 movl         $1, %ecx
	0xe9, 0x0b, 0x00, 0x00, 0x00, //0x00000097 jmp          LBB0_5
	//0x0000009c LBB0_4
	0x31, 0xc9, //0x0000009c xorl         %ecx, %ecx
	0x83, 0xfe, 0x64, //0x0000009e cmpl         $100, %esi
	0x0f, 0x82, 0x45, 0x00, 0x00, 0x00, //0x000000a1 jb           LBB0_6
	//0x000000a7 LBB0_5
	0x0f, 0xb7, 0xd2, //0x000000a7 movzwl       %dx, %edx
	0x48, 0x83, 0xca, 0x01, //0x000000aa orq          $1, %rdx
	0x48, 0x8d, 0x35, 0x6b, 0x08, 0x00, 0x00, //0x000000ae leaq         $2155(%rip), %rsi  /* _Digits+0(%rip) */
	0x8a, 0x14, 0x32, //0x000000b5 movb         (%rdx,%rsi), %dl
	0x89, 0xce, //0x000000b8 movl         %ecx, %esi
	0xff, 0xc1, //0x000000ba incl         %ecx
	0x88, 0x14, 0x37, //0x000000bc movb         %dl, (%rdi,%rsi)
	//0x000000bf LBB0_7
	0x48, 0x8d, 0x15, 0x5a, 0x08, 0x00, 0x00, //0x000000bf leaq         $2138(%rip), %rdx  /* _Digits+0(%rip) */
	0x8a, 0x14, 0x10, //0x000000c6 movb         (%rax,%rdx), %dl
	0x89, 0xce, //0x000000c9 movl         %ecx, %esi
	0xff, 0xc1, //0x000000cb incl         %ecx
	0x88, 0x14, 0x37, //0x000000cd movb         %dl, (%rdi,%rsi)
	//0x000000d0 LBB0_8
	0x0f, 0xb7, 0xc0, //0x000000d0 movzwl       %ax, %eax
	0x48, 0x83, 0xc8, 0x01, //0x000000d3 orq          $1, %rax
	0x48, 0x8d, 0x15, 0x42, 0x08, 0x00, 0x00, //0x000000d7 leaq         $2114(%rip), %rdx  /* _Digits+0(%rip) */
	0x8a, 0x04, 0x10, //0x000000de movb         (%rax,%rdx), %al
	0x89, 0xca, //0x000000e1 movl         %ecx, %edx
	0xff, 0xc1, //0x000000e3 incl         %ecx
	0x88, 0x04, 0x17, //0x000000e5 movb         %al, (%rdi,%rdx)
	0x89, 0xc8, //0x000000e8 movl         %ecx, %eax
	0x5d, //0x000000ea popq         %rbp
	0xc3, //0x000000eb retq         
	//0x000000ec LBB0_6
	0x31, 0xc9, //0x000000ec xorl         %ecx, %ecx
	0x83, 0xfe, 0x0a, //0x000000ee cmpl         $10, %esi
	0x0f, 0x83, 0xc8, 0xff, 0xff, 0xff, //0x000000f1 jae          LBB0_7
	0xe9, 0xd4, 0xff, 0xff, 0xff, //0x000000f7 jmp          LBB0_8
	//0x000000fc LBB0_25
	0xc6, 0x07, 0x2d, //0x000000fc movb         $45, (%rdi)
	0x48, 0xf7, 0xde, //0x000000ff negq         %rsi
	0x48, 0x81, 0xfe, 0x0f, 0x27, 0x00, 0x00, //0x00000102 cmpq         $9999, %rsi
	0x0f, 0x87, 0xd3, 0x01, 0x00, 0x00, //0x00000109 ja           LBB0_33
	0x0f, 0xb7, 0xc6, //0x0000010f movzwl       %si, %eax
	0xc1, 0xe8, 0x02, //0x00000112 shrl         $2, %eax
	0x69, 0xc0, 0x7b, 0x14, 0x00, 0x00, //0x00000115 imull        $5243, %eax, %eax
	0xc1, 0xe8, 0x11, //0x0000011b shrl         $17, %eax
	0x48, 0x8d, 0x14, 0x00, //0x0000011e leaq         (%rax,%rax), %rdx
	0x6b, 0xc0, 0x64, //0x00000122 imull        $100, %eax, %eax
	0x89, 0xf1, //0x00000125 movl         %esi, %ecx
	0x29, 0xc1, //0x00000127 subl         %eax, %ecx
	0x0f, 0xb7, 0xc1, //0x00000129 movzwl       %cx, %eax
	0x48, 0x01, 0xc0, //0x0000012c addq         %rax, %rax
	0x81, 0xfe, 0xe8, 0x03, 0x00, 0x00, //0x0000012f cmpl         $1000, %esi
	0x0f, 0x82, 0xab, 0x00, 0x00, 0x00, //0x00000135 jb           LBB0_28
	0x48, 0x8d, 0x0d, 0xde, 0x07, 0x00, 0x00, //0x0000013b leaq         $2014(%rip), %rcx  /* _Digits+0(%rip) */
	0x8a, 0x0c, 0x0a, //0x00000142 movb         (%rdx,%rcx), %cl
	0x88, 0x4f, 0x01, //0x00000145 movb         %cl, $1(%rdi)
	0xb9, 0x01, 0x00, 0x00, 0x00, //0x00000148 movl         $1, %ecx
	0xe9, 0x9f, 0x00, 0x00, 0x00, //0x0000014d jmp          LBB0_29
	//0x00000152 LBB0_9
	0x48, 0x81, 0xfe, 0xff, 0xe0, 0xf5, 0x05, //0x00000152 cmpq         $99999999, %rsi
	0x0f, 0x87, 0x18, 0x02, 0x00, 0x00, //0x00000159 ja           LBB0_17
	0x89, 0xf0, //0x0000015f movl         %esi, %eax
	0xba, 0x59, 0x17, 0xb7, 0xd1, //0x00000161 movl         $3518437209, %edx
	0x48, 0x0f, 0xaf, 0xd0, //0x00000166 imulq        %rax, %rdx
	0x48, 0xc1, 0xea, 0x2d, //0x0000016a shrq         $45, %rdx
	0x44, 0x69, 0xc2, 0x10, 0x27, 0x00, 0x00, //0x0000016e imull        $10000, %edx, %r8d
	0x89, 0xf1, //0x00000175 movl         %esi, %ecx
	0x44, 0x29, 0xc1, //0x00000177 subl         %r8d, %ecx
	0x4c, 0x69, 0xd0, 0x83, 0xde, 0x1b, 0x43, //0x0000017a imulq        $1125899907, %rax, %r10
	0x49, 0xc1, 0xea, 0x31, //0x00000181 shrq         $49, %r10
	0x41, 0x83, 0xe2, 0xfe, //0x00000185 andl         $-2, %r10d
	0x0f, 0xb7, 0xc2, //0x00000189 movzwl       %dx, %eax
	0xc1, 0xe8, 0x02, //0x0000018c shrl         $2, %eax
	0x69, 0xc0, 0x7b, 0x14, 0x00, 0x00, //0x0000018f imull        $5243, %eax, %eax
	0xc1, 0xe8, 0x11, //0x00000195 shrl         $17, %eax
	0x6b, 0xc0, 0x64, //0x00000198 imull        $100, %eax, %eax
	0x29, 0xc2, //0x0000019b subl         %eax, %edx
	0x44, 0x0f, 0xb7, 0xca, //0x0000019d movzwl       %dx, %r9d
	0x4d, 0x01, 0xc9, //0x000001a1 addq         %r9, %r9
	0x0f, 0xb7, 0xc1, //0x000001a4 movzwl       %cx, %eax
	0xc1, 0xe8, 0x02, //0x000001a7 shrl         $2, %eax
	0x69, 0xc0, 0x7b, 0x14, 0x00, 0x00, //0x000001aa imull        $5243, %eax, %eax
	0xc1, 0xe8, 0x11, //0x000001b0 shrl         $17, %eax
	0x4c, 0x8d, 0x04, 0x00, //0x000001b3 leaq         (%rax,%rax), %r8
	0x6b, 0xc0, 0x64, //0x000001b7 imull        $100, %eax, %eax
	0x29, 0xc1, //0x000001ba subl         %eax, %ecx
	0x44, 0x0f, 0xb7, 0xd9, //0x000001bc movzwl       %cx, %r11d
	0x4d, 0x01, 0xdb, //0x000001c0 addq         %r11, %r11
	0x81, 0xfe, 0x80, 0x96, 0x98, 0x00, //0x000001c3 cmpl         $10000000, %esi
	0x0f, 0x82, 0x6c, 0x00, 0x00, 0x00, //0x000001c9 jb           LBB0_12
	0x48, 0x8d, 0x05, 0x4a, 0x07, 0x00, 0x00, //0x000001cf leaq         $1866(%rip), %rax  /* _Digits+0(%rip) */
	0x41, 0x8a, 0x04, 0x02, //0x000001d6 movb         (%r10,%rax), %al
	0x88, 0x07, //0x000001da movb         %al, (%rdi)
	0xb9, 0x01, 0x00, 0x00, 0x00, //0x000001dc movl         $1, %ecx
	0xe9, 0x63, 0x00, 0x00, 0x00, //0x000001e1 jmp          LBB0_13
	//0x000001e6 LBB0_28
	0x31, 0xc9, //0x000001e6 xorl         %ecx, %ecx
	0x83, 0xfe, 0x64, //0x000001e8 cmpl         $100, %esi
	0x0f, 0x82, 0xce, 0x00, 0x00, 0x00, //0x000001eb jb           LBB0_30
	//0x000001f1 LBB0_29
	0x0f, 0xb7, 0xd2, //0x000001f1 movzwl       %dx, %edx
	0x48, 0x83, 0xca, 0x01, //0x000001f4 orq          $1, %rdx
	0x48, 0x8d, 0x35, 0x21, 0x07, 0x00, 0x00, //0x000001f8 leaq         $1825(%rip), %rsi  /* _Digits+0(%rip) */
	0x8a, 0x14, 0x32, //0x000001ff movb         (%rdx,%rsi), %dl
	0x89, 0xce, //0x00000202 movl         %ecx, %esi
	0xff, 0xc1, //0x00000204 incl         %ecx
	0x88, 0x54, 0x37, 0x01, //0x00000206 movb         %dl, $1(%rdi,%rsi)
	//0x0000020a LBB0_31
	0x48, 0x8d, 0x15, 0x0f, 0x07, 0x00, 0x00, //0x0000020a leaq         $1807(%rip), %rdx  /* _Digits+0(%rip) */
	0x8a, 0x14, 0x10, //0x00000211 movb         (%rax,%rdx), %dl
	0x89, 0xce, //0x00000214 movl         %ecx, %esi
	0xff, 0xc1, //0x00000216 incl         %ecx
	0x88, 0x54, 0x37, 0x01, //0x00000218 movb         %dl, $1(%rdi,%rsi)
	//0x0000021c LBB0_32
	0x0f, 0xb7, 0xc0, //0x0000021c movzwl       %ax, %eax
	0x48, 0x83, 0xc8, 0x01, //0x0000021f orq          $1, %rax
	0x48, 0x8d, 0x15, 0xf6, 0x06, 0x00, 0x00, //0x00000223 leaq         $1782(%rip), %rdx  /* _Digits+0(%rip) */
	0x8a, 0x04, 0x10, //0x0000022a movb         (%rax,%rdx), %al
	0x89, 0xca, //0x0000022d movl         %ecx, %edx
	0xff, 0xc1, //0x0000022f incl         %ecx
	0x88, 0x44, 0x17, 0x01, //0x00000231 movb         %al, $1(%rdi,%rdx)
	0xff, 0xc1, //0x00000235 incl         %ecx
	0x89, 0xc8, //0x00000237 movl         %ecx, %eax
	0x5d, //0x00000239 popq         %rbp
	0xc3, //0x0000023a retq         
	//0x0000023b LBB0_12
	0x31, 0xc9, //0x0000023b xorl         %ecx, %ecx
	0x81, 0xfe, 0x40, 0x42, 0x0f, 0x00, //0x0000023d cmpl         $1000000, %esi
	0x0f, 0x82, 0x86, 0x00, 0x00, 0x00, //0x00000243 jb           LBB0_14
	//0x00000249 LBB0_13
	0x44, 0x89, 0xd0, //0x00000249 movl         %r10d, %eax
	0x48, 0x83, 0xc8, 0x01, //0x0000024c orq          $1, %rax
	0x48, 0x8d, 0x35, 0xc9, 0x06, 0x00, 0x00, //0x00000250 leaq         $1737(%rip), %rsi  /* _Digits+0(%rip) */
	0x8a, 0x04, 0x30, //0x00000257 movb         (%rax,%rsi), %al
	0x89, 0xce, //0x0000025a movl         %ecx, %esi
	0xff, 0xc1, //0x0000025c incl         %ecx
	0x88, 0x04, 0x37, //0x0000025e movb         %al, (%rdi,%rsi)
	//0x00000261 LBB0_15
	0x48, 0x8d, 0x05, 0xb8, 0x06, 0x00, 0x00, //0x00000261 leaq         $1720(%rip), %rax  /* _Digits+0(%rip) */
	0x41, 0x8a, 0x04, 0x01, //0x00000268 movb         (%r9,%rax), %al
	0x89, 0xce, //0x0000026c movl         %ecx, %esi
	0xff, 0xc1, //0x0000026e incl         %ecx
	0x88, 0x04, 0x37, //0x00000270 movb         %al, (%rdi,%rsi)
	//0x00000273 LBB0_16
	0x41, 0x0f, 0xb7, 0xc1, //0x00000273 movzwl       %r9w, %eax
	0x48, 0x83, 0xc8, 0x01, //0x00000277 orq          $1, %rax
	0x48, 0x8d, 0x35, 0x9e, 0x06, 0x00, 0x00, //0x0000027b leaq         $1694(%rip), %rsi  /* _Digits+0(%rip) */
	0x8a, 0x04, 0x30, //0x00000282 movb         (%rax,%rsi), %al
	0x89, 0xca, //0x00000285 movl         %ecx, %edx
	0x88, 0x04, 0x3a, //0x00000287 movb         %al, (%rdx,%rdi)
	0x41, 0x8a, 0x04, 0x30, //0x0000028a movb         (%r8,%rsi), %al
	0x88, 0x44, 0x3a, 0x01, //0x0000028e movb         %al, $1(%rdx,%rdi)
	0x41, 0x0f, 0xb7, 0xc0, //0x00000292 movzwl       %r8w, %eax
	0x48, 0x83, 0xc8, 0x01, //0x00000296 orq          $1, %rax
	0x8a, 0x04, 0x30, //0x0000029a movb         (%rax,%rsi), %al
	0x88, 0x44, 0x3a, 0x02, //0x0000029d movb         %al, $2(%rdx,%rdi)
	0x41, 0x8a, 0x04, 0x33, //0x000002a1 movb         (%r11,%rsi), %al
	0x88, 0x44, 0x3a, 0x03, //0x000002a5 movb         %al, $3(%rdx,%rdi)
	0x41, 0x0f, 0xb7, 0xc3, //0x000002a9 movzwl       %r11w, %eax
	0x48, 0x83, 0xc8, 0x01, //0x000002ad orq          $1, %rax
	0x8a, 0x04, 0x30, //0x000002b1 movb         (%rax,%rsi), %al
	0x83, 0xc1, 0x05, //0x000002b4 addl         $5, %ecx
	0x88, 0x44, 0x3a, 0x04, //0x000002b7 movb         %al, $4(%rdx,%rdi)
	0x89, 0xc8, //0x000002bb movl         %ecx, %eax
	0x5d, //0x000002bd popq         %rbp
	0xc3, //0x000002be retq         
	//0x000002bf LBB0_30
	0x31, 0xc9, //0x000002bf xorl         %ecx, %ecx
	0x83, 0xfe, 0x0a, //0x000002c1 cmpl         $10, %esi
	0x0f, 0x83, 0x40, 0xff, 0xff, 0xff, //0x000002c4 jae          LBB0_31
	0xe9, 0x4d, 0xff, 0xff, 0xff, //0x000002ca jmp          LBB0_32
	//0x000002cf LBB0_14
	0x31, 0xc9, //0x000002cf xorl         %ecx, %ecx
	0x81, 0xfe, 0xa0, 0x86, 0x01, 0x00, //0x000002d1 cmpl         $100000, %esi
	0x0f, 0x83, 0x84, 0xff, 0xff, 0xff, //0x000002d7 jae          LBB0_15
	0xe9, 0x91, 0xff, 0xff, 0xff, //0x000002dd jmp          LBB0_16
	//0x000002e2 LBB0_33
	0x48, 0x81, 0xfe, 0xff, 0xe0, 0xf5, 0x05, //0x000002e2 cmpq         $99999999, %rsi
	0x0f, 0x87, 0x3c, 0x02, 0x00, 0x00, //0x000002e9 ja           LBB0_41
	0x89, 0xf0, //0x000002ef movl         %esi, %eax
	0xba, 0x59, 0x17, 0xb7, 0xd1, //0x000002f1 movl         $3518437209, %edx
	0x48, 0x0f, 0xaf, 0xd0, //0x000002f6 imulq        %rax, %rdx
	0x48, 0xc1, 0xea, 0x2d, //0x000002fa shrq         $45, %rdx
	0x44, 0x69, 0xc2, 0x10, 0x27, 0x00, 0x00, //0x000002fe imull        $10000, %edx, %r8d
	0x89, 0xf1, //0x00000305 movl         %esi, %ecx
	0x44, 0x29, 0xc1, //0x00000307 subl         %r8d, %ecx
	0x4c, 0x69, 0xd0, 0x83, 0xde, 0x1b, 0x43, //0x0000030a imulq        $1125899907, %rax, %r10
	0x49, 0xc1, 0xea, 0x31, //0x00000311 shrq         $49, %r10
	0x41, 0x83, 0xe2, 0xfe, //0x00000315 andl         $-2, %r10d
	0x0f, 0xb7, 0xc2, //0x00000319 movzwl       %dx, %eax
	0xc1, 0xe8, 0x02, //0x0000031c shrl         $2, %eax
	0x69, 0xc0, 0x7b, 0x14, 0x00, 0x00, //0x0000031f imull        $5243, %eax, %eax
	0xc1, 0xe8, 0x11, //0x00000325 shrl         $17, %eax
	0x6b, 0xc0, 0x64, //0x00000328 imull        $100, %eax, %eax
	0x29, 0xc2, //0x0000032b subl         %eax, %edx
	0x44, 0x0f, 0xb7, 0xca, //0x0000032d movzwl       %dx, %r9d
	0x4d, 0x01, 0xc9, //0x00000331 addq         %r9, %r9
	0x0f, 0xb7, 0xc1, //0x00000334 movzwl       %cx, %eax
	0xc1, 0xe8, 0x02, //0x00000337 shrl         $2, %eax
	0x69, 0xc0, 0x7b, 0x14, 0x00, 0x00, //0x0000033a imull        $5243, %eax, %eax
	0xc1, 0xe8, 0x11, //0x00000340 shrl         $17, %eax
	0x4c, 0x8d, 0x04, 0x00, //0x00000343 leaq         (%rax,%rax), %r8
	0x6b, 0xc0, 0x64, //0x00000347 imull        $100, %eax, %eax
	0x29, 0xc1, //0x0000034a subl         %eax, %ecx
	0x44, 0x0f, 0xb7, 0xd9, //0x0000034c movzwl       %cx, %r11d
	0x4d, 0x01, 0xdb, //0x00000350 addq         %r11, %r11
	0x81, 0xfe, 0x80, 0x96, 0x98, 0x00, //0x00000353 cmpl         $10000000, %esi
	0x0f, 0x82, 0x30, 0x01, 0x00, 0x00, //0x00000359 jb           LBB0_36
	0x48, 0x8d, 0x05, 0xba, 0x05, 0x00, 0x00, //0x0000035f leaq         $1466(%rip), %rax  /* _Digits+0(%rip) */
	0x41, 0x8a, 0x04, 0x02, //0x00000366 movb         (%r10,%rax), %al
	0x88, 0x47, 0x01, //0x0000036a movb         %al, $1(%rdi)
	0xb9, 0x01, 0x00, 0x00, 0x00, //0x0000036d movl         $1, %ecx
	0xe9, 0x26, 0x01, 0x00, 0x00, //0x00000372 jmp          LBB0_37
	//0x00000377 LBB0_17
	0x48, 0xb9, 0x00, 0x00, 0xc1, 0x6f, 0xf2, 0x86, 0x23, 0x00, //0x00000377 movabsq      $10000000000000000, %rcx
	0x48, 0x39, 0xce, //0x00000381 cmpq         %rcx, %rsi
	0x0f, 0x83, 0xbc, 0x02, 0x00, 0x00, //0x00000384 jae          LBB0_19
	0x48, 0xb9, 0xfd, 0xce, 0x61, 0x84, 0x11, 0x77, 0xcc, 0xab, //0x0000038a movabsq      $-6067343680855748867, %rcx
	0x48, 0x89, 0xf0, //0x00000394 movq         %rsi, %rax
	0x48, 0xf7, 0xe1, //0x00000397 mulq         %rcx
	0x48, 0xc1, 0xea, 0x1a, //0x0000039a shrq         $26, %rdx
	0x69, 0xc2, 0x00, 0xe1, 0xf5, 0x05, //0x0000039e imull        $100000000, %edx, %eax
	0x29, 0xc6, //0x000003a4 subl         %eax, %esi
	0xc5, 0xf9, 0x6e, 0xc2, //0x000003a6 vmovd        %edx, %xmm0
	0xc5, 0xfa, 0x6f, 0x0d, 0x4e, 0xfc, 0xff, 0xff, //0x000003aa vmovdqu      $-946(%rip), %xmm1  /* LCPI0_0+0(%rip) */
	0xc5, 0xf9, 0xf4, 0xd1, //0x000003b2 vpmuludq     %xmm1, %xmm0, %xmm2
	0xc5, 0xe9, 0x73, 0xd2, 0x2d, //0x000003b6 vpsrlq       $45, %xmm2, %xmm2
	0xb8, 0x10, 0x27, 0x00, 0x00, //0x000003bb movl         $10000, %eax
	0xc4, 0xe1, 0xf9, 0x6e, 0xd8, //0x000003c0 vmovq        %rax, %xmm3
	0xc5, 0xe9, 0xf4, 0xe3, //0x000003c5 vpmuludq     %xmm3, %xmm2, %xmm4
	0xc5, 0xf9, 0xfa, 0xc4, //0x000003c9 vpsubd       %xmm4, %xmm0, %xmm0
	0xc5, 0xe9, 0x61, 0xc0, //0x000003cd vpunpcklwd   %xmm0, %xmm2, %xmm0
	0xc5, 0xf9, 0x73, 0xf0, 0x02, //0x000003d1 vpsllq       $2, %xmm0, %xmm0
	0xc5, 0xfb, 0x70, 0xc0, 0x50, //0x000003d6 vpshuflw     $80, %xmm0, %xmm0
	0xc5, 0xf9, 0x70, 0xc0, 0x50, //0x000003db vpshufd      $80, %xmm0, %xmm0
	0xc5, 0xfb, 0x12, 0x15, 0x48, 0xfc, 0xff, 0xff, //0x000003e0 vmovddup     $-952(%rip), %xmm2  /* LCPI0_1+0(%rip) */
	0xc5, 0xf9, 0xe4, 0xc2, //0x000003e8 vpmulhuw     %xmm2, %xmm0, %xmm0
	0xc5, 0xfb, 0x12, 0x25, 0x44, 0xfc, 0xff, 0xff, //0x000003ec vmovddup     $-956(%rip), %xmm4  /* LCPI0_2+0(%rip) */
	0xc5, 0xf9, 0xe4, 0xc4, //0x000003f4 vpmulhuw     %xmm4, %xmm0, %xmm0
	0xc5, 0xfa, 0x6f, 0x2d, 0x10, 0xfc, 0xff, 0xff, //0x000003f8 vmovdqu      $-1008(%rip), %xmm5  /* LCPI0_3+0(%rip) */
	0xc5, 0xf9, 0xd5, 0xf5, //0x00000400 vpmullw      %xmm5, %xmm0, %xmm6
	0xc5, 0xc9, 0x73, 0xf6, 0x10, //0x00000404 vpsllq       $16, %xmm6, %xmm6
	0xc5, 0xf9, 0xf9, 0xc6, //0x00000409 vpsubw       %xmm6, %xmm0, %xmm0
	0xc5, 0xf9, 0x6e, 0xf6, //0x0000040d vmovd        %esi, %xmm6
	0xc5, 0xc9, 0xf4, 0xc9, //0x00000411 vpmuludq     %xmm1, %xmm6, %xmm1
	0xc5, 0xf1, 0x73, 0xd1, 0x2d, //0x00000415 vpsrlq       $45, %xmm1, %xmm1
	0xc5, 0xf1, 0xf4, 0xdb, //0x0000041a vpmuludq     %xmm3, %xmm1, %xmm3
	0xc5, 0xc9, 0xfa, 0xdb, //0x0000041e vpsubd       %xmm3, %xmm6, %xmm3
	0xc5, 0xf1, 0x61, 0xcb, //0x00000422 vpunpcklwd   %xmm3, %xmm1, %xmm1
	0xc5, 0xf1, 0x73, 0xf1, 0x02, //0x00000426 vpsllq       $2, %xmm1, %xmm1
	0xc5, 0xfb, 0x70, 0xc9, 0x50, //0x0000042b vpshuflw     $80, %xmm1, %xmm1
	0xc5, 0xf9, 0x70, 0xc9, 0x50, //0x00000430 vpshufd      $80, %xmm1, %xmm1
	0xc5, 0xf1, 0xe4, 0xca, //0x00000435 vpmulhuw     %xmm2, %xmm1, %xmm1
	0xc5, 0xf1, 0xe4, 0xcc, //0x00000439 vpmulhuw     %xmm4, %xmm1, %xmm1
	0xc5, 0xf1, 0xd5, 0xd5, //0x0000043d vpmullw      %xmm5, %xmm1, %xmm2
	0xc5, 0xe9, 0x73, 0xf2, 0x10, //0x00000441 vpsllq       $16, %xmm2, %xmm2
	0xc5, 0xf1, 0xf9, 0xca, //0x00000446 vpsubw       %xmm2, %xmm1, %xmm1
	0xc5, 0xf9, 0x67, 0xc1, //0x0000044a vpackuswb    %xmm1, %xmm0, %xmm0
	0xc5, 0xf9, 0xfc, 0x0d, 0xca, 0xfb, 0xff, 0xff, //0x0000044e vpaddb       $-1078(%rip), %xmm0, %xmm1  /* LCPI0_4+0(%rip) */
	0xc5, 0xe9, 0xef, 0xd2, //0x00000456 vpxor        %xmm2, %xmm2, %xmm2
	0xc5, 0xf9, 0x74, 0xc2, //0x0000045a vpcmpeqb     %xmm2, %xmm0, %xmm0
	0xc5, 0xf9, 0xd7, 0xc0, //0x0000045e vpmovmskb    %xmm0, %eax
	0x0d, 0x00, 0x80, 0x00, 0x00, //0x00000462 orl          $32768, %eax
	0x35, 0xff, 0x7f, 0xff, 0xff, //0x00000467 xorl         $-32769, %eax
	0x0f, 0xbc, 0xc0, //0x0000046c bsfl         %eax, %eax
	0xb9, 0x10, 0x00, 0x00, 0x00, //0x0000046f movl         $16, %ecx
	0x29, 0xc1, //0x00000474 subl         %eax, %ecx
	0x48, 0xc1, 0xe0, 0x04, //0x00000476 shlq         $4, %rax
	0x48, 0x8d, 0x15, 0x6f, 0x05, 0x00, 0x00, //0x0000047a leaq         $1391(%rip), %rdx  /* _VecShiftShuffles+0(%rip) */
	0xc4, 0xe2, 0x71, 0x00, 0x04, 0x10, //0x00000481 vpshufb      (%rax,%rdx), %xmm1, %xmm0
	0xc5, 0xfa, 0x7f, 0x07, //0x00000487 vmovdqu      %xmm0, (%rdi)
	0x89, 0xc8, //0x0000048b movl         %ecx, %eax
	0x5d, //0x0000048d popq         %rbp
	0xc3, //0x0000048e retq         
	//0x0000048f LBB0_36
	0x31, 0xc9, //0x0000048f xorl         %ecx, %ecx
	0x81, 0xfe, 0x40, 0x42, 0x0f, 0x00, //0x00000491 cmpl         $1000000, %esi
	0x0f, 0x82, 0x7b, 0x00, 0x00, 0x00, //0x00000497 jb           LBB0_38
	//0x0000049d LBB0_37
	0x44, 0x89, 0xd0, //0x0000049d movl         %r10d, %eax
	0x48, 0x83, 0xc8, 0x01, //0x000004a0 orq          $1, %rax
	0x48, 0x8d, 0x35, 0x75, 0x04, 0x00, 0x00, //0x000004a4 leaq         $1141(%rip), %rsi  /* _Digits+0(%rip) */
	0x8a, 0x04, 0x30, //0x000004ab movb         (%rax,%rsi), %al
	0x89, 0xce, //0x000004ae movl         %ecx, %esi
	0xff, 0xc1, //0x000004b0 incl         %ecx
	0x88, 0x44, 0x37, 0x01, //0x000004b2 movb         %al, $1(%rdi,%rsi)
	//0x000004b6 LBB0_39
	0x48, 0x8d, 0x05, 0x63, 0x04, 0x00, 0x00, //0x000004b6 leaq         $1123(%rip), %rax  /* _Digits+0(%rip) */
	0x41, 0x8a, 0x04, 0x01, //0x000004bd movb         (%r9,%rax), %al
	0x89, 0xce, //0x000004c1 movl         %ecx, %esi
	0xff, 0xc1, //0x000004c3 incl         %ecx
	0x88, 0x44, 0x37, 0x01, //0x000004c5 movb         %al, $1(%rdi,%rsi)
	//0x000004c9 LBB0_40
	0x41, 0x0f, 0xb7, 0xc1, //0x000004c9 movzwl       %r9w, %eax
	0x48, 0x83, 0xc8, 0x01, //0x000004cd orq          $1, %rax
	0x48, 0x8d, 0x35, 0x48, 0x04, 0x00, 0x00, //0x000004d1 leaq         $1096(%rip), %rsi  /* _Digits+0(%rip) */
	0x8a, 0x04, 0x30, //0x000004d8 movb         (%rax,%rsi), %al
	0x89, 0xca, //0x000004db movl         %ecx, %edx
	0x88, 0x44, 0x17, 0x01, //0x000004dd movb         %al, $1(%rdi,%rdx)
	0x41, 0x8a, 0x04, 0x30, //0x000004e1 movb         (%r8,%rsi), %al
	0x88, 0x44, 0x17, 0x02, //0x000004e5 movb         %al, $2(%rdi,%rdx)
	0x41, 0x0f, 0xb7, 0xc0, //0x000004e9 movzwl       %r8w, %eax
	0x48, 0x83, 0xc8, 0x01, //0x000004ed orq          $1, %rax
	0x8a, 0x04, 0x30, //0x000004f1 movb         (%rax,%rsi), %al
	0x88, 0x44, 0x17, 0x03, //0x000004f4 movb         %al, $3(%rdi,%rdx)
	0x41, 0x8a, 0x04, 0x33, //0x000004f8 movb         (%r11,%rsi), %al
	0x88, 0x44, 0x17, 0x04, //0x000004fc movb         %al, $4(%rdi,%rdx)
	0x41, 0x0f, 0xb7, 0xc3, //0x00000500 movzwl       %r11w, %eax
	0x48, 0x83, 0xc8, 0x01, //0x00000504 orq          $1, %rax
	0x8a, 0x04, 0x30, //0x00000508 movb         (%rax,%rsi), %al
	0x83, 0xc1, 0x05, //0x0000050b addl         $5, %ecx
	0x88, 0x44, 0x17, 0x05, //0x0000050e movb         %al, $5(%rdi,%rdx)
	0xff, 0xc1, //0x00000512 incl         %ecx
	0x89, 0xc8, //0x00000514 movl         %ecx, %eax
	0x5d, //0x00000516 popq         %rbp
	0xc3, //0x00000517 retq         
	//0x00000518 LBB0_38
	0x31, 0xc9, //0x00000518 xorl         %ecx, %ecx
	0x81, 0xfe, 0xa0, 0x86, 0x01, 0x00, //0x0000051a cmpl         $100000, %esi
	0x0f, 0x83, 0x90, 0xff, 0xff, 0xff, //0x00000520 jae          LBB0_39
	0xe9, 0x9e, 0xff, 0xff, 0xff, //0x00000526 jmp          LBB0_40
	//0x0000052b LBB0_41
	0x48, 0xb9, 0x00, 0x00, 0xc1, 0x6f, 0xf2, 0x86, 0x23, 0x00, //0x0000052b movabsq      $10000000000000000, %rcx
	0x48, 0x39, 0xce, //0x00000535 cmpq         %rcx, %rsi
	0x0f, 0x83, 0x71, 0x02, 0x00, 0x00, //0x00000538 jae          LBB0_43
	0x48, 0xb9, 0xfd, 0xce, 0x61, 0x84, 0x11, 0x77, 0xcc, 0xab, //0x0000053e movabsq      $-6067343680855748867, %rcx
	0x48, 0x89, 0xf0, //0x00000548 movq         %rsi, %rax
	0x48, 0xf7, 0xe1, //0x0000054b mulq         %rcx
	0x48, 0xc1, 0xea, 0x1a, //0x0000054e shrq         $26, %rdx
	0x69, 0xc2, 0x00, 0xe1, 0xf5, 0x05, //0x00000552 imull        $100000000, %edx, %eax
	0x29, 0xc6, //0x00000558 subl         %eax, %esi
	0xc5, 0xf9, 0x6e, 0xc2, //0x0000055a vmovd        %edx, %xmm0
	0xc5, 0xfa, 0x6f, 0x0d, 0x9a, 0xfa, 0xff, 0xff, //0x0000055e vmovdqu      $-1382(%rip), %xmm1  /* LCPI0_0+0(%rip) */
	0xc5, 0xf9, 0xf4, 0xd1, //0x00000566 vpmuludq     %xmm1, %xmm0, %xmm2
	0xc5, 0xe9, 0x73, 0xd2, 0x2d, //0x0000056a vpsrlq       $45, %xmm2, %xmm2
	0xb8, 0x10, 0x27, 0x00, 0x00, //0x0000056f movl         $10000, %eax
	0xc4, 0xe1, 0xf9, 0x6e, 0xd8, //0x00000574 vmovq        %rax, %xmm3
	0xc5, 0xe9, 0xf4, 0xe3, //0x00000579 vpmuludq     %xmm3, %xmm2, %xmm4
	0xc5, 0xf9, 0xfa, 0xc4, //0x0000057d vpsubd       %xmm4, %xmm0, %xmm0
	0xc5, 0xe9, 0x61, 0xc0, //0x00000581 vpunpcklwd   %xmm0, %xmm2, %xmm0
	0xc5, 0xf9, 0x73, 0xf0, 0x02, //0x00000585 vpsllq       $2, %xmm0, %xmm0
	0xc5, 0xfb, 0x70, 0xc0, 0x50, //0x0000058a vpshuflw     $80, %xmm0, %xmm0
	0xc5, 0xf9, 0x70, 0xc0, 0x50, //0x0000058f vpshufd      $80, %xmm0, %xmm0
	0xc5, 0xfb, 0x12, 0x15, 0x94, 0xfa, 0xff, 0xff, //0x00000594 vmovddup     $-1388(%rip), %xmm2  /* LCPI0_1+0(%rip) */
	0xc5, 0xf9, 0xe4, 0xc2, //0x0000059c vpmulhuw     %xmm2, %xmm0, %xmm0
	0xc5, 0xfb, 0x12, 0x25, 0x90, 0xfa, 0xff, 0xff, //0x000005a0 vmovddup     $-1392(%rip), %xmm4  /* LCPI0_2+0(%rip) */
	0xc5, 0xf9, 0xe4, 0xc4, //0x000005a8 vpmulhuw     %xmm4, %xmm0, %xmm0
	0xc5, 0xfa, 0x6f, 0x2d, 0x5c, 0xfa, 0xff, 0xff, //0x000005ac vmovdqu      $-1444(%rip), %xmm5  /* LCPI0_3+0(%rip) */
	0xc5, 0xf9, 0xd5, 0xf5, //0x000005b4 vpmullw      %xmm5, %xmm0, %xmm6
	0xc5, 0xc9, 0x73, 0xf6, 0x10, //0x000005b8 vpsllq       $16, %xmm6, %xmm6
	0xc5, 0xf9, 0xf9, 0xc6, //0x000005bd vpsubw       %xmm6, %xmm0, %xmm0
	0xc5, 0xf9, 0x6e, 0xf6, //0x000005c1 vmovd        %esi, %xmm6
	0xc5, 0xc9, 0xf4, 0xc9, //0x000005c5 vpmuludq     %xmm1, %xmm6, %xmm1
	0xc5, 0xf1, 0x73, 0xd1, 0x2d, //0x000005c9 vpsrlq       $45, %xmm1, %xmm1
	0xc5, 0xf1, 0xf4, 0xdb, //0x000005ce vpmuludq     %xmm3, %xmm1, %xmm3
	0xc5, 0xc9, 0xfa, 0xdb, //0x000005d2 vpsubd       %xmm3, %xmm6, %xmm3
	0xc5, 0xf1, 0x61, 0xcb, //0x000005d6 vpunpcklwd   %xmm3, %xmm1, %xmm1
	0xc5, 0xf1, 0x73, 0xf1, 0x02, //0x000005da vpsllq       $2, %xmm1, %xmm1
	0xc5, 0xfb, 0x70, 0xc9, 0x50, //0x000005df vpshuflw     $80, %xmm1, %xmm1
	0xc5, 0xf9, 0x70, 0xc9, 0x50, //0x000005e4 vpshufd      $80, %xmm1, %xmm1
	0xc5, 0xf1, 0xe4, 0xca, //0x000005e9 vpmulhuw     %xmm2, %xmm1, %xmm1
	0xc5, 0xf1, 0xe4, 0xcc, //0x000005ed vpmulhuw     %xmm4, %xmm1, %xmm1
	0xc5, 0xf1, 0xd5, 0xd5, //0x000005f1 vpmullw      %xmm5, %xmm1, %xmm2
	0xc5, 0xe9, 0x73, 0xf2, 0x10, //0x000005f5 vpsllq       $16, %xmm2, %xmm2
	0xc5, 0xf1, 0xf9, 0xca, //0x000005fa vpsubw       %xmm2, %xmm1, %xmm1
	0xc5, 0xf9, 0x67, 0xc1, //0x000005fe vpackuswb    %xmm1, %xmm0, %xmm0
	0xc5, 0xf9, 0xfc, 0x0d, 0x16, 0xfa, 0xff, 0xff, //0x00000602 vpaddb       $-1514(%rip), %xmm0, %xmm1  /* LCPI0_4+0(%rip) */
	0xc5, 0xe9, 0xef, 0xd2, //0x0000060a vpxor        %xmm2, %xmm2, %xmm2
	0xc5, 0xf9, 0x74, 0xc2, //0x0000060e vpcmpeqb     %xmm2, %xmm0, %xmm0
	0xc5, 0xf9, 0xd7, 0xc0, //0x00000612 vpmovmskb    %xmm0, %eax
	0x0d, 0x00, 0x80, 0x00, 0x00, //0x00000616 orl          $32768, %eax
	0x35, 0xff, 0x7f, 0xff, 0xff, //0x0000061b xorl         $-32769, %eax
	0x0f, 0xbc, 0xc0, //0x00000620 bsfl         %eax, %eax
	0xb9, 0x10, 0x00, 0x00, 0x00, //0x00000623 movl         $16, %ecx
	0x29, 0xc1, //0x00000628 subl         %eax, %ecx
	0x48, 0xc1, 0xe0, 0x04, //0x0000062a shlq         $4, %rax
	0x48, 0x8d, 0x15, 0xbb, 0x03, 0x00, 0x00, //0x0000062e leaq         $955(%rip), %rdx  /* _VecShiftShuffles+0(%rip) */
	0xc4, 0xe2, 0x71, 0x00, 0x04, 0x10, //0x00000635 vpshufb      (%rax,%rdx), %xmm1, %xmm0
	0xc5, 0xfa, 0x7f, 0x47, 0x01, //0x0000063b vmovdqu      %xmm0, $1(%rdi)
	0xff, 0xc1, //0x00000640 incl         %ecx
	0x89, 0xc8, //0x00000642 movl         %ecx, %eax
	0x5d, //0x00000644 popq         %rbp
	0xc3, //0x00000645 retq         
	//0x00000646 LBB0_19
	0x48, 0xba, 0x57, 0x78, 0x13, 0xb1, 0x2f, 0x65, 0xa5, 0x39, //0x00000646 movabsq      $4153837486827862103, %rdx
	0x48, 0x89, 0xf0, //0x00000650 movq         %rsi, %rax
	0x48, 0xf7, 0xe2, //0x00000653 mulq         %rdx
	0x48, 0xc1, 0xea, 0x33, //0x00000656 shrq         $51, %rdx
	0x48, 0x0f, 0xaf, 0xca, //0x0000065a imulq        %rdx, %rcx
	0x48, 0x29, 0xce, //0x0000065e subq         %rcx, %rsi
	0x83, 0xfa, 0x09, //0x00000661 cmpl         $9, %edx
	0x0f, 0x87, 0x0f, 0x00, 0x00, 0x00, //0x00000664 ja           LBB0_21
	0x80, 0xc2, 0x30, //0x0000066a addb         $48, %dl
	0x88, 0x17, //0x0000066d movb         %dl, (%rdi)
	0xb9, 0x01, 0x00, 0x00, 0x00, //0x0000066f movl         $1, %ecx
	0xe9, 0x5c, 0x00, 0x00, 0x00, //0x00000674 jmp          LBB0_24
	//0x00000679 LBB0_21
	0x83, 0xfa, 0x63, //0x00000679 cmpl         $99, %edx
	0x0f, 0x87, 0x1f, 0x00, 0x00, 0x00, //0x0000067c ja           LBB0_23
	0x89, 0xd0, //0x00000682 movl         %edx, %eax
	0x48, 0x8d, 0x0d, 0x95, 0x02, 0x00, 0x00, //0x00000684 leaq         $661(%rip), %rcx  /* _Digits+0(%rip) */
	0x8a, 0x14, 0x41, //0x0000068b movb         (%rcx,%rax,2), %dl
	0x8a, 0x44, 0x41, 0x01, //0x0000068e movb         $1(%rcx,%rax,2), %al
	0x88, 0x17, //0x00000692 movb         %dl, (%rdi)
	0x88, 0x47, 0x01, //0x00000694 movb         %al, $1(%rdi)
	0xb9, 0x02, 0x00, 0x00, 0x00, //0x00000697 movl         $2, %ecx
	0xe9, 0x34, 0x00, 0x00, 0x00, //0x0000069c jmp          LBB0_24
	//0x000006a1 LBB0_23
	0x89, 0xd0, //0x000006a1 movl         %edx, %eax
	0xc1, 0xe8, 0x02, //0x000006a3 shrl         $2, %eax
	0x69, 0xc0, 0x7b, 0x14, 0x00, 0x00, //0x000006a6 imull        $5243, %eax, %eax
	0xc1, 0xe8, 0x11, //0x000006ac shrl         $17, %eax
	0x8d, 0x48, 0x30, //0x000006af leal         $48(%rax), %ecx
	0x88, 0x0f, //0x000006b2 movb         %cl, (%rdi)
	0x6b, 0xc0, 0x64, //0x000006b4 imull        $100, %eax, %eax
	0x29, 0xc2, //0x000006b7 subl         %eax, %edx
	0x0f, 0xb7, 0xc2, //0x000006b9 movzwl       %dx, %eax
	0x48, 0x8d, 0x0d, 0x5d, 0x02, 0x00, 0x00, //0x000006bc leaq         $605(%rip), %rcx  /* _Digits+0(%rip) */
	0x8a, 0x14, 0x41, //0x000006c3 movb         (%rcx,%rax,2), %dl
	0x8a, 0x44, 0x41, 0x01, //0x000006c6 movb         $1(%rcx,%rax,2), %al
	0x88, 0x57, 0x01, //0x000006ca movb         %dl, $1(%rdi)
	0x88, 0x47, 0x02, //0x000006cd movb         %al, $2(%rdi)
	0xb9, 0x03, 0x00, 0x00, 0x00, //0x000006d0 movl         $3, %ecx
	//0x000006d5 LBB0_24
	0x48, 0xba, 0xfd, 0xce, 0x61, 0x84, 0x11, 0x77, 0xcc, 0xab, //0x000006d5 movabsq      $-6067343680855748867, %rdx
	0x48, 0x89, 0xf0, //0x000006df movq         %rsi, %rax
	0x48, 0xf7, 0xe2, //0x000006e2 mulq         %rdx
	0x48, 0xc1, 0xea, 0x1a, //0x000006e5 shrq         $26, %rdx
	0xc5, 0xf9, 0x6e, 0xc2, //0x000006e9 vmovd        %edx, %xmm0
	0xc5, 0xfa, 0x6f, 0x0d, 0x0b, 0xf9, 0xff, 0xff, //0x000006ed vmovdqu      $-1781(%rip), %xmm1  /* LCPI0_0+0(%rip) */
	0xc5, 0xf9, 0xf4, 0xd1, //0x000006f5 vpmuludq     %xmm1, %xmm0, %xmm2
	0xc5, 0xe9, 0x73, 0xd2, 0x2d, //0x000006f9 vpsrlq       $45, %xmm2, %xmm2
	0xb8, 0x10, 0x27, 0x00, 0x00, //0x000006fe movl         $10000, %eax
	0xc4, 0xe1, 0xf9, 0x6e, 0xd8, //0x00000703 vmovq        %rax, %xmm3
	0xc5, 0xe9, 0xf4, 0xe3, //0x00000708 vpmuludq     %xmm3, %xmm2, %xmm4
	0xc5, 0xf9, 0xfa, 0xc4, //0x0000070c vpsubd       %xmm4, %xmm0, %xmm0
	0xc5, 0xe9, 0x61, 0xc0, //0x00000710 vpunpcklwd   %xmm0, %xmm2, %xmm0
	0xc5, 0xf9, 0x73, 0xf0, 0x02, //0x00000714 vpsllq       $2, %xmm0, %xmm0
	0xc5, 0xfb, 0x70, 0xc0, 0x50, //0x00000719 vpshuflw     $80, %xmm0, %xmm0
	0xc5, 0xf9, 0x70, 0xc0, 0x50, //0x0000071e vpshufd      $80, %xmm0, %xmm0
	0xc5, 0xfb, 0x12, 0x15, 0x05, 0xf9, 0xff, 0xff, //0x00000723 vmovddup     $-1787(%rip), %xmm2  /* LCPI0_1+0(%rip) */
	0xc5, 0xf9, 0xe4, 0xc2, //0x0000072b vpmulhuw     %xmm2, %xmm0, %xmm0
	0xc5, 0xfb, 0x12, 0x25, 0x01, 0xf9, 0xff, 0xff, //0x0000072f vmovddup     $-1791(%rip), %xmm4  /* LCPI0_2+0(%rip) */
	0xc5, 0xf9, 0xe4, 0xc4, //0x00000737 vpmulhuw     %xmm4, %xmm0, %xmm0
	0xc5, 0xfa, 0x6f, 0x2d, 0xcd, 0xf8, 0xff, 0xff, //0x0000073b vmovdqu      $-1843(%rip), %xmm5  /* LCPI0_3+0(%rip) */
	0xc5, 0xf9, 0xd5, 0xf5, //0x00000743 vpmullw      %xmm5, %xmm0, %xmm6
	0xc5, 0xc9, 0x73, 0xf6, 0x10, //0x00000747 vpsllq       $16, %xmm6, %xmm6
	0xc5, 0xf9, 0xf9, 0xc6, //0x0000074c vpsubw       %xmm6, %xmm0, %xmm0
	0x69, 0xc2, 0x00, 0xe1, 0xf5, 0x05, //0x00000750 imull        $100000000, %edx, %eax
	0x29, 0xc6, //0x00000756 subl         %eax, %esi
	0xc5, 0xf9, 0x6e, 0xf6, //0x00000758 vmovd        %esi, %xmm6
	0xc5, 0xc9, 0xf4, 0xc9, //0x0000075c vpmuludq     %xmm1, %xmm6, %xmm1
	0xc5, 0xf1, 0x73, 0xd1, 0x2d, //0x00000760 vpsrlq       $45, %xmm1, %xmm1
	0xc5, 0xf1, 0xf4, 0xdb, //0x00000765 vpmuludq     %xmm3, %xmm1, %xmm3
	0xc5, 0xc9, 0xfa, 0xdb, //0x00000769 vpsubd       %xmm3, %xmm6, %xmm3
	0xc5, 0xf1, 0x61, 0xcb, //0x0000076d vpunpcklwd   %xmm3, %xmm1, %xmm1
	0xc5, 0xf1, 0x73, 0xf1, 0x02, //0x00000771 vpsllq       $2, %xmm1, %xmm1
	0xc5, 0xfb, 0x70, 0xc9, 0x50, //0x00000776 vpshuflw     $80, %xmm1, %xmm1
	0xc5, 0xf9, 0x70, 0xc9, 0x50, //0x0000077b vpshufd      $80, %xmm1, %xmm1
	0xc5, 0xf1, 0xe4, 0xca, //0x00000780 vpmulhuw     %xmm2, %xmm1, %xmm1
	0xc5, 0xf1, 0xe4, 0xcc, //0x00000784 vpmulhuw     %xmm4, %xmm1, %xmm1
	0xc5, 0xf1, 0xd5, 0xd5, //0x00000788 vpmullw      %xmm5, %xmm1, %xmm2
	0xc5, 0xe9, 0x73, 0xf2, 0x10, //0x0000078c vpsllq       $16, %xmm2, %xmm2
	0xc5, 0xf1, 0xf9, 0xca, //0x00000791 vpsubw       %xmm2, %xmm1, %xmm1
	0xc5, 0xf9, 0x67, 0xc1, //0x00000795 vpackuswb    %xmm1, %xmm0, %xmm0
	0xc5, 0xf9, 0xfc, 0x05, 0x7f, 0xf8, 0xff, 0xff, //0x00000799 vpaddb       $-1921(%rip), %xmm0, %xmm0  /* LCPI0_4+0(%rip) */
	0x89, 0xc8, //0x000007a1 movl         %ecx, %eax
	0xc5, 0xfa, 0x7f, 0x04, 0x07, //0x000007a3 vmovdqu      %xmm0, (%rdi,%rax)
	0x83, 0xc9, 0x10, //0x000007a8 orl          $16, %ecx
	0x89, 0xc8, //0x000007ab movl         %ecx, %eax
	0x5d, //0x000007ad popq         %rbp
	0xc3, //0x000007ae retq         
	//0x000007af LBB0_43
	0x48, 0xba, 0x57, 0x78, 0x13, 0xb1, 0x2f, 0x65, 0xa5, 0x39, //0x000007af movabsq      $4153837486827862103, %rdx
	0x48, 0x89, 0xf0, //0x000007b9 movq         %rsi, %rax
	0x48, 0xf7, 0xe2, //0x000007bc mulq         %rdx
	0x48, 0xc1, 0xea, 0x33, //0x000007bf shrq         $51, %rdx
	0x48, 0x0f, 0xaf, 0xca, //0x000007c3 imulq        %rdx, %rcx
	0x48, 0x29, 0xce, //0x000007c7 subq         %rcx, %rsi
	0x83, 0xfa, 0x09, //0x000007ca cmpl         $9, %edx
	0x0f, 0x87, 0x10, 0x00, 0x00, 0x00, //0x000007cd ja           LBB0_45
	0x80, 0xc2, 0x30, //0x000007d3 addb         $48, %dl
	0x88, 0x57, 0x01, //0x000007d6 movb         %dl, $1(%rdi)
	0xb9, 0x01, 0x00, 0x00, 0x00, //0x000007d9 movl         $1, %ecx
	0xe9, 0x5e, 0x00, 0x00, 0x00, //0x000007de jmp          LBB0_48
	//0x000007e3 LBB0_45
	0x83, 0xfa, 0x63, //0x000007e3 cmpl         $99, %edx
	0x0f, 0x87, 0x20, 0x00, 0x00, 0x00, //0x000007e6 ja           LBB0_47
	0x89, 0xd0, //0x000007ec movl         %edx, %eax
	0x48, 0x8d, 0x0d, 0x2b, 0x01, 0x00, 0x00, //0x000007ee leaq         $299(%rip), %rcx  /* _Digits+0(%rip) */
	0x8a, 0x14, 0x41, //0x000007f5 movb         (%rcx,%rax,2), %dl
	0x8a, 0x44, 0x41, 0x01, //0x000007f8 movb         $1(%rcx,%rax,2), %al
	0x88, 0x57, 0x01, //0x000007fc movb         %dl, $1(%rdi)
	0x88, 0x47, 0x02, //0x000007ff movb         %al, $2(%rdi)
	0xb9, 0x02, 0x00, 0x00, 0x00, //0x00000802 movl         $2, %ecx
	0xe9, 0x35, 0x00, 0x00, 0x00, //0x00000807 jmp          LBB0_48
	//0x0000080c LBB0_47
	0x89, 0xd0, //0x0000080c movl         %edx, %eax
	0xc1, 0xe8, 0x02, //0x0000080e shrl         $2, %eax
	0x69, 0xc0, 0x7b, 0x14, 0x00, 0x00, //0x00000811 imull        $5243, %eax, %eax
	0xc1, 0xe8, 0x11, //0x00000817 shrl         $17, %eax
	0x8d, 0x48, 0x30, //0x0000081a leal         $48(%rax), %ecx
	0x88, 0x4f, 0x01, //0x0000081d movb         %cl, $1(%rdi)
	0x6b, 0xc0, 0x64, //0x00000820 imull        $100, %eax, %eax
	0x29, 0xc2, //0x00000823 subl         %eax, %edx
	0x0f, 0xb7, 0xc2, //0x00000825 movzwl       %dx, %eax
	0x48, 0x8d, 0x0d, 0xf1, 0x00, 0x00, 0x00, //0x00000828 leaq         $241(%rip), %rcx  /* _Digits+0(%rip) */
	0x8a, 0x14, 0x41, //0x0000082f movb         (%rcx,%rax,2), %dl
	0x8a, 0x44, 0x41, 0x01, //0x00000832 movb         $1(%rcx,%rax,2), %al
	0x88, 0x57, 0x02, //0x00000836 movb         %dl, $2(%rdi)
	0x88, 0x47, 0x03, //0x00000839 movb         %al, $3(%rdi)
	0xb9, 0x03, 0x00, 0x00, 0x00, //0x0000083c movl         $3, %ecx
	//0x00000841 LBB0_48
	0x48, 0xba, 0xfd, 0xce, 0x61, 0x84, 0x11, 0x77, 0xcc, 0xab, //0x00000841 movabsq      $-6067343680855748867, %rdx
	0x48, 0x89, 0xf0, //0x0000084b movq         %rsi, %rax
	0x48, 0xf7, 0xe2, //0x0000084e mulq         %rdx
	0x48, 0xc1, 0xea, 0x1a, //0x00000851 shrq         $26, %rdx
	0xc5, 0xf9, 0x6e, 0xc2, //0x00000855 vmovd        %edx, %xmm0
	0xc5, 0xfa, 0x6f, 0x0d, 0x9f, 0xf7, 0xff, 0xff, //0x00000859 vmovdqu      $-2145(%rip), %xmm1  /* LCPI0_0+0(%rip) */
	0xc5, 0xf9, 0xf4, 0xd1, //0x00000861 vpmuludq     %xmm1, %xmm0, %xmm2
	0xc5, 0xe9, 0x73, 0xd2, 0x2d, //0x00000865 vpsrlq       $45, %xmm2, %xmm2
	0xb8, 0x10, 0x27, 0x00, 0x00, //0x0000086a movl         $10000, %eax
	0xc4, 0xe1, 0xf9, 0x6e, 0xd8, //0x0000086f vmovq        %rax, %xmm3
	0xc5, 0xe9, 0xf4, 0xe3, //0x00000874 vpmuludq     %xmm3, %xmm2, %xmm4
	0xc5, 0xf9, 0xfa, 0xc4, //0x00000878 vpsubd       %xmm4, %xmm0, %xmm0
	0xc5, 0xe9, 0x61, 0xc0, //0x0000087c vpunpcklwd   %xmm0, %xmm2, %xmm0
	0xc5, 0xf9, 0x73, 0xf0, 0x02, //0x00000880 vpsllq       $2, %xmm0, %xmm0
	0xc5, 0xfb, 0x70, 0xc0, 0x50, //0x00000885 vpshuflw     $80, %xmm0, %xmm0
	0xc5, 0xf9, 0x70, 0xc0, 0x50, //0x0000088a vpshufd      $80, %xmm0, %xmm0
	0xc5, 0xfb, 0x12, 0x15, 0x99, 0xf7, 0xff, 0xff, //0x0000088f vmovddup     $-2151(%rip), %xmm2  /* LCPI0_1+0(%rip) */
	0xc5, 0xf9, 0xe4, 0xc2, //0x00000897 vpmulhuw     %xmm2, %xmm0, %xmm0
	0xc5, 0xfb, 0x12, 0x25, 0x95, 0xf7, 0xff, 0xff, //0x0000089b vmovddup     $-2155(%rip), %xmm4  /* LCPI0_2+0(%rip) */
	0xc5, 0xf9, 0xe4, 0xc4, //0x000008a3 vpmulhuw     %xmm4, %xmm0, %xmm0
	0xc5, 0xfa, 0x6f, 0x2d, 0x61, 0xf7, 0xff, 0xff, //0x000008a7 vmovdqu      $-2207(%rip), %xmm5  /* LCPI0_3+0(%rip) */
	0xc5, 0xf9, 0xd5, 0xf5, //0x000008af vpmullw      %xmm5, %xmm0, %xmm6
	0xc5, 0xc9, 0x73, 0xf6, 0x10, //0x000008b3 vpsllq       $16, %xmm6, %xmm6
	0xc5, 0xf9, 0xf9, 0xc6, //0x000008b8 vpsubw       %xmm6, %xmm0, %xmm0
	0x69, 0xc2, 0x00, 0xe1, 0xf5, 0x05, //0x000008bc imull        $100000000, %edx, %eax
	0x29, 0xc6, //0x000008c2 subl         %eax, %esi
	0xc5, 0xf9, 0x6e, 0xf6, //0x000008c4 vmovd        %esi, %xmm6
	0xc5, 0xc9, 0xf4, 0xc9, //0x000008c8 vpmuludq     %xmm1, %xmm6, %xmm1
	0xc5, 0xf1, 0x73, 0xd1, 0x2d, //0x000008cc vpsrlq       $45, %xmm1, %xmm1
	0xc5, 0xf1, 0xf4, 0xdb, //0x000008d1 vpmuludq     %xmm3, %xmm1, %xmm3
	0xc5, 0xc9, 0xfa, 0xdb, //0x000008d5 vpsubd       %xmm3, %xmm6, %xmm3
	0xc5, 0xf1, 0x61, 0xcb, //0x000008d9 vpunpcklwd   %xmm3, %xmm1, %xmm1
	0xc5, 0xf1, 0x73, 0xf1, 0x02, //0x000008dd vpsllq       $2, %xmm1, %xmm1
	0xc5, 0xfb, 0x70, 0xc9, 0x50, //0x000008e2 vpshuflw     $80, %xmm1, %xmm1
	0xc5, 0xf9, 0x70, 0xc9, 0x50, //0x000008e7 vpshufd      $80, %xmm1, %xmm1
	0xc5, 0xf1, 0xe4, 0xca, //0x000008ec vpmulhuw     %xmm2, %xmm1, %xmm1
	0xc5, 0xf1, 0xe4, 0xcc, //0x000008f0 vpmulhuw     %xmm4, %xmm1, %xmm1
	0xc5, 0xf1, 0xd5, 0xd5, //0x000008f4 vpmullw      %xmm5, %xmm1, %xmm2
	0xc5, 0xe9, 0x73, 0xf2, 0x10, //0x000008f8 vpsllq       $16, %xmm2, %xmm2
	0xc5, 0xf1, 0xf9, 0xca, //0x000008fd vpsubw       %xmm2, %xmm1, %xmm1
	0xc5, 0xf9, 0x67, 0xc1, //0x00000901 vpackuswb    %xmm1, %xmm0, %xmm0
	0xc5, 0xf9, 0xfc, 0x05, 0x13, 0xf7, 0xff, 0xff, //0x00000905 vpaddb       $-2285(%rip), %xmm0, %xmm0  /* LCPI0_4+0(%rip) */
	0x89, 0xc8, //0x0000090d movl         %ecx, %eax
	0xc5, 0xfa, 0x7f, 0x44, 0x07, 0x01, //0x0000090f vmovdqu      %xmm0, $1(%rdi,%rax)
	0x83, 0xc9, 0x10, //0x00000915 orl          $16, %ecx
	0xff, 0xc1, //0x00000918 incl         %ecx
	0x89, 0xc8, //0x0000091a movl         %ecx, %eax
	0x5d, //0x0000091c popq         %rbp
	0xc3, //0x0000091d retq         
	0x00, 0x00, //0x0000091e .p2align 4, 0x00
	//0x00000920 _Digits
	0x30, 0x30, 0x30, 0x31, 0x30, 0x32, 0x30, 0x33, 0x30, 0x34, 0x30, 0x35, 0x30, 0x36, 0x30, 0x37, //0x00000920 QUAD $0x3330323031303030; QUAD $0x3730363035303430  // .ascii 16, '0001020304050607'
	0x30, 0x38, 0x30, 0x39, 0x31, 0x30, 0x31, 0x31, 0x31, 0x32, 0x31, 0x33, 0x31, 0x34, 0x31, 0x35, //0x00000930 QUAD $0x3131303139303830; QUAD $0x3531343133313231  // .ascii 16, '0809101112131415'
	0x31, 0x36, 0x31, 0x37, 0x31, 0x38, 0x31, 0x39, 0x32, 0x30, 0x32, 0x31, 0x32, 0x32, 0x32, 0x33, //0x00000940 QUAD $0x3931383137313631; QUAD $0x3332323231323032  // .ascii 16, '1617181920212223'
	0x32, 0x34, 0x32, 0x35, 0x32, 0x36, 0x32, 0x37, 0x32, 0x38, 0x32, 0x39, 0x33, 0x30, 0x33, 0x31, //0x00000950 QUAD $0x3732363235323432; QUAD $0x3133303339323832  // .ascii 16, '2425262728293031'
	0x33, 0x32, 0x33, 0x33, 0x33, 0x34, 0x33, 0x35, 0x33, 0x36, 0x33, 0x37, 0x33, 0x38, 0x33, 0x39, //0x00000960 QUAD $0x3533343333333233; QUAD $0x3933383337333633  // .ascii 16, '3233343536373839'
	0x34, 0x30, 0x34, 0x31, 0x34, 0x32, 0x34, 0x33, 0x34, 0x34, 0x34, 0x35, 0x34, 0x36, 0x34, 0x37, //0x00000970 QUAD $0x3334323431343034; QUAD $0x3734363435343434  // .ascii 16, '4041424344454647'
	0x34, 0x38, 0x34, 0x39, 0x35, 0x30, 0x35, 0x31, 0x35, 0x32, 0x35, 0x33, 0x35, 0x34, 0x35, 0x35, //0x00000980 QUAD $0x3135303539343834; QUAD $0x3535343533353235  // .ascii 16, '4849505152535455'
	0x35, 0x36, 0x35, 0x37, 0x35, 0x38, 0x35, 0x39, 0x36, 0x30, 0x36, 0x31, 0x36, 0x32, 0x36, 0x33, //0x00000990 QUAD $0x3935383537353635; QUAD $0x3336323631363036  // .ascii 16, '5657585960616263'
	0x36, 0x34, 0x36, 0x35, 0x36, 0x36, 0x36, 0x37, 0x36, 0x38, 0x36, 0x39, 0x37, 0x30, 0x37, 0x31, //0x000009a0 QUAD $0x3736363635363436; QUAD $0x3137303739363836  // .ascii 16, '6465666768697071'
	0x37, 0x32, 0x37, 0x33, 0x37, 0x34, 0x37, 0x35, 0x37, 0x36, 0x37, 0x37, 0x37, 0x38, 0x37, 0x39, //0x000009b0 QUAD $0x3537343733373237; QUAD $0x3937383737373637  // .ascii 16, '7273747576777879'
	0x38, 0x30, 0x38, 0x31, 0x38, 0x32, 0x38, 0x33, 0x38, 0x34, 0x38, 0x35, 0x38, 0x36, 0x38, 0x37, //0x000009c0 QUAD $0x3338323831383038; QUAD $0x3738363835383438  // .ascii 16, '8081828384858687'
	0x38, 0x38, 0x38, 0x39, 0x39, 0x30, 0x39, 0x31, 0x39, 0x32, 0x39, 0x33, 0x39, 0x34, 0x39, 0x35, //0x000009d0 QUAD $0x3139303939383838; QUAD $0x3539343933393239  // .ascii 16, '8889909192939495'
	0x39, 0x36, 0x39, 0x37, 0x39, 0x38, 0x39, 0x39, //0x000009e0 QUAD $0x3939383937393639  // .ascii 8, '96979899'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000009e8 .p2align 4, 0x00
	//0x000009f0 _VecShiftShuffles
	0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, //0x000009f0 QUAD $0x0706050403020100; QUAD $0x0f0e0d0c0b0a0908  // .ascii 16, '\x00\x01\x02\x03\x04\x05\x06\x07\x08\t\n\x0b\x0c\r\x0e\x0f'
	0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, 0xff, //0x00000a00 QUAD $0x0807060504030201; QUAD $0xff0f0e0d0c0b0a09  // .ascii 16, '\x01\x02\x03\x04\x05\x06\x07\x08\t\n\x0b\x0c\r\x0e\x0f\xff'
	0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, 0xff, 0xff, //0x00000a10 QUAD $0x0908070605040302; QUAD $0xffff0f0e0d0c0b0a  // .ascii 16, '\x02\x03\x04\x05\x06\x07\x08\t\n\x0b\x0c\r\x0e\x0f\xff\xff'
	0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, 0xff, 0xff, 0xff, //0x00000a20 QUAD $0x0a09080706050403; QUAD $0xffffff0f0e0d0c0b  // .ascii 16, '\x03\x04\x05\x06\x07\x08\t\n\x0b\x0c\r\x0e\x0f\xff\xff\xff'
	0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, 0xff, 0xff, 0xff, 0xff, //0x00000a30 QUAD $0x0b0a090807060504; QUAD $0xffffffff0f0e0d0c  // .ascii 16, '\x04\x05\x06\x07\x08\t\n\x0b\x0c\r\x0e\x0f\xff\xff\xff\xff'
	0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00000a40 QUAD $0x0c0b0a0908070605; QUAD $0xffffffffff0f0e0d  // .ascii 16, '\x05\x06\x07\x08\t\n\x0b\x0c\r\x0e\x0f\xff\xff\xff\xff\xff'
	0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00000a50 QUAD $0x0d0c0b0a09080706; QUAD $0xffffffffffff0f0e  // .ascii 16, '\x06\x07\x08\t\n\x0b\x0c\r\x0e\x0f\xff\xff\xff\xff\xff\xff'
	0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00000a60 QUAD $0x0e0d0c0b0a090807; QUAD $0xffffffffffffff0f  // .ascii 16, '\x07\x08\t\n\x0b\x0c\r\x0e\x0f\xff\xff\xff\xff\xff\xff\xff'
	0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00000a70 QUAD $0x0f0e0d0c0b0a0908; QUAD $0xffffffffffffffff  // .ascii 16, '\x08\t\n\x0b\x0c\r\x0e\x0f\xff\xff\xff\xff\xff\xff\xff\xff'
}
 
