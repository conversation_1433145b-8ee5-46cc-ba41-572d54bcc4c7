// +build !noasm !appengine
// Code generated by asm2asm, DO NOT EDIT.

package avx

import (
	`github.com/bytedance/sonic/loader`
)

const (
    _entry__skip_number = 112
)

const (
    _stack__skip_number = 72
)

const (
    _size__skip_number = 1108
)

var (
    _pcsp__skip_number = [][2]uint32{
        {1, 0},
        {4, 8},
        {6, 16},
        {8, 24},
        {10, 32},
        {12, 40},
        {13, 48},
        {1025, 72},
        {1029, 48},
        {1030, 40},
        {1032, 32},
        {1034, 24},
        {1036, 16},
        {1038, 8},
        {1039, 0},
        {1108, 72},
    }
)

var _cfunc_skip_number = []loader.CFunc{
    {"_skip_number_entry", 0,  _entry__skip_number, 0, nil},
    {"_skip_number", _entry__skip_number, _size__skip_number, _stack__skip_number, _pcsp__skip_number},
}
