// +build !noasm !appengine
// Code generated by asm2asm, DO NOT EDIT.

package avx

import (
	`github.com/bytedance/sonic/loader`
)

const (
    _entry__skip_one = 144
)

const (
    _stack__skip_one = 160
)

const (
    _size__skip_one = 9472
)

var (
    _pcsp__skip_one = [][2]uint32{
        {1, 0},
        {4, 8},
        {6, 16},
        {8, 24},
        {10, 32},
        {12, 40},
        {13, 48},
        {9044, 160},
        {9048, 48},
        {9049, 40},
        {9051, 32},
        {9053, 24},
        {9055, 16},
        {9057, 8},
        {9058, 0},
        {9472, 160},
    }
)

var _cfunc_skip_one = []loader.CFunc{
    {"_skip_one_entry", 0,  _entry__skip_one, 0, nil},
    {"_skip_one", _entry__skip_one, _size__skip_one, _stack__skip_one, _pcsp__skip_one},
}
