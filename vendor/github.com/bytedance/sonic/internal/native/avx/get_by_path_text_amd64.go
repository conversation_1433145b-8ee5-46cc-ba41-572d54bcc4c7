// +build amd64
// Code generated by asm2asm, DO NOT EDIT.

package avx

var _text_get_by_path = []byte{
	// .p2align 4, 0x00
	// LCPI0_0
	0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, // QUAD $0x2c2c2c2c2c2c2c2c; QUAD $0x2c2c2c2c2c2c2c2c  // .space 16, ',,,,,,,,,,,,,,,,'
	//0x00000010 LCPI0_1
	0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, //0x00000010 QUAD $0x2020202020202020; QUAD $0x2020202020202020  // .space 16, '                '
	//0x00000020 LCPI0_2
	0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, //0x00000020 QUAD $0x7d7d7d7d7d7d7d7d; QUAD $0x7d7d7d7d7d7d7d7d  // .space 16, '}}}}}}}}}}}}}}}}'
	//0x00000030 LCPI0_3
	0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, //0x00000030 QUAD $0x2222222222222222; QUAD $0x2222222222222222  // .space 16, '""""""""""""""""'
	//0x00000040 LCPI0_4
	0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, //0x00000040 QUAD $0x5c5c5c5c5c5c5c5c; QUAD $0x5c5c5c5c5c5c5c5c  // .space 16, '\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\'
	//0x00000050 LCPI0_5
	0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, //0x00000050 QUAD $0x7b7b7b7b7b7b7b7b; QUAD $0x7b7b7b7b7b7b7b7b  // .space 16, '{{{{{{{{{{{{{{{{'
	//0x00000060 LCPI0_6
	0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, //0x00000060 QUAD $0x5b5b5b5b5b5b5b5b; QUAD $0x5b5b5b5b5b5b5b5b  // .space 16, '[[[[[[[[[[[[[[[['
	//0x00000070 LCPI0_7
	0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, //0x00000070 QUAD $0x5d5d5d5d5d5d5d5d; QUAD $0x5d5d5d5d5d5d5d5d  // .space 16, ']]]]]]]]]]]]]]]]'
	//0x00000080 LCPI0_8
	0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, //0x00000080 QUAD $0x2f2f2f2f2f2f2f2f; QUAD $0x2f2f2f2f2f2f2f2f  // .space 16, '////////////////'
	//0x00000090 LCPI0_9
	0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, //0x00000090 QUAD $0x3a3a3a3a3a3a3a3a; QUAD $0x3a3a3a3a3a3a3a3a  // .space 16, '::::::::::::::::'
	//0x000000a0 LCPI0_10
	0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, //0x000000a0 QUAD $0x2b2b2b2b2b2b2b2b; QUAD $0x2b2b2b2b2b2b2b2b  // .space 16, '++++++++++++++++'
	//0x000000b0 LCPI0_11
	0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, //0x000000b0 QUAD $0x2d2d2d2d2d2d2d2d; QUAD $0x2d2d2d2d2d2d2d2d  // .space 16, '----------------'
	//0x000000c0 LCPI0_12
	0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, //0x000000c0 QUAD $0x2e2e2e2e2e2e2e2e; QUAD $0x2e2e2e2e2e2e2e2e  // .space 16, '................'
	//0x000000d0 LCPI0_13
	0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, //0x000000d0 QUAD $0x6565656565656565; QUAD $0x6565656565656565  // .space 16, 'eeeeeeeeeeeeeeee'
	//0x000000e0 .p2align 4, 0x90
	//0x000000e0 _get_by_path
	0x55, //0x000000e0 pushq        %rbp
	0x48, 0x89, 0xe5, //0x000000e1 movq         %rsp, %rbp
	0x41, 0x57, //0x000000e4 pushq        %r15
	0x41, 0x56, //0x000000e6 pushq        %r14
	0x41, 0x55, //0x000000e8 pushq        %r13
	0x41, 0x54, //0x000000ea pushq        %r12
	0x53, //0x000000ec pushq        %rbx
	0x48, 0x81, 0xec, 0xe0, 0x00, 0x00, 0x00, //0x000000ed subq         $224, %rsp
	0x49, 0x89, 0xf0, //0x000000f4 movq         %rsi, %r8
	0x49, 0x89, 0xff, //0x000000f7 movq         %rdi, %r15
	0x48, 0x8b, 0x42, 0x08, //0x000000fa movq         $8(%rdx), %rax
	0x48, 0x85, 0xc0, //0x000000fe testq        %rax, %rax
	0x48, 0x89, 0x74, 0x24, 0x18, //0x00000101 movq         %rsi, $24(%rsp)
	0x48, 0x89, 0x7c, 0x24, 0x28, //0x00000106 movq         %rdi, $40(%rsp)
	0x48, 0x89, 0x4c, 0x24, 0x40, //0x0000010b movq         %rcx, $64(%rsp)
	0x0f, 0x84, 0xb4, 0x2a, 0x00, 0x00, //0x00000110 je           LBB0_447
	0x4c, 0x8b, 0x2a, //0x00000116 movq         (%rdx), %r13
	0x48, 0xc1, 0xe0, 0x04, //0x00000119 shlq         $4, %rax
	0x4c, 0x01, 0xe8, //0x0000011d addq         %r13, %rax
	0x48, 0x89, 0x84, 0x24, 0xc0, 0x00, 0x00, 0x00, //0x00000120 movq         %rax, $192(%rsp)
	0x4d, 0x8d, 0x5f, 0x08, //0x00000128 leaq         $8(%r15), %r11
	0x49, 0x8b, 0x3f, //0x0000012c movq         (%r15), %rdi
	0x49, 0x8b, 0x00, //0x0000012f movq         (%r8), %rax
	0x49, 0xb9, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00000132 movabsq      $4294977024, %r9
	0xc5, 0xfa, 0x6f, 0x05, 0xec, 0xfe, 0xff, 0xff, //0x0000013c vmovdqu      $-276(%rip), %xmm0  /* LCPI0_3+0(%rip) */
	0xc5, 0xfa, 0x6f, 0x0d, 0xf4, 0xfe, 0xff, 0xff, //0x00000144 vmovdqu      $-268(%rip), %xmm1  /* LCPI0_4+0(%rip) */
	0xc5, 0x7a, 0x6f, 0x35, 0xac, 0xfe, 0xff, 0xff, //0x0000014c vmovdqu      $-340(%rip), %xmm14  /* LCPI0_0+0(%rip) */
	0xc5, 0x7a, 0x6f, 0x3d, 0xb4, 0xfe, 0xff, 0xff, //0x00000154 vmovdqu      $-332(%rip), %xmm15  /* LCPI0_1+0(%rip) */
	0xc5, 0xfa, 0x6f, 0x25, 0xbc, 0xfe, 0xff, 0xff, //0x0000015c vmovdqu      $-324(%rip), %xmm4  /* LCPI0_2+0(%rip) */
	0xc4, 0x41, 0x31, 0x76, 0xc9, //0x00000164 vpcmpeqd     %xmm9, %xmm9, %xmm9
	0xc5, 0x7a, 0x6f, 0x15, 0xef, 0xfe, 0xff, 0xff, //0x00000169 vmovdqu      $-273(%rip), %xmm10  /* LCPI0_6+0(%rip) */
	0xc5, 0x7a, 0x6f, 0x1d, 0xf7, 0xfe, 0xff, 0xff, //0x00000171 vmovdqu      $-265(%rip), %xmm11  /* LCPI0_7+0(%rip) */
	0xc4, 0x41, 0x39, 0xef, 0xc0, //0x00000179 vpxor        %xmm8, %xmm8, %xmm8
	0xc5, 0x7a, 0x6f, 0x25, 0xca, 0xfe, 0xff, 0xff, //0x0000017e vmovdqu      $-310(%rip), %xmm12  /* LCPI0_5+0(%rip) */
	0xc5, 0x7a, 0x6f, 0x2d, 0x92, 0xfe, 0xff, 0xff, //0x00000186 vmovdqu      $-366(%rip), %xmm13  /* LCPI0_2+0(%rip) */
	0x4c, 0x89, 0x5c, 0x24, 0x38, //0x0000018e movq         %r11, $56(%rsp)
	//0x00000193 LBB0_2
	0x49, 0x8b, 0x0b, //0x00000193 movq         (%r11), %rcx
	0x48, 0x89, 0xc2, //0x00000196 movq         %rax, %rdx
	0x48, 0x29, 0xca, //0x00000199 subq         %rcx, %rdx
	0x0f, 0x83, 0x2e, 0x00, 0x00, 0x00, //0x0000019c jae          LBB0_7
	0x8a, 0x1c, 0x07, //0x000001a2 movb         (%rdi,%rax), %bl
	0x80, 0xfb, 0x0d, //0x000001a5 cmpb         $13, %bl
	0x0f, 0x84, 0x22, 0x00, 0x00, 0x00, //0x000001a8 je           LBB0_7
	0x80, 0xfb, 0x20, //0x000001ae cmpb         $32, %bl
	0x0f, 0x84, 0x19, 0x00, 0x00, 0x00, //0x000001b1 je           LBB0_7
	0x80, 0xc3, 0xf7, //0x000001b7 addb         $-9, %bl
	0x80, 0xfb, 0x01, //0x000001ba cmpb         $1, %bl
	0x0f, 0x86, 0x0d, 0x00, 0x00, 0x00, //0x000001bd jbe          LBB0_7
	0x48, 0x89, 0xc6, //0x000001c3 movq         %rax, %rsi
	0xe9, 0x35, 0x01, 0x00, 0x00, //0x000001c6 jmp          LBB0_27
	0x90, 0x90, 0x90, 0x90, 0x90, //0x000001cb .p2align 4, 0x90
	//0x000001d0 LBB0_7
	0x48, 0x8d, 0x70, 0x01, //0x000001d0 leaq         $1(%rax), %rsi
	0x48, 0x39, 0xce, //0x000001d4 cmpq         %rcx, %rsi
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x000001d7 jae          LBB0_11
	0x8a, 0x1c, 0x37, //0x000001dd movb         (%rdi,%rsi), %bl
	0x80, 0xfb, 0x0d, //0x000001e0 cmpb         $13, %bl
	0x0f, 0x84, 0x17, 0x00, 0x00, 0x00, //0x000001e3 je           LBB0_11
	0x80, 0xfb, 0x20, //0x000001e9 cmpb         $32, %bl
	0x0f, 0x84, 0x0e, 0x00, 0x00, 0x00, //0x000001ec je           LBB0_11
	0x80, 0xc3, 0xf7, //0x000001f2 addb         $-9, %bl
	0x80, 0xfb, 0x01, //0x000001f5 cmpb         $1, %bl
	0x0f, 0x87, 0x02, 0x01, 0x00, 0x00, //0x000001f8 ja           LBB0_27
	0x90, 0x90, //0x000001fe .p2align 4, 0x90
	//0x00000200 LBB0_11
	0x48, 0x8d, 0x70, 0x02, //0x00000200 leaq         $2(%rax), %rsi
	0x48, 0x39, 0xce, //0x00000204 cmpq         %rcx, %rsi
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00000207 jae          LBB0_15
	0x8a, 0x1c, 0x37, //0x0000020d movb         (%rdi,%rsi), %bl
	0x80, 0xfb, 0x0d, //0x00000210 cmpb         $13, %bl
	0x0f, 0x84, 0x17, 0x00, 0x00, 0x00, //0x00000213 je           LBB0_15
	0x80, 0xfb, 0x20, //0x00000219 cmpb         $32, %bl
	0x0f, 0x84, 0x0e, 0x00, 0x00, 0x00, //0x0000021c je           LBB0_15
	0x80, 0xc3, 0xf7, //0x00000222 addb         $-9, %bl
	0x80, 0xfb, 0x01, //0x00000225 cmpb         $1, %bl
	0x0f, 0x87, 0xd2, 0x00, 0x00, 0x00, //0x00000228 ja           LBB0_27
	0x90, 0x90, //0x0000022e .p2align 4, 0x90
	//0x00000230 LBB0_15
	0x48, 0x8d, 0x70, 0x03, //0x00000230 leaq         $3(%rax), %rsi
	0x48, 0x39, 0xce, //0x00000234 cmpq         %rcx, %rsi
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00000237 jae          LBB0_19
	0x8a, 0x1c, 0x37, //0x0000023d movb         (%rdi,%rsi), %bl
	0x80, 0xfb, 0x0d, //0x00000240 cmpb         $13, %bl
	0x0f, 0x84, 0x17, 0x00, 0x00, 0x00, //0x00000243 je           LBB0_19
	0x80, 0xfb, 0x20, //0x00000249 cmpb         $32, %bl
	0x0f, 0x84, 0x0e, 0x00, 0x00, 0x00, //0x0000024c je           LBB0_19
	0x80, 0xc3, 0xf7, //0x00000252 addb         $-9, %bl
	0x80, 0xfb, 0x01, //0x00000255 cmpb         $1, %bl
	0x0f, 0x87, 0xa2, 0x00, 0x00, 0x00, //0x00000258 ja           LBB0_27
	0x90, 0x90, //0x0000025e .p2align 4, 0x90
	//0x00000260 LBB0_19
	0x4c, 0x8d, 0x50, 0x04, //0x00000260 leaq         $4(%rax), %r10
	0x4c, 0x39, 0xd1, //0x00000264 cmpq         %r10, %rcx
	0x0f, 0x86, 0x63, 0x00, 0x00, 0x00, //0x00000267 jbe          LBB0_834
	0x4c, 0x39, 0xd1, //0x0000026d cmpq         %r10, %rcx
	0x0f, 0x84, 0x71, 0x00, 0x00, 0x00, //0x00000270 je           LBB0_26
	0x4c, 0x8d, 0x14, 0x0f, //0x00000276 leaq         (%rdi,%rcx), %r10
	0x48, 0x83, 0xc2, 0x04, //0x0000027a addq         $4, %rdx
	0x48, 0x89, 0xfb, //0x0000027e movq         %rdi, %rbx
	0x48, 0x8d, 0x74, 0x07, 0x05, //0x00000281 leaq         $5(%rdi,%rax), %rsi
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000286 .p2align 4, 0x90
	//0x00000290 LBB0_22
	0x0f, 0xbe, 0x7e, 0xff, //0x00000290 movsbl       $-1(%rsi), %edi
	0x83, 0xff, 0x20, //0x00000294 cmpl         $32, %edi
	0x0f, 0x87, 0x7f, 0x00, 0x00, 0x00, //0x00000297 ja           LBB0_28
	0x49, 0x0f, 0xa3, 0xf9, //0x0000029d btq          %rdi, %r9
	0x0f, 0x83, 0x75, 0x00, 0x00, 0x00, //0x000002a1 jae          LBB0_28
	0x48, 0xff, 0xc6, //0x000002a7 incq         %rsi
	0x48, 0xff, 0xc2, //0x000002aa incq         %rdx
	0x0f, 0x85, 0xdd, 0xff, 0xff, 0xff, //0x000002ad jne          LBB0_22
	0x48, 0x89, 0xdf, //0x000002b3 movq         %rbx, %rdi
	0x49, 0x29, 0xfa, //0x000002b6 subq         %rdi, %r10
	0x4c, 0x89, 0xd6, //0x000002b9 movq         %r10, %rsi
	0x48, 0x39, 0xce, //0x000002bc cmpq         %rcx, %rsi
	0x0f, 0x82, 0x3b, 0x00, 0x00, 0x00, //0x000002bf jb           LBB0_27
	0xe9, 0x67, 0x00, 0x00, 0x00, //0x000002c5 jmp          LBB0_29
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000002ca .p2align 4, 0x90
	//0x000002d0 LBB0_834
	0x4d, 0x89, 0x10, //0x000002d0 movq         %r10, (%r8)
	0x31, 0xc9, //0x000002d3 xorl         %ecx, %ecx
	0x49, 0x8b, 0x45, 0x00, //0x000002d5 movq         (%r13), %rax
	0x48, 0x85, 0xc0, //0x000002d9 testq        %rax, %rax
	0x0f, 0x85, 0x6e, 0x00, 0x00, 0x00, //0x000002dc jne          LBB0_30
	0xe9, 0x82, 0x40, 0x00, 0x00, //0x000002e2 jmp          LBB0_835
	//0x000002e7 LBB0_26
	0x49, 0x01, 0xfa, //0x000002e7 addq         %rdi, %r10
	0x49, 0x29, 0xfa, //0x000002ea subq         %rdi, %r10
	0x4c, 0x89, 0xd6, //0x000002ed movq         %r10, %rsi
	0x48, 0x39, 0xce, //0x000002f0 cmpq         %rcx, %rsi
	0x0f, 0x83, 0x38, 0x00, 0x00, 0x00, //0x000002f3 jae          LBB0_29
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000002f9 .p2align 4, 0x90
	//0x00000300 LBB0_27
	0x4c, 0x8d, 0x56, 0x01, //0x00000300 leaq         $1(%rsi), %r10
	0x4d, 0x89, 0x10, //0x00000304 movq         %r10, (%r8)
	0x8a, 0x0c, 0x37, //0x00000307 movb         (%rdi,%rsi), %cl
	0x49, 0x8b, 0x45, 0x00, //0x0000030a movq         (%r13), %rax
	0x48, 0x85, 0xc0, //0x0000030e testq        %rax, %rax
	0x0f, 0x85, 0x39, 0x00, 0x00, 0x00, //0x00000311 jne          LBB0_30
	0xe9, 0x4d, 0x40, 0x00, 0x00, //0x00000317 jmp          LBB0_835
	//0x0000031c LBB0_28
	0x48, 0x89, 0xdf, //0x0000031c movq         %rbx, %rdi
	0x48, 0x89, 0xda, //0x0000031f movq         %rbx, %rdx
	0x48, 0xf7, 0xd2, //0x00000322 notq         %rdx
	0x48, 0x01, 0xd6, //0x00000325 addq         %rdx, %rsi
	0x48, 0x39, 0xce, //0x00000328 cmpq         %rcx, %rsi
	0x0f, 0x82, 0xcf, 0xff, 0xff, 0xff, //0x0000032b jb           LBB0_27
	//0x00000331 LBB0_29
	0x31, 0xc9, //0x00000331 xorl         %ecx, %ecx
	0x49, 0x89, 0xc2, //0x00000333 movq         %rax, %r10
	0x49, 0x8b, 0x45, 0x00, //0x00000336 movq         (%r13), %rax
	0x48, 0x85, 0xc0, //0x0000033a testq        %rax, %rax
	0x0f, 0x84, 0x26, 0x40, 0x00, 0x00, //0x0000033d je           LBB0_835
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000343 .p2align 4, 0x90
	//0x00000350 LBB0_30
	0x8a, 0x40, 0x17, //0x00000350 movb         $23(%rax), %al
	0x24, 0x1f, //0x00000353 andb         $31, %al
	0x3c, 0x02, //0x00000355 cmpb         $2, %al
	0x0f, 0x84, 0x23, 0x00, 0x00, 0x00, //0x00000357 je           LBB0_343
	0x3c, 0x18, //0x0000035d cmpb         $24, %al
	0x0f, 0x85, 0x04, 0x40, 0x00, 0x00, //0x0000035f jne          LBB0_835
	0x80, 0xf9, 0x7b, //0x00000365 cmpb         $123, %cl
	0x4c, 0x89, 0x6c, 0x24, 0x58, //0x00000368 movq         %r13, $88(%rsp)
	0x0f, 0x84, 0x91, 0x00, 0x00, 0x00, //0x0000036d je           LBB0_33
	0xe9, 0x09, 0x40, 0x00, 0x00, //0x00000373 jmp          LBB0_837
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000378 .p2align 4, 0x90
	//0x00000380 LBB0_343
	0x80, 0xf9, 0x5b, //0x00000380 cmpb         $91, %cl
	0x0f, 0x85, 0xf8, 0x3f, 0x00, 0x00, //0x00000383 jne          LBB0_837
	0x49, 0x8b, 0x45, 0x08, //0x00000389 movq         $8(%r13), %rax
	0x4c, 0x8b, 0x30, //0x0000038d movq         (%rax), %r14
	0x4d, 0x85, 0xf6, //0x00000390 testq        %r14, %r14
	0x0f, 0x88, 0xd0, 0x3f, 0x00, 0x00, //0x00000393 js           LBB0_835
	0x49, 0x8b, 0x03, //0x00000399 movq         (%r11), %rax
	0x4c, 0x89, 0xd1, //0x0000039c movq         %r10, %rcx
	0x48, 0x29, 0xc1, //0x0000039f subq         %rax, %rcx
	0x0f, 0x83, 0xb8, 0x19, 0x00, 0x00, //0x000003a2 jae          LBB0_350
	0x42, 0x8a, 0x14, 0x17, //0x000003a8 movb         (%rdi,%r10), %dl
	0x80, 0xfa, 0x0d, //0x000003ac cmpb         $13, %dl
	0x0f, 0x84, 0xab, 0x19, 0x00, 0x00, //0x000003af je           LBB0_350
	0x80, 0xfa, 0x20, //0x000003b5 cmpb         $32, %dl
	0x0f, 0x84, 0xa2, 0x19, 0x00, 0x00, //0x000003b8 je           LBB0_350
	0x80, 0xc2, 0xf7, //0x000003be addb         $-9, %dl
	0x80, 0xfa, 0x01, //0x000003c1 cmpb         $1, %dl
	0x0f, 0x86, 0x96, 0x19, 0x00, 0x00, //0x000003c4 jbe          LBB0_350
	0x4c, 0x89, 0xd2, //0x000003ca movq         %r10, %rdx
	0xe9, 0xbf, 0x1a, 0x00, 0x00, //0x000003cd jmp          LBB0_372
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000003d2 .p2align 4, 0x90
	//0x000003e0 LBB0_246
	0x48, 0x01, 0xfe, //0x000003e0 addq         %rdi, %rsi
	0x48, 0x29, 0xfe, //0x000003e3 subq         %rdi, %rsi
	0x48, 0x89, 0xf2, //0x000003e6 movq         %rsi, %rdx
	0x48, 0x39, 0xc2, //0x000003e9 cmpq         %rax, %rdx
	0x0f, 0x83, 0x8f, 0x3f, 0x00, 0x00, //0x000003ec jae          LBB0_837
	//0x000003f2 LBB0_248
	0x4c, 0x8d, 0x52, 0x01, //0x000003f2 leaq         $1(%rdx), %r10
	0x4d, 0x89, 0x10, //0x000003f6 movq         %r10, (%r8)
	0x8a, 0x04, 0x17, //0x000003f9 movb         (%rdi,%rdx), %al
	0x3c, 0x2c, //0x000003fc cmpb         $44, %al
	0x0f, 0x85, 0x2f, 0x3f, 0x00, 0x00, //0x000003fe jne          LBB0_249
	//0x00000404 LBB0_33
	0x49, 0x8b, 0x03, //0x00000404 movq         (%r11), %rax
	0x4c, 0x89, 0xd1, //0x00000407 movq         %r10, %rcx
	0x48, 0x29, 0xc1, //0x0000040a subq         %rax, %rcx
	0x48, 0x89, 0x7c, 0x24, 0x20, //0x0000040d movq         %rdi, $32(%rsp)
	0x0f, 0x83, 0x38, 0x00, 0x00, 0x00, //0x00000412 jae          LBB0_38
	0x42, 0x8a, 0x14, 0x17, //0x00000418 movb         (%rdi,%r10), %dl
	0x80, 0xfa, 0x0d, //0x0000041c cmpb         $13, %dl
	0x0f, 0x84, 0x2b, 0x00, 0x00, 0x00, //0x0000041f je           LBB0_38
	0x80, 0xfa, 0x20, //0x00000425 cmpb         $32, %dl
	0x0f, 0x84, 0x22, 0x00, 0x00, 0x00, //0x00000428 je           LBB0_38
	0x80, 0xc2, 0xf7, //0x0000042e addb         $-9, %dl
	0x80, 0xfa, 0x01, //0x00000431 cmpb         $1, %dl
	0x0f, 0x86, 0x16, 0x00, 0x00, 0x00, //0x00000434 jbe          LBB0_38
	0x4c, 0x89, 0xd2, //0x0000043a movq         %r10, %rdx
	0xe9, 0x45, 0x01, 0x00, 0x00, //0x0000043d jmp          LBB0_59
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000442 .p2align 4, 0x90
	//0x00000450 LBB0_38
	0x49, 0x8d, 0x52, 0x01, //0x00000450 leaq         $1(%r10), %rdx
	0x48, 0x39, 0xc2, //0x00000454 cmpq         %rax, %rdx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00000457 jae          LBB0_42
	0x8a, 0x1c, 0x17, //0x0000045d movb         (%rdi,%rdx), %bl
	0x80, 0xfb, 0x0d, //0x00000460 cmpb         $13, %bl
	0x0f, 0x84, 0x17, 0x00, 0x00, 0x00, //0x00000463 je           LBB0_42
	0x80, 0xfb, 0x20, //0x00000469 cmpb         $32, %bl
	0x0f, 0x84, 0x0e, 0x00, 0x00, 0x00, //0x0000046c je           LBB0_42
	0x80, 0xc3, 0xf7, //0x00000472 addb         $-9, %bl
	0x80, 0xfb, 0x01, //0x00000475 cmpb         $1, %bl
	0x0f, 0x87, 0x09, 0x01, 0x00, 0x00, //0x00000478 ja           LBB0_59
	0x90, 0x90, //0x0000047e .p2align 4, 0x90
	//0x00000480 LBB0_42
	0x49, 0x8d, 0x52, 0x02, //0x00000480 leaq         $2(%r10), %rdx
	0x48, 0x39, 0xc2, //0x00000484 cmpq         %rax, %rdx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00000487 jae          LBB0_46
	0x8a, 0x1c, 0x17, //0x0000048d movb         (%rdi,%rdx), %bl
	0x80, 0xfb, 0x0d, //0x00000490 cmpb         $13, %bl
	0x0f, 0x84, 0x17, 0x00, 0x00, 0x00, //0x00000493 je           LBB0_46
	0x80, 0xfb, 0x20, //0x00000499 cmpb         $32, %bl
	0x0f, 0x84, 0x0e, 0x00, 0x00, 0x00, //0x0000049c je           LBB0_46
	0x80, 0xc3, 0xf7, //0x000004a2 addb         $-9, %bl
	0x80, 0xfb, 0x01, //0x000004a5 cmpb         $1, %bl
	0x0f, 0x87, 0xd9, 0x00, 0x00, 0x00, //0x000004a8 ja           LBB0_59
	0x90, 0x90, //0x000004ae .p2align 4, 0x90
	//0x000004b0 LBB0_46
	0x49, 0x8d, 0x52, 0x03, //0x000004b0 leaq         $3(%r10), %rdx
	0x48, 0x39, 0xc2, //0x000004b4 cmpq         %rax, %rdx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x000004b7 jae          LBB0_50
	0x8a, 0x1c, 0x17, //0x000004bd movb         (%rdi,%rdx), %bl
	0x80, 0xfb, 0x0d, //0x000004c0 cmpb         $13, %bl
	0x0f, 0x84, 0x17, 0x00, 0x00, 0x00, //0x000004c3 je           LBB0_50
	0x80, 0xfb, 0x20, //0x000004c9 cmpb         $32, %bl
	0x0f, 0x84, 0x0e, 0x00, 0x00, 0x00, //0x000004cc je           LBB0_50
	0x80, 0xc3, 0xf7, //0x000004d2 addb         $-9, %bl
	0x80, 0xfb, 0x01, //0x000004d5 cmpb         $1, %bl
	0x0f, 0x87, 0xa9, 0x00, 0x00, 0x00, //0x000004d8 ja           LBB0_59
	0x90, 0x90, //0x000004de .p2align 4, 0x90
	//0x000004e0 LBB0_50
	0x49, 0x8d, 0x72, 0x04, //0x000004e0 leaq         $4(%r10), %rsi
	0x48, 0x39, 0xf0, //0x000004e4 cmpq         %rsi, %rax
	0x0f, 0x86, 0x9d, 0x28, 0x00, 0x00, //0x000004e7 jbe          LBB0_550
	0x48, 0x39, 0xf0, //0x000004ed cmpq         %rsi, %rax
	0x0f, 0x84, 0x5a, 0x00, 0x00, 0x00, //0x000004f0 je           LBB0_57
	0x48, 0x8d, 0x34, 0x07, //0x000004f6 leaq         (%rdi,%rax), %rsi
	0x48, 0x83, 0xc1, 0x04, //0x000004fa addq         $4, %rcx
	0x4a, 0x8d, 0x54, 0x17, 0x05, //0x000004fe leaq         $5(%rdi,%r10), %rdx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000503 .p2align 4, 0x90
	//0x00000510 LBB0_53
	0x0f, 0xbe, 0x7a, 0xff, //0x00000510 movsbl       $-1(%rdx), %edi
	0x83, 0xff, 0x20, //0x00000514 cmpl         $32, %edi
	0x0f, 0x87, 0x53, 0x00, 0x00, 0x00, //0x00000517 ja           LBB0_58
	0x49, 0x0f, 0xa3, 0xf9, //0x0000051d btq          %rdi, %r9
	0x0f, 0x83, 0x49, 0x00, 0x00, 0x00, //0x00000521 jae          LBB0_58
	0x48, 0xff, 0xc2, //0x00000527 incq         %rdx
	0x48, 0xff, 0xc1, //0x0000052a incq         %rcx
	0x0f, 0x85, 0xdd, 0xff, 0xff, 0xff, //0x0000052d jne          LBB0_53
	0x48, 0x8b, 0x7c, 0x24, 0x20, //0x00000533 movq         $32(%rsp), %rdi
	0x48, 0x29, 0xfe, //0x00000538 subq         %rdi, %rsi
	0x48, 0x89, 0xf2, //0x0000053b movq         %rsi, %rdx
	0x48, 0x39, 0xc2, //0x0000053e cmpq         %rax, %rdx
	0x0f, 0x82, 0x40, 0x00, 0x00, 0x00, //0x00000541 jb           LBB0_59
	0xe9, 0x35, 0x3e, 0x00, 0x00, //0x00000547 jmp          LBB0_837
	0x90, 0x90, 0x90, 0x90, //0x0000054c .p2align 4, 0x90
	//0x00000550 LBB0_57
	0x48, 0x01, 0xfe, //0x00000550 addq         %rdi, %rsi
	0x48, 0x29, 0xfe, //0x00000553 subq         %rdi, %rsi
	0x48, 0x89, 0xf2, //0x00000556 movq         %rsi, %rdx
	0x48, 0x39, 0xc2, //0x00000559 cmpq         %rax, %rdx
	0x0f, 0x82, 0x25, 0x00, 0x00, 0x00, //0x0000055c jb           LBB0_59
	0xe9, 0x1a, 0x3e, 0x00, 0x00, //0x00000562 jmp          LBB0_837
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000567 .p2align 4, 0x90
	//0x00000570 LBB0_58
	0x48, 0x8b, 0x7c, 0x24, 0x20, //0x00000570 movq         $32(%rsp), %rdi
	0x48, 0x89, 0xf9, //0x00000575 movq         %rdi, %rcx
	0x48, 0xf7, 0xd1, //0x00000578 notq         %rcx
	0x48, 0x01, 0xca, //0x0000057b addq         %rcx, %rdx
	0x48, 0x39, 0xc2, //0x0000057e cmpq         %rax, %rdx
	0x0f, 0x83, 0xfa, 0x3d, 0x00, 0x00, //0x00000581 jae          LBB0_837
	//0x00000587 LBB0_59
	0x4c, 0x8d, 0x52, 0x01, //0x00000587 leaq         $1(%rdx), %r10
	0x4d, 0x89, 0x10, //0x0000058b movq         %r10, (%r8)
	0x8a, 0x04, 0x17, //0x0000058e movb         (%rdi,%rdx), %al
	0x3c, 0x22, //0x00000591 cmpb         $34, %al
	0x0f, 0x85, 0x9a, 0x3d, 0x00, 0x00, //0x00000593 jne          LBB0_249
	0x49, 0x8b, 0x33, //0x00000599 movq         (%r11), %rsi
	0x48, 0x89, 0xf1, //0x0000059c movq         %rsi, %rcx
	0x4c, 0x29, 0xd1, //0x0000059f subq         %r10, %rcx
	0x0f, 0x84, 0x8f, 0x47, 0x00, 0x00, //0x000005a2 je           LBB0_936
	0x49, 0x8b, 0x45, 0x08, //0x000005a8 movq         $8(%r13), %rax
	0x4c, 0x8b, 0x20, //0x000005ac movq         (%rax), %r12
	0x48, 0x8b, 0x40, 0x08, //0x000005af movq         $8(%rax), %rax
	0x48, 0x89, 0x44, 0x24, 0x48, //0x000005b3 movq         %rax, $72(%rsp)
	0x49, 0x01, 0xfa, //0x000005b8 addq         %rdi, %r10
	0x48, 0x83, 0xf9, 0x40, //0x000005bb cmpq         $64, %rcx
	0x4c, 0x89, 0x94, 0x24, 0xb0, 0x00, 0x00, 0x00, //0x000005bf movq         %r10, $176(%rsp)
	0x48, 0x89, 0xb4, 0x24, 0xb8, 0x00, 0x00, 0x00, //0x000005c7 movq         %rsi, $184(%rsp)
	0x0f, 0x82, 0x0c, 0x11, 0x00, 0x00, //0x000005cf jb           LBB0_105
	0x4c, 0x89, 0x64, 0x24, 0x30, //0x000005d5 movq         %r12, $48(%rsp)
	0x89, 0xc8, //0x000005da movl         %ecx, %eax
	0x83, 0xe0, 0x3f, //0x000005dc andl         $63, %eax
	0x48, 0x89, 0x84, 0x24, 0xa8, 0x00, 0x00, 0x00, //0x000005df movq         %rax, $168(%rsp)
	0x48, 0x29, 0xd6, //0x000005e7 subq         %rdx, %rsi
	0x48, 0x83, 0xc6, 0xbf, //0x000005ea addq         $-65, %rsi
	0x48, 0x83, 0xe6, 0xc0, //0x000005ee andq         $-64, %rsi
	0x48, 0x01, 0xd6, //0x000005f2 addq         %rdx, %rsi
	0x48, 0x8d, 0x44, 0x37, 0x41, //0x000005f5 leaq         $65(%rdi,%rsi), %rax
	0x48, 0x89, 0x44, 0x24, 0x50, //0x000005fa movq         %rax, $80(%rsp)
	0x49, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x000005ff movq         $-1, %r8
	0x4c, 0x89, 0xd6, //0x00000606 movq         %r10, %rsi
	0x31, 0xdb, //0x00000609 xorl         %ebx, %ebx
	0x90, 0x90, 0x90, 0x90, 0x90, //0x0000060b .p2align 4, 0x90
	//0x00000610 LBB0_63
	0xc5, 0xfa, 0x6f, 0x16, //0x00000610 vmovdqu      (%rsi), %xmm2
	0xc5, 0xfa, 0x6f, 0x6e, 0x10, //0x00000614 vmovdqu      $16(%rsi), %xmm5
	0xc5, 0xfa, 0x6f, 0x76, 0x20, //0x00000619 vmovdqu      $32(%rsi), %xmm6
	0xc5, 0xfa, 0x6f, 0x7e, 0x30, //0x0000061e vmovdqu      $48(%rsi), %xmm7
	0xc5, 0xe9, 0x74, 0xd8, //0x00000623 vpcmpeqb     %xmm0, %xmm2, %xmm3
	0xc5, 0xf9, 0xd7, 0xfb, //0x00000627 vpmovmskb    %xmm3, %edi
	0xc5, 0xd1, 0x74, 0xd8, //0x0000062b vpcmpeqb     %xmm0, %xmm5, %xmm3
	0xc5, 0x79, 0xd7, 0xe3, //0x0000062f vpmovmskb    %xmm3, %r12d
	0xc5, 0xc9, 0x74, 0xd8, //0x00000633 vpcmpeqb     %xmm0, %xmm6, %xmm3
	0xc5, 0x79, 0xd7, 0xeb, //0x00000637 vpmovmskb    %xmm3, %r13d
	0xc5, 0xc1, 0x74, 0xd8, //0x0000063b vpcmpeqb     %xmm0, %xmm7, %xmm3
	0xc5, 0x79, 0xd7, 0xfb, //0x0000063f vpmovmskb    %xmm3, %r15d
	0xc5, 0xe9, 0x74, 0xd1, //0x00000643 vpcmpeqb     %xmm1, %xmm2, %xmm2
	0xc5, 0x79, 0xd7, 0xca, //0x00000647 vpmovmskb    %xmm2, %r9d
	0xc5, 0xd1, 0x74, 0xd1, //0x0000064b vpcmpeqb     %xmm1, %xmm5, %xmm2
	0xc5, 0x79, 0xd7, 0xf2, //0x0000064f vpmovmskb    %xmm2, %r14d
	0xc5, 0xc9, 0x74, 0xd1, //0x00000653 vpcmpeqb     %xmm1, %xmm6, %xmm2
	0xc5, 0x79, 0xd7, 0xda, //0x00000657 vpmovmskb    %xmm2, %r11d
	0xc5, 0xc1, 0x74, 0xd1, //0x0000065b vpcmpeqb     %xmm1, %xmm7, %xmm2
	0xc5, 0x79, 0xd7, 0xd2, //0x0000065f vpmovmskb    %xmm2, %r10d
	0x49, 0xc1, 0xe7, 0x30, //0x00000663 shlq         $48, %r15
	0x49, 0xc1, 0xe5, 0x20, //0x00000667 shlq         $32, %r13
	0x49, 0xc1, 0xe4, 0x10, //0x0000066b shlq         $16, %r12
	0x4c, 0x09, 0xe7, //0x0000066f orq          %r12, %rdi
	0x4c, 0x09, 0xef, //0x00000672 orq          %r13, %rdi
	0x49, 0xc1, 0xe2, 0x30, //0x00000675 shlq         $48, %r10
	0x49, 0xc1, 0xe3, 0x20, //0x00000679 shlq         $32, %r11
	0x49, 0xc1, 0xe6, 0x10, //0x0000067d shlq         $16, %r14
	0x4d, 0x09, 0xf1, //0x00000681 orq          %r14, %r9
	0x4d, 0x09, 0xd9, //0x00000684 orq          %r11, %r9
	0x4d, 0x09, 0xd1, //0x00000687 orq          %r10, %r9
	0x49, 0x83, 0xf8, 0xff, //0x0000068a cmpq         $-1, %r8
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x0000068e jne          LBB0_65
	0x4d, 0x85, 0xc9, //0x00000694 testq        %r9, %r9
	0x0f, 0x85, 0x2f, 0x00, 0x00, 0x00, //0x00000697 jne          LBB0_74
	//0x0000069d LBB0_65
	0x4c, 0x09, 0xff, //0x0000069d orq          %r15, %rdi
	0x4c, 0x89, 0xc8, //0x000006a0 movq         %r9, %rax
	0x48, 0x09, 0xd8, //0x000006a3 orq          %rbx, %rax
	0x0f, 0x85, 0x3e, 0x00, 0x00, 0x00, //0x000006a6 jne          LBB0_75
	//0x000006ac LBB0_66
	0x48, 0x85, 0xff, //0x000006ac testq        %rdi, %rdi
	0x0f, 0x85, 0x8b, 0x00, 0x00, 0x00, //0x000006af jne          LBB0_76
	//0x000006b5 LBB0_67
	0x48, 0x83, 0xc1, 0xc0, //0x000006b5 addq         $-64, %rcx
	0x48, 0x83, 0xc6, 0x40, //0x000006b9 addq         $64, %rsi
	0x48, 0x83, 0xf9, 0x3f, //0x000006bd cmpq         $63, %rcx
	0x0f, 0x87, 0x49, 0xff, 0xff, 0xff, //0x000006c1 ja           LBB0_63
	0xe9, 0x53, 0x0f, 0x00, 0x00, //0x000006c7 jmp          LBB0_68
	//0x000006cc LBB0_74
	0x49, 0x89, 0xf2, //0x000006cc movq         %rsi, %r10
	0x4c, 0x2b, 0x54, 0x24, 0x20, //0x000006cf subq         $32(%rsp), %r10
	0x4d, 0x0f, 0xbc, 0xc1, //0x000006d4 bsfq         %r9, %r8
	0x4d, 0x01, 0xd0, //0x000006d8 addq         %r10, %r8
	0x4c, 0x09, 0xff, //0x000006db orq          %r15, %rdi
	0x4c, 0x89, 0xc8, //0x000006de movq         %r9, %rax
	0x48, 0x09, 0xd8, //0x000006e1 orq          %rbx, %rax
	0x0f, 0x84, 0xc2, 0xff, 0xff, 0xff, //0x000006e4 je           LBB0_66
	//0x000006ea LBB0_75
	0x49, 0x89, 0xdb, //0x000006ea movq         %rbx, %r11
	0x49, 0xf7, 0xd3, //0x000006ed notq         %r11
	0x4d, 0x21, 0xcb, //0x000006f0 andq         %r9, %r11
	0x4f, 0x8d, 0x14, 0x1b, //0x000006f3 leaq         (%r11,%r11), %r10
	0x49, 0x09, 0xda, //0x000006f7 orq          %rbx, %r10
	0x4d, 0x89, 0xd6, //0x000006fa movq         %r10, %r14
	0x49, 0xf7, 0xd6, //0x000006fd notq         %r14
	0x4d, 0x21, 0xce, //0x00000700 andq         %r9, %r14
	0x48, 0xbb, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00000703 movabsq      $-6148914691236517206, %rbx
	0x49, 0x21, 0xde, //0x0000070d andq         %rbx, %r14
	0x31, 0xdb, //0x00000710 xorl         %ebx, %ebx
	0x4d, 0x01, 0xde, //0x00000712 addq         %r11, %r14
	0x0f, 0x92, 0xc3, //0x00000715 setb         %bl
	0x4d, 0x01, 0xf6, //0x00000718 addq         %r14, %r14
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x0000071b movabsq      $6148914691236517205, %rax
	0x49, 0x31, 0xc6, //0x00000725 xorq         %rax, %r14
	0x4d, 0x21, 0xd6, //0x00000728 andq         %r10, %r14
	0x49, 0xf7, 0xd6, //0x0000072b notq         %r14
	0x4c, 0x21, 0xf7, //0x0000072e andq         %r14, %rdi
	0x48, 0x85, 0xff, //0x00000731 testq        %rdi, %rdi
	0x0f, 0x84, 0x7b, 0xff, 0xff, 0xff, //0x00000734 je           LBB0_67
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x0000073a .p2align 4, 0x90
	//0x00000740 LBB0_76
	0x48, 0x0f, 0xbc, 0xc7, //0x00000740 bsfq         %rdi, %rax
	0x48, 0x8b, 0x7c, 0x24, 0x20, //0x00000744 movq         $32(%rsp), %rdi
	0x48, 0x29, 0xfe, //0x00000749 subq         %rdi, %rsi
	0x4c, 0x8d, 0x54, 0x06, 0x01, //0x0000074c leaq         $1(%rsi,%rax), %r10
	0x4c, 0x8b, 0x7c, 0x24, 0x28, //0x00000751 movq         $40(%rsp), %r15
	0x4c, 0x8b, 0x6c, 0x24, 0x58, //0x00000756 movq         $88(%rsp), %r13
	0x4c, 0x8b, 0x5c, 0x24, 0x38, //0x0000075b movq         $56(%rsp), %r11
	0x49, 0xb9, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00000760 movabsq      $4294977024, %r9
	0x4c, 0x8b, 0x64, 0x24, 0x30, //0x0000076a movq         $48(%rsp), %r12
	0x48, 0x8b, 0x74, 0x24, 0x48, //0x0000076f movq         $72(%rsp), %rsi
	0x4d, 0x85, 0xd2, //0x00000774 testq        %r10, %r10
	0x48, 0x8b, 0x9c, 0x24, 0xb0, 0x00, 0x00, 0x00, //0x00000777 movq         $176(%rsp), %rbx
	0x0f, 0x88, 0xba, 0x45, 0x00, 0x00, //0x0000077f js           LBB0_937
	//0x00000785 LBB0_79
	0x48, 0x8b, 0x44, 0x24, 0x18, //0x00000785 movq         $24(%rsp), %rax
	0x4c, 0x89, 0x10, //0x0000078a movq         %r10, (%rax)
	0x49, 0x83, 0xf8, 0xff, //0x0000078d cmpq         $-1, %r8
	0x0f, 0x84, 0x09, 0x00, 0x00, 0x00, //0x00000791 je           LBB0_81
	0x4d, 0x39, 0xd0, //0x00000797 cmpq         %r10, %r8
	0x0f, 0x8e, 0x5e, 0x0f, 0x00, 0x00, //0x0000079a jle          LBB0_107
	//0x000007a0 LBB0_81
	0x4c, 0x89, 0xd1, //0x000007a0 movq         %r10, %rcx
	0x48, 0x29, 0xd1, //0x000007a3 subq         %rdx, %rcx
	0x48, 0x83, 0xc1, 0xfe, //0x000007a6 addq         $-2, %rcx
	0xba, 0x01, 0x00, 0x00, 0x00, //0x000007aa movl         $1, %edx
	0x48, 0x89, 0xc8, //0x000007af movq         %rcx, %rax
	0x48, 0x09, 0xf0, //0x000007b2 orq          %rsi, %rax
	0x0f, 0x84, 0xe5, 0x00, 0x00, 0x00, //0x000007b5 je           LBB0_92
	0x48, 0x39, 0xf1, //0x000007bb cmpq         %rsi, %rcx
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x000007be movq         $24(%rsp), %r8
	0x0f, 0x85, 0xf7, 0x00, 0x00, 0x00, //0x000007c3 jne          LBB0_93
	0x48, 0x83, 0xfe, 0x10, //0x000007c9 cmpq         $16, %rsi
	0x0f, 0x82, 0x68, 0x00, 0x00, 0x00, //0x000007cd jb           LBB0_88
	0x48, 0x8d, 0x4e, 0xf0, //0x000007d3 leaq         $-16(%rsi), %rcx
	0x48, 0x89, 0xc8, //0x000007d7 movq         %rcx, %rax
	0x48, 0x83, 0xe0, 0xf0, //0x000007da andq         $-16, %rax
	0x48, 0x8d, 0x74, 0x03, 0x10, //0x000007de leaq         $16(%rbx,%rax), %rsi
	0x49, 0x8d, 0x7c, 0x04, 0x10, //0x000007e3 leaq         $16(%r12,%rax), %rdi
	0x83, 0xe1, 0x0f, //0x000007e8 andl         $15, %ecx
	0x31, 0xdb, //0x000007eb xorl         %ebx, %ebx
	0x90, 0x90, 0x90, //0x000007ed .p2align 4, 0x90
	//0x000007f0 LBB0_85
	0x48, 0x8b, 0x84, 0x24, 0xb0, 0x00, 0x00, 0x00, //0x000007f0 movq         $176(%rsp), %rax
	0xc5, 0xfa, 0x6f, 0x14, 0x18, //0x000007f8 vmovdqu      (%rax,%rbx), %xmm2
	0xc4, 0xc1, 0x69, 0x74, 0x14, 0x1c, //0x000007fd vpcmpeqb     (%r12,%rbx), %xmm2, %xmm2
	0xc5, 0xf9, 0xd7, 0xc2, //0x00000803 vpmovmskb    %xmm2, %eax
	0x66, 0x83, 0xf8, 0xff, //0x00000807 cmpw         $-1, %ax
	0x0f, 0x85, 0x34, 0x01, 0x00, 0x00, //0x0000080b jne          LBB0_98
	0x48, 0x8b, 0x44, 0x24, 0x48, //0x00000811 movq         $72(%rsp), %rax
	0x48, 0x83, 0xc0, 0xf0, //0x00000816 addq         $-16, %rax
	0x48, 0x83, 0xc3, 0x10, //0x0000081a addq         $16, %rbx
	0x48, 0x89, 0x44, 0x24, 0x48, //0x0000081e movq         %rax, $72(%rsp)
	0x48, 0x83, 0xf8, 0x0f, //0x00000823 cmpq         $15, %rax
	0x0f, 0x87, 0xc3, 0xff, 0xff, 0xff, //0x00000827 ja           LBB0_85
	0x49, 0x89, 0xfc, //0x0000082d movq         %rdi, %r12
	0x48, 0x89, 0xf3, //0x00000830 movq         %rsi, %rbx
	0x48, 0x89, 0xce, //0x00000833 movq         %rcx, %rsi
	0x48, 0x8b, 0x7c, 0x24, 0x20, //0x00000836 movq         $32(%rsp), %rdi
	//0x0000083b LBB0_88
	0x44, 0x89, 0xe0, //0x0000083b movl         %r12d, %eax
	0x25, 0xff, 0x0f, 0x00, 0x00, //0x0000083e andl         $4095, %eax
	0x3d, 0xf0, 0x0f, 0x00, 0x00, //0x00000843 cmpl         $4080, %eax
	0x0f, 0x87, 0x88, 0x00, 0x00, 0x00, //0x00000848 ja           LBB0_94
	0x89, 0xd8, //0x0000084e movl         %ebx, %eax
	0x25, 0xff, 0x0f, 0x00, 0x00, //0x00000850 andl         $4095, %eax
	0x3d, 0xf1, 0x0f, 0x00, 0x00, //0x00000855 cmpl         $4081, %eax
	0x0f, 0x83, 0x76, 0x00, 0x00, 0x00, //0x0000085a jae          LBB0_94
	0xc5, 0xfa, 0x6f, 0x13, //0x00000860 vmovdqu      (%rbx), %xmm2
	0xc4, 0xc1, 0x69, 0x74, 0x14, 0x24, //0x00000864 vpcmpeqb     (%r12), %xmm2, %xmm2
	0xc5, 0xf9, 0xd7, 0xca, //0x0000086a vpmovmskb    %xmm2, %ecx
	0x66, 0x83, 0xf9, 0xff, //0x0000086e cmpw         $-1, %cx
	0x0f, 0x84, 0x98, 0x00, 0x00, 0x00, //0x00000872 je           LBB0_100
	0xf7, 0xd1, //0x00000878 notl         %ecx
	0x0f, 0xb7, 0xc1, //0x0000087a movzwl       %cx, %eax
	0x48, 0x0f, 0xbc, 0xc0, //0x0000087d bsfq         %rax, %rax
	0x31, 0xd2, //0x00000881 xorl         %edx, %edx
	0x48, 0x39, 0xf0, //0x00000883 cmpq         %rsi, %rax
	0x0f, 0x93, 0xc2, //0x00000886 setae        %dl
	0x49, 0x8b, 0x0b, //0x00000889 movq         (%r11), %rcx
	0x4c, 0x89, 0xd6, //0x0000088c movq         %r10, %rsi
	0x48, 0x29, 0xce, //0x0000088f subq         %rcx, %rsi
	0x0f, 0x82, 0x87, 0x00, 0x00, 0x00, //0x00000892 jb           LBB0_101
	0xe9, 0xe3, 0x00, 0x00, 0x00, //0x00000898 jmp          LBB0_154
	0x90, 0x90, 0x90, //0x0000089d .p2align 4, 0x90
	//0x000008a0 LBB0_92
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x000008a0 movq         $24(%rsp), %r8
	0x49, 0x8b, 0x0b, //0x000008a5 movq         (%r11), %rcx
	0x4c, 0x89, 0xd6, //0x000008a8 movq         %r10, %rsi
	0x48, 0x29, 0xce, //0x000008ab subq         %rcx, %rsi
	0x0f, 0x82, 0x6b, 0x00, 0x00, 0x00, //0x000008ae jb           LBB0_101
	0xe9, 0xc7, 0x00, 0x00, 0x00, //0x000008b4 jmp          LBB0_154
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000008b9 .p2align 4, 0x90
	//0x000008c0 LBB0_93
	0x31, 0xd2, //0x000008c0 xorl         %edx, %edx
	0x49, 0x8b, 0x0b, //0x000008c2 movq         (%r11), %rcx
	0x4c, 0x89, 0xd6, //0x000008c5 movq         %r10, %rsi
	0x48, 0x29, 0xce, //0x000008c8 subq         %rcx, %rsi
	0x0f, 0x82, 0x4e, 0x00, 0x00, 0x00, //0x000008cb jb           LBB0_101
	0xe9, 0xaa, 0x00, 0x00, 0x00, //0x000008d1 jmp          LBB0_154
	//0x000008d6 LBB0_94
	0x48, 0x85, 0xf6, //0x000008d6 testq        %rsi, %rsi
	0x0f, 0x84, 0x31, 0x00, 0x00, 0x00, //0x000008d9 je           LBB0_100
	0x31, 0xc9, //0x000008df xorl         %ecx, %ecx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000008e1 .p2align 4, 0x90
	//0x000008f0 LBB0_96
	0x0f, 0xb6, 0x04, 0x0b, //0x000008f0 movzbl       (%rbx,%rcx), %eax
	0x41, 0x3a, 0x04, 0x0c, //0x000008f4 cmpb         (%r12,%rcx), %al
	0x0f, 0x85, 0x62, 0x00, 0x00, 0x00, //0x000008f8 jne          LBB0_99
	0x48, 0xff, 0xc1, //0x000008fe incq         %rcx
	0x48, 0x39, 0xce, //0x00000901 cmpq         %rcx, %rsi
	0x0f, 0x85, 0xe6, 0xff, 0xff, 0xff, //0x00000904 jne          LBB0_96
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x0000090a .p2align 4, 0x90
	//0x00000910 LBB0_100
	0x49, 0x8b, 0x0b, //0x00000910 movq         (%r11), %rcx
	0x4c, 0x89, 0xd6, //0x00000913 movq         %r10, %rsi
	0x48, 0x29, 0xce, //0x00000916 subq         %rcx, %rsi
	0x0f, 0x83, 0x61, 0x00, 0x00, 0x00, //0x00000919 jae          LBB0_154
	//0x0000091f LBB0_101
	0x42, 0x8a, 0x04, 0x17, //0x0000091f movb         (%rdi,%r10), %al
	0x3c, 0x0d, //0x00000923 cmpb         $13, %al
	0x0f, 0x84, 0x55, 0x00, 0x00, 0x00, //0x00000925 je           LBB0_154
	0x3c, 0x20, //0x0000092b cmpb         $32, %al
	0x0f, 0x84, 0x4d, 0x00, 0x00, 0x00, //0x0000092d je           LBB0_154
	0x04, 0xf7, //0x00000933 addb         $-9, %al
	0x3c, 0x01, //0x00000935 cmpb         $1, %al
	0x0f, 0x86, 0x43, 0x00, 0x00, 0x00, //0x00000937 jbe          LBB0_154
	0x4c, 0x89, 0xd0, //0x0000093d movq         %r10, %rax
	0xe9, 0x62, 0x01, 0x00, 0x00, //0x00000940 jmp          LBB0_176
	//0x00000945 LBB0_98
	0x31, 0xd2, //0x00000945 xorl         %edx, %edx
	0x48, 0x8b, 0x7c, 0x24, 0x20, //0x00000947 movq         $32(%rsp), %rdi
	0x49, 0x8b, 0x0b, //0x0000094c movq         (%r11), %rcx
	0x4c, 0x89, 0xd6, //0x0000094f movq         %r10, %rsi
	0x48, 0x29, 0xce, //0x00000952 subq         %rcx, %rsi
	0x0f, 0x82, 0xc4, 0xff, 0xff, 0xff, //0x00000955 jb           LBB0_101
	0xe9, 0x20, 0x00, 0x00, 0x00, //0x0000095b jmp          LBB0_154
	//0x00000960 LBB0_99
	0x31, 0xd2, //0x00000960 xorl         %edx, %edx
	0x49, 0x8b, 0x0b, //0x00000962 movq         (%r11), %rcx
	0x4c, 0x89, 0xd6, //0x00000965 movq         %r10, %rsi
	0x48, 0x29, 0xce, //0x00000968 subq         %rcx, %rsi
	0x0f, 0x82, 0xae, 0xff, 0xff, 0xff, //0x0000096b jb           LBB0_101
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000971 .p2align 4, 0x90
	//0x00000980 LBB0_154
	0x49, 0x8d, 0x42, 0x01, //0x00000980 leaq         $1(%r10), %rax
	0x48, 0x39, 0xc8, //0x00000984 cmpq         %rcx, %rax
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00000987 jae          LBB0_158
	0x8a, 0x1c, 0x07, //0x0000098d movb         (%rdi,%rax), %bl
	0x80, 0xfb, 0x0d, //0x00000990 cmpb         $13, %bl
	0x0f, 0x84, 0x17, 0x00, 0x00, 0x00, //0x00000993 je           LBB0_158
	0x80, 0xfb, 0x20, //0x00000999 cmpb         $32, %bl
	0x0f, 0x84, 0x0e, 0x00, 0x00, 0x00, //0x0000099c je           LBB0_158
	0x80, 0xc3, 0xf7, //0x000009a2 addb         $-9, %bl
	0x80, 0xfb, 0x01, //0x000009a5 cmpb         $1, %bl
	0x0f, 0x87, 0xf9, 0x00, 0x00, 0x00, //0x000009a8 ja           LBB0_176
	0x90, 0x90, //0x000009ae .p2align 4, 0x90
	//0x000009b0 LBB0_158
	0x49, 0x8d, 0x42, 0x02, //0x000009b0 leaq         $2(%r10), %rax
	0x48, 0x39, 0xc8, //0x000009b4 cmpq         %rcx, %rax
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x000009b7 jae          LBB0_162
	0x8a, 0x1c, 0x07, //0x000009bd movb         (%rdi,%rax), %bl
	0x80, 0xfb, 0x0d, //0x000009c0 cmpb         $13, %bl
	0x0f, 0x84, 0x17, 0x00, 0x00, 0x00, //0x000009c3 je           LBB0_162
	0x80, 0xfb, 0x20, //0x000009c9 cmpb         $32, %bl
	0x0f, 0x84, 0x0e, 0x00, 0x00, 0x00, //0x000009cc je           LBB0_162
	0x80, 0xc3, 0xf7, //0x000009d2 addb         $-9, %bl
	0x80, 0xfb, 0x01, //0x000009d5 cmpb         $1, %bl
	0x0f, 0x87, 0xc9, 0x00, 0x00, 0x00, //0x000009d8 ja           LBB0_176
	0x90, 0x90, //0x000009de .p2align 4, 0x90
	//0x000009e0 LBB0_162
	0x49, 0x8d, 0x42, 0x03, //0x000009e0 leaq         $3(%r10), %rax
	0x48, 0x39, 0xc8, //0x000009e4 cmpq         %rcx, %rax
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x000009e7 jae          LBB0_166
	0x8a, 0x1c, 0x07, //0x000009ed movb         (%rdi,%rax), %bl
	0x80, 0xfb, 0x0d, //0x000009f0 cmpb         $13, %bl
	0x0f, 0x84, 0x17, 0x00, 0x00, 0x00, //0x000009f3 je           LBB0_166
	0x80, 0xfb, 0x20, //0x000009f9 cmpb         $32, %bl
	0x0f, 0x84, 0x0e, 0x00, 0x00, 0x00, //0x000009fc je           LBB0_166
	0x80, 0xc3, 0xf7, //0x00000a02 addb         $-9, %bl
	0x80, 0xfb, 0x01, //0x00000a05 cmpb         $1, %bl
	0x0f, 0x87, 0x99, 0x00, 0x00, 0x00, //0x00000a08 ja           LBB0_176
	0x90, 0x90, //0x00000a0e .p2align 4, 0x90
	//0x00000a10 LBB0_166
	0x49, 0x8d, 0x7a, 0x04, //0x00000a10 leaq         $4(%r10), %rdi
	0x48, 0x39, 0xf9, //0x00000a14 cmpq         %rdi, %rcx
	0x0f, 0x86, 0x5e, 0x39, 0x00, 0x00, //0x00000a17 jbe          LBB0_836
	0x48, 0x39, 0xf9, //0x00000a1d cmpq         %rdi, %rcx
	0x0f, 0x84, 0x4a, 0x00, 0x00, 0x00, //0x00000a20 je           LBB0_173
	0x48, 0x8b, 0x44, 0x24, 0x20, //0x00000a26 movq         $32(%rsp), %rax
	0x48, 0x8d, 0x3c, 0x08, //0x00000a2b leaq         (%rax,%rcx), %rdi
	0x48, 0x83, 0xc6, 0x04, //0x00000a2f addq         $4, %rsi
	0x4a, 0x8d, 0x44, 0x10, 0x05, //0x00000a33 leaq         $5(%rax,%r10), %rax
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000a38 .p2align 4, 0x90
	//0x00000a40 LBB0_169
	0x0f, 0xbe, 0x58, 0xff, //0x00000a40 movsbl       $-1(%rax), %ebx
	0x83, 0xfb, 0x20, //0x00000a44 cmpl         $32, %ebx
	0x0f, 0x87, 0x43, 0x00, 0x00, 0x00, //0x00000a47 ja           LBB0_175
	0x49, 0x0f, 0xa3, 0xd9, //0x00000a4d btq          %rbx, %r9
	0x0f, 0x83, 0x39, 0x00, 0x00, 0x00, //0x00000a51 jae          LBB0_175
	0x48, 0xff, 0xc0, //0x00000a57 incq         %rax
	0x48, 0xff, 0xc6, //0x00000a5a incq         %rsi
	0x0f, 0x85, 0xdd, 0xff, 0xff, 0xff, //0x00000a5d jne          LBB0_169
	0x48, 0x8b, 0x74, 0x24, 0x20, //0x00000a63 movq         $32(%rsp), %rsi
	0xe9, 0x0b, 0x00, 0x00, 0x00, //0x00000a68 jmp          LBB0_174
	0x90, 0x90, 0x90, //0x00000a6d .p2align 4, 0x90
	//0x00000a70 LBB0_173
	0x48, 0x8b, 0x74, 0x24, 0x20, //0x00000a70 movq         $32(%rsp), %rsi
	0x48, 0x01, 0xf7, //0x00000a75 addq         %rsi, %rdi
	//0x00000a78 LBB0_174
	0x48, 0x29, 0xf7, //0x00000a78 subq         %rsi, %rdi
	0x48, 0x89, 0xf8, //0x00000a7b movq         %rdi, %rax
	0x48, 0x89, 0xf7, //0x00000a7e movq         %rsi, %rdi
	0x48, 0x39, 0xc8, //0x00000a81 cmpq         %rcx, %rax
	0x0f, 0x82, 0x1d, 0x00, 0x00, 0x00, //0x00000a84 jb           LBB0_176
	0xe9, 0xf2, 0x38, 0x00, 0x00, //0x00000a8a jmp          LBB0_837
	0x90, //0x00000a8f .p2align 4, 0x90
	//0x00000a90 LBB0_175
	0x48, 0x8b, 0x7c, 0x24, 0x20, //0x00000a90 movq         $32(%rsp), %rdi
	0x48, 0x89, 0xfe, //0x00000a95 movq         %rdi, %rsi
	0x48, 0xf7, 0xd6, //0x00000a98 notq         %rsi
	0x48, 0x01, 0xf0, //0x00000a9b addq         %rsi, %rax
	0x48, 0x39, 0xc8, //0x00000a9e cmpq         %rcx, %rax
	0x0f, 0x83, 0xda, 0x38, 0x00, 0x00, //0x00000aa1 jae          LBB0_837
	//0x00000aa7 LBB0_176
	0x4c, 0x8d, 0x50, 0x01, //0x00000aa7 leaq         $1(%rax), %r10
	0x4d, 0x89, 0x10, //0x00000aab movq         %r10, (%r8)
	0x80, 0x3c, 0x07, 0x3a, //0x00000aae cmpb         $58, (%rdi,%rax)
	0x0f, 0x85, 0xc9, 0x38, 0x00, 0x00, //0x00000ab2 jne          LBB0_837
	0x48, 0x85, 0xd2, //0x00000ab8 testq        %rdx, %rdx
	0x0f, 0x85, 0xef, 0x20, 0x00, 0x00, //0x00000abb jne          LBB0_446
	0x49, 0x8b, 0x13, //0x00000ac1 movq         (%r11), %rdx
	0x49, 0x39, 0xd2, //0x00000ac4 cmpq         %rdx, %r10
	0x0f, 0x83, 0x33, 0x00, 0x00, 0x00, //0x00000ac7 jae          LBB0_183
	0x42, 0x8a, 0x0c, 0x17, //0x00000acd movb         (%rdi,%r10), %cl
	0x80, 0xf9, 0x0d, //0x00000ad1 cmpb         $13, %cl
	0x0f, 0x84, 0x26, 0x00, 0x00, 0x00, //0x00000ad4 je           LBB0_183
	0x80, 0xf9, 0x20, //0x00000ada cmpb         $32, %cl
	0x0f, 0x84, 0x1d, 0x00, 0x00, 0x00, //0x00000add je           LBB0_183
	0x80, 0xc1, 0xf7, //0x00000ae3 addb         $-9, %cl
	0x80, 0xf9, 0x01, //0x00000ae6 cmpb         $1, %cl
	0x0f, 0x86, 0x11, 0x00, 0x00, 0x00, //0x00000ae9 jbe          LBB0_183
	0x4c, 0x89, 0xd1, //0x00000aef movq         %r10, %rcx
	0xe9, 0x49, 0x01, 0x00, 0x00, //0x00000af2 jmp          LBB0_205
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000af7 .p2align 4, 0x90
	//0x00000b00 LBB0_183
	0x48, 0x8d, 0x48, 0x02, //0x00000b00 leaq         $2(%rax), %rcx
	0x48, 0x39, 0xd1, //0x00000b04 cmpq         %rdx, %rcx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00000b07 jae          LBB0_187
	0x8a, 0x1c, 0x0f, //0x00000b0d movb         (%rdi,%rcx), %bl
	0x80, 0xfb, 0x0d, //0x00000b10 cmpb         $13, %bl
	0x0f, 0x84, 0x17, 0x00, 0x00, 0x00, //0x00000b13 je           LBB0_187
	0x80, 0xfb, 0x20, //0x00000b19 cmpb         $32, %bl
	0x0f, 0x84, 0x0e, 0x00, 0x00, 0x00, //0x00000b1c je           LBB0_187
	0x80, 0xc3, 0xf7, //0x00000b22 addb         $-9, %bl
	0x80, 0xfb, 0x01, //0x00000b25 cmpb         $1, %bl
	0x0f, 0x87, 0x12, 0x01, 0x00, 0x00, //0x00000b28 ja           LBB0_205
	0x90, 0x90, //0x00000b2e .p2align 4, 0x90
	//0x00000b30 LBB0_187
	0x48, 0x8d, 0x48, 0x03, //0x00000b30 leaq         $3(%rax), %rcx
	0x48, 0x39, 0xd1, //0x00000b34 cmpq         %rdx, %rcx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00000b37 jae          LBB0_191
	0x8a, 0x1c, 0x0f, //0x00000b3d movb         (%rdi,%rcx), %bl
	0x80, 0xfb, 0x0d, //0x00000b40 cmpb         $13, %bl
	0x0f, 0x84, 0x17, 0x00, 0x00, 0x00, //0x00000b43 je           LBB0_191
	0x80, 0xfb, 0x20, //0x00000b49 cmpb         $32, %bl
	0x0f, 0x84, 0x0e, 0x00, 0x00, 0x00, //0x00000b4c je           LBB0_191
	0x80, 0xc3, 0xf7, //0x00000b52 addb         $-9, %bl
	0x80, 0xfb, 0x01, //0x00000b55 cmpb         $1, %bl
	0x0f, 0x87, 0xe2, 0x00, 0x00, 0x00, //0x00000b58 ja           LBB0_205
	0x90, 0x90, //0x00000b5e .p2align 4, 0x90
	//0x00000b60 LBB0_191
	0x48, 0x8d, 0x48, 0x04, //0x00000b60 leaq         $4(%rax), %rcx
	0x48, 0x39, 0xd1, //0x00000b64 cmpq         %rdx, %rcx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00000b67 jae          LBB0_195
	0x8a, 0x1c, 0x0f, //0x00000b6d movb         (%rdi,%rcx), %bl
	0x80, 0xfb, 0x0d, //0x00000b70 cmpb         $13, %bl
	0x0f, 0x84, 0x17, 0x00, 0x00, 0x00, //0x00000b73 je           LBB0_195
	0x80, 0xfb, 0x20, //0x00000b79 cmpb         $32, %bl
	0x0f, 0x84, 0x0e, 0x00, 0x00, 0x00, //0x00000b7c je           LBB0_195
	0x80, 0xc3, 0xf7, //0x00000b82 addb         $-9, %bl
	0x80, 0xfb, 0x01, //0x00000b85 cmpb         $1, %bl
	0x0f, 0x87, 0xb2, 0x00, 0x00, 0x00, //0x00000b88 ja           LBB0_205
	0x90, 0x90, //0x00000b8e .p2align 4, 0x90
	//0x00000b90 LBB0_195
	0x48, 0x8d, 0x70, 0x05, //0x00000b90 leaq         $5(%rax), %rsi
	0x48, 0x39, 0xf2, //0x00000b94 cmpq         %rsi, %rdx
	0x0f, 0x86, 0x63, 0x00, 0x00, 0x00, //0x00000b97 jbe          LBB0_202
	0x48, 0x39, 0xf2, //0x00000b9d cmpq         %rsi, %rdx
	0x0f, 0x84, 0x65, 0x00, 0x00, 0x00, //0x00000ba0 je           LBB0_203
	0x48, 0x8d, 0x34, 0x17, //0x00000ba6 leaq         (%rdi,%rdx), %rsi
	0x48, 0x8d, 0x4c, 0x07, 0x06, //0x00000baa leaq         $6(%rdi,%rax), %rcx
	0x48, 0x29, 0xd0, //0x00000baf subq         %rdx, %rax
	0x48, 0x83, 0xc0, 0x05, //0x00000bb2 addq         $5, %rax
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000bb6 .p2align 4, 0x90
	//0x00000bc0 LBB0_198
	0x0f, 0xbe, 0x79, 0xff, //0x00000bc0 movsbl       $-1(%rcx), %edi
	0x83, 0xff, 0x20, //0x00000bc4 cmpl         $32, %edi
	0x0f, 0x87, 0x55, 0x00, 0x00, 0x00, //0x00000bc7 ja           LBB0_204
	0x49, 0x0f, 0xa3, 0xf9, //0x00000bcd btq          %rdi, %r9
	0x0f, 0x83, 0x4b, 0x00, 0x00, 0x00, //0x00000bd1 jae          LBB0_204
	0x48, 0xff, 0xc1, //0x00000bd7 incq         %rcx
	0x48, 0xff, 0xc0, //0x00000bda incq         %rax
	0x0f, 0x85, 0xdd, 0xff, 0xff, 0xff, //0x00000bdd jne          LBB0_198
	0x48, 0x8b, 0x7c, 0x24, 0x20, //0x00000be3 movq         $32(%rsp), %rdi
	0x48, 0x29, 0xfe, //0x00000be8 subq         %rdi, %rsi
	0x48, 0x89, 0xf1, //0x00000beb movq         %rsi, %rcx
	0x48, 0x39, 0xd1, //0x00000bee cmpq         %rdx, %rcx
	0x0f, 0x82, 0x49, 0x00, 0x00, 0x00, //0x00000bf1 jb           LBB0_205
	0xe9, 0x34, 0x01, 0x00, 0x00, //0x00000bf7 jmp          LBB0_222
	0x90, 0x90, 0x90, 0x90, //0x00000bfc .p2align 4, 0x90
	//0x00000c00 LBB0_202
	0x49, 0x89, 0x30, //0x00000c00 movq         %rsi, (%r8)
	0x49, 0x89, 0xf2, //0x00000c03 movq         %rsi, %r10
	0xe9, 0x25, 0x01, 0x00, 0x00, //0x00000c06 jmp          LBB0_222
	//0x00000c0b LBB0_203
	0x48, 0x01, 0xfe, //0x00000c0b addq         %rdi, %rsi
	0x48, 0x29, 0xfe, //0x00000c0e subq         %rdi, %rsi
	0x48, 0x89, 0xf1, //0x00000c11 movq         %rsi, %rcx
	0x48, 0x39, 0xd1, //0x00000c14 cmpq         %rdx, %rcx
	0x0f, 0x82, 0x23, 0x00, 0x00, 0x00, //0x00000c17 jb           LBB0_205
	0xe9, 0x0e, 0x01, 0x00, 0x00, //0x00000c1d jmp          LBB0_222
	//0x00000c22 LBB0_204
	0x48, 0x8b, 0x7c, 0x24, 0x20, //0x00000c22 movq         $32(%rsp), %rdi
	0x48, 0x89, 0xf8, //0x00000c27 movq         %rdi, %rax
	0x48, 0xf7, 0xd0, //0x00000c2a notq         %rax
	0x48, 0x01, 0xc1, //0x00000c2d addq         %rax, %rcx
	0x48, 0x39, 0xd1, //0x00000c30 cmpq         %rdx, %rcx
	0x0f, 0x83, 0xf7, 0x00, 0x00, 0x00, //0x00000c33 jae          LBB0_222
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000c39 .p2align 4, 0x90
	//0x00000c40 LBB0_205
	0x4c, 0x8d, 0x51, 0x01, //0x00000c40 leaq         $1(%rcx), %r10
	0x4d, 0x89, 0x10, //0x00000c44 movq         %r10, (%r8)
	0x0f, 0xbe, 0x04, 0x0f, //0x00000c47 movsbl       (%rdi,%rcx), %eax
	0x83, 0xf8, 0x7b, //0x00000c4b cmpl         $123, %eax
	0x0f, 0x87, 0x75, 0x06, 0x00, 0x00, //0x00000c4e ja           LBB0_289
	0x48, 0x8d, 0x15, 0x4d, 0x45, 0x00, 0x00, //0x00000c54 leaq         $17741(%rip), %rdx  /* LJTI0_1+0(%rip) */
	0x48, 0x63, 0x04, 0x82, //0x00000c5b movslq       (%rdx,%rax,4), %rax
	0x48, 0x01, 0xd0, //0x00000c5f addq         %rdx, %rax
	0xff, 0xe0, //0x00000c62 jmpq         *%rax
	//0x00000c64 LBB0_207
	0x49, 0x8b, 0x13, //0x00000c64 movq         (%r11), %rdx
	0x48, 0x89, 0xd0, //0x00000c67 movq         %rdx, %rax
	0x4c, 0x29, 0xd0, //0x00000c6a subq         %r10, %rax
	0x49, 0x01, 0xfa, //0x00000c6d addq         %rdi, %r10
	0x48, 0x83, 0xf8, 0x10, //0x00000c70 cmpq         $16, %rax
	0x0f, 0x82, 0x60, 0x00, 0x00, 0x00, //0x00000c74 jb           LBB0_212
	0x48, 0x29, 0xca, //0x00000c7a subq         %rcx, %rdx
	0x48, 0x83, 0xc2, 0xef, //0x00000c7d addq         $-17, %rdx
	0x48, 0x89, 0xd6, //0x00000c81 movq         %rdx, %rsi
	0x48, 0x83, 0xe6, 0xf0, //0x00000c84 andq         $-16, %rsi
	0x48, 0x01, 0xce, //0x00000c88 addq         %rcx, %rsi
	0x48, 0x8d, 0x4c, 0x37, 0x11, //0x00000c8b leaq         $17(%rdi,%rsi), %rcx
	0x83, 0xe2, 0x0f, //0x00000c90 andl         $15, %edx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000c93 .p2align 4, 0x90
	//0x00000ca0 LBB0_209
	0xc4, 0xc1, 0x7a, 0x6f, 0x12, //0x00000ca0 vmovdqu      (%r10), %xmm2
	0xc5, 0x89, 0x74, 0xda, //0x00000ca5 vpcmpeqb     %xmm2, %xmm14, %xmm3
	0xc5, 0x81, 0xeb, 0xd2, //0x00000ca9 vpor         %xmm2, %xmm15, %xmm2
	0xc5, 0xe9, 0x74, 0xd4, //0x00000cad vpcmpeqb     %xmm4, %xmm2, %xmm2
	0xc5, 0xe9, 0xeb, 0xd3, //0x00000cb1 vpor         %xmm3, %xmm2, %xmm2
	0xc5, 0xf9, 0xd7, 0xf2, //0x00000cb5 vpmovmskb    %xmm2, %esi
	0x66, 0x85, 0xf6, //0x00000cb9 testw        %si, %si
	0x0f, 0x85, 0x5e, 0x00, 0x00, 0x00, //0x00000cbc jne          LBB0_220
	0x49, 0x83, 0xc2, 0x10, //0x00000cc2 addq         $16, %r10
	0x48, 0x83, 0xc0, 0xf0, //0x00000cc6 addq         $-16, %rax
	0x48, 0x83, 0xf8, 0x0f, //0x00000cca cmpq         $15, %rax
	0x0f, 0x87, 0xcc, 0xff, 0xff, 0xff, //0x00000cce ja           LBB0_209
	0x48, 0x89, 0xd0, //0x00000cd4 movq         %rdx, %rax
	0x49, 0x89, 0xca, //0x00000cd7 movq         %rcx, %r10
	//0x00000cda LBB0_212
	0x48, 0x85, 0xc0, //0x00000cda testq        %rax, %rax
	0x0f, 0x84, 0x32, 0x00, 0x00, 0x00, //0x00000cdd je           LBB0_219
	0x49, 0x8d, 0x0c, 0x02, //0x00000ce3 leaq         (%r10,%rax), %rcx
	//0x00000ce7 LBB0_214
	0x41, 0x0f, 0xb6, 0x12, //0x00000ce7 movzbl       (%r10), %edx
	0x80, 0xfa, 0x2c, //0x00000ceb cmpb         $44, %dl
	0x0f, 0x84, 0x21, 0x00, 0x00, 0x00, //0x00000cee je           LBB0_219
	0x80, 0xfa, 0x7d, //0x00000cf4 cmpb         $125, %dl
	0x0f, 0x84, 0x18, 0x00, 0x00, 0x00, //0x00000cf7 je           LBB0_219
	0x80, 0xfa, 0x5d, //0x00000cfd cmpb         $93, %dl
	0x0f, 0x84, 0x0f, 0x00, 0x00, 0x00, //0x00000d00 je           LBB0_219
	0x49, 0xff, 0xc2, //0x00000d06 incq         %r10
	0x48, 0xff, 0xc8, //0x00000d09 decq         %rax
	0x0f, 0x85, 0xd5, 0xff, 0xff, 0xff, //0x00000d0c jne          LBB0_214
	0x49, 0x89, 0xca, //0x00000d12 movq         %rcx, %r10
	//0x00000d15 LBB0_219
	0x49, 0x29, 0xfa, //0x00000d15 subq         %rdi, %r10
	0xe9, 0x10, 0x00, 0x00, 0x00, //0x00000d18 jmp          LBB0_221
	0x90, 0x90, 0x90, //0x00000d1d .p2align 4, 0x90
	//0x00000d20 LBB0_220
	0x0f, 0xb7, 0xc6, //0x00000d20 movzwl       %si, %eax
	0x48, 0x0f, 0xbc, 0xc0, //0x00000d23 bsfq         %rax, %rax
	0x49, 0x29, 0xfa, //0x00000d27 subq         %rdi, %r10
	0x49, 0x01, 0xc2, //0x00000d2a addq         %rax, %r10
	//0x00000d2d LBB0_221
	0x4d, 0x89, 0x10, //0x00000d2d movq         %r10, (%r8)
	//0x00000d30 LBB0_222
	0x49, 0x8b, 0x3f, //0x00000d30 movq         (%r15), %rdi
	0x49, 0x8b, 0x47, 0x08, //0x00000d33 movq         $8(%r15), %rax
	0x4c, 0x89, 0xd1, //0x00000d37 movq         %r10, %rcx
	0x48, 0x29, 0xc1, //0x00000d3a subq         %rax, %rcx
	0x0f, 0x83, 0x2d, 0x00, 0x00, 0x00, //0x00000d3d jae          LBB0_227
	0x42, 0x8a, 0x14, 0x17, //0x00000d43 movb         (%rdi,%r10), %dl
	0x80, 0xfa, 0x0d, //0x00000d47 cmpb         $13, %dl
	0x0f, 0x84, 0x20, 0x00, 0x00, 0x00, //0x00000d4a je           LBB0_227
	0x80, 0xfa, 0x20, //0x00000d50 cmpb         $32, %dl
	0x0f, 0x84, 0x17, 0x00, 0x00, 0x00, //0x00000d53 je           LBB0_227
	0x80, 0xc2, 0xf7, //0x00000d59 addb         $-9, %dl
	0x80, 0xfa, 0x01, //0x00000d5c cmpb         $1, %dl
	0x0f, 0x86, 0x0b, 0x00, 0x00, 0x00, //0x00000d5f jbe          LBB0_227
	0x4c, 0x89, 0xd2, //0x00000d65 movq         %r10, %rdx
	0xe9, 0x85, 0xf6, 0xff, 0xff, //0x00000d68 jmp          LBB0_248
	0x90, 0x90, 0x90, //0x00000d6d .p2align 4, 0x90
	//0x00000d70 LBB0_227
	0x49, 0x8d, 0x52, 0x01, //0x00000d70 leaq         $1(%r10), %rdx
	0x48, 0x39, 0xc2, //0x00000d74 cmpq         %rax, %rdx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00000d77 jae          LBB0_231
	0x8a, 0x1c, 0x17, //0x00000d7d movb         (%rdi,%rdx), %bl
	0x80, 0xfb, 0x0d, //0x00000d80 cmpb         $13, %bl
	0x0f, 0x84, 0x17, 0x00, 0x00, 0x00, //0x00000d83 je           LBB0_231
	0x80, 0xfb, 0x20, //0x00000d89 cmpb         $32, %bl
	0x0f, 0x84, 0x0e, 0x00, 0x00, 0x00, //0x00000d8c je           LBB0_231
	0x80, 0xc3, 0xf7, //0x00000d92 addb         $-9, %bl
	0x80, 0xfb, 0x01, //0x00000d95 cmpb         $1, %bl
	0x0f, 0x87, 0x54, 0xf6, 0xff, 0xff, //0x00000d98 ja           LBB0_248
	0x90, 0x90, //0x00000d9e .p2align 4, 0x90
	//0x00000da0 LBB0_231
	0x49, 0x8d, 0x52, 0x02, //0x00000da0 leaq         $2(%r10), %rdx
	0x48, 0x39, 0xc2, //0x00000da4 cmpq         %rax, %rdx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00000da7 jae          LBB0_235
	0x8a, 0x1c, 0x17, //0x00000dad movb         (%rdi,%rdx), %bl
	0x80, 0xfb, 0x0d, //0x00000db0 cmpb         $13, %bl
	0x0f, 0x84, 0x17, 0x00, 0x00, 0x00, //0x00000db3 je           LBB0_235
	0x80, 0xfb, 0x20, //0x00000db9 cmpb         $32, %bl
	0x0f, 0x84, 0x0e, 0x00, 0x00, 0x00, //0x00000dbc je           LBB0_235
	0x80, 0xc3, 0xf7, //0x00000dc2 addb         $-9, %bl
	0x80, 0xfb, 0x01, //0x00000dc5 cmpb         $1, %bl
	0x0f, 0x87, 0x24, 0xf6, 0xff, 0xff, //0x00000dc8 ja           LBB0_248
	0x90, 0x90, //0x00000dce .p2align 4, 0x90
	//0x00000dd0 LBB0_235
	0x49, 0x8d, 0x52, 0x03, //0x00000dd0 leaq         $3(%r10), %rdx
	0x48, 0x39, 0xc2, //0x00000dd4 cmpq         %rax, %rdx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00000dd7 jae          LBB0_239
	0x8a, 0x1c, 0x17, //0x00000ddd movb         (%rdi,%rdx), %bl
	0x80, 0xfb, 0x0d, //0x00000de0 cmpb         $13, %bl
	0x0f, 0x84, 0x17, 0x00, 0x00, 0x00, //0x00000de3 je           LBB0_239
	0x80, 0xfb, 0x20, //0x00000de9 cmpb         $32, %bl
	0x0f, 0x84, 0x0e, 0x00, 0x00, 0x00, //0x00000dec je           LBB0_239
	0x80, 0xc3, 0xf7, //0x00000df2 addb         $-9, %bl
	0x80, 0xfb, 0x01, //0x00000df5 cmpb         $1, %bl
	0x0f, 0x87, 0xf4, 0xf5, 0xff, 0xff, //0x00000df8 ja           LBB0_248
	0x90, 0x90, //0x00000dfe .p2align 4, 0x90
	//0x00000e00 LBB0_239
	0x49, 0x8d, 0x72, 0x04, //0x00000e00 leaq         $4(%r10), %rsi
	0x48, 0x39, 0xf0, //0x00000e04 cmpq         %rsi, %rax
	0x0f, 0x86, 0x7d, 0x1f, 0x00, 0x00, //0x00000e07 jbe          LBB0_550
	0x48, 0x39, 0xf0, //0x00000e0d cmpq         %rsi, %rax
	0x0f, 0x84, 0xca, 0xf5, 0xff, 0xff, //0x00000e10 je           LBB0_246
	0x48, 0x8d, 0x34, 0x07, //0x00000e16 leaq         (%rdi,%rax), %rsi
	0x48, 0x83, 0xc1, 0x04, //0x00000e1a addq         $4, %rcx
	0x48, 0x89, 0xfb, //0x00000e1e movq         %rdi, %rbx
	0x4a, 0x8d, 0x54, 0x17, 0x05, //0x00000e21 leaq         $5(%rdi,%r10), %rdx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000e26 .p2align 4, 0x90
	//0x00000e30 LBB0_242
	0x0f, 0xbe, 0x7a, 0xff, //0x00000e30 movsbl       $-1(%rdx), %edi
	0x83, 0xff, 0x20, //0x00000e34 cmpl         $32, %edi
	0x0f, 0x87, 0x2d, 0x00, 0x00, 0x00, //0x00000e37 ja           LBB0_247
	0x49, 0x0f, 0xa3, 0xf9, //0x00000e3d btq          %rdi, %r9
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00000e41 jae          LBB0_247
	0x48, 0xff, 0xc2, //0x00000e47 incq         %rdx
	0x48, 0xff, 0xc1, //0x00000e4a incq         %rcx
	0x0f, 0x85, 0xdd, 0xff, 0xff, 0xff, //0x00000e4d jne          LBB0_242
	0x48, 0x89, 0xdf, //0x00000e53 movq         %rbx, %rdi
	0x48, 0x29, 0xfe, //0x00000e56 subq         %rdi, %rsi
	0x48, 0x89, 0xf2, //0x00000e59 movq         %rsi, %rdx
	0x48, 0x39, 0xc2, //0x00000e5c cmpq         %rax, %rdx
	0x0f, 0x82, 0x8d, 0xf5, 0xff, 0xff, //0x00000e5f jb           LBB0_248
	0xe9, 0x17, 0x35, 0x00, 0x00, //0x00000e65 jmp          LBB0_837
	//0x00000e6a LBB0_247
	0x48, 0x89, 0xdf, //0x00000e6a movq         %rbx, %rdi
	0x48, 0x89, 0xd9, //0x00000e6d movq         %rbx, %rcx
	0x48, 0xf7, 0xd1, //0x00000e70 notq         %rcx
	0x48, 0x01, 0xca, //0x00000e73 addq         %rcx, %rdx
	0x48, 0x39, 0xc2, //0x00000e76 cmpq         %rax, %rdx
	0x0f, 0x82, 0x73, 0xf5, 0xff, 0xff, //0x00000e79 jb           LBB0_248
	0xe9, 0xfd, 0x34, 0x00, 0x00, //0x00000e7f jmp          LBB0_837
	//0x00000e84 LBB0_250
	0x48, 0x83, 0xc1, 0x04, //0x00000e84 addq         $4, %rcx
	0x49, 0x3b, 0x0b, //0x00000e88 cmpq         (%r11), %rcx
	0x0f, 0x87, 0x9f, 0xfe, 0xff, 0xff, //0x00000e8b ja           LBB0_222
	0xe9, 0x33, 0x04, 0x00, 0x00, //0x00000e91 jmp          LBB0_289
	//0x00000e96 LBB0_251
	0x4d, 0x8b, 0x03, //0x00000e96 movq         (%r11), %r8
	0x4c, 0x89, 0xc0, //0x00000e99 movq         %r8, %rax
	0x4c, 0x29, 0xd0, //0x00000e9c subq         %r10, %rax
	0x48, 0x83, 0xf8, 0x20, //0x00000e9f cmpq         $32, %rax
	0x0f, 0x8c, 0xbd, 0x0c, 0x00, 0x00, //0x00000ea3 jl           LBB0_323
	0x4c, 0x8d, 0x0c, 0x0f, //0x00000ea9 leaq         (%rdi,%rcx), %r9
	0x49, 0x29, 0xc8, //0x00000ead subq         %rcx, %r8
	0xb9, 0x1f, 0x00, 0x00, 0x00, //0x00000eb0 movl         $31, %ecx
	0x31, 0xc0, //0x00000eb5 xorl         %eax, %eax
	0x45, 0x31, 0xdb, //0x00000eb7 xorl         %r11d, %r11d
	0xe9, 0x23, 0x00, 0x00, 0x00, //0x00000eba jmp          LBB0_253
	0x90, //0x00000ebf .p2align 4, 0x90
	//0x00000ec0 LBB0_256
	0x45, 0x31, 0xdb, //0x00000ec0 xorl         %r11d, %r11d
	0x85, 0xdb, //0x00000ec3 testl        %ebx, %ebx
	0x0f, 0x85, 0xa9, 0x00, 0x00, 0x00, //0x00000ec5 jne          LBB0_255
	//0x00000ecb LBB0_257
	0x48, 0x83, 0xc0, 0x20, //0x00000ecb addq         $32, %rax
	0x49, 0x8d, 0x54, 0x08, 0xe0, //0x00000ecf leaq         $-32(%r8,%rcx), %rdx
	0x48, 0x83, 0xc1, 0xe0, //0x00000ed4 addq         $-32, %rcx
	0x48, 0x83, 0xfa, 0x3f, //0x00000ed8 cmpq         $63, %rdx
	0x0f, 0x8e, 0xbc, 0x0b, 0x00, 0x00, //0x00000edc jle          LBB0_258
	//0x00000ee2 LBB0_253
	0xc4, 0xc1, 0x7a, 0x6f, 0x54, 0x01, 0x01, //0x00000ee2 vmovdqu      $1(%r9,%rax), %xmm2
	0xc4, 0xc1, 0x7a, 0x6f, 0x5c, 0x01, 0x11, //0x00000ee9 vmovdqu      $17(%r9,%rax), %xmm3
	0xc5, 0xe9, 0x74, 0xe8, //0x00000ef0 vpcmpeqb     %xmm0, %xmm2, %xmm5
	0xc5, 0xf9, 0xd7, 0xd5, //0x00000ef4 vpmovmskb    %xmm5, %edx
	0xc5, 0xe1, 0x74, 0xe8, //0x00000ef8 vpcmpeqb     %xmm0, %xmm3, %xmm5
	0xc5, 0xf9, 0xd7, 0xdd, //0x00000efc vpmovmskb    %xmm5, %ebx
	0x48, 0xc1, 0xe3, 0x10, //0x00000f00 shlq         $16, %rbx
	0x48, 0x09, 0xd3, //0x00000f04 orq          %rdx, %rbx
	0xc5, 0xe9, 0x74, 0xd1, //0x00000f07 vpcmpeqb     %xmm1, %xmm2, %xmm2
	0xc5, 0xf9, 0xd7, 0xf2, //0x00000f0b vpmovmskb    %xmm2, %esi
	0xc5, 0xe1, 0x74, 0xd1, //0x00000f0f vpcmpeqb     %xmm1, %xmm3, %xmm2
	0xc5, 0xf9, 0xd7, 0xd2, //0x00000f13 vpmovmskb    %xmm2, %edx
	0x48, 0xc1, 0xe2, 0x10, //0x00000f17 shlq         $16, %rdx
	0x48, 0x09, 0xf2, //0x00000f1b orq          %rsi, %rdx
	0x48, 0x89, 0xd6, //0x00000f1e movq         %rdx, %rsi
	0x4c, 0x09, 0xde, //0x00000f21 orq          %r11, %rsi
	0x0f, 0x84, 0x96, 0xff, 0xff, 0xff, //0x00000f24 je           LBB0_256
	0x44, 0x89, 0xde, //0x00000f2a movl         %r11d, %esi
	0x41, 0xbe, 0xff, 0xff, 0xff, 0xff, //0x00000f2d movl         $4294967295, %r14d
	0x44, 0x31, 0xf6, //0x00000f33 xorl         %r14d, %esi
	0x21, 0xf2, //0x00000f36 andl         %esi, %edx
	0x8d, 0x34, 0x12, //0x00000f38 leal         (%rdx,%rdx), %esi
	0x44, 0x09, 0xde, //0x00000f3b orl          %r11d, %esi
	0x41, 0x8d, 0xbe, 0xab, 0xaa, 0xaa, 0xaa, //0x00000f3e leal         $-1431655765(%r14), %edi
	0x31, 0xf7, //0x00000f45 xorl         %esi, %edi
	0x21, 0xd7, //0x00000f47 andl         %edx, %edi
	0x81, 0xe7, 0xaa, 0xaa, 0xaa, 0xaa, //0x00000f49 andl         $-1431655766, %edi
	0x45, 0x31, 0xdb, //0x00000f4f xorl         %r11d, %r11d
	0x01, 0xd7, //0x00000f52 addl         %edx, %edi
	0x41, 0x0f, 0x92, 0xc3, //0x00000f54 setb         %r11b
	0x01, 0xff, //0x00000f58 addl         %edi, %edi
	0x81, 0xf7, 0x55, 0x55, 0x55, 0x55, //0x00000f5a xorl         $1431655765, %edi
	0x21, 0xf7, //0x00000f60 andl         %esi, %edi
	0x44, 0x31, 0xf7, //0x00000f62 xorl         %r14d, %edi
	0x21, 0xfb, //0x00000f65 andl         %edi, %ebx
	0x48, 0x8b, 0x7c, 0x24, 0x20, //0x00000f67 movq         $32(%rsp), %rdi
	0x85, 0xdb, //0x00000f6c testl        %ebx, %ebx
	0x0f, 0x84, 0x57, 0xff, 0xff, 0xff, //0x00000f6e je           LBB0_257
	//0x00000f74 LBB0_255
	0x48, 0x0f, 0xbc, 0xcb, //0x00000f74 bsfq         %rbx, %rcx
	0x49, 0x01, 0xc9, //0x00000f78 addq         %rcx, %r9
	0x49, 0x01, 0xc1, //0x00000f7b addq         %rax, %r9
	0x49, 0x29, 0xf9, //0x00000f7e subq         %rdi, %r9
	0x49, 0x83, 0xc1, 0x02, //0x00000f81 addq         $2, %r9
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x00000f85 movq         $24(%rsp), %r8
	0x4d, 0x89, 0x08, //0x00000f8a movq         %r9, (%r8)
	0x4d, 0x89, 0xca, //0x00000f8d movq         %r9, %r10
	//0x00000f90 LBB0_341
	0x4c, 0x8b, 0x5c, 0x24, 0x38, //0x00000f90 movq         $56(%rsp), %r11
	0x49, 0xb9, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00000f95 movabsq      $4294977024, %r9
	0xe9, 0x8c, 0xfd, 0xff, 0xff, //0x00000f9f jmp          LBB0_222
	//0x00000fa4 LBB0_262
	0x4d, 0x8b, 0x0b, //0x00000fa4 movq         (%r11), %r9
	0x4d, 0x29, 0xd1, //0x00000fa7 subq         %r10, %r9
	0x4c, 0x01, 0xd7, //0x00000faa addq         %r10, %rdi
	0x45, 0x31, 0xc0, //0x00000fad xorl         %r8d, %r8d
	0x45, 0x31, 0xd2, //0x00000fb0 xorl         %r10d, %r10d
	0x45, 0x31, 0xdb, //0x00000fb3 xorl         %r11d, %r11d
	0x31, 0xd2, //0x00000fb6 xorl         %edx, %edx
	0xe9, 0x1a, 0x00, 0x00, 0x00, //0x00000fb8 jmp          LBB0_264
	//0x00000fbd LBB0_263
	0x49, 0xc1, 0xfe, 0x3f, //0x00000fbd sarq         $63, %r14
	0xf3, 0x48, 0x0f, 0xb8, 0xc7, //0x00000fc1 popcntq      %rdi, %rax
	0x49, 0x01, 0xc3, //0x00000fc6 addq         %rax, %r11
	0x4c, 0x89, 0xe7, //0x00000fc9 movq         %r12, %rdi
	0x48, 0x83, 0xc7, 0x40, //0x00000fcc addq         $64, %rdi
	0x49, 0x83, 0xc1, 0xc0, //0x00000fd0 addq         $-64, %r9
	0x4d, 0x89, 0xf0, //0x00000fd4 movq         %r14, %r8
	//0x00000fd7 LBB0_264
	0x49, 0x83, 0xf9, 0x40, //0x00000fd7 cmpq         $64, %r9
	0x0f, 0x8c, 0xab, 0x01, 0x00, 0x00, //0x00000fdb jl           LBB0_272
	//0x00000fe1 LBB0_265
	0xc5, 0xfa, 0x6f, 0x17, //0x00000fe1 vmovdqu      (%rdi), %xmm2
	0xc5, 0xfa, 0x6f, 0x77, 0x10, //0x00000fe5 vmovdqu      $16(%rdi), %xmm6
	0xc5, 0xfa, 0x6f, 0x6f, 0x20, //0x00000fea vmovdqu      $32(%rdi), %xmm5
	0x49, 0x89, 0xfc, //0x00000fef movq         %rdi, %r12
	0xc5, 0xfa, 0x6f, 0x7f, 0x30, //0x00000ff2 vmovdqu      $48(%rdi), %xmm7
	0xc5, 0xe9, 0x74, 0xd8, //0x00000ff7 vpcmpeqb     %xmm0, %xmm2, %xmm3
	0xc5, 0xf9, 0xd7, 0xf3, //0x00000ffb vpmovmskb    %xmm3, %esi
	0xc5, 0xc9, 0x74, 0xd8, //0x00000fff vpcmpeqb     %xmm0, %xmm6, %xmm3
	0xc5, 0xf9, 0xd7, 0xc3, //0x00001003 vpmovmskb    %xmm3, %eax
	0xc5, 0xd1, 0x74, 0xd8, //0x00001007 vpcmpeqb     %xmm0, %xmm5, %xmm3
	0xc5, 0xf9, 0xd7, 0xfb, //0x0000100b vpmovmskb    %xmm3, %edi
	0xc5, 0xc1, 0x74, 0xd8, //0x0000100f vpcmpeqb     %xmm0, %xmm7, %xmm3
	0xc5, 0xf9, 0xd7, 0xdb, //0x00001013 vpmovmskb    %xmm3, %ebx
	0x48, 0xc1, 0xe3, 0x30, //0x00001017 shlq         $48, %rbx
	0x48, 0xc1, 0xe7, 0x20, //0x0000101b shlq         $32, %rdi
	0x48, 0xc1, 0xe0, 0x10, //0x0000101f shlq         $16, %rax
	0x48, 0x09, 0xc6, //0x00001023 orq          %rax, %rsi
	0x48, 0x09, 0xfe, //0x00001026 orq          %rdi, %rsi
	0x48, 0x09, 0xde, //0x00001029 orq          %rbx, %rsi
	0xc5, 0xe9, 0x74, 0xd9, //0x0000102c vpcmpeqb     %xmm1, %xmm2, %xmm3
	0xc5, 0xf9, 0xd7, 0xfb, //0x00001030 vpmovmskb    %xmm3, %edi
	0xc5, 0xc9, 0x74, 0xd9, //0x00001034 vpcmpeqb     %xmm1, %xmm6, %xmm3
	0xc5, 0xf9, 0xd7, 0xc3, //0x00001038 vpmovmskb    %xmm3, %eax
	0xc5, 0xd1, 0x74, 0xd9, //0x0000103c vpcmpeqb     %xmm1, %xmm5, %xmm3
	0xc5, 0xf9, 0xd7, 0xdb, //0x00001040 vpmovmskb    %xmm3, %ebx
	0xc5, 0xc1, 0x74, 0xd9, //0x00001044 vpcmpeqb     %xmm1, %xmm7, %xmm3
	0xc5, 0xf9, 0xd7, 0xcb, //0x00001048 vpmovmskb    %xmm3, %ecx
	0x48, 0xc1, 0xe1, 0x30, //0x0000104c shlq         $48, %rcx
	0x48, 0xc1, 0xe3, 0x20, //0x00001050 shlq         $32, %rbx
	0x48, 0xc1, 0xe0, 0x10, //0x00001054 shlq         $16, %rax
	0x48, 0x09, 0xc7, //0x00001058 orq          %rax, %rdi
	0x48, 0x09, 0xdf, //0x0000105b orq          %rbx, %rdi
	0x48, 0x09, 0xcf, //0x0000105e orq          %rcx, %rdi
	0x48, 0x89, 0xf8, //0x00001061 movq         %rdi, %rax
	0x4c, 0x09, 0xd0, //0x00001064 orq          %r10, %rax
	0x0f, 0x84, 0x4b, 0x00, 0x00, 0x00, //0x00001067 je           LBB0_267
	0x4c, 0x89, 0xd0, //0x0000106d movq         %r10, %rax
	0x48, 0xf7, 0xd0, //0x00001070 notq         %rax
	0x48, 0x21, 0xf8, //0x00001073 andq         %rdi, %rax
	0x4c, 0x8d, 0x34, 0x00, //0x00001076 leaq         (%rax,%rax), %r14
	0x4d, 0x09, 0xd6, //0x0000107a orq          %r10, %r14
	0x4c, 0x89, 0xf1, //0x0000107d movq         %r14, %rcx
	0x48, 0xbb, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00001080 movabsq      $-6148914691236517206, %rbx
	0x48, 0x31, 0xd9, //0x0000108a xorq         %rbx, %rcx
	0x48, 0x21, 0xdf, //0x0000108d andq         %rbx, %rdi
	0x48, 0x21, 0xcf, //0x00001090 andq         %rcx, %rdi
	0x45, 0x31, 0xd2, //0x00001093 xorl         %r10d, %r10d
	0x48, 0x01, 0xc7, //0x00001096 addq         %rax, %rdi
	0x41, 0x0f, 0x92, 0xc2, //0x00001099 setb         %r10b
	0x48, 0x01, 0xff, //0x0000109d addq         %rdi, %rdi
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x000010a0 movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc7, //0x000010aa xorq         %rax, %rdi
	0x4c, 0x21, 0xf7, //0x000010ad andq         %r14, %rdi
	0x48, 0xf7, 0xd7, //0x000010b0 notq         %rdi
	0xe9, 0x0a, 0x00, 0x00, 0x00, //0x000010b3 jmp          LBB0_268
	//0x000010b8 LBB0_267
	0x48, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x000010b8 movq         $-1, %rdi
	0x45, 0x31, 0xd2, //0x000010bf xorl         %r10d, %r10d
	//0x000010c2 LBB0_268
	0x48, 0x21, 0xf7, //0x000010c2 andq         %rsi, %rdi
	0xc4, 0xe1, 0xf9, 0x6e, 0xdf, //0x000010c5 vmovq        %rdi, %xmm3
	0xc4, 0xc3, 0x61, 0x44, 0xd9, 0x00, //0x000010ca vpclmulqdq   $0, %xmm9, %xmm3, %xmm3
	0xc4, 0xc1, 0xf9, 0x7e, 0xde, //0x000010d0 vmovq        %xmm3, %r14
	0x4d, 0x31, 0xc6, //0x000010d5 xorq         %r8, %r14
	0xc5, 0xa9, 0x74, 0xda, //0x000010d8 vpcmpeqb     %xmm2, %xmm10, %xmm3
	0xc5, 0xf9, 0xd7, 0xfb, //0x000010dc vpmovmskb    %xmm3, %edi
	0xc5, 0xa9, 0x74, 0xde, //0x000010e0 vpcmpeqb     %xmm6, %xmm10, %xmm3
	0xc5, 0xf9, 0xd7, 0xc3, //0x000010e4 vpmovmskb    %xmm3, %eax
	0xc5, 0xa9, 0x74, 0xdd, //0x000010e8 vpcmpeqb     %xmm5, %xmm10, %xmm3
	0xc5, 0xf9, 0xd7, 0xcb, //0x000010ec vpmovmskb    %xmm3, %ecx
	0xc5, 0xa9, 0x74, 0xdf, //0x000010f0 vpcmpeqb     %xmm7, %xmm10, %xmm3
	0xc5, 0xf9, 0xd7, 0xdb, //0x000010f4 vpmovmskb    %xmm3, %ebx
	0x48, 0xc1, 0xe3, 0x30, //0x000010f8 shlq         $48, %rbx
	0x48, 0xc1, 0xe1, 0x20, //0x000010fc shlq         $32, %rcx
	0x48, 0xc1, 0xe0, 0x10, //0x00001100 shlq         $16, %rax
	0x48, 0x09, 0xc7, //0x00001104 orq          %rax, %rdi
	0x48, 0x09, 0xcf, //0x00001107 orq          %rcx, %rdi
	0x48, 0x09, 0xdf, //0x0000110a orq          %rbx, %rdi
	0x4d, 0x89, 0xf0, //0x0000110d movq         %r14, %r8
	0x49, 0xf7, 0xd0, //0x00001110 notq         %r8
	0x4c, 0x21, 0xc7, //0x00001113 andq         %r8, %rdi
	0xc5, 0xa1, 0x74, 0xd2, //0x00001116 vpcmpeqb     %xmm2, %xmm11, %xmm2
	0xc5, 0xf9, 0xd7, 0xc2, //0x0000111a vpmovmskb    %xmm2, %eax
	0xc5, 0xa1, 0x74, 0xd6, //0x0000111e vpcmpeqb     %xmm6, %xmm11, %xmm2
	0xc5, 0xf9, 0xd7, 0xda, //0x00001122 vpmovmskb    %xmm2, %ebx
	0xc5, 0xa1, 0x74, 0xd5, //0x00001126 vpcmpeqb     %xmm5, %xmm11, %xmm2
	0xc5, 0xf9, 0xd7, 0xf2, //0x0000112a vpmovmskb    %xmm2, %esi
	0xc5, 0xa1, 0x74, 0xd7, //0x0000112e vpcmpeqb     %xmm7, %xmm11, %xmm2
	0xc5, 0xf9, 0xd7, 0xca, //0x00001132 vpmovmskb    %xmm2, %ecx
	0x48, 0xc1, 0xe1, 0x30, //0x00001136 shlq         $48, %rcx
	0x48, 0xc1, 0xe6, 0x20, //0x0000113a shlq         $32, %rsi
	0x48, 0xc1, 0xe3, 0x10, //0x0000113e shlq         $16, %rbx
	0x48, 0x09, 0xd8, //0x00001142 orq          %rbx, %rax
	0x48, 0x09, 0xf0, //0x00001145 orq          %rsi, %rax
	0x48, 0x09, 0xc8, //0x00001148 orq          %rcx, %rax
	0x4c, 0x21, 0xc0, //0x0000114b andq         %r8, %rax
	0x0f, 0x84, 0x69, 0xfe, 0xff, 0xff, //0x0000114e je           LBB0_263
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x00001154 movq         $24(%rsp), %r8
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001159 .p2align 4, 0x90
	//0x00001160 LBB0_270
	0x48, 0x8d, 0x58, 0xff, //0x00001160 leaq         $-1(%rax), %rbx
	0x48, 0x89, 0xd9, //0x00001164 movq         %rbx, %rcx
	0x48, 0x21, 0xf9, //0x00001167 andq         %rdi, %rcx
	0xf3, 0x48, 0x0f, 0xb8, 0xc9, //0x0000116a popcntq      %rcx, %rcx
	0x4c, 0x01, 0xd9, //0x0000116f addq         %r11, %rcx
	0x48, 0x39, 0xd1, //0x00001172 cmpq         %rdx, %rcx
	0x0f, 0x86, 0x71, 0x04, 0x00, 0x00, //0x00001175 jbe          LBB0_316
	0x48, 0xff, 0xc2, //0x0000117b incq         %rdx
	0x48, 0x21, 0xd8, //0x0000117e andq         %rbx, %rax
	0x0f, 0x85, 0xd9, 0xff, 0xff, 0xff, //0x00001181 jne          LBB0_270
	0xe9, 0x31, 0xfe, 0xff, 0xff, //0x00001187 jmp          LBB0_263
	//0x0000118c LBB0_272
	0x4d, 0x85, 0xc9, //0x0000118c testq        %r9, %r9
	0x0f, 0x8e, 0x5c, 0x0b, 0x00, 0x00, //0x0000118f jle          LBB0_338
	0x48, 0x89, 0xf9, //0x00001195 movq         %rdi, %rcx
	0xc5, 0x7e, 0x7f, 0x84, 0x24, 0x80, 0x00, 0x00, 0x00, //0x00001198 vmovdqu      %ymm8, $128(%rsp)
	0xc5, 0x7e, 0x7f, 0x44, 0x24, 0x60, //0x000011a1 vmovdqu      %ymm8, $96(%rsp)
	0x89, 0xc8, //0x000011a7 movl         %ecx, %eax
	0x25, 0xff, 0x0f, 0x00, 0x00, //0x000011a9 andl         $4095, %eax
	0x3d, 0xc1, 0x0f, 0x00, 0x00, //0x000011ae cmpl         $4033, %eax
	0x0f, 0x82, 0x34, 0x00, 0x00, 0x00, //0x000011b3 jb           LBB0_276
	0x49, 0x83, 0xf9, 0x20, //0x000011b9 cmpq         $32, %r9
	0x0f, 0x82, 0x37, 0x00, 0x00, 0x00, //0x000011bd jb           LBB0_277
	0xc5, 0xf8, 0x10, 0x11, //0x000011c3 vmovups      (%rcx), %xmm2
	0xc5, 0xf8, 0x11, 0x54, 0x24, 0x60, //0x000011c7 vmovups      %xmm2, $96(%rsp)
	0xc5, 0xfa, 0x6f, 0x51, 0x10, //0x000011cd vmovdqu      $16(%rcx), %xmm2
	0xc5, 0xfa, 0x7f, 0x54, 0x24, 0x70, //0x000011d2 vmovdqu      %xmm2, $112(%rsp)
	0x48, 0x83, 0xc1, 0x20, //0x000011d8 addq         $32, %rcx
	0x49, 0x8d, 0x79, 0xe0, //0x000011dc leaq         $-32(%r9), %rdi
	0x48, 0x8d, 0xb4, 0x24, 0x80, 0x00, 0x00, 0x00, //0x000011e0 leaq         $128(%rsp), %rsi
	0xe9, 0x15, 0x00, 0x00, 0x00, //0x000011e8 jmp          LBB0_278
	//0x000011ed LBB0_276
	0x4c, 0x8b, 0x7c, 0x24, 0x28, //0x000011ed movq         $40(%rsp), %r15
	0x48, 0x89, 0xcf, //0x000011f2 movq         %rcx, %rdi
	0xe9, 0xe7, 0xfd, 0xff, 0xff, //0x000011f5 jmp          LBB0_265
	//0x000011fa LBB0_277
	0x48, 0x8d, 0x74, 0x24, 0x60, //0x000011fa leaq         $96(%rsp), %rsi
	0x4c, 0x89, 0xcf, //0x000011ff movq         %r9, %rdi
	//0x00001202 LBB0_278
	0x48, 0x83, 0xff, 0x10, //0x00001202 cmpq         $16, %rdi
	0x0f, 0x82, 0x47, 0x00, 0x00, 0x00, //0x00001206 jb           LBB0_279
	0xc5, 0xfa, 0x6f, 0x11, //0x0000120c vmovdqu      (%rcx), %xmm2
	0xc5, 0xfa, 0x7f, 0x16, //0x00001210 vmovdqu      %xmm2, (%rsi)
	0x48, 0x83, 0xc1, 0x10, //0x00001214 addq         $16, %rcx
	0x48, 0x83, 0xc6, 0x10, //0x00001218 addq         $16, %rsi
	0x48, 0x83, 0xc7, 0xf0, //0x0000121c addq         $-16, %rdi
	0x48, 0x83, 0xff, 0x08, //0x00001220 cmpq         $8, %rdi
	0x0f, 0x83, 0x33, 0x00, 0x00, 0x00, //0x00001224 jae          LBB0_286
	//0x0000122a LBB0_280
	0x48, 0x83, 0xff, 0x04, //0x0000122a cmpq         $4, %rdi
	0x0f, 0x8c, 0x45, 0x00, 0x00, 0x00, //0x0000122e jl           LBB0_281
	//0x00001234 LBB0_287
	0x8b, 0x01, //0x00001234 movl         (%rcx), %eax
	0x89, 0x06, //0x00001236 movl         %eax, (%rsi)
	0x48, 0x83, 0xc1, 0x04, //0x00001238 addq         $4, %rcx
	0x48, 0x83, 0xc6, 0x04, //0x0000123c addq         $4, %rsi
	0x48, 0x83, 0xc7, 0xfc, //0x00001240 addq         $-4, %rdi
	0x48, 0x83, 0xff, 0x02, //0x00001244 cmpq         $2, %rdi
	0x0f, 0x83, 0x35, 0x00, 0x00, 0x00, //0x00001248 jae          LBB0_282
	0xe9, 0x42, 0x00, 0x00, 0x00, //0x0000124e jmp          LBB0_283
	//0x00001253 LBB0_279
	0x48, 0x83, 0xff, 0x08, //0x00001253 cmpq         $8, %rdi
	0x0f, 0x82, 0xcd, 0xff, 0xff, 0xff, //0x00001257 jb           LBB0_280
	//0x0000125d LBB0_286
	0x48, 0x8b, 0x01, //0x0000125d movq         (%rcx), %rax
	0x48, 0x89, 0x06, //0x00001260 movq         %rax, (%rsi)
	0x48, 0x83, 0xc1, 0x08, //0x00001263 addq         $8, %rcx
	0x48, 0x83, 0xc6, 0x08, //0x00001267 addq         $8, %rsi
	0x48, 0x83, 0xc7, 0xf8, //0x0000126b addq         $-8, %rdi
	0x48, 0x83, 0xff, 0x04, //0x0000126f cmpq         $4, %rdi
	0x0f, 0x8d, 0xbb, 0xff, 0xff, 0xff, //0x00001273 jge          LBB0_287
	//0x00001279 LBB0_281
	0x48, 0x83, 0xff, 0x02, //0x00001279 cmpq         $2, %rdi
	0x0f, 0x82, 0x12, 0x00, 0x00, 0x00, //0x0000127d jb           LBB0_283
	//0x00001283 LBB0_282
	0x0f, 0xb7, 0x01, //0x00001283 movzwl       (%rcx), %eax
	0x66, 0x89, 0x06, //0x00001286 movw         %ax, (%rsi)
	0x48, 0x83, 0xc1, 0x02, //0x00001289 addq         $2, %rcx
	0x48, 0x83, 0xc6, 0x02, //0x0000128d addq         $2, %rsi
	0x48, 0x83, 0xc7, 0xfe, //0x00001291 addq         $-2, %rdi
	//0x00001295 LBB0_283
	0x48, 0x89, 0xc8, //0x00001295 movq         %rcx, %rax
	0x48, 0x8d, 0x4c, 0x24, 0x60, //0x00001298 leaq         $96(%rsp), %rcx
	0x48, 0x85, 0xff, //0x0000129d testq        %rdi, %rdi
	0x48, 0x89, 0xcf, //0x000012a0 movq         %rcx, %rdi
	0x4c, 0x8b, 0x7c, 0x24, 0x28, //0x000012a3 movq         $40(%rsp), %r15
	0x0f, 0x84, 0x33, 0xfd, 0xff, 0xff, //0x000012a8 je           LBB0_265
	0x8a, 0x00, //0x000012ae movb         (%rax), %al
	0x88, 0x06, //0x000012b0 movb         %al, (%rsi)
	0x48, 0x8d, 0x7c, 0x24, 0x60, //0x000012b2 leaq         $96(%rsp), %rdi
	0xe9, 0x25, 0xfd, 0xff, 0xff, //0x000012b7 jmp          LBB0_265
	//0x000012bc LBB0_288
	0x48, 0x83, 0xc1, 0x05, //0x000012bc addq         $5, %rcx
	0x49, 0x3b, 0x0b, //0x000012c0 cmpq         (%r11), %rcx
	0x0f, 0x87, 0x67, 0xfa, 0xff, 0xff, //0x000012c3 ja           LBB0_222
	//0x000012c9 LBB0_289
	0x49, 0x89, 0x08, //0x000012c9 movq         %rcx, (%r8)
	0x49, 0x89, 0xca, //0x000012cc movq         %rcx, %r10
	0xe9, 0x5c, 0xfa, 0xff, 0xff, //0x000012cf jmp          LBB0_222
	//0x000012d4 LBB0_290
	0x4d, 0x8b, 0x0b, //0x000012d4 movq         (%r11), %r9
	0x4d, 0x29, 0xd1, //0x000012d7 subq         %r10, %r9
	0x4c, 0x01, 0xd7, //0x000012da addq         %r10, %rdi
	0x45, 0x31, 0xc0, //0x000012dd xorl         %r8d, %r8d
	0x45, 0x31, 0xd2, //0x000012e0 xorl         %r10d, %r10d
	0x45, 0x31, 0xdb, //0x000012e3 xorl         %r11d, %r11d
	0x31, 0xd2, //0x000012e6 xorl         %edx, %edx
	0xe9, 0x1a, 0x00, 0x00, 0x00, //0x000012e8 jmp          LBB0_292
	//0x000012ed LBB0_291
	0x49, 0xc1, 0xfe, 0x3f, //0x000012ed sarq         $63, %r14
	0xf3, 0x48, 0x0f, 0xb8, 0xc7, //0x000012f1 popcntq      %rdi, %rax
	0x49, 0x01, 0xc3, //0x000012f6 addq         %rax, %r11
	0x4c, 0x89, 0xe7, //0x000012f9 movq         %r12, %rdi
	0x48, 0x83, 0xc7, 0x40, //0x000012fc addq         $64, %rdi
	0x49, 0x83, 0xc1, 0xc0, //0x00001300 addq         $-64, %r9
	0x4d, 0x89, 0xf0, //0x00001304 movq         %r14, %r8
	//0x00001307 LBB0_292
	0x49, 0x83, 0xf9, 0x40, //0x00001307 cmpq         $64, %r9
	0x0f, 0x8c, 0xab, 0x01, 0x00, 0x00, //0x0000130b jl           LBB0_300
	//0x00001311 LBB0_293
	0xc5, 0xfa, 0x6f, 0x17, //0x00001311 vmovdqu      (%rdi), %xmm2
	0xc5, 0xfa, 0x6f, 0x77, 0x10, //0x00001315 vmovdqu      $16(%rdi), %xmm6
	0xc5, 0xfa, 0x6f, 0x6f, 0x20, //0x0000131a vmovdqu      $32(%rdi), %xmm5
	0x49, 0x89, 0xfc, //0x0000131f movq         %rdi, %r12
	0xc5, 0xfa, 0x6f, 0x7f, 0x30, //0x00001322 vmovdqu      $48(%rdi), %xmm7
	0xc5, 0xe9, 0x74, 0xd8, //0x00001327 vpcmpeqb     %xmm0, %xmm2, %xmm3
	0xc5, 0xf9, 0xd7, 0xf3, //0x0000132b vpmovmskb    %xmm3, %esi
	0xc5, 0xc9, 0x74, 0xd8, //0x0000132f vpcmpeqb     %xmm0, %xmm6, %xmm3
	0xc5, 0xf9, 0xd7, 0xc3, //0x00001333 vpmovmskb    %xmm3, %eax
	0xc5, 0xd1, 0x74, 0xd8, //0x00001337 vpcmpeqb     %xmm0, %xmm5, %xmm3
	0xc5, 0xf9, 0xd7, 0xfb, //0x0000133b vpmovmskb    %xmm3, %edi
	0xc5, 0xc1, 0x74, 0xd8, //0x0000133f vpcmpeqb     %xmm0, %xmm7, %xmm3
	0xc5, 0xf9, 0xd7, 0xdb, //0x00001343 vpmovmskb    %xmm3, %ebx
	0x48, 0xc1, 0xe3, 0x30, //0x00001347 shlq         $48, %rbx
	0x48, 0xc1, 0xe7, 0x20, //0x0000134b shlq         $32, %rdi
	0x48, 0xc1, 0xe0, 0x10, //0x0000134f shlq         $16, %rax
	0x48, 0x09, 0xc6, //0x00001353 orq          %rax, %rsi
	0x48, 0x09, 0xfe, //0x00001356 orq          %rdi, %rsi
	0x48, 0x09, 0xde, //0x00001359 orq          %rbx, %rsi
	0xc5, 0xe9, 0x74, 0xd9, //0x0000135c vpcmpeqb     %xmm1, %xmm2, %xmm3
	0xc5, 0xf9, 0xd7, 0xfb, //0x00001360 vpmovmskb    %xmm3, %edi
	0xc5, 0xc9, 0x74, 0xd9, //0x00001364 vpcmpeqb     %xmm1, %xmm6, %xmm3
	0xc5, 0xf9, 0xd7, 0xc3, //0x00001368 vpmovmskb    %xmm3, %eax
	0xc5, 0xd1, 0x74, 0xd9, //0x0000136c vpcmpeqb     %xmm1, %xmm5, %xmm3
	0xc5, 0xf9, 0xd7, 0xdb, //0x00001370 vpmovmskb    %xmm3, %ebx
	0xc5, 0xc1, 0x74, 0xd9, //0x00001374 vpcmpeqb     %xmm1, %xmm7, %xmm3
	0xc5, 0xf9, 0xd7, 0xcb, //0x00001378 vpmovmskb    %xmm3, %ecx
	0x48, 0xc1, 0xe1, 0x30, //0x0000137c shlq         $48, %rcx
	0x48, 0xc1, 0xe3, 0x20, //0x00001380 shlq         $32, %rbx
	0x48, 0xc1, 0xe0, 0x10, //0x00001384 shlq         $16, %rax
	0x48, 0x09, 0xc7, //0x00001388 orq          %rax, %rdi
	0x48, 0x09, 0xdf, //0x0000138b orq          %rbx, %rdi
	0x48, 0x09, 0xcf, //0x0000138e orq          %rcx, %rdi
	0x48, 0x89, 0xf8, //0x00001391 movq         %rdi, %rax
	0x4c, 0x09, 0xd0, //0x00001394 orq          %r10, %rax
	0x0f, 0x84, 0x4b, 0x00, 0x00, 0x00, //0x00001397 je           LBB0_295
	0x4c, 0x89, 0xd0, //0x0000139d movq         %r10, %rax
	0x48, 0xf7, 0xd0, //0x000013a0 notq         %rax
	0x48, 0x21, 0xf8, //0x000013a3 andq         %rdi, %rax
	0x4c, 0x8d, 0x34, 0x00, //0x000013a6 leaq         (%rax,%rax), %r14
	0x4d, 0x09, 0xd6, //0x000013aa orq          %r10, %r14
	0x4c, 0x89, 0xf1, //0x000013ad movq         %r14, %rcx
	0x48, 0xbb, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x000013b0 movabsq      $-6148914691236517206, %rbx
	0x48, 0x31, 0xd9, //0x000013ba xorq         %rbx, %rcx
	0x48, 0x21, 0xdf, //0x000013bd andq         %rbx, %rdi
	0x48, 0x21, 0xcf, //0x000013c0 andq         %rcx, %rdi
	0x45, 0x31, 0xd2, //0x000013c3 xorl         %r10d, %r10d
	0x48, 0x01, 0xc7, //0x000013c6 addq         %rax, %rdi
	0x41, 0x0f, 0x92, 0xc2, //0x000013c9 setb         %r10b
	0x48, 0x01, 0xff, //0x000013cd addq         %rdi, %rdi
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x000013d0 movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc7, //0x000013da xorq         %rax, %rdi
	0x4c, 0x21, 0xf7, //0x000013dd andq         %r14, %rdi
	0x48, 0xf7, 0xd7, //0x000013e0 notq         %rdi
	0xe9, 0x0a, 0x00, 0x00, 0x00, //0x000013e3 jmp          LBB0_296
	//0x000013e8 LBB0_295
	0x48, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x000013e8 movq         $-1, %rdi
	0x45, 0x31, 0xd2, //0x000013ef xorl         %r10d, %r10d
	//0x000013f2 LBB0_296
	0x48, 0x21, 0xf7, //0x000013f2 andq         %rsi, %rdi
	0xc4, 0xe1, 0xf9, 0x6e, 0xdf, //0x000013f5 vmovq        %rdi, %xmm3
	0xc4, 0xc3, 0x61, 0x44, 0xd9, 0x00, //0x000013fa vpclmulqdq   $0, %xmm9, %xmm3, %xmm3
	0xc4, 0xc1, 0xf9, 0x7e, 0xde, //0x00001400 vmovq        %xmm3, %r14
	0x4d, 0x31, 0xc6, //0x00001405 xorq         %r8, %r14
	0xc5, 0x99, 0x74, 0xda, //0x00001408 vpcmpeqb     %xmm2, %xmm12, %xmm3
	0xc5, 0xf9, 0xd7, 0xfb, //0x0000140c vpmovmskb    %xmm3, %edi
	0xc5, 0x99, 0x74, 0xde, //0x00001410 vpcmpeqb     %xmm6, %xmm12, %xmm3
	0xc5, 0xf9, 0xd7, 0xc3, //0x00001414 vpmovmskb    %xmm3, %eax
	0xc5, 0x99, 0x74, 0xdd, //0x00001418 vpcmpeqb     %xmm5, %xmm12, %xmm3
	0xc5, 0xf9, 0xd7, 0xcb, //0x0000141c vpmovmskb    %xmm3, %ecx
	0xc5, 0x99, 0x74, 0xdf, //0x00001420 vpcmpeqb     %xmm7, %xmm12, %xmm3
	0xc5, 0xf9, 0xd7, 0xdb, //0x00001424 vpmovmskb    %xmm3, %ebx
	0x48, 0xc1, 0xe3, 0x30, //0x00001428 shlq         $48, %rbx
	0x48, 0xc1, 0xe1, 0x20, //0x0000142c shlq         $32, %rcx
	0x48, 0xc1, 0xe0, 0x10, //0x00001430 shlq         $16, %rax
	0x48, 0x09, 0xc7, //0x00001434 orq          %rax, %rdi
	0x48, 0x09, 0xcf, //0x00001437 orq          %rcx, %rdi
	0x48, 0x09, 0xdf, //0x0000143a orq          %rbx, %rdi
	0x4d, 0x89, 0xf0, //0x0000143d movq         %r14, %r8
	0x49, 0xf7, 0xd0, //0x00001440 notq         %r8
	0x4c, 0x21, 0xc7, //0x00001443 andq         %r8, %rdi
	0xc5, 0x91, 0x74, 0xd2, //0x00001446 vpcmpeqb     %xmm2, %xmm13, %xmm2
	0xc5, 0xf9, 0xd7, 0xc2, //0x0000144a vpmovmskb    %xmm2, %eax
	0xc5, 0x91, 0x74, 0xd6, //0x0000144e vpcmpeqb     %xmm6, %xmm13, %xmm2
	0xc5, 0xf9, 0xd7, 0xda, //0x00001452 vpmovmskb    %xmm2, %ebx
	0xc5, 0x91, 0x74, 0xd5, //0x00001456 vpcmpeqb     %xmm5, %xmm13, %xmm2
	0xc5, 0xf9, 0xd7, 0xf2, //0x0000145a vpmovmskb    %xmm2, %esi
	0xc5, 0x91, 0x74, 0xd7, //0x0000145e vpcmpeqb     %xmm7, %xmm13, %xmm2
	0xc5, 0xf9, 0xd7, 0xca, //0x00001462 vpmovmskb    %xmm2, %ecx
	0x48, 0xc1, 0xe1, 0x30, //0x00001466 shlq         $48, %rcx
	0x48, 0xc1, 0xe6, 0x20, //0x0000146a shlq         $32, %rsi
	0x48, 0xc1, 0xe3, 0x10, //0x0000146e shlq         $16, %rbx
	0x48, 0x09, 0xd8, //0x00001472 orq          %rbx, %rax
	0x48, 0x09, 0xf0, //0x00001475 orq          %rsi, %rax
	0x48, 0x09, 0xc8, //0x00001478 orq          %rcx, %rax
	0x4c, 0x21, 0xc0, //0x0000147b andq         %r8, %rax
	0x0f, 0x84, 0x69, 0xfe, 0xff, 0xff, //0x0000147e je           LBB0_291
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x00001484 movq         $24(%rsp), %r8
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001489 .p2align 4, 0x90
	//0x00001490 LBB0_298
	0x48, 0x8d, 0x58, 0xff, //0x00001490 leaq         $-1(%rax), %rbx
	0x48, 0x89, 0xd9, //0x00001494 movq         %rbx, %rcx
	0x48, 0x21, 0xf9, //0x00001497 andq         %rdi, %rcx
	0xf3, 0x48, 0x0f, 0xb8, 0xc9, //0x0000149a popcntq      %rcx, %rcx
	0x4c, 0x01, 0xd9, //0x0000149f addq         %r11, %rcx
	0x48, 0x39, 0xd1, //0x000014a2 cmpq         %rdx, %rcx
	0x0f, 0x86, 0x41, 0x01, 0x00, 0x00, //0x000014a5 jbe          LBB0_316
	0x48, 0xff, 0xc2, //0x000014ab incq         %rdx
	0x48, 0x21, 0xd8, //0x000014ae andq         %rbx, %rax
	0x0f, 0x85, 0xd9, 0xff, 0xff, 0xff, //0x000014b1 jne          LBB0_298
	0xe9, 0x31, 0xfe, 0xff, 0xff, //0x000014b7 jmp          LBB0_291
	//0x000014bc LBB0_300
	0x4d, 0x85, 0xc9, //0x000014bc testq        %r9, %r9
	0x0f, 0x8e, 0x2c, 0x08, 0x00, 0x00, //0x000014bf jle          LBB0_338
	0x48, 0x89, 0xf9, //0x000014c5 movq         %rdi, %rcx
	0xc5, 0x7e, 0x7f, 0x84, 0x24, 0x80, 0x00, 0x00, 0x00, //0x000014c8 vmovdqu      %ymm8, $128(%rsp)
	0xc5, 0x7e, 0x7f, 0x44, 0x24, 0x60, //0x000014d1 vmovdqu      %ymm8, $96(%rsp)
	0x89, 0xc8, //0x000014d7 movl         %ecx, %eax
	0x25, 0xff, 0x0f, 0x00, 0x00, //0x000014d9 andl         $4095, %eax
	0x3d, 0xc1, 0x0f, 0x00, 0x00, //0x000014de cmpl         $4033, %eax
	0x0f, 0x82, 0x34, 0x00, 0x00, 0x00, //0x000014e3 jb           LBB0_304
	0x49, 0x83, 0xf9, 0x20, //0x000014e9 cmpq         $32, %r9
	0x0f, 0x82, 0x37, 0x00, 0x00, 0x00, //0x000014ed jb           LBB0_305
	0xc5, 0xf8, 0x10, 0x11, //0x000014f3 vmovups      (%rcx), %xmm2
	0xc5, 0xf8, 0x11, 0x54, 0x24, 0x60, //0x000014f7 vmovups      %xmm2, $96(%rsp)
	0xc5, 0xfa, 0x6f, 0x51, 0x10, //0x000014fd vmovdqu      $16(%rcx), %xmm2
	0xc5, 0xfa, 0x7f, 0x54, 0x24, 0x70, //0x00001502 vmovdqu      %xmm2, $112(%rsp)
	0x48, 0x83, 0xc1, 0x20, //0x00001508 addq         $32, %rcx
	0x49, 0x8d, 0x79, 0xe0, //0x0000150c leaq         $-32(%r9), %rdi
	0x48, 0x8d, 0xb4, 0x24, 0x80, 0x00, 0x00, 0x00, //0x00001510 leaq         $128(%rsp), %rsi
	0xe9, 0x15, 0x00, 0x00, 0x00, //0x00001518 jmp          LBB0_306
	//0x0000151d LBB0_304
	0x4c, 0x8b, 0x7c, 0x24, 0x28, //0x0000151d movq         $40(%rsp), %r15
	0x48, 0x89, 0xcf, //0x00001522 movq         %rcx, %rdi
	0xe9, 0xe7, 0xfd, 0xff, 0xff, //0x00001525 jmp          LBB0_293
	//0x0000152a LBB0_305
	0x48, 0x8d, 0x74, 0x24, 0x60, //0x0000152a leaq         $96(%rsp), %rsi
	0x4c, 0x89, 0xcf, //0x0000152f movq         %r9, %rdi
	//0x00001532 LBB0_306
	0x48, 0x83, 0xff, 0x10, //0x00001532 cmpq         $16, %rdi
	0x0f, 0x82, 0x47, 0x00, 0x00, 0x00, //0x00001536 jb           LBB0_307
	0xc5, 0xfa, 0x6f, 0x11, //0x0000153c vmovdqu      (%rcx), %xmm2
	0xc5, 0xfa, 0x7f, 0x16, //0x00001540 vmovdqu      %xmm2, (%rsi)
	0x48, 0x83, 0xc1, 0x10, //0x00001544 addq         $16, %rcx
	0x48, 0x83, 0xc6, 0x10, //0x00001548 addq         $16, %rsi
	0x48, 0x83, 0xc7, 0xf0, //0x0000154c addq         $-16, %rdi
	0x48, 0x83, 0xff, 0x08, //0x00001550 cmpq         $8, %rdi
	0x0f, 0x83, 0x33, 0x00, 0x00, 0x00, //0x00001554 jae          LBB0_314
	//0x0000155a LBB0_308
	0x48, 0x83, 0xff, 0x04, //0x0000155a cmpq         $4, %rdi
	0x0f, 0x8c, 0x45, 0x00, 0x00, 0x00, //0x0000155e jl           LBB0_309
	//0x00001564 LBB0_315
	0x8b, 0x01, //0x00001564 movl         (%rcx), %eax
	0x89, 0x06, //0x00001566 movl         %eax, (%rsi)
	0x48, 0x83, 0xc1, 0x04, //0x00001568 addq         $4, %rcx
	0x48, 0x83, 0xc6, 0x04, //0x0000156c addq         $4, %rsi
	0x48, 0x83, 0xc7, 0xfc, //0x00001570 addq         $-4, %rdi
	0x48, 0x83, 0xff, 0x02, //0x00001574 cmpq         $2, %rdi
	0x0f, 0x83, 0x35, 0x00, 0x00, 0x00, //0x00001578 jae          LBB0_310
	0xe9, 0x42, 0x00, 0x00, 0x00, //0x0000157e jmp          LBB0_311
	//0x00001583 LBB0_307
	0x48, 0x83, 0xff, 0x08, //0x00001583 cmpq         $8, %rdi
	0x0f, 0x82, 0xcd, 0xff, 0xff, 0xff, //0x00001587 jb           LBB0_308
	//0x0000158d LBB0_314
	0x48, 0x8b, 0x01, //0x0000158d movq         (%rcx), %rax
	0x48, 0x89, 0x06, //0x00001590 movq         %rax, (%rsi)
	0x48, 0x83, 0xc1, 0x08, //0x00001593 addq         $8, %rcx
	0x48, 0x83, 0xc6, 0x08, //0x00001597 addq         $8, %rsi
	0x48, 0x83, 0xc7, 0xf8, //0x0000159b addq         $-8, %rdi
	0x48, 0x83, 0xff, 0x04, //0x0000159f cmpq         $4, %rdi
	0x0f, 0x8d, 0xbb, 0xff, 0xff, 0xff, //0x000015a3 jge          LBB0_315
	//0x000015a9 LBB0_309
	0x48, 0x83, 0xff, 0x02, //0x000015a9 cmpq         $2, %rdi
	0x0f, 0x82, 0x12, 0x00, 0x00, 0x00, //0x000015ad jb           LBB0_311
	//0x000015b3 LBB0_310
	0x0f, 0xb7, 0x01, //0x000015b3 movzwl       (%rcx), %eax
	0x66, 0x89, 0x06, //0x000015b6 movw         %ax, (%rsi)
	0x48, 0x83, 0xc1, 0x02, //0x000015b9 addq         $2, %rcx
	0x48, 0x83, 0xc6, 0x02, //0x000015bd addq         $2, %rsi
	0x48, 0x83, 0xc7, 0xfe, //0x000015c1 addq         $-2, %rdi
	//0x000015c5 LBB0_311
	0x48, 0x89, 0xc8, //0x000015c5 movq         %rcx, %rax
	0x48, 0x8d, 0x4c, 0x24, 0x60, //0x000015c8 leaq         $96(%rsp), %rcx
	0x48, 0x85, 0xff, //0x000015cd testq        %rdi, %rdi
	0x48, 0x89, 0xcf, //0x000015d0 movq         %rcx, %rdi
	0x4c, 0x8b, 0x7c, 0x24, 0x28, //0x000015d3 movq         $40(%rsp), %r15
	0x0f, 0x84, 0x33, 0xfd, 0xff, 0xff, //0x000015d8 je           LBB0_293
	0x8a, 0x00, //0x000015de movb         (%rax), %al
	0x88, 0x06, //0x000015e0 movb         %al, (%rsi)
	0x48, 0x8d, 0x7c, 0x24, 0x60, //0x000015e2 leaq         $96(%rsp), %rdi
	0xe9, 0x25, 0xfd, 0xff, 0xff, //0x000015e7 jmp          LBB0_293
	//0x000015ec LBB0_316
	0x4c, 0x8b, 0x5c, 0x24, 0x38, //0x000015ec movq         $56(%rsp), %r11
	0x49, 0x8b, 0x0b, //0x000015f1 movq         (%r11), %rcx
	0x48, 0x0f, 0xbc, 0xc0, //0x000015f4 bsfq         %rax, %rax
	0x4c, 0x29, 0xc8, //0x000015f8 subq         %r9, %rax
	0x4c, 0x8d, 0x54, 0x08, 0x01, //0x000015fb leaq         $1(%rax,%rcx), %r10
	0x4d, 0x89, 0x10, //0x00001600 movq         %r10, (%r8)
	0x49, 0x8b, 0x03, //0x00001603 movq         (%r11), %rax
	0x49, 0x39, 0xc2, //0x00001606 cmpq         %rax, %r10
	0x4c, 0x0f, 0x47, 0xd0, //0x00001609 cmovaq       %rax, %r10
	0x4d, 0x89, 0x10, //0x0000160d movq         %r10, (%r8)
	0x49, 0xb9, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00001610 movabsq      $4294977024, %r9
	0xe9, 0x11, 0xf7, 0xff, 0xff, //0x0000161a jmp          LBB0_222
	//0x0000161f LBB0_68
	0x48, 0x8b, 0x8c, 0x24, 0xa8, 0x00, 0x00, 0x00, //0x0000161f movq         $168(%rsp), %rcx
	0x4c, 0x8b, 0x7c, 0x24, 0x28, //0x00001627 movq         $40(%rsp), %r15
	0x4c, 0x8b, 0x6c, 0x24, 0x58, //0x0000162c movq         $88(%rsp), %r13
	0x4c, 0x8b, 0x5c, 0x24, 0x38, //0x00001631 movq         $56(%rsp), %r11
	0x4c, 0x8b, 0x64, 0x24, 0x30, //0x00001636 movq         $48(%rsp), %r12
	0x4c, 0x8b, 0x54, 0x24, 0x50, //0x0000163b movq         $80(%rsp), %r10
	0x48, 0x83, 0xf9, 0x20, //0x00001640 cmpq         $32, %rcx
	0x0f, 0x82, 0xaa, 0x00, 0x00, 0x00, //0x00001644 jb           LBB0_106
	//0x0000164a LBB0_69
	0xc4, 0xc1, 0x7a, 0x6f, 0x12, //0x0000164a vmovdqu      (%r10), %xmm2
	0xc4, 0xc1, 0x7a, 0x6f, 0x5a, 0x10, //0x0000164f vmovdqu      $16(%r10), %xmm3
	0xc5, 0xe9, 0x74, 0xe8, //0x00001655 vpcmpeqb     %xmm0, %xmm2, %xmm5
	0xc5, 0x79, 0xd7, 0xcd, //0x00001659 vpmovmskb    %xmm5, %r9d
	0xc5, 0xe1, 0x74, 0xe8, //0x0000165d vpcmpeqb     %xmm0, %xmm3, %xmm5
	0xc5, 0xf9, 0xd7, 0xf5, //0x00001661 vpmovmskb    %xmm5, %esi
	0xc5, 0xe9, 0x74, 0xd1, //0x00001665 vpcmpeqb     %xmm1, %xmm2, %xmm2
	0xc5, 0xf9, 0xd7, 0xfa, //0x00001669 vpmovmskb    %xmm2, %edi
	0xc5, 0xe1, 0x74, 0xd1, //0x0000166d vpcmpeqb     %xmm1, %xmm3, %xmm2
	0xc5, 0xf9, 0xd7, 0xc2, //0x00001671 vpmovmskb    %xmm2, %eax
	0x48, 0xc1, 0xe6, 0x10, //0x00001675 shlq         $16, %rsi
	0x48, 0xc1, 0xe0, 0x10, //0x00001679 shlq         $16, %rax
	0x48, 0x09, 0xc7, //0x0000167d orq          %rax, %rdi
	0x49, 0x83, 0xf8, 0xff, //0x00001680 cmpq         $-1, %r8
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x00001684 jne          LBB0_71
	0x48, 0x85, 0xff, //0x0000168a testq        %rdi, %rdi
	0x0f, 0x85, 0xdc, 0x04, 0x00, 0x00, //0x0000168d jne          LBB0_324
	//0x00001693 LBB0_71
	0x4c, 0x09, 0xce, //0x00001693 orq          %r9, %rsi
	0x48, 0x89, 0xf8, //0x00001696 movq         %rdi, %rax
	0x48, 0x09, 0xd8, //0x00001699 orq          %rbx, %rax
	0x0f, 0x85, 0xeb, 0x04, 0x00, 0x00, //0x0000169c jne          LBB0_325
	//0x000016a2 LBB0_72
	0x49, 0xb9, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x000016a2 movabsq      $4294977024, %r9
	0x48, 0x8b, 0x7c, 0x24, 0x20, //0x000016ac movq         $32(%rsp), %rdi
	0x48, 0x85, 0xf6, //0x000016b1 testq        %rsi, %rsi
	0x0f, 0x84, 0x23, 0x05, 0x00, 0x00, //0x000016b4 je           LBB0_326
	//0x000016ba LBB0_73
	0x48, 0x0f, 0xbc, 0xc6, //0x000016ba bsfq         %rsi, %rax
	0x49, 0x29, 0xfa, //0x000016be subq         %rdi, %r10
	0x4d, 0x8d, 0x54, 0x02, 0x01, //0x000016c1 leaq         $1(%r10,%rax), %r10
	0x48, 0x8b, 0x74, 0x24, 0x48, //0x000016c6 movq         $72(%rsp), %rsi
	0x4d, 0x85, 0xd2, //0x000016cb testq        %r10, %r10
	0x48, 0x8b, 0x9c, 0x24, 0xb0, 0x00, 0x00, 0x00, //0x000016ce movq         $176(%rsp), %rbx
	0x0f, 0x89, 0xa9, 0xf0, 0xff, 0xff, //0x000016d6 jns          LBB0_79
	0xe9, 0x5e, 0x36, 0x00, 0x00, //0x000016dc jmp          LBB0_937
	//0x000016e1 LBB0_105
	0x49, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x000016e1 movq         $-1, %r8
	0x31, 0xdb, //0x000016e8 xorl         %ebx, %ebx
	0x48, 0x83, 0xf9, 0x20, //0x000016ea cmpq         $32, %rcx
	0x0f, 0x83, 0x56, 0xff, 0xff, 0xff, //0x000016ee jae          LBB0_69
	//0x000016f4 LBB0_106
	0x48, 0x8b, 0x7c, 0x24, 0x20, //0x000016f4 movq         $32(%rsp), %rdi
	0xe9, 0xe7, 0x04, 0x00, 0x00, //0x000016f9 jmp          LBB0_327
	//0x000016fe LBB0_107
	0x48, 0xc7, 0x44, 0x24, 0x60, 0x00, 0x00, 0x00, 0x00, //0x000016fe movq         $0, $96(%rsp)
	0x4e, 0x8d, 0x4c, 0x17, 0xff, //0x00001707 leaq         $-1(%rdi,%r10), %r9
	0x4d, 0x8d, 0x1c, 0x34, //0x0000170c leaq         (%r12,%rsi), %r11
	0x48, 0x85, 0xf6, //0x00001710 testq        %rsi, %rsi
	0x0f, 0x8e, 0x10, 0x04, 0x00, 0x00, //0x00001713 jle          LBB0_151
	0x49, 0x39, 0xd9, //0x00001719 cmpq         %rbx, %r9
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x0000171c movq         $24(%rsp), %r8
	0x0f, 0x86, 0x07, 0x04, 0x00, 0x00, //0x00001721 jbe          LBB0_152
	//0x00001727 LBB0_109
	0x8a, 0x03, //0x00001727 movb         (%rbx), %al
	0x3c, 0x5c, //0x00001729 cmpb         $92, %al
	0x0f, 0x85, 0xaf, 0x00, 0x00, 0x00, //0x0000172b jne          LBB0_114
	0x4c, 0x89, 0xcf, //0x00001731 movq         %r9, %rdi
	0x48, 0x29, 0xdf, //0x00001734 subq         %rbx, %rdi
	0x48, 0x85, 0xff, //0x00001737 testq        %rdi, %rdi
	0x0f, 0x8e, 0xff, 0x37, 0x00, 0x00, //0x0000173a jle          LBB0_958
	0x49, 0x89, 0xde, //0x00001740 movq         %rbx, %r14
	0x0f, 0xb6, 0x43, 0x01, //0x00001743 movzbl       $1(%rbx), %eax
	0x48, 0x8d, 0x0d, 0x22, 0x42, 0x00, 0x00, //0x00001747 leaq         $16930(%rip), %rcx  /* __UnquoteTab+0(%rip) */
	0x8a, 0x1c, 0x08, //0x0000174e movb         (%rax,%rcx), %bl
	0x80, 0xfb, 0xff, //0x00001751 cmpb         $-1, %bl
	0x0f, 0x84, 0x9b, 0x00, 0x00, 0x00, //0x00001754 je           LBB0_116
	0x84, 0xdb, //0x0000175a testb        %bl, %bl
	0x0f, 0x84, 0xc5, 0x37, 0x00, 0x00, //0x0000175c je           LBB0_956
	0x88, 0x5c, 0x24, 0x60, //0x00001762 movb         %bl, $96(%rsp)
	0x49, 0x83, 0xc6, 0x02, //0x00001766 addq         $2, %r14
	0xbf, 0x01, 0x00, 0x00, 0x00, //0x0000176a movl         $1, %edi
	0x48, 0x8d, 0x44, 0x3c, 0x60, //0x0000176f leaq         $96(%rsp,%rdi), %rax
	0x4d, 0x39, 0xdc, //0x00001774 cmpq         %r11, %r12
	0x0f, 0x83, 0x7f, 0x01, 0x00, 0x00, //0x00001777 jae          LBB0_146
	//0x0000177d LBB0_126
	0x48, 0x8d, 0x4c, 0x24, 0x60, //0x0000177d leaq         $96(%rsp), %rcx
	0x48, 0x39, 0xc8, //0x00001782 cmpq         %rcx, %rax
	0x48, 0x8b, 0x7c, 0x24, 0x20, //0x00001785 movq         $32(%rsp), %rdi
	0x0f, 0x86, 0x7b, 0x01, 0x00, 0x00, //0x0000178a jbe          LBB0_133
	0x41, 0x38, 0x1c, 0x24, //0x00001790 cmpb         %bl, (%r12)
	0x0f, 0x85, 0x71, 0x01, 0x00, 0x00, //0x00001794 jne          LBB0_133
	0x49, 0xff, 0xc4, //0x0000179a incq         %r12
	0x48, 0x8d, 0x4c, 0x24, 0x61, //0x0000179d leaq         $97(%rsp), %rcx
	0x4c, 0x89, 0xe7, //0x000017a2 movq         %r12, %rdi
	0x4c, 0x89, 0xf3, //0x000017a5 movq         %r14, %rbx
	//0x000017a8 LBB0_129
	0x49, 0x89, 0xfc, //0x000017a8 movq         %rdi, %r12
	0x48, 0x89, 0xce, //0x000017ab movq         %rcx, %rsi
	0x48, 0x39, 0xc1, //0x000017ae cmpq         %rax, %rcx
	0x0f, 0x83, 0x1f, 0x00, 0x00, 0x00, //0x000017b1 jae          LBB0_132
	0x4d, 0x39, 0xdc, //0x000017b7 cmpq         %r11, %r12
	0x0f, 0x83, 0x16, 0x00, 0x00, 0x00, //0x000017ba jae          LBB0_132
	0x41, 0x0f, 0xb6, 0x14, 0x24, //0x000017c0 movzbl       (%r12), %edx
	0x49, 0x8d, 0x7c, 0x24, 0x01, //0x000017c5 leaq         $1(%r12), %rdi
	0x48, 0x8d, 0x4e, 0x01, //0x000017ca leaq         $1(%rsi), %rcx
	0x3a, 0x16, //0x000017ce cmpb         (%rsi), %dl
	0x0f, 0x84, 0xd2, 0xff, 0xff, 0xff, //0x000017d0 je           LBB0_129
	//0x000017d6 LBB0_132
	0x48, 0x8b, 0x7c, 0x24, 0x20, //0x000017d6 movq         $32(%rsp), %rdi
	0xe9, 0x33, 0x01, 0x00, 0x00, //0x000017db jmp          LBB0_148
	//0x000017e0 LBB0_114
	0x41, 0x3a, 0x04, 0x24, //0x000017e0 cmpb         (%r12), %al
	0x0f, 0x85, 0x75, 0x03, 0x00, 0x00, //0x000017e4 jne          LBB0_322
	0x48, 0xff, 0xc3, //0x000017ea incq         %rbx
	0x49, 0xff, 0xc4, //0x000017ed incq         %r12
	0xe9, 0x27, 0x01, 0x00, 0x00, //0x000017f0 jmp          LBB0_149
	//0x000017f5 LBB0_116
	0x48, 0x83, 0xff, 0x03, //0x000017f5 cmpq         $3, %rdi
	0x0f, 0x8e, 0x3a, 0x37, 0x00, 0x00, //0x000017f9 jle          LBB0_957
	0x41, 0x8b, 0x4e, 0x02, //0x000017ff movl         $2(%r14), %ecx
	0x89, 0xce, //0x00001803 movl         %ecx, %esi
	0xf7, 0xd6, //0x00001805 notl         %esi
	0x8d, 0x81, 0xd0, 0xcf, 0xcf, 0xcf, //0x00001807 leal         $-808464432(%rcx), %eax
	0x81, 0xe6, 0x80, 0x80, 0x80, 0x80, //0x0000180d andl         $-2139062144, %esi
	0x85, 0xc6, //0x00001813 testl        %eax, %esi
	0x0f, 0x85, 0x72, 0x36, 0x00, 0x00, //0x00001815 jne          LBB0_954
	0x8d, 0x81, 0x19, 0x19, 0x19, 0x19, //0x0000181b leal         $421075225(%rcx), %eax
	0x09, 0xc8, //0x00001821 orl          %ecx, %eax
	0xa9, 0x80, 0x80, 0x80, 0x80, //0x00001823 testl        $-2139062144, %eax
	0x0f, 0x85, 0x5f, 0x36, 0x00, 0x00, //0x00001828 jne          LBB0_954
	0x89, 0xc8, //0x0000182e movl         %ecx, %eax
	0x25, 0x7f, 0x7f, 0x7f, 0x7f, //0x00001830 andl         $2139062143, %eax
	0xbb, 0xc0, 0xc0, 0xc0, 0xc0, //0x00001835 movl         $-1061109568, %ebx
	0x29, 0xc3, //0x0000183a subl         %eax, %ebx
	0x8d, 0x90, 0x46, 0x46, 0x46, 0x46, //0x0000183c leal         $1179010630(%rax), %edx
	0x21, 0xf3, //0x00001842 andl         %esi, %ebx
	0x85, 0xd3, //0x00001844 testl        %edx, %ebx
	0x0f, 0x85, 0x41, 0x36, 0x00, 0x00, //0x00001846 jne          LBB0_954
	0xba, 0xe0, 0xe0, 0xe0, 0xe0, //0x0000184c movl         $-522133280, %edx
	0x29, 0xc2, //0x00001851 subl         %eax, %edx
	0x05, 0x39, 0x39, 0x39, 0x39, //0x00001853 addl         $960051513, %eax
	0x21, 0xd6, //0x00001858 andl         %edx, %esi
	0x85, 0xc6, //0x0000185a testl        %eax, %esi
	0x0f, 0x85, 0x2b, 0x36, 0x00, 0x00, //0x0000185c jne          LBB0_954
	0x0f, 0xc9, //0x00001862 bswapl       %ecx
	0x89, 0xc8, //0x00001864 movl         %ecx, %eax
	0xc1, 0xe8, 0x04, //0x00001866 shrl         $4, %eax
	0xf7, 0xd0, //0x00001869 notl         %eax
	0x25, 0x01, 0x01, 0x01, 0x01, //0x0000186b andl         $16843009, %eax
	0x8d, 0x04, 0xc0, //0x00001870 leal         (%rax,%rax,8), %eax
	0x81, 0xe1, 0x0f, 0x0f, 0x0f, 0x0f, //0x00001873 andl         $252645135, %ecx
	0x01, 0xc1, //0x00001879 addl         %eax, %ecx
	0x89, 0xcb, //0x0000187b movl         %ecx, %ebx
	0xc1, 0xeb, 0x04, //0x0000187d shrl         $4, %ebx
	0x09, 0xcb, //0x00001880 orl          %ecx, %ebx
	0x89, 0xde, //0x00001882 movl         %ebx, %esi
	0xc1, 0xee, 0x08, //0x00001884 shrl         $8, %esi
	0x81, 0xe6, 0x00, 0xff, 0x00, 0x00, //0x00001887 andl         $65280, %esi
	0x0f, 0xb6, 0xc3, //0x0000188d movzbl       %bl, %eax
	0x09, 0xf0, //0x00001890 orl          %esi, %eax
	0x4d, 0x8d, 0x46, 0x06, //0x00001892 leaq         $6(%r14), %r8
	0x83, 0xf8, 0x7f, //0x00001896 cmpl         $127, %eax
	0x0f, 0x86, 0x94, 0x00, 0x00, 0x00, //0x00001899 jbe          LBB0_134
	0x3d, 0xff, 0x07, 0x00, 0x00, //0x0000189f cmpl         $2047, %eax
	0x0f, 0x86, 0x97, 0x00, 0x00, 0x00, //0x000018a4 jbe          LBB0_135
	0x89, 0xd9, //0x000018aa movl         %ebx, %ecx
	0x81, 0xe1, 0x00, 0x00, 0xf8, 0x00, //0x000018ac andl         $16252928, %ecx
	0x81, 0xf9, 0x00, 0x00, 0xd8, 0x00, //0x000018b2 cmpl         $14155776, %ecx
	0x0f, 0x84, 0xa2, 0x00, 0x00, 0x00, //0x000018b8 je           LBB0_136
	0xc1, 0xee, 0x0c, //0x000018be shrl         $12, %esi
	0x40, 0x80, 0xce, 0xe0, //0x000018c1 orb          $-32, %sil
	0x40, 0x88, 0x74, 0x24, 0x60, //0x000018c5 movb         %sil, $96(%rsp)
	0xc1, 0xe8, 0x06, //0x000018ca shrl         $6, %eax
	0x24, 0x3f, //0x000018cd andb         $63, %al
	0x0c, 0x80, //0x000018cf orb          $-128, %al
	0x88, 0x44, 0x24, 0x61, //0x000018d1 movb         %al, $97(%rsp)
	0x80, 0xe3, 0x3f, //0x000018d5 andb         $63, %bl
	0x80, 0xcb, 0x80, //0x000018d8 orb          $-128, %bl
	0x88, 0x5c, 0x24, 0x62, //0x000018db movb         %bl, $98(%rsp)
	0xbf, 0x03, 0x00, 0x00, 0x00, //0x000018df movl         $3, %edi
	0x89, 0xf3, //0x000018e4 movl         %esi, %ebx
	//0x000018e6 LBB0_125
	0x4d, 0x89, 0xc6, //0x000018e6 movq         %r8, %r14
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x000018e9 movq         $24(%rsp), %r8
	0x48, 0x8d, 0x44, 0x3c, 0x60, //0x000018ee leaq         $96(%rsp,%rdi), %rax
	0x4d, 0x39, 0xdc, //0x000018f3 cmpq         %r11, %r12
	0x0f, 0x82, 0x81, 0xfe, 0xff, 0xff, //0x000018f6 jb           LBB0_126
	//0x000018fc LBB0_146
	0x48, 0x8d, 0x74, 0x24, 0x60, //0x000018fc leaq         $96(%rsp), %rsi
	0x48, 0x8b, 0x7c, 0x24, 0x20, //0x00001901 movq         $32(%rsp), %rdi
	0xe9, 0x05, 0x00, 0x00, 0x00, //0x00001906 jmp          LBB0_147
	//0x0000190b LBB0_133
	0x48, 0x8d, 0x74, 0x24, 0x60, //0x0000190b leaq         $96(%rsp), %rsi
	//0x00001910 LBB0_147
	0x4c, 0x89, 0xf3, //0x00001910 movq         %r14, %rbx
	//0x00001913 LBB0_148
	0x48, 0x39, 0xc6, //0x00001913 cmpq         %rax, %rsi
	0x0f, 0x85, 0x43, 0x02, 0x00, 0x00, //0x00001916 jne          LBB0_322
	//0x0000191c LBB0_149
	0x49, 0x39, 0xd9, //0x0000191c cmpq         %rbx, %r9
	0x0f, 0x86, 0x09, 0x02, 0x00, 0x00, //0x0000191f jbe          LBB0_152
	0x4d, 0x39, 0xdc, //0x00001925 cmpq         %r11, %r12
	0x0f, 0x82, 0xf9, 0xfd, 0xff, 0xff, //0x00001928 jb           LBB0_109
	0xe9, 0xfb, 0x01, 0x00, 0x00, //0x0000192e jmp          LBB0_152
	//0x00001933 LBB0_134
	0x88, 0x5c, 0x24, 0x60, //0x00001933 movb         %bl, $96(%rsp)
	0xbf, 0x01, 0x00, 0x00, 0x00, //0x00001937 movl         $1, %edi
	0xe9, 0xa5, 0xff, 0xff, 0xff, //0x0000193c jmp          LBB0_125
	//0x00001941 LBB0_135
	0xc1, 0xe8, 0x06, //0x00001941 shrl         $6, %eax
	0x0c, 0xc0, //0x00001944 orb          $-64, %al
	0x88, 0x44, 0x24, 0x60, //0x00001946 movb         %al, $96(%rsp)
	0x80, 0xe3, 0x3f, //0x0000194a andb         $63, %bl
	0x80, 0xcb, 0x80, //0x0000194d orb          $-128, %bl
	0x88, 0x5c, 0x24, 0x61, //0x00001950 movb         %bl, $97(%rsp)
	0xbf, 0x02, 0x00, 0x00, 0x00, //0x00001954 movl         $2, %edi
	0x89, 0xc3, //0x00001959 movl         %eax, %ebx
	0xe9, 0x86, 0xff, 0xff, 0xff, //0x0000195b jmp          LBB0_125
	//0x00001960 LBB0_136
	0x48, 0x83, 0xff, 0x06, //0x00001960 cmpq         $6, %rdi
	0x0f, 0x8c, 0x0a, 0x36, 0x00, 0x00, //0x00001964 jl           LBB0_963
	0x3d, 0xff, 0xdb, 0x00, 0x00, //0x0000196a cmpl         $56319, %eax
	0x0f, 0x87, 0xff, 0x35, 0x00, 0x00, //0x0000196f ja           LBB0_963
	0x41, 0x80, 0x38, 0x5c, //0x00001975 cmpb         $92, (%r8)
	0x0f, 0x85, 0xf5, 0x35, 0x00, 0x00, //0x00001979 jne          LBB0_963
	0x41, 0x80, 0x7e, 0x07, 0x75, //0x0000197f cmpb         $117, $7(%r14)
	0x0f, 0x85, 0xea, 0x35, 0x00, 0x00, //0x00001984 jne          LBB0_963
	0x4c, 0x89, 0xf1, //0x0000198a movq         %r14, %rcx
	0x4d, 0x8d, 0x46, 0x08, //0x0000198d leaq         $8(%r14), %r8
	0x41, 0x8b, 0x7e, 0x08, //0x00001991 movl         $8(%r14), %edi
	0x89, 0xfb, //0x00001995 movl         %edi, %ebx
	0xf7, 0xd3, //0x00001997 notl         %ebx
	0x8d, 0x8f, 0xd0, 0xcf, 0xcf, 0xcf, //0x00001999 leal         $-808464432(%rdi), %ecx
	0x81, 0xe3, 0x80, 0x80, 0x80, 0x80, //0x0000199f andl         $-2139062144, %ebx
	0x85, 0xcb, //0x000019a5 testl        %ecx, %ebx
	0x0f, 0x85, 0xd3, 0x35, 0x00, 0x00, //0x000019a7 jne          LBB0_964
	0x8d, 0x8f, 0x19, 0x19, 0x19, 0x19, //0x000019ad leal         $421075225(%rdi), %ecx
	0x09, 0xf9, //0x000019b3 orl          %edi, %ecx
	0xf7, 0xc1, 0x80, 0x80, 0x80, 0x80, //0x000019b5 testl        $-2139062144, %ecx
	0x0f, 0x85, 0xbf, 0x35, 0x00, 0x00, //0x000019bb jne          LBB0_964
	0x89, 0xf9, //0x000019c1 movl         %edi, %ecx
	0x81, 0xe1, 0x7f, 0x7f, 0x7f, 0x7f, //0x000019c3 andl         $2139062143, %ecx
	0xba, 0xc0, 0xc0, 0xc0, 0xc0, //0x000019c9 movl         $-1061109568, %edx
	0x29, 0xca, //0x000019ce subl         %ecx, %edx
	0x8d, 0xb1, 0x46, 0x46, 0x46, 0x46, //0x000019d0 leal         $1179010630(%rcx), %esi
	0x21, 0xda, //0x000019d6 andl         %ebx, %edx
	0x85, 0xf2, //0x000019d8 testl        %esi, %edx
	0x0f, 0x85, 0xa0, 0x35, 0x00, 0x00, //0x000019da jne          LBB0_964
	0xba, 0xe0, 0xe0, 0xe0, 0xe0, //0x000019e0 movl         $-522133280, %edx
	0x29, 0xca, //0x000019e5 subl         %ecx, %edx
	0x81, 0xc1, 0x39, 0x39, 0x39, 0x39, //0x000019e7 addl         $960051513, %ecx
	0x21, 0xd3, //0x000019ed andl         %edx, %ebx
	0x85, 0xcb, //0x000019ef testl        %ecx, %ebx
	0x0f, 0x85, 0x89, 0x35, 0x00, 0x00, //0x000019f1 jne          LBB0_964
	0x0f, 0xcf, //0x000019f7 bswapl       %edi
	0x89, 0xf9, //0x000019f9 movl         %edi, %ecx
	0xc1, 0xe9, 0x04, //0x000019fb shrl         $4, %ecx
	0xf7, 0xd1, //0x000019fe notl         %ecx
	0x81, 0xe1, 0x01, 0x01, 0x01, 0x01, //0x00001a00 andl         $16843009, %ecx
	0x8d, 0x0c, 0xc9, //0x00001a06 leal         (%rcx,%rcx,8), %ecx
	0x81, 0xe7, 0x0f, 0x0f, 0x0f, 0x0f, //0x00001a09 andl         $252645135, %edi
	0x01, 0xcf, //0x00001a0f addl         %ecx, %edi
	0x89, 0xf9, //0x00001a11 movl         %edi, %ecx
	0xc1, 0xe9, 0x04, //0x00001a13 shrl         $4, %ecx
	0x09, 0xf9, //0x00001a16 orl          %edi, %ecx
	0x89, 0xca, //0x00001a18 movl         %ecx, %edx
	0x81, 0xe2, 0x00, 0x00, 0xfc, 0x00, //0x00001a1a andl         $16515072, %edx
	0x81, 0xfa, 0x00, 0x00, 0xdc, 0x00, //0x00001a20 cmpl         $14417920, %edx
	0x0f, 0x85, 0x48, 0x35, 0x00, 0x00, //0x00001a26 jne          LBB0_963
	0x89, 0xca, //0x00001a2c movl         %ecx, %edx
	0xc1, 0xea, 0x08, //0x00001a2e shrl         $8, %edx
	0x81, 0xe2, 0x00, 0xff, 0x00, 0x00, //0x00001a31 andl         $65280, %edx
	0x0f, 0xb6, 0xc9, //0x00001a37 movzbl       %cl, %ecx
	0x09, 0xd1, //0x00001a3a orl          %edx, %ecx
	0xc1, 0xe0, 0x0a, //0x00001a3c shll         $10, %eax
	0x8d, 0x84, 0x08, 0x00, 0x24, 0xa0, 0xfc, //0x00001a3f leal         $-56613888(%rax,%rcx), %eax
	0x89, 0xc3, //0x00001a46 movl         %eax, %ebx
	0xc1, 0xeb, 0x12, //0x00001a48 shrl         $18, %ebx
	0x80, 0xcb, 0xf0, //0x00001a4b orb          $-16, %bl
	0x88, 0x5c, 0x24, 0x60, //0x00001a4e movb         %bl, $96(%rsp)
	0x89, 0xc1, //0x00001a52 movl         %eax, %ecx
	0xc1, 0xe9, 0x0c, //0x00001a54 shrl         $12, %ecx
	0x80, 0xe1, 0x3f, //0x00001a57 andb         $63, %cl
	0x80, 0xc9, 0x80, //0x00001a5a orb          $-128, %cl
	0x88, 0x4c, 0x24, 0x61, //0x00001a5d movb         %cl, $97(%rsp)
	0x89, 0xc1, //0x00001a61 movl         %eax, %ecx
	0xc1, 0xe9, 0x06, //0x00001a63 shrl         $6, %ecx
	0x80, 0xe1, 0x3f, //0x00001a66 andb         $63, %cl
	0x80, 0xc9, 0x80, //0x00001a69 orb          $-128, %cl
	0x88, 0x4c, 0x24, 0x62, //0x00001a6c movb         %cl, $98(%rsp)
	0x24, 0x3f, //0x00001a70 andb         $63, %al
	0x0c, 0x80, //0x00001a72 orb          $-128, %al
	0x88, 0x44, 0x24, 0x63, //0x00001a74 movb         %al, $99(%rsp)
	0x49, 0x83, 0xc6, 0x0c, //0x00001a78 addq         $12, %r14
	0xbf, 0x04, 0x00, 0x00, 0x00, //0x00001a7c movl         $4, %edi
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x00001a81 movq         $24(%rsp), %r8
	0x4c, 0x8b, 0x7c, 0x24, 0x28, //0x00001a86 movq         $40(%rsp), %r15
	0x48, 0x8d, 0x44, 0x3c, 0x60, //0x00001a8b leaq         $96(%rsp,%rdi), %rax
	0x4d, 0x39, 0xdc, //0x00001a90 cmpq         %r11, %r12
	0x0f, 0x82, 0xe4, 0xfc, 0xff, 0xff, //0x00001a93 jb           LBB0_126
	0xe9, 0x5e, 0xfe, 0xff, 0xff, //0x00001a99 jmp          LBB0_146
	//0x00001a9e LBB0_258
	0x4d, 0x85, 0xdb, //0x00001a9e testq        %r11, %r11
	0x0f, 0x85, 0x6e, 0x02, 0x00, 0x00, //0x00001aa1 jne          LBB0_339
	0x4a, 0x8d, 0x4c, 0x08, 0x01, //0x00001aa7 leaq         $1(%rax,%r9), %rcx
	0x48, 0xf7, 0xd0, //0x00001aac notq         %rax
	0x4c, 0x01, 0xc0, //0x00001aaf addq         %r8, %rax
	//0x00001ab2 LBB0_260
	0x4c, 0x8b, 0x5c, 0x24, 0x38, //0x00001ab2 movq         $56(%rsp), %r11
	//0x00001ab7 LBB0_261
	0x48, 0x85, 0xc0, //0x00001ab7 testq        %rax, %rax
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x00001aba movq         $24(%rsp), %r8
	0x49, 0xb9, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00001abf movabsq      $4294977024, %r9
	0x0f, 0x8f, 0x1d, 0x00, 0x00, 0x00, //0x00001ac9 jg           LBB0_318
	0xe9, 0x5c, 0xf2, 0xff, 0xff, //0x00001acf jmp          LBB0_222
	//0x00001ad4 LBB0_317
	0x48, 0xc7, 0xc2, 0xfe, 0xff, 0xff, 0xff, //0x00001ad4 movq         $-2, %rdx
	0xbe, 0x02, 0x00, 0x00, 0x00, //0x00001adb movl         $2, %esi
	0x48, 0x01, 0xf1, //0x00001ae0 addq         %rsi, %rcx
	0x48, 0x01, 0xd0, //0x00001ae3 addq         %rdx, %rax
	0x0f, 0x8e, 0x44, 0xf2, 0xff, 0xff, //0x00001ae6 jle          LBB0_222
	//0x00001aec LBB0_318
	0x0f, 0xb6, 0x11, //0x00001aec movzbl       (%rcx), %edx
	0x80, 0xfa, 0x5c, //0x00001aef cmpb         $92, %dl
	0x0f, 0x84, 0xdc, 0xff, 0xff, 0xff, //0x00001af2 je           LBB0_317
	0x80, 0xfa, 0x22, //0x00001af8 cmpb         $34, %dl
	0x0f, 0x84, 0x1d, 0x00, 0x00, 0x00, //0x00001afb je           LBB0_321
	0x48, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x00001b01 movq         $-1, %rdx
	0xbe, 0x01, 0x00, 0x00, 0x00, //0x00001b08 movl         $1, %esi
	0x48, 0x01, 0xf1, //0x00001b0d addq         %rsi, %rcx
	0x48, 0x01, 0xd0, //0x00001b10 addq         %rdx, %rax
	0x0f, 0x8f, 0xd3, 0xff, 0xff, 0xff, //0x00001b13 jg           LBB0_318
	0xe9, 0x12, 0xf2, 0xff, 0xff, //0x00001b19 jmp          LBB0_222
	//0x00001b1e LBB0_321
	0x48, 0x29, 0xf9, //0x00001b1e subq         %rdi, %rcx
	0x48, 0xff, 0xc1, //0x00001b21 incq         %rcx
	0xe9, 0xa0, 0xf7, 0xff, 0xff, //0x00001b24 jmp          LBB0_289
	//0x00001b29 LBB0_151
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x00001b29 movq         $24(%rsp), %r8
	//0x00001b2e LBB0_152
	0x49, 0x31, 0xd9, //0x00001b2e xorq         %rbx, %r9
	0x4d, 0x31, 0xdc, //0x00001b31 xorq         %r11, %r12
	0x31, 0xd2, //0x00001b34 xorl         %edx, %edx
	0x4d, 0x09, 0xcc, //0x00001b36 orq          %r9, %r12
	0x0f, 0x94, 0xc2, //0x00001b39 sete         %dl
	//0x00001b3c LBB0_153
	0x4c, 0x8b, 0x5c, 0x24, 0x38, //0x00001b3c movq         $56(%rsp), %r11
	0x49, 0xb9, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00001b41 movabsq      $4294977024, %r9
	0x49, 0x8b, 0x0b, //0x00001b4b movq         (%r11), %rcx
	0x4c, 0x89, 0xd6, //0x00001b4e movq         %r10, %rsi
	0x48, 0x29, 0xce, //0x00001b51 subq         %rcx, %rsi
	0x0f, 0x82, 0xc5, 0xed, 0xff, 0xff, //0x00001b54 jb           LBB0_101
	0xe9, 0x21, 0xee, 0xff, 0xff, //0x00001b5a jmp          LBB0_154
	//0x00001b5f LBB0_322
	0x31, 0xd2, //0x00001b5f xorl         %edx, %edx
	0xe9, 0xd6, 0xff, 0xff, 0xff, //0x00001b61 jmp          LBB0_153
	//0x00001b66 LBB0_323
	0x4a, 0x8d, 0x0c, 0x17, //0x00001b66 leaq         (%rdi,%r10), %rcx
	0xe9, 0x43, 0xff, 0xff, 0xff, //0x00001b6a jmp          LBB0_260
	//0x00001b6f LBB0_324
	0x4c, 0x89, 0xd0, //0x00001b6f movq         %r10, %rax
	0x48, 0x2b, 0x44, 0x24, 0x20, //0x00001b72 subq         $32(%rsp), %rax
	0x4c, 0x0f, 0xbc, 0xc7, //0x00001b77 bsfq         %rdi, %r8
	0x49, 0x01, 0xc0, //0x00001b7b addq         %rax, %r8
	0x4c, 0x09, 0xce, //0x00001b7e orq          %r9, %rsi
	0x48, 0x89, 0xf8, //0x00001b81 movq         %rdi, %rax
	0x48, 0x09, 0xd8, //0x00001b84 orq          %rbx, %rax
	0x0f, 0x84, 0x15, 0xfb, 0xff, 0xff, //0x00001b87 je           LBB0_72
	//0x00001b8d LBB0_325
	0x4d, 0x89, 0xd6, //0x00001b8d movq         %r10, %r14
	0x41, 0x89, 0xda, //0x00001b90 movl         %ebx, %r10d
	0x41, 0xf7, 0xd2, //0x00001b93 notl         %r10d
	0x41, 0x21, 0xfa, //0x00001b96 andl         %edi, %r10d
	0x47, 0x8d, 0x0c, 0x12, //0x00001b99 leal         (%r10,%r10), %r9d
	0x41, 0x09, 0xd9, //0x00001b9d orl          %ebx, %r9d
	0x44, 0x89, 0xc8, //0x00001ba0 movl         %r9d, %eax
	0xf7, 0xd0, //0x00001ba3 notl         %eax
	0x21, 0xf8, //0x00001ba5 andl         %edi, %eax
	0x25, 0xaa, 0xaa, 0xaa, 0xaa, //0x00001ba7 andl         $-1431655766, %eax
	0x31, 0xdb, //0x00001bac xorl         %ebx, %ebx
	0x44, 0x01, 0xd0, //0x00001bae addl         %r10d, %eax
	0x4d, 0x89, 0xf2, //0x00001bb1 movq         %r14, %r10
	0x0f, 0x92, 0xc3, //0x00001bb4 setb         %bl
	0x01, 0xc0, //0x00001bb7 addl         %eax, %eax
	0x35, 0x55, 0x55, 0x55, 0x55, //0x00001bb9 xorl         $1431655765, %eax
	0x44, 0x21, 0xc8, //0x00001bbe andl         %r9d, %eax
	0xf7, 0xd0, //0x00001bc1 notl         %eax
	0x21, 0xc6, //0x00001bc3 andl         %eax, %esi
	0x49, 0xb9, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00001bc5 movabsq      $4294977024, %r9
	0x48, 0x8b, 0x7c, 0x24, 0x20, //0x00001bcf movq         $32(%rsp), %rdi
	0x48, 0x85, 0xf6, //0x00001bd4 testq        %rsi, %rsi
	0x0f, 0x85, 0xdd, 0xfa, 0xff, 0xff, //0x00001bd7 jne          LBB0_73
	//0x00001bdd LBB0_326
	0x49, 0x83, 0xc2, 0x20, //0x00001bdd addq         $32, %r10
	0x48, 0x83, 0xc1, 0xe0, //0x00001be1 addq         $-32, %rcx
	//0x00001be5 LBB0_327
	0x48, 0x85, 0xdb, //0x00001be5 testq        %rbx, %rbx
	0x48, 0x8b, 0x74, 0x24, 0x48, //0x00001be8 movq         $72(%rsp), %rsi
	0x0f, 0x85, 0xb2, 0x00, 0x00, 0x00, //0x00001bed jne          LBB0_336
	0x4d, 0x89, 0xc1, //0x00001bf3 movq         %r8, %r9
	0x48, 0x85, 0xc9, //0x00001bf6 testq        %rcx, %rcx
	0x0f, 0x84, 0x40, 0x31, 0x00, 0x00, //0x00001bf9 je           LBB0_937
	//0x00001bff LBB0_329
	0x48, 0xf7, 0xd7, //0x00001bff notq         %rdi
	//0x00001c02 LBB0_330
	0x4c, 0x89, 0xd3, //0x00001c02 movq         %r10, %rbx
	0x49, 0xff, 0xc2, //0x00001c05 incq         %r10
	0x48, 0x89, 0xd8, //0x00001c08 movq         %rbx, %rax
	0x0f, 0xb6, 0x1b, //0x00001c0b movzbl       (%rbx), %ebx
	0x80, 0xfb, 0x22, //0x00001c0e cmpb         $34, %bl
	0x0f, 0x84, 0x66, 0x00, 0x00, 0x00, //0x00001c11 je           LBB0_335
	0x48, 0x8d, 0x71, 0xff, //0x00001c17 leaq         $-1(%rcx), %rsi
	0x80, 0xfb, 0x5c, //0x00001c1b cmpb         $92, %bl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00001c1e je           LBB0_333
	0x48, 0x89, 0xf1, //0x00001c24 movq         %rsi, %rcx
	0x48, 0x85, 0xf6, //0x00001c27 testq        %rsi, %rsi
	0x48, 0x8b, 0x74, 0x24, 0x48, //0x00001c2a movq         $72(%rsp), %rsi
	0x0f, 0x85, 0xcd, 0xff, 0xff, 0xff, //0x00001c2f jne          LBB0_330
	0xe9, 0x05, 0x31, 0x00, 0x00, //0x00001c35 jmp          LBB0_937
	//0x00001c3a LBB0_333
	0x48, 0x85, 0xf6, //0x00001c3a testq        %rsi, %rsi
	0x0f, 0x84, 0xfc, 0x30, 0x00, 0x00, //0x00001c3d je           LBB0_937
	0x49, 0x01, 0xfa, //0x00001c43 addq         %rdi, %r10
	0x49, 0x83, 0xf9, 0xff, //0x00001c46 cmpq         $-1, %r9
	0x4d, 0x0f, 0x44, 0xc2, //0x00001c4a cmoveq       %r10, %r8
	0x4d, 0x0f, 0x44, 0xca, //0x00001c4e cmoveq       %r10, %r9
	0x49, 0x89, 0xc2, //0x00001c52 movq         %rax, %r10
	0x49, 0x83, 0xc2, 0x02, //0x00001c55 addq         $2, %r10
	0x48, 0x83, 0xc1, 0xfe, //0x00001c59 addq         $-2, %rcx
	0x48, 0x89, 0xce, //0x00001c5d movq         %rcx, %rsi
	0x4c, 0x8b, 0x7c, 0x24, 0x28, //0x00001c60 movq         $40(%rsp), %r15
	0x4c, 0x8b, 0x5c, 0x24, 0x38, //0x00001c65 movq         $56(%rsp), %r11
	0x48, 0x85, 0xf6, //0x00001c6a testq        %rsi, %rsi
	0x48, 0x8b, 0x74, 0x24, 0x48, //0x00001c6d movq         $72(%rsp), %rsi
	0x0f, 0x85, 0x8a, 0xff, 0xff, 0xff, //0x00001c72 jne          LBB0_330
	0xe9, 0xc2, 0x30, 0x00, 0x00, //0x00001c78 jmp          LBB0_937
	//0x00001c7d LBB0_335
	0x48, 0x8b, 0x7c, 0x24, 0x20, //0x00001c7d movq         $32(%rsp), %rdi
	0x49, 0x29, 0xfa, //0x00001c82 subq         %rdi, %r10
	0x49, 0xb9, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00001c85 movabsq      $4294977024, %r9
	0x4d, 0x85, 0xd2, //0x00001c8f testq        %r10, %r10
	0x48, 0x8b, 0x9c, 0x24, 0xb0, 0x00, 0x00, 0x00, //0x00001c92 movq         $176(%rsp), %rbx
	0x0f, 0x89, 0xe5, 0xea, 0xff, 0xff, //0x00001c9a jns          LBB0_79
	0xe9, 0x9a, 0x30, 0x00, 0x00, //0x00001ca0 jmp          LBB0_937
	//0x00001ca5 LBB0_336
	0x48, 0x85, 0xc9, //0x00001ca5 testq        %rcx, %rcx
	0x0f, 0x84, 0x91, 0x30, 0x00, 0x00, //0x00001ca8 je           LBB0_937
	0x48, 0x8b, 0x7c, 0x24, 0x20, //0x00001cae movq         $32(%rsp), %rdi
	0x49, 0x89, 0xf9, //0x00001cb3 movq         %rdi, %r9
	0x49, 0xf7, 0xd1, //0x00001cb6 notq         %r9
	0x4d, 0x01, 0xd1, //0x00001cb9 addq         %r10, %r9
	0x49, 0x83, 0xf8, 0xff, //0x00001cbc cmpq         $-1, %r8
	0x4c, 0x89, 0xc0, //0x00001cc0 movq         %r8, %rax
	0x49, 0x0f, 0x44, 0xc1, //0x00001cc3 cmoveq       %r9, %rax
	0x4d, 0x0f, 0x45, 0xc8, //0x00001cc7 cmovneq      %r8, %r9
	0x49, 0xff, 0xc2, //0x00001ccb incq         %r10
	0x48, 0xff, 0xc9, //0x00001cce decq         %rcx
	0x49, 0x89, 0xc0, //0x00001cd1 movq         %rax, %r8
	0x4c, 0x8b, 0x7c, 0x24, 0x28, //0x00001cd4 movq         $40(%rsp), %r15
	0x4c, 0x8b, 0x5c, 0x24, 0x38, //0x00001cd9 movq         $56(%rsp), %r11
	0x48, 0x8b, 0x74, 0x24, 0x48, //0x00001cde movq         $72(%rsp), %rsi
	0x48, 0x85, 0xc9, //0x00001ce3 testq        %rcx, %rcx
	0x0f, 0x85, 0x13, 0xff, 0xff, 0xff, //0x00001ce6 jne          LBB0_329
	0xe9, 0x4e, 0x30, 0x00, 0x00, //0x00001cec jmp          LBB0_937
	//0x00001cf1 LBB0_338
	0x4c, 0x8b, 0x5c, 0x24, 0x38, //0x00001cf1 movq         $56(%rsp), %r11
	0x4d, 0x8b, 0x13, //0x00001cf6 movq         (%r11), %r10
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x00001cf9 movq         $24(%rsp), %r8
	0x4d, 0x89, 0x10, //0x00001cfe movq         %r10, (%r8)
	0x4c, 0x8b, 0x7c, 0x24, 0x28, //0x00001d01 movq         $40(%rsp), %r15
	0x49, 0xb9, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00001d06 movabsq      $4294977024, %r9
	0xe9, 0x1b, 0xf0, 0xff, 0xff, //0x00001d10 jmp          LBB0_222
	//0x00001d15 LBB0_339
	0x49, 0x8d, 0x48, 0xff, //0x00001d15 leaq         $-1(%r8), %rcx
	0x48, 0x39, 0xc1, //0x00001d19 cmpq         %rax, %rcx
	0x0f, 0x85, 0x0f, 0x00, 0x00, 0x00, //0x00001d1c jne          LBB0_342
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x00001d22 movq         $24(%rsp), %r8
	0x4c, 0x8b, 0x7c, 0x24, 0x28, //0x00001d27 movq         $40(%rsp), %r15
	0xe9, 0x5f, 0xf2, 0xff, 0xff, //0x00001d2c jmp          LBB0_341
	//0x00001d31 LBB0_342
	0x4a, 0x8d, 0x4c, 0x08, 0x02, //0x00001d31 leaq         $2(%rax,%r9), %rcx
	0x49, 0x29, 0xc0, //0x00001d36 subq         %rax, %r8
	0x49, 0x83, 0xc0, 0xfe, //0x00001d39 addq         $-2, %r8
	0x4c, 0x89, 0xc0, //0x00001d3d movq         %r8, %rax
	0x4c, 0x8b, 0x7c, 0x24, 0x28, //0x00001d40 movq         $40(%rsp), %r15
	0x4c, 0x8b, 0x5c, 0x24, 0x38, //0x00001d45 movq         $56(%rsp), %r11
	0x48, 0x8b, 0x7c, 0x24, 0x20, //0x00001d4a movq         $32(%rsp), %rdi
	0xe9, 0x63, 0xfd, 0xff, 0xff, //0x00001d4f jmp          LBB0_261
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001d54 .p2align 4, 0x90
	//0x00001d60 LBB0_350
	0x49, 0x8d, 0x52, 0x01, //0x00001d60 leaq         $1(%r10), %rdx
	0x48, 0x39, 0xc2, //0x00001d64 cmpq         %rax, %rdx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00001d67 jae          LBB0_354
	0x8a, 0x1c, 0x17, //0x00001d6d movb         (%rdi,%rdx), %bl
	0x80, 0xfb, 0x0d, //0x00001d70 cmpb         $13, %bl
	0x0f, 0x84, 0x17, 0x00, 0x00, 0x00, //0x00001d73 je           LBB0_354
	0x80, 0xfb, 0x20, //0x00001d79 cmpb         $32, %bl
	0x0f, 0x84, 0x0e, 0x00, 0x00, 0x00, //0x00001d7c je           LBB0_354
	0x80, 0xc3, 0xf7, //0x00001d82 addb         $-9, %bl
	0x80, 0xfb, 0x01, //0x00001d85 cmpb         $1, %bl
	0x0f, 0x87, 0x03, 0x01, 0x00, 0x00, //0x00001d88 ja           LBB0_372
	0x90, 0x90, //0x00001d8e .p2align 4, 0x90
	//0x00001d90 LBB0_354
	0x49, 0x8d, 0x52, 0x02, //0x00001d90 leaq         $2(%r10), %rdx
	0x48, 0x39, 0xc2, //0x00001d94 cmpq         %rax, %rdx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00001d97 jae          LBB0_358
	0x8a, 0x1c, 0x17, //0x00001d9d movb         (%rdi,%rdx), %bl
	0x80, 0xfb, 0x0d, //0x00001da0 cmpb         $13, %bl
	0x0f, 0x84, 0x17, 0x00, 0x00, 0x00, //0x00001da3 je           LBB0_358
	0x80, 0xfb, 0x20, //0x00001da9 cmpb         $32, %bl
	0x0f, 0x84, 0x0e, 0x00, 0x00, 0x00, //0x00001dac je           LBB0_358
	0x80, 0xc3, 0xf7, //0x00001db2 addb         $-9, %bl
	0x80, 0xfb, 0x01, //0x00001db5 cmpb         $1, %bl
	0x0f, 0x87, 0xd3, 0x00, 0x00, 0x00, //0x00001db8 ja           LBB0_372
	0x90, 0x90, //0x00001dbe .p2align 4, 0x90
	//0x00001dc0 LBB0_358
	0x49, 0x8d, 0x52, 0x03, //0x00001dc0 leaq         $3(%r10), %rdx
	0x48, 0x39, 0xc2, //0x00001dc4 cmpq         %rax, %rdx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00001dc7 jae          LBB0_362
	0x8a, 0x1c, 0x17, //0x00001dcd movb         (%rdi,%rdx), %bl
	0x80, 0xfb, 0x0d, //0x00001dd0 cmpb         $13, %bl
	0x0f, 0x84, 0x17, 0x00, 0x00, 0x00, //0x00001dd3 je           LBB0_362
	0x80, 0xfb, 0x20, //0x00001dd9 cmpb         $32, %bl
	0x0f, 0x84, 0x0e, 0x00, 0x00, 0x00, //0x00001ddc je           LBB0_362
	0x80, 0xc3, 0xf7, //0x00001de2 addb         $-9, %bl
	0x80, 0xfb, 0x01, //0x00001de5 cmpb         $1, %bl
	0x0f, 0x87, 0xa3, 0x00, 0x00, 0x00, //0x00001de8 ja           LBB0_372
	0x90, 0x90, //0x00001dee .p2align 4, 0x90
	//0x00001df0 LBB0_362
	0x49, 0x8d, 0x72, 0x04, //0x00001df0 leaq         $4(%r10), %rsi
	0x48, 0x39, 0xf0, //0x00001df4 cmpq         %rsi, %rax
	0x0f, 0x86, 0x5d, 0x00, 0x00, 0x00, //0x00001df7 jbe          LBB0_369
	0x48, 0x39, 0xf0, //0x00001dfd cmpq         %rsi, %rax
	0x0f, 0x84, 0x5f, 0x00, 0x00, 0x00, //0x00001e00 je           LBB0_370
	0x48, 0x8d, 0x34, 0x07, //0x00001e06 leaq         (%rdi,%rax), %rsi
	0x48, 0x83, 0xc1, 0x04, //0x00001e0a addq         $4, %rcx
	0x48, 0x89, 0xfb, //0x00001e0e movq         %rdi, %rbx
	0x4a, 0x8d, 0x54, 0x17, 0x05, //0x00001e11 leaq         $5(%rdi,%r10), %rdx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001e16 .p2align 4, 0x90
	//0x00001e20 LBB0_365
	0x0f, 0xbe, 0x7a, 0xff, //0x00001e20 movsbl       $-1(%rdx), %edi
	0x83, 0xff, 0x20, //0x00001e24 cmpl         $32, %edi
	0x0f, 0x87, 0x4f, 0x00, 0x00, 0x00, //0x00001e27 ja           LBB0_371
	0x49, 0x0f, 0xa3, 0xf9, //0x00001e2d btq          %rdi, %r9
	0x0f, 0x83, 0x45, 0x00, 0x00, 0x00, //0x00001e31 jae          LBB0_371
	0x48, 0xff, 0xc2, //0x00001e37 incq         %rdx
	0x48, 0xff, 0xc1, //0x00001e3a incq         %rcx
	0x0f, 0x85, 0xdd, 0xff, 0xff, 0xff, //0x00001e3d jne          LBB0_365
	0x48, 0x89, 0xdf, //0x00001e43 movq         %rbx, %rdi
	0x48, 0x29, 0xfe, //0x00001e46 subq         %rdi, %rsi
	0x48, 0x89, 0xf2, //0x00001e49 movq         %rsi, %rdx
	0x48, 0x39, 0xc2, //0x00001e4c cmpq         %rax, %rdx
	0x0f, 0x82, 0x3c, 0x00, 0x00, 0x00, //0x00001e4f jb           LBB0_372
	0xe9, 0x48, 0x00, 0x00, 0x00, //0x00001e55 jmp          LBB0_373
	//0x00001e5a LBB0_369
	0x49, 0x89, 0x30, //0x00001e5a movq         %rsi, (%r8)
	0x49, 0x89, 0xf2, //0x00001e5d movq         %rsi, %r10
	0xe9, 0x3d, 0x00, 0x00, 0x00, //0x00001e60 jmp          LBB0_373
	//0x00001e65 LBB0_370
	0x48, 0x01, 0xfe, //0x00001e65 addq         %rdi, %rsi
	0x48, 0x29, 0xfe, //0x00001e68 subq         %rdi, %rsi
	0x48, 0x89, 0xf2, //0x00001e6b movq         %rsi, %rdx
	0x48, 0x39, 0xc2, //0x00001e6e cmpq         %rax, %rdx
	0x0f, 0x82, 0x1a, 0x00, 0x00, 0x00, //0x00001e71 jb           LBB0_372
	0xe9, 0x26, 0x00, 0x00, 0x00, //0x00001e77 jmp          LBB0_373
	//0x00001e7c LBB0_371
	0x48, 0x89, 0xdf, //0x00001e7c movq         %rbx, %rdi
	0x48, 0x89, 0xd9, //0x00001e7f movq         %rbx, %rcx
	0x48, 0xf7, 0xd1, //0x00001e82 notq         %rcx
	0x48, 0x01, 0xca, //0x00001e85 addq         %rcx, %rdx
	0x48, 0x39, 0xc2, //0x00001e88 cmpq         %rax, %rdx
	0x0f, 0x83, 0x11, 0x00, 0x00, 0x00, //0x00001e8b jae          LBB0_373
	//0x00001e91 LBB0_372
	0x4c, 0x8d, 0x52, 0x01, //0x00001e91 leaq         $1(%rdx), %r10
	0x4d, 0x89, 0x10, //0x00001e95 movq         %r10, (%r8)
	0x80, 0x3c, 0x17, 0x5d, //0x00001e98 cmpb         $93, (%rdi,%rdx)
	0x0f, 0x84, 0xb5, 0x24, 0x00, 0x00, //0x00001e9c je           LBB0_833
	//0x00001ea2 LBB0_373
	0x49, 0xff, 0xca, //0x00001ea2 decq         %r10
	0x4d, 0x89, 0x10, //0x00001ea5 movq         %r10, (%r8)
	0x4d, 0x85, 0xf6, //0x00001ea8 testq        %r14, %r14
	0x0f, 0x8e, 0xff, 0x0c, 0x00, 0x00, //0x00001eab jle          LBB0_446
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001eb1 .p2align 4, 0x90
	//0x00001ec0 LBB0_374
	0x49, 0x8b, 0x03, //0x00001ec0 movq         (%r11), %rax
	0x4c, 0x89, 0xd2, //0x00001ec3 movq         %r10, %rdx
	0x48, 0x29, 0xc2, //0x00001ec6 subq         %rax, %rdx
	0x0f, 0x83, 0x31, 0x00, 0x00, 0x00, //0x00001ec9 jae          LBB0_379
	0x42, 0x8a, 0x0c, 0x17, //0x00001ecf movb         (%rdi,%r10), %cl
	0x80, 0xf9, 0x0d, //0x00001ed3 cmpb         $13, %cl
	0x0f, 0x84, 0x24, 0x00, 0x00, 0x00, //0x00001ed6 je           LBB0_379
	0x80, 0xf9, 0x20, //0x00001edc cmpb         $32, %cl
	0x0f, 0x84, 0x1b, 0x00, 0x00, 0x00, //0x00001edf je           LBB0_379
	0x80, 0xc1, 0xf7, //0x00001ee5 addb         $-9, %cl
	0x80, 0xf9, 0x01, //0x00001ee8 cmpb         $1, %cl
	0x0f, 0x86, 0x0f, 0x00, 0x00, 0x00, //0x00001eeb jbe          LBB0_379
	0x4c, 0x89, 0xd1, //0x00001ef1 movq         %r10, %rcx
	0xe9, 0x47, 0x01, 0x00, 0x00, //0x00001ef4 jmp          LBB0_401
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001ef9 .p2align 4, 0x90
	//0x00001f00 LBB0_379
	0x49, 0x8d, 0x4a, 0x01, //0x00001f00 leaq         $1(%r10), %rcx
	0x48, 0x39, 0xc1, //0x00001f04 cmpq         %rax, %rcx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00001f07 jae          LBB0_383
	0x8a, 0x1c, 0x0f, //0x00001f0d movb         (%rdi,%rcx), %bl
	0x80, 0xfb, 0x0d, //0x00001f10 cmpb         $13, %bl
	0x0f, 0x84, 0x17, 0x00, 0x00, 0x00, //0x00001f13 je           LBB0_383
	0x80, 0xfb, 0x20, //0x00001f19 cmpb         $32, %bl
	0x0f, 0x84, 0x0e, 0x00, 0x00, 0x00, //0x00001f1c je           LBB0_383
	0x80, 0xc3, 0xf7, //0x00001f22 addb         $-9, %bl
	0x80, 0xfb, 0x01, //0x00001f25 cmpb         $1, %bl
	0x0f, 0x87, 0x12, 0x01, 0x00, 0x00, //0x00001f28 ja           LBB0_401
	0x90, 0x90, //0x00001f2e .p2align 4, 0x90
	//0x00001f30 LBB0_383
	0x49, 0x8d, 0x4a, 0x02, //0x00001f30 leaq         $2(%r10), %rcx
	0x48, 0x39, 0xc1, //0x00001f34 cmpq         %rax, %rcx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00001f37 jae          LBB0_387
	0x8a, 0x1c, 0x0f, //0x00001f3d movb         (%rdi,%rcx), %bl
	0x80, 0xfb, 0x0d, //0x00001f40 cmpb         $13, %bl
	0x0f, 0x84, 0x17, 0x00, 0x00, 0x00, //0x00001f43 je           LBB0_387
	0x80, 0xfb, 0x20, //0x00001f49 cmpb         $32, %bl
	0x0f, 0x84, 0x0e, 0x00, 0x00, 0x00, //0x00001f4c je           LBB0_387
	0x80, 0xc3, 0xf7, //0x00001f52 addb         $-9, %bl
	0x80, 0xfb, 0x01, //0x00001f55 cmpb         $1, %bl
	0x0f, 0x87, 0xe2, 0x00, 0x00, 0x00, //0x00001f58 ja           LBB0_401
	0x90, 0x90, //0x00001f5e .p2align 4, 0x90
	//0x00001f60 LBB0_387
	0x49, 0x8d, 0x4a, 0x03, //0x00001f60 leaq         $3(%r10), %rcx
	0x48, 0x39, 0xc1, //0x00001f64 cmpq         %rax, %rcx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00001f67 jae          LBB0_391
	0x8a, 0x1c, 0x0f, //0x00001f6d movb         (%rdi,%rcx), %bl
	0x80, 0xfb, 0x0d, //0x00001f70 cmpb         $13, %bl
	0x0f, 0x84, 0x17, 0x00, 0x00, 0x00, //0x00001f73 je           LBB0_391
	0x80, 0xfb, 0x20, //0x00001f79 cmpb         $32, %bl
	0x0f, 0x84, 0x0e, 0x00, 0x00, 0x00, //0x00001f7c je           LBB0_391
	0x80, 0xc3, 0xf7, //0x00001f82 addb         $-9, %bl
	0x80, 0xfb, 0x01, //0x00001f85 cmpb         $1, %bl
	0x0f, 0x87, 0xb2, 0x00, 0x00, 0x00, //0x00001f88 ja           LBB0_401
	0x90, 0x90, //0x00001f8e .p2align 4, 0x90
	//0x00001f90 LBB0_391
	0x49, 0x8d, 0x72, 0x04, //0x00001f90 leaq         $4(%r10), %rsi
	0x48, 0x39, 0xf0, //0x00001f94 cmpq         %rsi, %rax
	0x0f, 0x86, 0x63, 0x00, 0x00, 0x00, //0x00001f97 jbe          LBB0_398
	0x48, 0x39, 0xf0, //0x00001f9d cmpq         %rsi, %rax
	0x0f, 0x84, 0x65, 0x00, 0x00, 0x00, //0x00001fa0 je           LBB0_399
	0x48, 0x8d, 0x34, 0x07, //0x00001fa6 leaq         (%rdi,%rax), %rsi
	0x48, 0x83, 0xc2, 0x04, //0x00001faa addq         $4, %rdx
	0x48, 0x89, 0xfb, //0x00001fae movq         %rdi, %rbx
	0x4a, 0x8d, 0x4c, 0x17, 0x05, //0x00001fb1 leaq         $5(%rdi,%r10), %rcx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001fb6 .p2align 4, 0x90
	//0x00001fc0 LBB0_394
	0x0f, 0xbe, 0x79, 0xff, //0x00001fc0 movsbl       $-1(%rcx), %edi
	0x83, 0xff, 0x20, //0x00001fc4 cmpl         $32, %edi
	0x0f, 0x87, 0x55, 0x00, 0x00, 0x00, //0x00001fc7 ja           LBB0_400
	0x49, 0x0f, 0xa3, 0xf9, //0x00001fcd btq          %rdi, %r9
	0x0f, 0x83, 0x4b, 0x00, 0x00, 0x00, //0x00001fd1 jae          LBB0_400
	0x48, 0xff, 0xc1, //0x00001fd7 incq         %rcx
	0x48, 0xff, 0xc2, //0x00001fda incq         %rdx
	0x0f, 0x85, 0xdd, 0xff, 0xff, 0xff, //0x00001fdd jne          LBB0_394
	0x48, 0x89, 0xdf, //0x00001fe3 movq         %rbx, %rdi
	0x48, 0x29, 0xfe, //0x00001fe6 subq         %rdi, %rsi
	0x48, 0x89, 0xf1, //0x00001fe9 movq         %rsi, %rcx
	0x48, 0x39, 0xc1, //0x00001fec cmpq         %rax, %rcx
	0x0f, 0x82, 0x4b, 0x00, 0x00, 0x00, //0x00001fef jb           LBB0_401
	0xe9, 0x46, 0x01, 0x00, 0x00, //0x00001ff5 jmp          LBB0_418
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001ffa .p2align 4, 0x90
	//0x00002000 LBB0_398
	0x49, 0x89, 0x30, //0x00002000 movq         %rsi, (%r8)
	0x49, 0x89, 0xf2, //0x00002003 movq         %rsi, %r10
	0xe9, 0x35, 0x01, 0x00, 0x00, //0x00002006 jmp          LBB0_418
	//0x0000200b LBB0_399
	0x48, 0x01, 0xfe, //0x0000200b addq         %rdi, %rsi
	0x48, 0x29, 0xfe, //0x0000200e subq         %rdi, %rsi
	0x48, 0x89, 0xf1, //0x00002011 movq         %rsi, %rcx
	0x48, 0x39, 0xc1, //0x00002014 cmpq         %rax, %rcx
	0x0f, 0x82, 0x23, 0x00, 0x00, 0x00, //0x00002017 jb           LBB0_401
	0xe9, 0x1e, 0x01, 0x00, 0x00, //0x0000201d jmp          LBB0_418
	//0x00002022 LBB0_400
	0x48, 0x89, 0xdf, //0x00002022 movq         %rbx, %rdi
	0x48, 0x89, 0xda, //0x00002025 movq         %rbx, %rdx
	0x48, 0xf7, 0xd2, //0x00002028 notq         %rdx
	0x48, 0x01, 0xd1, //0x0000202b addq         %rdx, %rcx
	0x48, 0x39, 0xc1, //0x0000202e cmpq         %rax, %rcx
	0x0f, 0x83, 0x09, 0x01, 0x00, 0x00, //0x00002031 jae          LBB0_418
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002037 .p2align 4, 0x90
	//0x00002040 LBB0_401
	0x4c, 0x8d, 0x51, 0x01, //0x00002040 leaq         $1(%rcx), %r10
	0x4d, 0x89, 0x10, //0x00002044 movq         %r10, (%r8)
	0x0f, 0xbe, 0x04, 0x0f, //0x00002047 movsbl       (%rdi,%rcx), %eax
	0x83, 0xf8, 0x7b, //0x0000204b cmpl         $123, %eax
	0x0f, 0x87, 0xf1, 0x06, 0x00, 0x00, //0x0000204e ja           LBB0_488
	0x48, 0x8d, 0x15, 0x5d, 0x2f, 0x00, 0x00, //0x00002054 leaq         $12125(%rip), %rdx  /* LJTI0_0+0(%rip) */
	0x48, 0x63, 0x04, 0x82, //0x0000205b movslq       (%rdx,%rax,4), %rax
	0x48, 0x01, 0xd0, //0x0000205f addq         %rdx, %rax
	0x4c, 0x89, 0x74, 0x24, 0x48, //0x00002062 movq         %r14, $72(%rsp)
	0xff, 0xe0, //0x00002067 jmpq         *%rax
	//0x00002069 LBB0_403
	0x49, 0x8b, 0x13, //0x00002069 movq         (%r11), %rdx
	0x48, 0x89, 0xd0, //0x0000206c movq         %rdx, %rax
	0x4c, 0x29, 0xd0, //0x0000206f subq         %r10, %rax
	0x49, 0x01, 0xfa, //0x00002072 addq         %rdi, %r10
	0x48, 0x83, 0xf8, 0x10, //0x00002075 cmpq         $16, %rax
	0x0f, 0x82, 0x5b, 0x00, 0x00, 0x00, //0x00002079 jb           LBB0_408
	0x48, 0x29, 0xca, //0x0000207f subq         %rcx, %rdx
	0x48, 0x83, 0xc2, 0xef, //0x00002082 addq         $-17, %rdx
	0x48, 0x89, 0xd6, //0x00002086 movq         %rdx, %rsi
	0x48, 0x83, 0xe6, 0xf0, //0x00002089 andq         $-16, %rsi
	0x48, 0x01, 0xce, //0x0000208d addq         %rcx, %rsi
	0x48, 0x8d, 0x4c, 0x37, 0x11, //0x00002090 leaq         $17(%rdi,%rsi), %rcx
	0x83, 0xe2, 0x0f, //0x00002095 andl         $15, %edx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002098 .p2align 4, 0x90
	//0x000020a0 LBB0_405
	0xc4, 0xc1, 0x7a, 0x6f, 0x12, //0x000020a0 vmovdqu      (%r10), %xmm2
	0xc5, 0x89, 0x74, 0xda, //0x000020a5 vpcmpeqb     %xmm2, %xmm14, %xmm3
	0xc5, 0x81, 0xeb, 0xd2, //0x000020a9 vpor         %xmm2, %xmm15, %xmm2
	0xc5, 0xe9, 0x74, 0xd4, //0x000020ad vpcmpeqb     %xmm4, %xmm2, %xmm2
	0xc5, 0xe9, 0xeb, 0xd3, //0x000020b1 vpor         %xmm3, %xmm2, %xmm2
	0xc5, 0xf9, 0xd7, 0xf2, //0x000020b5 vpmovmskb    %xmm2, %esi
	0x66, 0x85, 0xf6, //0x000020b9 testw        %si, %si
	0x0f, 0x85, 0x6e, 0x00, 0x00, 0x00, //0x000020bc jne          LBB0_416
	0x49, 0x83, 0xc2, 0x10, //0x000020c2 addq         $16, %r10
	0x48, 0x83, 0xc0, 0xf0, //0x000020c6 addq         $-16, %rax
	0x48, 0x83, 0xf8, 0x0f, //0x000020ca cmpq         $15, %rax
	0x0f, 0x87, 0xcc, 0xff, 0xff, 0xff, //0x000020ce ja           LBB0_405
	0x48, 0x89, 0xd0, //0x000020d4 movq         %rdx, %rax
	0x49, 0x89, 0xca, //0x000020d7 movq         %rcx, %r10
	//0x000020da LBB0_408
	0x48, 0x85, 0xc0, //0x000020da testq        %rax, %rax
	0x0f, 0x84, 0x3b, 0x00, 0x00, 0x00, //0x000020dd je           LBB0_415
	0x49, 0x8d, 0x0c, 0x02, //0x000020e3 leaq         (%r10,%rax), %rcx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000020e7 .p2align 4, 0x90
	//0x000020f0 LBB0_410
	0x41, 0x0f, 0xb6, 0x12, //0x000020f0 movzbl       (%r10), %edx
	0x80, 0xfa, 0x2c, //0x000020f4 cmpb         $44, %dl
	0x0f, 0x84, 0x21, 0x00, 0x00, 0x00, //0x000020f7 je           LBB0_415
	0x80, 0xfa, 0x7d, //0x000020fd cmpb         $125, %dl
	0x0f, 0x84, 0x18, 0x00, 0x00, 0x00, //0x00002100 je           LBB0_415
	0x80, 0xfa, 0x5d, //0x00002106 cmpb         $93, %dl
	0x0f, 0x84, 0x0f, 0x00, 0x00, 0x00, //0x00002109 je           LBB0_415
	0x49, 0xff, 0xc2, //0x0000210f incq         %r10
	0x48, 0xff, 0xc8, //0x00002112 decq         %rax
	0x0f, 0x85, 0xd5, 0xff, 0xff, 0xff, //0x00002115 jne          LBB0_410
	0x49, 0x89, 0xca, //0x0000211b movq         %rcx, %r10
	//0x0000211e LBB0_415
	0x49, 0x29, 0xfa, //0x0000211e subq         %rdi, %r10
	0xe9, 0x17, 0x00, 0x00, 0x00, //0x00002121 jmp          LBB0_417
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002126 .p2align 4, 0x90
	//0x00002130 LBB0_416
	0x0f, 0xb7, 0xc6, //0x00002130 movzwl       %si, %eax
	0x48, 0x0f, 0xbc, 0xc0, //0x00002133 bsfq         %rax, %rax
	0x49, 0x29, 0xfa, //0x00002137 subq         %rdi, %r10
	0x49, 0x01, 0xc2, //0x0000213a addq         %rax, %r10
	//0x0000213d LBB0_417
	0x4d, 0x89, 0x10, //0x0000213d movq         %r10, (%r8)
	//0x00002140 LBB0_418
	0x49, 0x8b, 0x3f, //0x00002140 movq         (%r15), %rdi
	0x49, 0x8b, 0x47, 0x08, //0x00002143 movq         $8(%r15), %rax
	0x4c, 0x89, 0xd1, //0x00002147 movq         %r10, %rcx
	0x48, 0x29, 0xc1, //0x0000214a subq         %rax, %rcx
	0x0f, 0x83, 0x2d, 0x00, 0x00, 0x00, //0x0000214d jae          LBB0_423
	0x42, 0x8a, 0x14, 0x17, //0x00002153 movb         (%rdi,%r10), %dl
	0x80, 0xfa, 0x0d, //0x00002157 cmpb         $13, %dl
	0x0f, 0x84, 0x20, 0x00, 0x00, 0x00, //0x0000215a je           LBB0_423
	0x80, 0xfa, 0x20, //0x00002160 cmpb         $32, %dl
	0x0f, 0x84, 0x17, 0x00, 0x00, 0x00, //0x00002163 je           LBB0_423
	0x80, 0xc2, 0xf7, //0x00002169 addb         $-9, %dl
	0x80, 0xfa, 0x01, //0x0000216c cmpb         $1, %dl
	0x0f, 0x86, 0x0b, 0x00, 0x00, 0x00, //0x0000216f jbe          LBB0_423
	0x4c, 0x89, 0xd2, //0x00002175 movq         %r10, %rdx
	0xe9, 0x38, 0x01, 0x00, 0x00, //0x00002178 jmp          LBB0_444
	0x90, 0x90, 0x90, //0x0000217d .p2align 4, 0x90
	//0x00002180 LBB0_423
	0x49, 0x8d, 0x52, 0x01, //0x00002180 leaq         $1(%r10), %rdx
	0x48, 0x39, 0xc2, //0x00002184 cmpq         %rax, %rdx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00002187 jae          LBB0_427
	0x8a, 0x1c, 0x17, //0x0000218d movb         (%rdi,%rdx), %bl
	0x80, 0xfb, 0x0d, //0x00002190 cmpb         $13, %bl
	0x0f, 0x84, 0x17, 0x00, 0x00, 0x00, //0x00002193 je           LBB0_427
	0x80, 0xfb, 0x20, //0x00002199 cmpb         $32, %bl
	0x0f, 0x84, 0x0e, 0x00, 0x00, 0x00, //0x0000219c je           LBB0_427
	0x80, 0xc3, 0xf7, //0x000021a2 addb         $-9, %bl
	0x80, 0xfb, 0x01, //0x000021a5 cmpb         $1, %bl
	0x0f, 0x87, 0x07, 0x01, 0x00, 0x00, //0x000021a8 ja           LBB0_444
	0x90, 0x90, //0x000021ae .p2align 4, 0x90
	//0x000021b0 LBB0_427
	0x49, 0x8d, 0x52, 0x02, //0x000021b0 leaq         $2(%r10), %rdx
	0x48, 0x39, 0xc2, //0x000021b4 cmpq         %rax, %rdx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x000021b7 jae          LBB0_431
	0x8a, 0x1c, 0x17, //0x000021bd movb         (%rdi,%rdx), %bl
	0x80, 0xfb, 0x0d, //0x000021c0 cmpb         $13, %bl
	0x0f, 0x84, 0x17, 0x00, 0x00, 0x00, //0x000021c3 je           LBB0_431
	0x80, 0xfb, 0x20, //0x000021c9 cmpb         $32, %bl
	0x0f, 0x84, 0x0e, 0x00, 0x00, 0x00, //0x000021cc je           LBB0_431
	0x80, 0xc3, 0xf7, //0x000021d2 addb         $-9, %bl
	0x80, 0xfb, 0x01, //0x000021d5 cmpb         $1, %bl
	0x0f, 0x87, 0xd7, 0x00, 0x00, 0x00, //0x000021d8 ja           LBB0_444
	0x90, 0x90, //0x000021de .p2align 4, 0x90
	//0x000021e0 LBB0_431
	0x49, 0x8d, 0x52, 0x03, //0x000021e0 leaq         $3(%r10), %rdx
	0x48, 0x39, 0xc2, //0x000021e4 cmpq         %rax, %rdx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x000021e7 jae          LBB0_435
	0x8a, 0x1c, 0x17, //0x000021ed movb         (%rdi,%rdx), %bl
	0x80, 0xfb, 0x0d, //0x000021f0 cmpb         $13, %bl
	0x0f, 0x84, 0x17, 0x00, 0x00, 0x00, //0x000021f3 je           LBB0_435
	0x80, 0xfb, 0x20, //0x000021f9 cmpb         $32, %bl
	0x0f, 0x84, 0x0e, 0x00, 0x00, 0x00, //0x000021fc je           LBB0_435
	0x80, 0xc3, 0xf7, //0x00002202 addb         $-9, %bl
	0x80, 0xfb, 0x01, //0x00002205 cmpb         $1, %bl
	0x0f, 0x87, 0xa7, 0x00, 0x00, 0x00, //0x00002208 ja           LBB0_444
	0x90, 0x90, //0x0000220e .p2align 4, 0x90
	//0x00002210 LBB0_435
	0x49, 0x8d, 0x72, 0x04, //0x00002210 leaq         $4(%r10), %rsi
	0x48, 0x39, 0xf0, //0x00002214 cmpq         %rsi, %rax
	0x0f, 0x86, 0x6d, 0x0b, 0x00, 0x00, //0x00002217 jbe          LBB0_550
	0x48, 0x39, 0xf0, //0x0000221d cmpq         %rsi, %rax
	0x0f, 0x84, 0x5a, 0x00, 0x00, 0x00, //0x00002220 je           LBB0_442
	0x48, 0x8d, 0x34, 0x07, //0x00002226 leaq         (%rdi,%rax), %rsi
	0x48, 0x83, 0xc1, 0x04, //0x0000222a addq         $4, %rcx
	0x48, 0x89, 0xfb, //0x0000222e movq         %rdi, %rbx
	0x4a, 0x8d, 0x54, 0x17, 0x05, //0x00002231 leaq         $5(%rdi,%r10), %rdx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002236 .p2align 4, 0x90
	//0x00002240 LBB0_438
	0x0f, 0xbe, 0x7a, 0xff, //0x00002240 movsbl       $-1(%rdx), %edi
	0x83, 0xff, 0x20, //0x00002244 cmpl         $32, %edi
	0x0f, 0x87, 0x53, 0x00, 0x00, 0x00, //0x00002247 ja           LBB0_443
	0x49, 0x0f, 0xa3, 0xf9, //0x0000224d btq          %rdi, %r9
	0x0f, 0x83, 0x49, 0x00, 0x00, 0x00, //0x00002251 jae          LBB0_443
	0x48, 0xff, 0xc2, //0x00002257 incq         %rdx
	0x48, 0xff, 0xc1, //0x0000225a incq         %rcx
	0x0f, 0x85, 0xdd, 0xff, 0xff, 0xff, //0x0000225d jne          LBB0_438
	0x48, 0x89, 0xdf, //0x00002263 movq         %rbx, %rdi
	0x48, 0x29, 0xfe, //0x00002266 subq         %rdi, %rsi
	0x48, 0x89, 0xf2, //0x00002269 movq         %rsi, %rdx
	0x48, 0x39, 0xc2, //0x0000226c cmpq         %rax, %rdx
	0x0f, 0x82, 0x40, 0x00, 0x00, 0x00, //0x0000226f jb           LBB0_444
	0xe9, 0x07, 0x21, 0x00, 0x00, //0x00002275 jmp          LBB0_837
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x0000227a .p2align 4, 0x90
	//0x00002280 LBB0_442
	0x48, 0x01, 0xfe, //0x00002280 addq         %rdi, %rsi
	0x48, 0x29, 0xfe, //0x00002283 subq         %rdi, %rsi
	0x48, 0x89, 0xf2, //0x00002286 movq         %rsi, %rdx
	0x48, 0x39, 0xc2, //0x00002289 cmpq         %rax, %rdx
	0x0f, 0x82, 0x23, 0x00, 0x00, 0x00, //0x0000228c jb           LBB0_444
	0xe9, 0xea, 0x20, 0x00, 0x00, //0x00002292 jmp          LBB0_837
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002297 .p2align 4, 0x90
	//0x000022a0 LBB0_443
	0x48, 0x89, 0xdf, //0x000022a0 movq         %rbx, %rdi
	0x48, 0x89, 0xd9, //0x000022a3 movq         %rbx, %rcx
	0x48, 0xf7, 0xd1, //0x000022a6 notq         %rcx
	0x48, 0x01, 0xca, //0x000022a9 addq         %rcx, %rdx
	0x48, 0x39, 0xc2, //0x000022ac cmpq         %rax, %rdx
	0x0f, 0x83, 0xcc, 0x20, 0x00, 0x00, //0x000022af jae          LBB0_837
	//0x000022b5 LBB0_444
	0x4c, 0x8d, 0x52, 0x01, //0x000022b5 leaq         $1(%rdx), %r10
	0x4d, 0x89, 0x10, //0x000022b9 movq         %r10, (%r8)
	0x8a, 0x04, 0x17, //0x000022bc movb         (%rdi,%rdx), %al
	0x3c, 0x2c, //0x000022bf cmpb         $44, %al
	0x0f, 0x85, 0x88, 0x20, 0x00, 0x00, //0x000022c1 jne          LBB0_832
	0x49, 0x83, 0xfe, 0x02, //0x000022c7 cmpq         $2, %r14
	0x4d, 0x8d, 0x76, 0xff, //0x000022cb leaq         $-1(%r14), %r14
	0x0f, 0x8d, 0xeb, 0xfb, 0xff, 0xff, //0x000022cf jge          LBB0_374
	0xe9, 0xd6, 0x08, 0x00, 0x00, //0x000022d5 jmp          LBB0_446
	//0x000022da LBB0_449
	0x48, 0x83, 0xc1, 0x04, //0x000022da addq         $4, %rcx
	0x49, 0x3b, 0x0b, //0x000022de cmpq         (%r11), %rcx
	0x0f, 0x87, 0x59, 0xfe, 0xff, 0xff, //0x000022e1 ja           LBB0_418
	0xe9, 0x59, 0x04, 0x00, 0x00, //0x000022e7 jmp          LBB0_488
	//0x000022ec LBB0_450
	0x4d, 0x8b, 0x03, //0x000022ec movq         (%r11), %r8
	0x4c, 0x89, 0xc0, //0x000022ef movq         %r8, %rax
	0x4c, 0x29, 0xd0, //0x000022f2 subq         %r10, %rax
	0x48, 0x83, 0xf8, 0x20, //0x000022f5 cmpq         $32, %rax
	0x0f, 0x8c, 0x3c, 0x08, 0x00, 0x00, //0x000022f9 jl           LBB0_522
	0x4c, 0x8d, 0x0c, 0x0f, //0x000022ff leaq         (%rdi,%rcx), %r9
	0x49, 0x29, 0xc8, //0x00002303 subq         %rcx, %r8
	0xb9, 0x1f, 0x00, 0x00, 0x00, //0x00002306 movl         $31, %ecx
	0x31, 0xc0, //0x0000230b xorl         %eax, %eax
	0x45, 0x31, 0xdb, //0x0000230d xorl         %r11d, %r11d
	0xe9, 0x2d, 0x00, 0x00, 0x00, //0x00002310 jmp          LBB0_452
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002315 .p2align 4, 0x90
	//0x00002320 LBB0_455
	0x45, 0x31, 0xdb, //0x00002320 xorl         %r11d, %r11d
	0x85, 0xdb, //0x00002323 testl        %ebx, %ebx
	0x0f, 0x85, 0xbb, 0x00, 0x00, 0x00, //0x00002325 jne          LBB0_454
	//0x0000232b LBB0_456
	0x48, 0x83, 0xc0, 0x20, //0x0000232b addq         $32, %rax
	0x49, 0x8d, 0x54, 0x08, 0xe0, //0x0000232f leaq         $-32(%r8,%rcx), %rdx
	0x48, 0x83, 0xc1, 0xe0, //0x00002334 addq         $-32, %rcx
	0x48, 0x83, 0xfa, 0x3f, //0x00002338 cmpq         $63, %rdx
	0x0f, 0x8e, 0x6e, 0x07, 0x00, 0x00, //0x0000233c jle          LBB0_457
	//0x00002342 LBB0_452
	0xc4, 0xc1, 0x7a, 0x6f, 0x54, 0x01, 0x01, //0x00002342 vmovdqu      $1(%r9,%rax), %xmm2
	0xc4, 0xc1, 0x7a, 0x6f, 0x5c, 0x01, 0x11, //0x00002349 vmovdqu      $17(%r9,%rax), %xmm3
	0xc5, 0xe9, 0x74, 0xe8, //0x00002350 vpcmpeqb     %xmm0, %xmm2, %xmm5
	0xc5, 0xf9, 0xd7, 0xd5, //0x00002354 vpmovmskb    %xmm5, %edx
	0xc5, 0xe1, 0x74, 0xe8, //0x00002358 vpcmpeqb     %xmm0, %xmm3, %xmm5
	0xc5, 0xf9, 0xd7, 0xdd, //0x0000235c vpmovmskb    %xmm5, %ebx
	0x48, 0xc1, 0xe3, 0x10, //0x00002360 shlq         $16, %rbx
	0x48, 0x09, 0xd3, //0x00002364 orq          %rdx, %rbx
	0xc5, 0xe9, 0x74, 0xd1, //0x00002367 vpcmpeqb     %xmm1, %xmm2, %xmm2
	0xc5, 0xf9, 0xd7, 0xf2, //0x0000236b vpmovmskb    %xmm2, %esi
	0xc5, 0xe1, 0x74, 0xd1, //0x0000236f vpcmpeqb     %xmm1, %xmm3, %xmm2
	0xc5, 0xf9, 0xd7, 0xd2, //0x00002373 vpmovmskb    %xmm2, %edx
	0x48, 0xc1, 0xe2, 0x10, //0x00002377 shlq         $16, %rdx
	0x48, 0x09, 0xf2, //0x0000237b orq          %rsi, %rdx
	0x48, 0x89, 0xd6, //0x0000237e movq         %rdx, %rsi
	0x4c, 0x09, 0xde, //0x00002381 orq          %r11, %rsi
	0x0f, 0x84, 0x96, 0xff, 0xff, 0xff, //0x00002384 je           LBB0_455
	0x44, 0x89, 0xde, //0x0000238a movl         %r11d, %esi
	0x41, 0xbe, 0xff, 0xff, 0xff, 0xff, //0x0000238d movl         $4294967295, %r14d
	0x44, 0x31, 0xf6, //0x00002393 xorl         %r14d, %esi
	0x21, 0xf2, //0x00002396 andl         %esi, %edx
	0x8d, 0x34, 0x12, //0x00002398 leal         (%rdx,%rdx), %esi
	0x44, 0x09, 0xde, //0x0000239b orl          %r11d, %esi
	0x4d, 0x89, 0xfc, //0x0000239e movq         %r15, %r12
	0x4d, 0x89, 0xef, //0x000023a1 movq         %r13, %r15
	0x49, 0x89, 0xfd, //0x000023a4 movq         %rdi, %r13
	0x41, 0x8d, 0xbe, 0xab, 0xaa, 0xaa, 0xaa, //0x000023a7 leal         $-1431655765(%r14), %edi
	0x31, 0xf7, //0x000023ae xorl         %esi, %edi
	0x21, 0xd7, //0x000023b0 andl         %edx, %edi
	0x81, 0xe7, 0xaa, 0xaa, 0xaa, 0xaa, //0x000023b2 andl         $-1431655766, %edi
	0x45, 0x31, 0xdb, //0x000023b8 xorl         %r11d, %r11d
	0x01, 0xd7, //0x000023bb addl         %edx, %edi
	0x41, 0x0f, 0x92, 0xc3, //0x000023bd setb         %r11b
	0x01, 0xff, //0x000023c1 addl         %edi, %edi
	0x81, 0xf7, 0x55, 0x55, 0x55, 0x55, //0x000023c3 xorl         $1431655765, %edi
	0x21, 0xf7, //0x000023c9 andl         %esi, %edi
	0x44, 0x31, 0xf7, //0x000023cb xorl         %r14d, %edi
	0x4c, 0x8b, 0x74, 0x24, 0x48, //0x000023ce movq         $72(%rsp), %r14
	0x21, 0xfb, //0x000023d3 andl         %edi, %ebx
	0x4c, 0x89, 0xef, //0x000023d5 movq         %r13, %rdi
	0x4d, 0x89, 0xfd, //0x000023d8 movq         %r15, %r13
	0x4d, 0x89, 0xe7, //0x000023db movq         %r12, %r15
	0x85, 0xdb, //0x000023de testl        %ebx, %ebx
	0x0f, 0x84, 0x45, 0xff, 0xff, 0xff, //0x000023e0 je           LBB0_456
	//0x000023e6 LBB0_454
	0x48, 0x0f, 0xbc, 0xcb, //0x000023e6 bsfq         %rbx, %rcx
	0x49, 0x01, 0xc9, //0x000023ea addq         %rcx, %r9
	0x49, 0x01, 0xc1, //0x000023ed addq         %rax, %r9
	0x49, 0x29, 0xf9, //0x000023f0 subq         %rdi, %r9
	0x49, 0x83, 0xc1, 0x02, //0x000023f3 addq         $2, %r9
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x000023f7 movq         $24(%rsp), %r8
	0x4d, 0x89, 0x08, //0x000023fc movq         %r9, (%r8)
	0x4d, 0x89, 0xca, //0x000023ff movq         %r9, %r10
	0x4c, 0x8b, 0x5c, 0x24, 0x38, //0x00002402 movq         $56(%rsp), %r11
	0x49, 0xb9, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00002407 movabsq      $4294977024, %r9
	0xe9, 0x2a, 0xfd, 0xff, 0xff, //0x00002411 jmp          LBB0_418
	//0x00002416 LBB0_461
	0x4d, 0x8b, 0x0b, //0x00002416 movq         (%r11), %r9
	0x4d, 0x29, 0xd1, //0x00002419 subq         %r10, %r9
	0x4c, 0x01, 0xd7, //0x0000241c addq         %r10, %rdi
	0x45, 0x31, 0xc0, //0x0000241f xorl         %r8d, %r8d
	0x45, 0x31, 0xd2, //0x00002422 xorl         %r10d, %r10d
	0x45, 0x31, 0xdb, //0x00002425 xorl         %r11d, %r11d
	0x31, 0xd2, //0x00002428 xorl         %edx, %edx
	0xe9, 0x1c, 0x00, 0x00, 0x00, //0x0000242a jmp          LBB0_463
	//0x0000242f LBB0_462
	0x49, 0xc1, 0xfe, 0x3f, //0x0000242f sarq         $63, %r14
	0xf3, 0x48, 0x0f, 0xb8, 0xc7, //0x00002433 popcntq      %rdi, %rax
	0x49, 0x01, 0xc3, //0x00002438 addq         %rax, %r11
	0x48, 0x8b, 0x7c, 0x24, 0x20, //0x0000243b movq         $32(%rsp), %rdi
	0x48, 0x83, 0xc7, 0x40, //0x00002440 addq         $64, %rdi
	0x49, 0x83, 0xc1, 0xc0, //0x00002444 addq         $-64, %r9
	0x4d, 0x89, 0xf0, //0x00002448 movq         %r14, %r8
	//0x0000244b LBB0_463
	0x49, 0x83, 0xf9, 0x40, //0x0000244b cmpq         $64, %r9
	0x0f, 0x8c, 0xa7, 0x01, 0x00, 0x00, //0x0000244f jl           LBB0_471
	//0x00002455 LBB0_464
	0xc5, 0xfa, 0x6f, 0x17, //0x00002455 vmovdqu      (%rdi), %xmm2
	0xc5, 0xfa, 0x6f, 0x77, 0x10, //0x00002459 vmovdqu      $16(%rdi), %xmm6
	0xc5, 0xfa, 0x6f, 0x6f, 0x20, //0x0000245e vmovdqu      $32(%rdi), %xmm5
	0x48, 0x89, 0x7c, 0x24, 0x20, //0x00002463 movq         %rdi, $32(%rsp)
	0xc5, 0xfa, 0x6f, 0x7f, 0x30, //0x00002468 vmovdqu      $48(%rdi), %xmm7
	0xc5, 0xe9, 0x74, 0xd8, //0x0000246d vpcmpeqb     %xmm0, %xmm2, %xmm3
	0xc5, 0xf9, 0xd7, 0xf3, //0x00002471 vpmovmskb    %xmm3, %esi
	0xc5, 0xc9, 0x74, 0xd8, //0x00002475 vpcmpeqb     %xmm0, %xmm6, %xmm3
	0xc5, 0xf9, 0xd7, 0xc3, //0x00002479 vpmovmskb    %xmm3, %eax
	0xc5, 0xd1, 0x74, 0xd8, //0x0000247d vpcmpeqb     %xmm0, %xmm5, %xmm3
	0xc5, 0xf9, 0xd7, 0xfb, //0x00002481 vpmovmskb    %xmm3, %edi
	0xc5, 0xc1, 0x74, 0xd8, //0x00002485 vpcmpeqb     %xmm0, %xmm7, %xmm3
	0xc5, 0xf9, 0xd7, 0xdb, //0x00002489 vpmovmskb    %xmm3, %ebx
	0x48, 0xc1, 0xe3, 0x30, //0x0000248d shlq         $48, %rbx
	0x48, 0xc1, 0xe7, 0x20, //0x00002491 shlq         $32, %rdi
	0x48, 0xc1, 0xe0, 0x10, //0x00002495 shlq         $16, %rax
	0x48, 0x09, 0xc6, //0x00002499 orq          %rax, %rsi
	0x48, 0x09, 0xfe, //0x0000249c orq          %rdi, %rsi
	0x48, 0x09, 0xde, //0x0000249f orq          %rbx, %rsi
	0xc5, 0xe9, 0x74, 0xd9, //0x000024a2 vpcmpeqb     %xmm1, %xmm2, %xmm3
	0xc5, 0xf9, 0xd7, 0xfb, //0x000024a6 vpmovmskb    %xmm3, %edi
	0xc5, 0xc9, 0x74, 0xd9, //0x000024aa vpcmpeqb     %xmm1, %xmm6, %xmm3
	0xc5, 0xf9, 0xd7, 0xc3, //0x000024ae vpmovmskb    %xmm3, %eax
	0xc5, 0xd1, 0x74, 0xd9, //0x000024b2 vpcmpeqb     %xmm1, %xmm5, %xmm3
	0xc5, 0xf9, 0xd7, 0xdb, //0x000024b6 vpmovmskb    %xmm3, %ebx
	0xc5, 0xc1, 0x74, 0xd9, //0x000024ba vpcmpeqb     %xmm1, %xmm7, %xmm3
	0xc5, 0xf9, 0xd7, 0xcb, //0x000024be vpmovmskb    %xmm3, %ecx
	0x48, 0xc1, 0xe1, 0x30, //0x000024c2 shlq         $48, %rcx
	0x48, 0xc1, 0xe3, 0x20, //0x000024c6 shlq         $32, %rbx
	0x48, 0xc1, 0xe0, 0x10, //0x000024ca shlq         $16, %rax
	0x48, 0x09, 0xc7, //0x000024ce orq          %rax, %rdi
	0x48, 0x09, 0xdf, //0x000024d1 orq          %rbx, %rdi
	0x48, 0x09, 0xcf, //0x000024d4 orq          %rcx, %rdi
	0x48, 0x89, 0xf8, //0x000024d7 movq         %rdi, %rax
	0x4c, 0x09, 0xd0, //0x000024da orq          %r10, %rax
	0x0f, 0x84, 0x4b, 0x00, 0x00, 0x00, //0x000024dd je           LBB0_466
	0x4c, 0x89, 0xd0, //0x000024e3 movq         %r10, %rax
	0x48, 0xf7, 0xd0, //0x000024e6 notq         %rax
	0x48, 0x21, 0xf8, //0x000024e9 andq         %rdi, %rax
	0x4c, 0x8d, 0x34, 0x00, //0x000024ec leaq         (%rax,%rax), %r14
	0x4d, 0x09, 0xd6, //0x000024f0 orq          %r10, %r14
	0x4c, 0x89, 0xf1, //0x000024f3 movq         %r14, %rcx
	0x48, 0xbb, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x000024f6 movabsq      $-6148914691236517206, %rbx
	0x48, 0x31, 0xd9, //0x00002500 xorq         %rbx, %rcx
	0x48, 0x21, 0xdf, //0x00002503 andq         %rbx, %rdi
	0x48, 0x21, 0xcf, //0x00002506 andq         %rcx, %rdi
	0x45, 0x31, 0xd2, //0x00002509 xorl         %r10d, %r10d
	0x48, 0x01, 0xc7, //0x0000250c addq         %rax, %rdi
	0x41, 0x0f, 0x92, 0xc2, //0x0000250f setb         %r10b
	0x48, 0x01, 0xff, //0x00002513 addq         %rdi, %rdi
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00002516 movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc7, //0x00002520 xorq         %rax, %rdi
	0x4c, 0x21, 0xf7, //0x00002523 andq         %r14, %rdi
	0x48, 0xf7, 0xd7, //0x00002526 notq         %rdi
	0xe9, 0x0a, 0x00, 0x00, 0x00, //0x00002529 jmp          LBB0_467
	//0x0000252e LBB0_466
	0x48, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x0000252e movq         $-1, %rdi
	0x45, 0x31, 0xd2, //0x00002535 xorl         %r10d, %r10d
	//0x00002538 LBB0_467
	0x48, 0x21, 0xf7, //0x00002538 andq         %rsi, %rdi
	0xc4, 0xe1, 0xf9, 0x6e, 0xdf, //0x0000253b vmovq        %rdi, %xmm3
	0xc4, 0xc3, 0x61, 0x44, 0xd9, 0x00, //0x00002540 vpclmulqdq   $0, %xmm9, %xmm3, %xmm3
	0xc4, 0xc1, 0xf9, 0x7e, 0xde, //0x00002546 vmovq        %xmm3, %r14
	0x4d, 0x31, 0xc6, //0x0000254b xorq         %r8, %r14
	0xc5, 0xa9, 0x74, 0xda, //0x0000254e vpcmpeqb     %xmm2, %xmm10, %xmm3
	0xc5, 0xf9, 0xd7, 0xfb, //0x00002552 vpmovmskb    %xmm3, %edi
	0xc5, 0xa9, 0x74, 0xde, //0x00002556 vpcmpeqb     %xmm6, %xmm10, %xmm3
	0xc5, 0xf9, 0xd7, 0xc3, //0x0000255a vpmovmskb    %xmm3, %eax
	0xc5, 0xa9, 0x74, 0xdd, //0x0000255e vpcmpeqb     %xmm5, %xmm10, %xmm3
	0xc5, 0xf9, 0xd7, 0xcb, //0x00002562 vpmovmskb    %xmm3, %ecx
	0xc5, 0xa9, 0x74, 0xdf, //0x00002566 vpcmpeqb     %xmm7, %xmm10, %xmm3
	0xc5, 0xf9, 0xd7, 0xdb, //0x0000256a vpmovmskb    %xmm3, %ebx
	0x48, 0xc1, 0xe3, 0x30, //0x0000256e shlq         $48, %rbx
	0x48, 0xc1, 0xe1, 0x20, //0x00002572 shlq         $32, %rcx
	0x48, 0xc1, 0xe0, 0x10, //0x00002576 shlq         $16, %rax
	0x48, 0x09, 0xc7, //0x0000257a orq          %rax, %rdi
	0x48, 0x09, 0xcf, //0x0000257d orq          %rcx, %rdi
	0x48, 0x09, 0xdf, //0x00002580 orq          %rbx, %rdi
	0x4d, 0x89, 0xf0, //0x00002583 movq         %r14, %r8
	0x49, 0xf7, 0xd0, //0x00002586 notq         %r8
	0x4c, 0x21, 0xc7, //0x00002589 andq         %r8, %rdi
	0xc5, 0xa1, 0x74, 0xd2, //0x0000258c vpcmpeqb     %xmm2, %xmm11, %xmm2
	0xc5, 0xf9, 0xd7, 0xc2, //0x00002590 vpmovmskb    %xmm2, %eax
	0xc5, 0xa1, 0x74, 0xd6, //0x00002594 vpcmpeqb     %xmm6, %xmm11, %xmm2
	0xc5, 0xf9, 0xd7, 0xda, //0x00002598 vpmovmskb    %xmm2, %ebx
	0xc5, 0xa1, 0x74, 0xd5, //0x0000259c vpcmpeqb     %xmm5, %xmm11, %xmm2
	0xc5, 0xf9, 0xd7, 0xf2, //0x000025a0 vpmovmskb    %xmm2, %esi
	0xc5, 0xa1, 0x74, 0xd7, //0x000025a4 vpcmpeqb     %xmm7, %xmm11, %xmm2
	0xc5, 0xf9, 0xd7, 0xca, //0x000025a8 vpmovmskb    %xmm2, %ecx
	0x48, 0xc1, 0xe1, 0x30, //0x000025ac shlq         $48, %rcx
	0x48, 0xc1, 0xe6, 0x20, //0x000025b0 shlq         $32, %rsi
	0x48, 0xc1, 0xe3, 0x10, //0x000025b4 shlq         $16, %rbx
	0x48, 0x09, 0xd8, //0x000025b8 orq          %rbx, %rax
	0x48, 0x09, 0xf0, //0x000025bb orq          %rsi, %rax
	0x48, 0x09, 0xc8, //0x000025be orq          %rcx, %rax
	0x4c, 0x21, 0xc0, //0x000025c1 andq         %r8, %rax
	0x0f, 0x84, 0x65, 0xfe, 0xff, 0xff, //0x000025c4 je           LBB0_462
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x000025ca movq         $24(%rsp), %r8
	0x90, //0x000025cf .p2align 4, 0x90
	//0x000025d0 LBB0_469
	0x48, 0x8d, 0x58, 0xff, //0x000025d0 leaq         $-1(%rax), %rbx
	0x48, 0x89, 0xd9, //0x000025d4 movq         %rbx, %rcx
	0x48, 0x21, 0xf9, //0x000025d7 andq         %rdi, %rcx
	0xf3, 0x48, 0x0f, 0xb8, 0xc9, //0x000025da popcntq      %rcx, %rcx
	0x4c, 0x01, 0xd9, //0x000025df addq         %r11, %rcx
	0x48, 0x39, 0xd1, //0x000025e2 cmpq         %rdx, %rcx
	0x0f, 0x86, 0x8d, 0x04, 0x00, 0x00, //0x000025e5 jbe          LBB0_515
	0x48, 0xff, 0xc2, //0x000025eb incq         %rdx
	0x48, 0x21, 0xd8, //0x000025ee andq         %rbx, %rax
	0x0f, 0x85, 0xd9, 0xff, 0xff, 0xff, //0x000025f1 jne          LBB0_469
	0xe9, 0x33, 0xfe, 0xff, 0xff, //0x000025f7 jmp          LBB0_462
	//0x000025fc LBB0_471
	0x4d, 0x85, 0xc9, //0x000025fc testq        %r9, %r9
	0x0f, 0x8e, 0x3f, 0x05, 0x00, 0x00, //0x000025ff jle          LBB0_523
	0xc5, 0x7e, 0x7f, 0x84, 0x24, 0x80, 0x00, 0x00, 0x00, //0x00002605 vmovdqu      %ymm8, $128(%rsp)
	0xc5, 0x7e, 0x7f, 0x44, 0x24, 0x60, //0x0000260e vmovdqu      %ymm8, $96(%rsp)
	0x89, 0xf8, //0x00002614 movl         %edi, %eax
	0x25, 0xff, 0x0f, 0x00, 0x00, //0x00002616 andl         $4095, %eax
	0x3d, 0xc1, 0x0f, 0x00, 0x00, //0x0000261b cmpl         $4033, %eax
	0x0f, 0x82, 0x37, 0x00, 0x00, 0x00, //0x00002620 jb           LBB0_475
	0x48, 0x89, 0xf8, //0x00002626 movq         %rdi, %rax
	0x49, 0x83, 0xf9, 0x20, //0x00002629 cmpq         $32, %r9
	0x0f, 0x82, 0x34, 0x00, 0x00, 0x00, //0x0000262d jb           LBB0_476
	0xc5, 0xf8, 0x10, 0x10, //0x00002633 vmovups      (%rax), %xmm2
	0xc5, 0xf8, 0x11, 0x54, 0x24, 0x60, //0x00002637 vmovups      %xmm2, $96(%rsp)
	0xc5, 0xf8, 0x10, 0x50, 0x10, //0x0000263d vmovups      $16(%rax), %xmm2
	0xc5, 0xf8, 0x11, 0x54, 0x24, 0x70, //0x00002642 vmovups      %xmm2, $112(%rsp)
	0x48, 0x83, 0xc0, 0x20, //0x00002648 addq         $32, %rax
	0x49, 0x8d, 0x79, 0xe0, //0x0000264c leaq         $-32(%r9), %rdi
	0x48, 0x8d, 0xb4, 0x24, 0x80, 0x00, 0x00, 0x00, //0x00002650 leaq         $128(%rsp), %rsi
	0xe9, 0x12, 0x00, 0x00, 0x00, //0x00002658 jmp          LBB0_477
	//0x0000265d LBB0_475
	0x4c, 0x8b, 0x7c, 0x24, 0x28, //0x0000265d movq         $40(%rsp), %r15
	0xe9, 0xee, 0xfd, 0xff, 0xff, //0x00002662 jmp          LBB0_464
	//0x00002667 LBB0_476
	0x48, 0x8d, 0x74, 0x24, 0x60, //0x00002667 leaq         $96(%rsp), %rsi
	0x4c, 0x89, 0xcf, //0x0000266c movq         %r9, %rdi
	//0x0000266f LBB0_477
	0x48, 0x83, 0xff, 0x10, //0x0000266f cmpq         $16, %rdi
	0x0f, 0x82, 0x4d, 0x00, 0x00, 0x00, //0x00002673 jb           LBB0_478
	0xc5, 0xf8, 0x10, 0x10, //0x00002679 vmovups      (%rax), %xmm2
	0xc5, 0xf8, 0x11, 0x16, //0x0000267d vmovups      %xmm2, (%rsi)
	0x48, 0x83, 0xc0, 0x10, //0x00002681 addq         $16, %rax
	0x48, 0x83, 0xc6, 0x10, //0x00002685 addq         $16, %rsi
	0x48, 0x83, 0xc7, 0xf0, //0x00002689 addq         $-16, %rdi
	0x48, 0x83, 0xff, 0x08, //0x0000268d cmpq         $8, %rdi
	0x0f, 0x83, 0x39, 0x00, 0x00, 0x00, //0x00002691 jae          LBB0_485
	//0x00002697 LBB0_479
	0x48, 0x83, 0xff, 0x04, //0x00002697 cmpq         $4, %rdi
	0x0f, 0x8c, 0x51, 0x00, 0x00, 0x00, //0x0000269b jl           LBB0_480
	//0x000026a1 LBB0_486
	0x48, 0x89, 0xc1, //0x000026a1 movq         %rax, %rcx
	0x8b, 0x00, //0x000026a4 movl         (%rax), %eax
	0x89, 0x06, //0x000026a6 movl         %eax, (%rsi)
	0x48, 0x83, 0xc1, 0x04, //0x000026a8 addq         $4, %rcx
	0x48, 0x89, 0xc8, //0x000026ac movq         %rcx, %rax
	0x48, 0x83, 0xc6, 0x04, //0x000026af addq         $4, %rsi
	0x48, 0x83, 0xc7, 0xfc, //0x000026b3 addq         $-4, %rdi
	0x48, 0x83, 0xff, 0x02, //0x000026b7 cmpq         $2, %rdi
	0x0f, 0x83, 0x3b, 0x00, 0x00, 0x00, //0x000026bb jae          LBB0_481
	0xe9, 0x4e, 0x00, 0x00, 0x00, //0x000026c1 jmp          LBB0_482
	//0x000026c6 LBB0_478
	0x48, 0x83, 0xff, 0x08, //0x000026c6 cmpq         $8, %rdi
	0x0f, 0x82, 0xc7, 0xff, 0xff, 0xff, //0x000026ca jb           LBB0_479
	//0x000026d0 LBB0_485
	0x48, 0x89, 0xc1, //0x000026d0 movq         %rax, %rcx
	0x48, 0x8b, 0x00, //0x000026d3 movq         (%rax), %rax
	0x48, 0x89, 0x06, //0x000026d6 movq         %rax, (%rsi)
	0x48, 0x83, 0xc1, 0x08, //0x000026d9 addq         $8, %rcx
	0x48, 0x89, 0xc8, //0x000026dd movq         %rcx, %rax
	0x48, 0x83, 0xc6, 0x08, //0x000026e0 addq         $8, %rsi
	0x48, 0x83, 0xc7, 0xf8, //0x000026e4 addq         $-8, %rdi
	0x48, 0x83, 0xff, 0x04, //0x000026e8 cmpq         $4, %rdi
	0x0f, 0x8d, 0xaf, 0xff, 0xff, 0xff, //0x000026ec jge          LBB0_486
	//0x000026f2 LBB0_480
	0x48, 0x83, 0xff, 0x02, //0x000026f2 cmpq         $2, %rdi
	0x0f, 0x82, 0x18, 0x00, 0x00, 0x00, //0x000026f6 jb           LBB0_482
	//0x000026fc LBB0_481
	0x48, 0x89, 0xc1, //0x000026fc movq         %rax, %rcx
	0x0f, 0xb7, 0x00, //0x000026ff movzwl       (%rax), %eax
	0x66, 0x89, 0x06, //0x00002702 movw         %ax, (%rsi)
	0x48, 0x83, 0xc1, 0x02, //0x00002705 addq         $2, %rcx
	0x48, 0x83, 0xc6, 0x02, //0x00002709 addq         $2, %rsi
	0x48, 0x83, 0xc7, 0xfe, //0x0000270d addq         $-2, %rdi
	0x48, 0x89, 0xc8, //0x00002711 movq         %rcx, %rax
	//0x00002714 LBB0_482
	0x48, 0x8d, 0x4c, 0x24, 0x60, //0x00002714 leaq         $96(%rsp), %rcx
	0x48, 0x85, 0xff, //0x00002719 testq        %rdi, %rdi
	0x48, 0x89, 0xcf, //0x0000271c movq         %rcx, %rdi
	0x4c, 0x8b, 0x7c, 0x24, 0x28, //0x0000271f movq         $40(%rsp), %r15
	0x0f, 0x84, 0x2b, 0xfd, 0xff, 0xff, //0x00002724 je           LBB0_464
	0x8a, 0x00, //0x0000272a movb         (%rax), %al
	0x88, 0x06, //0x0000272c movb         %al, (%rsi)
	0x48, 0x8d, 0x7c, 0x24, 0x60, //0x0000272e leaq         $96(%rsp), %rdi
	0xe9, 0x1d, 0xfd, 0xff, 0xff, //0x00002733 jmp          LBB0_464
	//0x00002738 LBB0_487
	0x48, 0x83, 0xc1, 0x05, //0x00002738 addq         $5, %rcx
	0x49, 0x3b, 0x0b, //0x0000273c cmpq         (%r11), %rcx
	0x0f, 0x87, 0xfb, 0xf9, 0xff, 0xff, //0x0000273f ja           LBB0_418
	//0x00002745 LBB0_488
	0x49, 0x89, 0x08, //0x00002745 movq         %rcx, (%r8)
	0x49, 0x89, 0xca, //0x00002748 movq         %rcx, %r10
	0xe9, 0xf0, 0xf9, 0xff, 0xff, //0x0000274b jmp          LBB0_418
	//0x00002750 LBB0_489
	0x4d, 0x8b, 0x0b, //0x00002750 movq         (%r11), %r9
	0x4d, 0x29, 0xd1, //0x00002753 subq         %r10, %r9
	0x4c, 0x01, 0xd7, //0x00002756 addq         %r10, %rdi
	0x45, 0x31, 0xc0, //0x00002759 xorl         %r8d, %r8d
	0x45, 0x31, 0xd2, //0x0000275c xorl         %r10d, %r10d
	0x45, 0x31, 0xdb, //0x0000275f xorl         %r11d, %r11d
	0x31, 0xd2, //0x00002762 xorl         %edx, %edx
	0xe9, 0x1c, 0x00, 0x00, 0x00, //0x00002764 jmp          LBB0_491
	//0x00002769 LBB0_490
	0x49, 0xc1, 0xfe, 0x3f, //0x00002769 sarq         $63, %r14
	0xf3, 0x48, 0x0f, 0xb8, 0xc7, //0x0000276d popcntq      %rdi, %rax
	0x49, 0x01, 0xc3, //0x00002772 addq         %rax, %r11
	0x48, 0x8b, 0x7c, 0x24, 0x20, //0x00002775 movq         $32(%rsp), %rdi
	0x48, 0x83, 0xc7, 0x40, //0x0000277a addq         $64, %rdi
	0x49, 0x83, 0xc1, 0xc0, //0x0000277e addq         $-64, %r9
	0x4d, 0x89, 0xf0, //0x00002782 movq         %r14, %r8
	//0x00002785 LBB0_491
	0x49, 0x83, 0xf9, 0x40, //0x00002785 cmpq         $64, %r9
	0x0f, 0x8c, 0xad, 0x01, 0x00, 0x00, //0x00002789 jl           LBB0_499
	//0x0000278f LBB0_492
	0xc5, 0xfa, 0x6f, 0x17, //0x0000278f vmovdqu      (%rdi), %xmm2
	0xc5, 0xfa, 0x6f, 0x77, 0x10, //0x00002793 vmovdqu      $16(%rdi), %xmm6
	0xc5, 0xfa, 0x6f, 0x6f, 0x20, //0x00002798 vmovdqu      $32(%rdi), %xmm5
	0x48, 0x89, 0x7c, 0x24, 0x20, //0x0000279d movq         %rdi, $32(%rsp)
	0xc5, 0xfa, 0x6f, 0x7f, 0x30, //0x000027a2 vmovdqu      $48(%rdi), %xmm7
	0xc5, 0xe9, 0x74, 0xd8, //0x000027a7 vpcmpeqb     %xmm0, %xmm2, %xmm3
	0xc5, 0xf9, 0xd7, 0xf3, //0x000027ab vpmovmskb    %xmm3, %esi
	0xc5, 0xc9, 0x74, 0xd8, //0x000027af vpcmpeqb     %xmm0, %xmm6, %xmm3
	0xc5, 0xf9, 0xd7, 0xc3, //0x000027b3 vpmovmskb    %xmm3, %eax
	0xc5, 0xd1, 0x74, 0xd8, //0x000027b7 vpcmpeqb     %xmm0, %xmm5, %xmm3
	0xc5, 0xf9, 0xd7, 0xfb, //0x000027bb vpmovmskb    %xmm3, %edi
	0xc5, 0xc1, 0x74, 0xd8, //0x000027bf vpcmpeqb     %xmm0, %xmm7, %xmm3
	0xc5, 0xf9, 0xd7, 0xdb, //0x000027c3 vpmovmskb    %xmm3, %ebx
	0x48, 0xc1, 0xe3, 0x30, //0x000027c7 shlq         $48, %rbx
	0x48, 0xc1, 0xe7, 0x20, //0x000027cb shlq         $32, %rdi
	0x48, 0xc1, 0xe0, 0x10, //0x000027cf shlq         $16, %rax
	0x48, 0x09, 0xc6, //0x000027d3 orq          %rax, %rsi
	0x48, 0x09, 0xfe, //0x000027d6 orq          %rdi, %rsi
	0x48, 0x09, 0xde, //0x000027d9 orq          %rbx, %rsi
	0xc5, 0xe9, 0x74, 0xd9, //0x000027dc vpcmpeqb     %xmm1, %xmm2, %xmm3
	0xc5, 0xf9, 0xd7, 0xfb, //0x000027e0 vpmovmskb    %xmm3, %edi
	0xc5, 0xc9, 0x74, 0xd9, //0x000027e4 vpcmpeqb     %xmm1, %xmm6, %xmm3
	0xc5, 0xf9, 0xd7, 0xc3, //0x000027e8 vpmovmskb    %xmm3, %eax
	0xc5, 0xd1, 0x74, 0xd9, //0x000027ec vpcmpeqb     %xmm1, %xmm5, %xmm3
	0xc5, 0xf9, 0xd7, 0xdb, //0x000027f0 vpmovmskb    %xmm3, %ebx
	0xc5, 0xc1, 0x74, 0xd9, //0x000027f4 vpcmpeqb     %xmm1, %xmm7, %xmm3
	0xc5, 0xf9, 0xd7, 0xcb, //0x000027f8 vpmovmskb    %xmm3, %ecx
	0x48, 0xc1, 0xe1, 0x30, //0x000027fc shlq         $48, %rcx
	0x48, 0xc1, 0xe3, 0x20, //0x00002800 shlq         $32, %rbx
	0x48, 0xc1, 0xe0, 0x10, //0x00002804 shlq         $16, %rax
	0x48, 0x09, 0xc7, //0x00002808 orq          %rax, %rdi
	0x48, 0x09, 0xdf, //0x0000280b orq          %rbx, %rdi
	0x48, 0x09, 0xcf, //0x0000280e orq          %rcx, %rdi
	0x48, 0x89, 0xf8, //0x00002811 movq         %rdi, %rax
	0x4c, 0x09, 0xd0, //0x00002814 orq          %r10, %rax
	0x0f, 0x84, 0x4b, 0x00, 0x00, 0x00, //0x00002817 je           LBB0_494
	0x4c, 0x89, 0xd0, //0x0000281d movq         %r10, %rax
	0x48, 0xf7, 0xd0, //0x00002820 notq         %rax
	0x48, 0x21, 0xf8, //0x00002823 andq         %rdi, %rax
	0x4c, 0x8d, 0x34, 0x00, //0x00002826 leaq         (%rax,%rax), %r14
	0x4d, 0x09, 0xd6, //0x0000282a orq          %r10, %r14
	0x4c, 0x89, 0xf1, //0x0000282d movq         %r14, %rcx
	0x48, 0xbb, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00002830 movabsq      $-6148914691236517206, %rbx
	0x48, 0x31, 0xd9, //0x0000283a xorq         %rbx, %rcx
	0x48, 0x21, 0xdf, //0x0000283d andq         %rbx, %rdi
	0x48, 0x21, 0xcf, //0x00002840 andq         %rcx, %rdi
	0x45, 0x31, 0xd2, //0x00002843 xorl         %r10d, %r10d
	0x48, 0x01, 0xc7, //0x00002846 addq         %rax, %rdi
	0x41, 0x0f, 0x92, 0xc2, //0x00002849 setb         %r10b
	0x48, 0x01, 0xff, //0x0000284d addq         %rdi, %rdi
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00002850 movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc7, //0x0000285a xorq         %rax, %rdi
	0x4c, 0x21, 0xf7, //0x0000285d andq         %r14, %rdi
	0x48, 0xf7, 0xd7, //0x00002860 notq         %rdi
	0xe9, 0x0a, 0x00, 0x00, 0x00, //0x00002863 jmp          LBB0_495
	//0x00002868 LBB0_494
	0x48, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x00002868 movq         $-1, %rdi
	0x45, 0x31, 0xd2, //0x0000286f xorl         %r10d, %r10d
	//0x00002872 LBB0_495
	0x48, 0x21, 0xf7, //0x00002872 andq         %rsi, %rdi
	0xc4, 0xe1, 0xf9, 0x6e, 0xdf, //0x00002875 vmovq        %rdi, %xmm3
	0xc4, 0xc3, 0x61, 0x44, 0xd9, 0x00, //0x0000287a vpclmulqdq   $0, %xmm9, %xmm3, %xmm3
	0xc4, 0xc1, 0xf9, 0x7e, 0xde, //0x00002880 vmovq        %xmm3, %r14
	0x4d, 0x31, 0xc6, //0x00002885 xorq         %r8, %r14
	0xc5, 0x99, 0x74, 0xda, //0x00002888 vpcmpeqb     %xmm2, %xmm12, %xmm3
	0xc5, 0xf9, 0xd7, 0xfb, //0x0000288c vpmovmskb    %xmm3, %edi
	0xc5, 0x99, 0x74, 0xde, //0x00002890 vpcmpeqb     %xmm6, %xmm12, %xmm3
	0xc5, 0xf9, 0xd7, 0xc3, //0x00002894 vpmovmskb    %xmm3, %eax
	0xc5, 0x99, 0x74, 0xdd, //0x00002898 vpcmpeqb     %xmm5, %xmm12, %xmm3
	0xc5, 0xf9, 0xd7, 0xcb, //0x0000289c vpmovmskb    %xmm3, %ecx
	0xc5, 0x99, 0x74, 0xdf, //0x000028a0 vpcmpeqb     %xmm7, %xmm12, %xmm3
	0xc5, 0xf9, 0xd7, 0xdb, //0x000028a4 vpmovmskb    %xmm3, %ebx
	0x48, 0xc1, 0xe3, 0x30, //0x000028a8 shlq         $48, %rbx
	0x48, 0xc1, 0xe1, 0x20, //0x000028ac shlq         $32, %rcx
	0x48, 0xc1, 0xe0, 0x10, //0x000028b0 shlq         $16, %rax
	0x48, 0x09, 0xc7, //0x000028b4 orq          %rax, %rdi
	0x48, 0x09, 0xcf, //0x000028b7 orq          %rcx, %rdi
	0x48, 0x09, 0xdf, //0x000028ba orq          %rbx, %rdi
	0x4d, 0x89, 0xf0, //0x000028bd movq         %r14, %r8
	0x49, 0xf7, 0xd0, //0x000028c0 notq         %r8
	0x4c, 0x21, 0xc7, //0x000028c3 andq         %r8, %rdi
	0xc5, 0x91, 0x74, 0xd2, //0x000028c6 vpcmpeqb     %xmm2, %xmm13, %xmm2
	0xc5, 0xf9, 0xd7, 0xc2, //0x000028ca vpmovmskb    %xmm2, %eax
	0xc5, 0x91, 0x74, 0xd6, //0x000028ce vpcmpeqb     %xmm6, %xmm13, %xmm2
	0xc5, 0xf9, 0xd7, 0xda, //0x000028d2 vpmovmskb    %xmm2, %ebx
	0xc5, 0x91, 0x74, 0xd5, //0x000028d6 vpcmpeqb     %xmm5, %xmm13, %xmm2
	0xc5, 0xf9, 0xd7, 0xf2, //0x000028da vpmovmskb    %xmm2, %esi
	0xc5, 0x91, 0x74, 0xd7, //0x000028de vpcmpeqb     %xmm7, %xmm13, %xmm2
	0xc5, 0xf9, 0xd7, 0xca, //0x000028e2 vpmovmskb    %xmm2, %ecx
	0x48, 0xc1, 0xe1, 0x30, //0x000028e6 shlq         $48, %rcx
	0x48, 0xc1, 0xe6, 0x20, //0x000028ea shlq         $32, %rsi
	0x48, 0xc1, 0xe3, 0x10, //0x000028ee shlq         $16, %rbx
	0x48, 0x09, 0xd8, //0x000028f2 orq          %rbx, %rax
	0x48, 0x09, 0xf0, //0x000028f5 orq          %rsi, %rax
	0x48, 0x09, 0xc8, //0x000028f8 orq          %rcx, %rax
	0x4c, 0x21, 0xc0, //0x000028fb andq         %r8, %rax
	0x0f, 0x84, 0x65, 0xfe, 0xff, 0xff, //0x000028fe je           LBB0_490
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x00002904 movq         $24(%rsp), %r8
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002909 .p2align 4, 0x90
	//0x00002910 LBB0_497
	0x48, 0x8d, 0x58, 0xff, //0x00002910 leaq         $-1(%rax), %rbx
	0x48, 0x89, 0xd9, //0x00002914 movq         %rbx, %rcx
	0x48, 0x21, 0xf9, //0x00002917 andq         %rdi, %rcx
	0xf3, 0x48, 0x0f, 0xb8, 0xc9, //0x0000291a popcntq      %rcx, %rcx
	0x4c, 0x01, 0xd9, //0x0000291f addq         %r11, %rcx
	0x48, 0x39, 0xd1, //0x00002922 cmpq         %rdx, %rcx
	0x0f, 0x86, 0x4d, 0x01, 0x00, 0x00, //0x00002925 jbe          LBB0_515
	0x48, 0xff, 0xc2, //0x0000292b incq         %rdx
	0x48, 0x21, 0xd8, //0x0000292e andq         %rbx, %rax
	0x0f, 0x85, 0xd9, 0xff, 0xff, 0xff, //0x00002931 jne          LBB0_497
	0xe9, 0x2d, 0xfe, 0xff, 0xff, //0x00002937 jmp          LBB0_490
	//0x0000293c LBB0_499
	0x4d, 0x85, 0xc9, //0x0000293c testq        %r9, %r9
	0x0f, 0x8e, 0xff, 0x01, 0x00, 0x00, //0x0000293f jle          LBB0_523
	0xc5, 0x7e, 0x7f, 0x84, 0x24, 0x80, 0x00, 0x00, 0x00, //0x00002945 vmovdqu      %ymm8, $128(%rsp)
	0xc5, 0x7e, 0x7f, 0x44, 0x24, 0x60, //0x0000294e vmovdqu      %ymm8, $96(%rsp)
	0x89, 0xf8, //0x00002954 movl         %edi, %eax
	0x25, 0xff, 0x0f, 0x00, 0x00, //0x00002956 andl         $4095, %eax
	0x3d, 0xc1, 0x0f, 0x00, 0x00, //0x0000295b cmpl         $4033, %eax
	0x0f, 0x82, 0x37, 0x00, 0x00, 0x00, //0x00002960 jb           LBB0_503
	0x48, 0x89, 0xf8, //0x00002966 movq         %rdi, %rax
	0x49, 0x83, 0xf9, 0x20, //0x00002969 cmpq         $32, %r9
	0x0f, 0x82, 0x34, 0x00, 0x00, 0x00, //0x0000296d jb           LBB0_504
	0xc5, 0xf8, 0x10, 0x10, //0x00002973 vmovups      (%rax), %xmm2
	0xc5, 0xf8, 0x11, 0x54, 0x24, 0x60, //0x00002977 vmovups      %xmm2, $96(%rsp)
	0xc5, 0xf8, 0x10, 0x50, 0x10, //0x0000297d vmovups      $16(%rax), %xmm2
	0xc5, 0xf8, 0x11, 0x54, 0x24, 0x70, //0x00002982 vmovups      %xmm2, $112(%rsp)
	0x48, 0x83, 0xc0, 0x20, //0x00002988 addq         $32, %rax
	0x49, 0x8d, 0x79, 0xe0, //0x0000298c leaq         $-32(%r9), %rdi
	0x48, 0x8d, 0xb4, 0x24, 0x80, 0x00, 0x00, 0x00, //0x00002990 leaq         $128(%rsp), %rsi
	0xe9, 0x12, 0x00, 0x00, 0x00, //0x00002998 jmp          LBB0_505
	//0x0000299d LBB0_503
	0x4c, 0x8b, 0x7c, 0x24, 0x28, //0x0000299d movq         $40(%rsp), %r15
	0xe9, 0xe8, 0xfd, 0xff, 0xff, //0x000029a2 jmp          LBB0_492
	//0x000029a7 LBB0_504
	0x48, 0x8d, 0x74, 0x24, 0x60, //0x000029a7 leaq         $96(%rsp), %rsi
	0x4c, 0x89, 0xcf, //0x000029ac movq         %r9, %rdi
	//0x000029af LBB0_505
	0x48, 0x83, 0xff, 0x10, //0x000029af cmpq         $16, %rdi
	0x0f, 0x82, 0x4d, 0x00, 0x00, 0x00, //0x000029b3 jb           LBB0_506
	0xc5, 0xf8, 0x10, 0x10, //0x000029b9 vmovups      (%rax), %xmm2
	0xc5, 0xf8, 0x11, 0x16, //0x000029bd vmovups      %xmm2, (%rsi)
	0x48, 0x83, 0xc0, 0x10, //0x000029c1 addq         $16, %rax
	0x48, 0x83, 0xc6, 0x10, //0x000029c5 addq         $16, %rsi
	0x48, 0x83, 0xc7, 0xf0, //0x000029c9 addq         $-16, %rdi
	0x48, 0x83, 0xff, 0x08, //0x000029cd cmpq         $8, %rdi
	0x0f, 0x83, 0x39, 0x00, 0x00, 0x00, //0x000029d1 jae          LBB0_513
	//0x000029d7 LBB0_507
	0x48, 0x83, 0xff, 0x04, //0x000029d7 cmpq         $4, %rdi
	0x0f, 0x8c, 0x51, 0x00, 0x00, 0x00, //0x000029db jl           LBB0_508
	//0x000029e1 LBB0_514
	0x48, 0x89, 0xc1, //0x000029e1 movq         %rax, %rcx
	0x8b, 0x00, //0x000029e4 movl         (%rax), %eax
	0x89, 0x06, //0x000029e6 movl         %eax, (%rsi)
	0x48, 0x83, 0xc1, 0x04, //0x000029e8 addq         $4, %rcx
	0x48, 0x89, 0xc8, //0x000029ec movq         %rcx, %rax
	0x48, 0x83, 0xc6, 0x04, //0x000029ef addq         $4, %rsi
	0x48, 0x83, 0xc7, 0xfc, //0x000029f3 addq         $-4, %rdi
	0x48, 0x83, 0xff, 0x02, //0x000029f7 cmpq         $2, %rdi
	0x0f, 0x83, 0x3b, 0x00, 0x00, 0x00, //0x000029fb jae          LBB0_509
	0xe9, 0x4e, 0x00, 0x00, 0x00, //0x00002a01 jmp          LBB0_510
	//0x00002a06 LBB0_506
	0x48, 0x83, 0xff, 0x08, //0x00002a06 cmpq         $8, %rdi
	0x0f, 0x82, 0xc7, 0xff, 0xff, 0xff, //0x00002a0a jb           LBB0_507
	//0x00002a10 LBB0_513
	0x48, 0x89, 0xc1, //0x00002a10 movq         %rax, %rcx
	0x48, 0x8b, 0x00, //0x00002a13 movq         (%rax), %rax
	0x48, 0x89, 0x06, //0x00002a16 movq         %rax, (%rsi)
	0x48, 0x83, 0xc1, 0x08, //0x00002a19 addq         $8, %rcx
	0x48, 0x89, 0xc8, //0x00002a1d movq         %rcx, %rax
	0x48, 0x83, 0xc6, 0x08, //0x00002a20 addq         $8, %rsi
	0x48, 0x83, 0xc7, 0xf8, //0x00002a24 addq         $-8, %rdi
	0x48, 0x83, 0xff, 0x04, //0x00002a28 cmpq         $4, %rdi
	0x0f, 0x8d, 0xaf, 0xff, 0xff, 0xff, //0x00002a2c jge          LBB0_514
	//0x00002a32 LBB0_508
	0x48, 0x83, 0xff, 0x02, //0x00002a32 cmpq         $2, %rdi
	0x0f, 0x82, 0x18, 0x00, 0x00, 0x00, //0x00002a36 jb           LBB0_510
	//0x00002a3c LBB0_509
	0x48, 0x89, 0xc1, //0x00002a3c movq         %rax, %rcx
	0x0f, 0xb7, 0x00, //0x00002a3f movzwl       (%rax), %eax
	0x66, 0x89, 0x06, //0x00002a42 movw         %ax, (%rsi)
	0x48, 0x83, 0xc1, 0x02, //0x00002a45 addq         $2, %rcx
	0x48, 0x83, 0xc6, 0x02, //0x00002a49 addq         $2, %rsi
	0x48, 0x83, 0xc7, 0xfe, //0x00002a4d addq         $-2, %rdi
	0x48, 0x89, 0xc8, //0x00002a51 movq         %rcx, %rax
	//0x00002a54 LBB0_510
	0x48, 0x8d, 0x4c, 0x24, 0x60, //0x00002a54 leaq         $96(%rsp), %rcx
	0x48, 0x85, 0xff, //0x00002a59 testq        %rdi, %rdi
	0x48, 0x89, 0xcf, //0x00002a5c movq         %rcx, %rdi
	0x4c, 0x8b, 0x7c, 0x24, 0x28, //0x00002a5f movq         $40(%rsp), %r15
	0x0f, 0x84, 0x25, 0xfd, 0xff, 0xff, //0x00002a64 je           LBB0_492
	0x8a, 0x00, //0x00002a6a movb         (%rax), %al
	0x88, 0x06, //0x00002a6c movb         %al, (%rsi)
	0x48, 0x8d, 0x7c, 0x24, 0x60, //0x00002a6e leaq         $96(%rsp), %rdi
	0xe9, 0x17, 0xfd, 0xff, 0xff, //0x00002a73 jmp          LBB0_492
	//0x00002a78 LBB0_515
	0x4c, 0x8b, 0x5c, 0x24, 0x38, //0x00002a78 movq         $56(%rsp), %r11
	0x49, 0x8b, 0x0b, //0x00002a7d movq         (%r11), %rcx
	0x48, 0x0f, 0xbc, 0xc0, //0x00002a80 bsfq         %rax, %rax
	0x4c, 0x29, 0xc8, //0x00002a84 subq         %r9, %rax
	0x4c, 0x8d, 0x54, 0x08, 0x01, //0x00002a87 leaq         $1(%rax,%rcx), %r10
	0x4d, 0x89, 0x10, //0x00002a8c movq         %r10, (%r8)
	0x49, 0x8b, 0x03, //0x00002a8f movq         (%r11), %rax
	0x49, 0x39, 0xc2, //0x00002a92 cmpq         %rax, %r10
	0x4c, 0x0f, 0x47, 0xd0, //0x00002a95 cmovaq       %rax, %r10
	0x4d, 0x89, 0x10, //0x00002a99 movq         %r10, (%r8)
	//0x00002a9c LBB0_516
	0x49, 0xb9, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00002a9c movabsq      $4294977024, %r9
	0x4c, 0x8b, 0x74, 0x24, 0x48, //0x00002aa6 movq         $72(%rsp), %r14
	0xe9, 0x90, 0xf6, 0xff, 0xff, //0x00002aab jmp          LBB0_418
	//0x00002ab0 LBB0_457
	0x4d, 0x85, 0xdb, //0x00002ab0 testq        %r11, %r11
	0x0f, 0x85, 0xa5, 0x00, 0x00, 0x00, //0x00002ab3 jne          LBB0_524
	0x4a, 0x8d, 0x4c, 0x08, 0x01, //0x00002ab9 leaq         $1(%rax,%r9), %rcx
	0x48, 0xf7, 0xd0, //0x00002abe notq         %rax
	0x4c, 0x01, 0xc0, //0x00002ac1 addq         %r8, %rax
	//0x00002ac4 LBB0_459
	0x4c, 0x8b, 0x5c, 0x24, 0x38, //0x00002ac4 movq         $56(%rsp), %r11
	//0x00002ac9 LBB0_460
	0x48, 0x85, 0xc0, //0x00002ac9 testq        %rax, %rax
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x00002acc movq         $24(%rsp), %r8
	0x49, 0xb9, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00002ad1 movabsq      $4294977024, %r9
	0x0f, 0x8f, 0x1d, 0x00, 0x00, 0x00, //0x00002adb jg           LBB0_518
	0xe9, 0x5a, 0xf6, 0xff, 0xff, //0x00002ae1 jmp          LBB0_418
	//0x00002ae6 LBB0_517
	0x48, 0xc7, 0xc2, 0xfe, 0xff, 0xff, 0xff, //0x00002ae6 movq         $-2, %rdx
	0xbe, 0x02, 0x00, 0x00, 0x00, //0x00002aed movl         $2, %esi
	0x48, 0x01, 0xf1, //0x00002af2 addq         %rsi, %rcx
	0x48, 0x01, 0xd0, //0x00002af5 addq         %rdx, %rax
	0x0f, 0x8e, 0x42, 0xf6, 0xff, 0xff, //0x00002af8 jle          LBB0_418
	//0x00002afe LBB0_518
	0x0f, 0xb6, 0x11, //0x00002afe movzbl       (%rcx), %edx
	0x80, 0xfa, 0x5c, //0x00002b01 cmpb         $92, %dl
	0x0f, 0x84, 0xdc, 0xff, 0xff, 0xff, //0x00002b04 je           LBB0_517
	0x80, 0xfa, 0x22, //0x00002b0a cmpb         $34, %dl
	0x0f, 0x84, 0x1d, 0x00, 0x00, 0x00, //0x00002b0d je           LBB0_521
	0x48, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x00002b13 movq         $-1, %rdx
	0xbe, 0x01, 0x00, 0x00, 0x00, //0x00002b1a movl         $1, %esi
	0x48, 0x01, 0xf1, //0x00002b1f addq         %rsi, %rcx
	0x48, 0x01, 0xd0, //0x00002b22 addq         %rdx, %rax
	0x0f, 0x8f, 0xd3, 0xff, 0xff, 0xff, //0x00002b25 jg           LBB0_518
	0xe9, 0x10, 0xf6, 0xff, 0xff, //0x00002b2b jmp          LBB0_418
	//0x00002b30 LBB0_521
	0x48, 0x29, 0xf9, //0x00002b30 subq         %rdi, %rcx
	0x48, 0xff, 0xc1, //0x00002b33 incq         %rcx
	0xe9, 0x0a, 0xfc, 0xff, 0xff, //0x00002b36 jmp          LBB0_488
	//0x00002b3b LBB0_522
	0x4a, 0x8d, 0x0c, 0x17, //0x00002b3b leaq         (%rdi,%r10), %rcx
	0xe9, 0x80, 0xff, 0xff, 0xff, //0x00002b3f jmp          LBB0_459
	//0x00002b44 LBB0_523
	0x4c, 0x8b, 0x5c, 0x24, 0x38, //0x00002b44 movq         $56(%rsp), %r11
	0x4d, 0x8b, 0x13, //0x00002b49 movq         (%r11), %r10
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x00002b4c movq         $24(%rsp), %r8
	0x4d, 0x89, 0x10, //0x00002b51 movq         %r10, (%r8)
	0x4c, 0x8b, 0x7c, 0x24, 0x28, //0x00002b54 movq         $40(%rsp), %r15
	0xe9, 0x3e, 0xff, 0xff, 0xff, //0x00002b59 jmp          LBB0_516
	//0x00002b5e LBB0_524
	0x49, 0x8d, 0x48, 0xff, //0x00002b5e leaq         $-1(%r8), %rcx
	0x48, 0x39, 0xc1, //0x00002b62 cmpq         %rax, %rcx
	0x0f, 0x85, 0x14, 0x00, 0x00, 0x00, //0x00002b65 jne          LBB0_526
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x00002b6b movq         $24(%rsp), %r8
	0x4c, 0x8b, 0x7c, 0x24, 0x28, //0x00002b70 movq         $40(%rsp), %r15
	0x4c, 0x8b, 0x5c, 0x24, 0x38, //0x00002b75 movq         $56(%rsp), %r11
	0xe9, 0x1d, 0xff, 0xff, 0xff, //0x00002b7a jmp          LBB0_516
	//0x00002b7f LBB0_526
	0x4a, 0x8d, 0x4c, 0x08, 0x02, //0x00002b7f leaq         $2(%rax,%r9), %rcx
	0x49, 0x29, 0xc0, //0x00002b84 subq         %rax, %r8
	0x49, 0x83, 0xc0, 0xfe, //0x00002b87 addq         $-2, %r8
	0x4c, 0x89, 0xc0, //0x00002b8b movq         %r8, %rax
	0x4c, 0x8b, 0x7c, 0x24, 0x28, //0x00002b8e movq         $40(%rsp), %r15
	0x4c, 0x8b, 0x5c, 0x24, 0x38, //0x00002b93 movq         $56(%rsp), %r11
	0x4c, 0x8b, 0x74, 0x24, 0x48, //0x00002b98 movq         $72(%rsp), %r14
	0xe9, 0x27, 0xff, 0xff, 0xff, //0x00002b9d jmp          LBB0_460
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002ba2 .p2align 4, 0x90
	//0x00002bb0 LBB0_446
	0x49, 0x83, 0xc5, 0x10, //0x00002bb0 addq         $16, %r13
	0x4c, 0x89, 0xd0, //0x00002bb4 movq         %r10, %rax
	0x4c, 0x3b, 0xac, 0x24, 0xc0, 0x00, 0x00, 0x00, //0x00002bb7 cmpq         $192(%rsp), %r13
	0x48, 0x8b, 0x4c, 0x24, 0x40, //0x00002bbf movq         $64(%rsp), %rcx
	0x0f, 0x85, 0xc9, 0xd5, 0xff, 0xff, //0x00002bc4 jne          LBB0_2
	//0x00002bca LBB0_447
	0x48, 0x85, 0xc9, //0x00002bca testq        %rcx, %rcx
	0x0f, 0x84, 0x95, 0x00, 0x00, 0x00, //0x00002bcd je           LBB0_527
	0x41, 0xbb, 0x01, 0x00, 0x00, 0x00, //0x00002bd3 movl         $1, %r11d
	0xc4, 0xc1, 0xf9, 0x6e, 0xc3, //0x00002bd9 vmovq        %r11, %xmm0
	0xc5, 0xfa, 0x7f, 0x01, //0x00002bde vmovdqu      %xmm0, (%rcx)
	0x4d, 0x8b, 0x27, //0x00002be2 movq         (%r15), %r12
	0x4c, 0x89, 0xe0, //0x00002be5 movq         %r12, %rax
	0x48, 0xf7, 0xd0, //0x00002be8 notq         %rax
	0x48, 0x89, 0x44, 0x24, 0x38, //0x00002beb movq         %rax, $56(%rsp)
	0xb8, 0x01, 0x00, 0x00, 0x00, //0x00002bf0 movl         $1, %eax
	0x4c, 0x29, 0xe0, //0x00002bf5 subq         %r12, %rax
	0x48, 0x89, 0x44, 0x24, 0x58, //0x00002bf8 movq         %rax, $88(%rsp)
	0x4d, 0x8b, 0x38, //0x00002bfd movq         (%r8), %r15
	0x49, 0x8d, 0x44, 0x24, 0x05, //0x00002c00 leaq         $5(%r12), %rax
	0x48, 0x89, 0x84, 0x24, 0xb0, 0x00, 0x00, 0x00, //0x00002c05 movq         %rax, $176(%rsp)
	0x48, 0xc7, 0x44, 0x24, 0x48, 0xff, 0xff, 0xff, 0xff, //0x00002c0d movq         $-1, $72(%rsp)
	0xc5, 0xfa, 0x6f, 0x05, 0x12, 0xd4, 0xff, 0xff, //0x00002c16 vmovdqu      $-11246(%rip), %xmm0  /* LCPI0_3+0(%rip) */
	0xc5, 0xfa, 0x6f, 0x0d, 0x1a, 0xd4, 0xff, 0xff, //0x00002c1e vmovdqu      $-11238(%rip), %xmm1  /* LCPI0_4+0(%rip) */
	0xc5, 0x7a, 0x6f, 0x05, 0x52, 0xd4, 0xff, 0xff, //0x00002c26 vmovdqu      $-11182(%rip), %xmm8  /* LCPI0_8+0(%rip) */
	0xc5, 0x7a, 0x6f, 0x0d, 0x5a, 0xd4, 0xff, 0xff, //0x00002c2e vmovdqu      $-11174(%rip), %xmm9  /* LCPI0_9+0(%rip) */
	0xc5, 0x7a, 0x6f, 0x15, 0x62, 0xd4, 0xff, 0xff, //0x00002c36 vmovdqu      $-11166(%rip), %xmm10  /* LCPI0_10+0(%rip) */
	0xc5, 0x7a, 0x6f, 0x1d, 0x6a, 0xd4, 0xff, 0xff, //0x00002c3e vmovdqu      $-11158(%rip), %xmm11  /* LCPI0_11+0(%rip) */
	0xc5, 0x7a, 0x6f, 0x25, 0xc2, 0xd3, 0xff, 0xff, //0x00002c46 vmovdqu      $-11326(%rip), %xmm12  /* LCPI0_1+0(%rip) */
	0xc5, 0x7a, 0x6f, 0x2d, 0x6a, 0xd4, 0xff, 0xff, //0x00002c4e vmovdqu      $-11158(%rip), %xmm13  /* LCPI0_12+0(%rip) */
	0xc5, 0xfa, 0x6f, 0x15, 0x72, 0xd4, 0xff, 0xff, //0x00002c56 vmovdqu      $-11150(%rip), %xmm2  /* LCPI0_13+0(%rip) */
	0x4c, 0x89, 0x64, 0x24, 0x30, //0x00002c5e movq         %r12, $48(%rsp)
	0xe9, 0x64, 0x01, 0x00, 0x00, //0x00002c63 jmp          LBB0_556
	//0x00002c68 LBB0_527
	0x4d, 0x8b, 0x1f, //0x00002c68 movq         (%r15), %r11
	0x49, 0x8b, 0x77, 0x08, //0x00002c6b movq         $8(%r15), %rsi
	0x49, 0x8b, 0x10, //0x00002c6f movq         (%r8), %rdx
	0x48, 0x89, 0xd1, //0x00002c72 movq         %rdx, %rcx
	0x48, 0x29, 0xf1, //0x00002c75 subq         %rsi, %rcx
	0x0f, 0x83, 0x26, 0x00, 0x00, 0x00, //0x00002c78 jae          LBB0_532
	0x41, 0x8a, 0x04, 0x13, //0x00002c7e movb         (%r11,%rdx), %al
	0x3c, 0x0d, //0x00002c82 cmpb         $13, %al
	0x0f, 0x84, 0x1a, 0x00, 0x00, 0x00, //0x00002c84 je           LBB0_532
	0x3c, 0x20, //0x00002c8a cmpb         $32, %al
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x00002c8c je           LBB0_532
	0x04, 0xf7, //0x00002c92 addb         $-9, %al
	0x3c, 0x01, //0x00002c94 cmpb         $1, %al
	0x0f, 0x86, 0x08, 0x00, 0x00, 0x00, //0x00002c96 jbe          LBB0_532
	0x49, 0x89, 0xd4, //0x00002c9c movq         %rdx, %r12
	0xe9, 0x42, 0x17, 0x00, 0x00, //0x00002c9f jmp          LBB0_843
	//0x00002ca4 LBB0_532
	0x4c, 0x8d, 0x62, 0x01, //0x00002ca4 leaq         $1(%rdx), %r12
	0x49, 0x39, 0xf4, //0x00002ca8 cmpq         %rsi, %r12
	0x0f, 0x83, 0x1e, 0x00, 0x00, 0x00, //0x00002cab jae          LBB0_536
	0x43, 0x8a, 0x04, 0x23, //0x00002cb1 movb         (%r11,%r12), %al
	0x3c, 0x0d, //0x00002cb5 cmpb         $13, %al
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x00002cb7 je           LBB0_536
	0x3c, 0x20, //0x00002cbd cmpb         $32, %al
	0x0f, 0x84, 0x0a, 0x00, 0x00, 0x00, //0x00002cbf je           LBB0_536
	0x04, 0xf7, //0x00002cc5 addb         $-9, %al
	0x3c, 0x01, //0x00002cc7 cmpb         $1, %al
	0x0f, 0x87, 0x17, 0x17, 0x00, 0x00, //0x00002cc9 ja           LBB0_843
	//0x00002ccf LBB0_536
	0x4c, 0x8d, 0x62, 0x02, //0x00002ccf leaq         $2(%rdx), %r12
	0x49, 0x39, 0xf4, //0x00002cd3 cmpq         %rsi, %r12
	0x0f, 0x83, 0x1e, 0x00, 0x00, 0x00, //0x00002cd6 jae          LBB0_540
	0x43, 0x8a, 0x04, 0x23, //0x00002cdc movb         (%r11,%r12), %al
	0x3c, 0x0d, //0x00002ce0 cmpb         $13, %al
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x00002ce2 je           LBB0_540
	0x3c, 0x20, //0x00002ce8 cmpb         $32, %al
	0x0f, 0x84, 0x0a, 0x00, 0x00, 0x00, //0x00002cea je           LBB0_540
	0x04, 0xf7, //0x00002cf0 addb         $-9, %al
	0x3c, 0x01, //0x00002cf2 cmpb         $1, %al
	0x0f, 0x87, 0xec, 0x16, 0x00, 0x00, //0x00002cf4 ja           LBB0_843
	//0x00002cfa LBB0_540
	0x4c, 0x8d, 0x62, 0x03, //0x00002cfa leaq         $3(%rdx), %r12
	0x49, 0x39, 0xf4, //0x00002cfe cmpq         %rsi, %r12
	0x0f, 0x83, 0x1e, 0x00, 0x00, 0x00, //0x00002d01 jae          LBB0_544
	0x43, 0x8a, 0x04, 0x23, //0x00002d07 movb         (%r11,%r12), %al
	0x3c, 0x0d, //0x00002d0b cmpb         $13, %al
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x00002d0d je           LBB0_544
	0x3c, 0x20, //0x00002d13 cmpb         $32, %al
	0x0f, 0x84, 0x0a, 0x00, 0x00, 0x00, //0x00002d15 je           LBB0_544
	0x04, 0xf7, //0x00002d1b addb         $-9, %al
	0x3c, 0x01, //0x00002d1d cmpb         $1, %al
	0x0f, 0x87, 0xc1, 0x16, 0x00, 0x00, //0x00002d1f ja           LBB0_843
	//0x00002d25 LBB0_544
	0x48, 0x8d, 0x7a, 0x04, //0x00002d25 leaq         $4(%rdx), %rdi
	0x48, 0x39, 0xfe, //0x00002d29 cmpq         %rdi, %rsi
	0x0f, 0x86, 0x0e, 0x16, 0x00, 0x00, //0x00002d2c jbe          LBB0_831
	0x48, 0x39, 0xfe, //0x00002d32 cmpq         %rdi, %rsi
	0x0f, 0x84, 0x74, 0x16, 0x00, 0x00, //0x00002d35 je           LBB0_840
	0x49, 0x8d, 0x3c, 0x33, //0x00002d3b leaq         (%r11,%rsi), %rdi
	0x48, 0x83, 0xc1, 0x04, //0x00002d3f addq         $4, %rcx
	0x4e, 0x8d, 0x64, 0x1a, 0x05, //0x00002d43 leaq         $5(%rdx,%r11), %r12
	0x48, 0xb8, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00002d48 movabsq      $4294977024, %rax
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002d52 .p2align 4, 0x90
	//0x00002d60 LBB0_547
	0x41, 0x0f, 0xbe, 0x54, 0x24, 0xff, //0x00002d60 movsbl       $-1(%r12), %edx
	0x83, 0xfa, 0x20, //0x00002d66 cmpl         $32, %edx
	0x0f, 0x87, 0x5e, 0x16, 0x00, 0x00, //0x00002d69 ja           LBB0_842
	0x48, 0x0f, 0xa3, 0xd0, //0x00002d6f btq          %rdx, %rax
	0x0f, 0x83, 0x54, 0x16, 0x00, 0x00, //0x00002d73 jae          LBB0_842
	0x49, 0xff, 0xc4, //0x00002d79 incq         %r12
	0x48, 0xff, 0xc1, //0x00002d7c incq         %rcx
	0x0f, 0x85, 0xdb, 0xff, 0xff, 0xff, //0x00002d7f jne          LBB0_547
	0xe9, 0x28, 0x16, 0x00, 0x00, //0x00002d85 jmp          LBB0_841
	//0x00002d8a LBB0_550
	0x49, 0x89, 0x30, //0x00002d8a movq         %rsi, (%r8)
	0x49, 0x89, 0xf2, //0x00002d8d movq         %rsi, %r10
	0xe9, 0xec, 0x15, 0x00, 0x00, //0x00002d90 jmp          LBB0_837
	//0x00002d95 LBB0_551
	0x49, 0x81, 0xfb, 0xff, 0x0f, 0x00, 0x00, //0x00002d95 cmpq         $4095, %r11
	0x0f, 0x8f, 0x5c, 0x17, 0x00, 0x00, //0x00002d9c jg           LBB0_942
	0x49, 0x8d, 0x43, 0x01, //0x00002da2 leaq         $1(%r11), %rax
	0x48, 0x89, 0x01, //0x00002da6 movq         %rax, (%rcx)
	0x4a, 0xc7, 0x44, 0xd9, 0x08, 0x00, 0x00, 0x00, 0x00, //0x00002da9 movq         $0, $8(%rcx,%r11,8)
	//0x00002db2 LBB0_553
	0x4d, 0x89, 0xfa, //0x00002db2 movq         %r15, %r10
	//0x00002db5 LBB0_554
	0x48, 0x8b, 0x31, //0x00002db5 movq         (%rcx), %rsi
	0x4d, 0x89, 0xd7, //0x00002db8 movq         %r10, %r15
	0x49, 0x89, 0xf3, //0x00002dbb movq         %rsi, %r11
	0x4c, 0x8b, 0x6c, 0x24, 0x48, //0x00002dbe movq         $72(%rsp), %r13
	0x48, 0x85, 0xf6, //0x00002dc3 testq        %rsi, %rsi
	0x0f, 0x84, 0xc2, 0x15, 0x00, 0x00, //0x00002dc6 je           LBB0_839
	//0x00002dcc LBB0_556
	0x4c, 0x8b, 0x54, 0x24, 0x28, //0x00002dcc movq         $40(%rsp), %r10
	0x49, 0x8b, 0x52, 0x08, //0x00002dd1 movq         $8(%r10), %rdx
	0x4c, 0x89, 0xfe, //0x00002dd5 movq         %r15, %rsi
	0x48, 0x29, 0xd6, //0x00002dd8 subq         %rdx, %rsi
	0x0f, 0x83, 0x2f, 0x00, 0x00, 0x00, //0x00002ddb jae          LBB0_561
	0x43, 0x8a, 0x04, 0x3c, //0x00002de1 movb         (%r12,%r15), %al
	0x3c, 0x0d, //0x00002de5 cmpb         $13, %al
	0x0f, 0x84, 0x23, 0x00, 0x00, 0x00, //0x00002de7 je           LBB0_561
	0x3c, 0x20, //0x00002ded cmpb         $32, %al
	0x0f, 0x84, 0x1b, 0x00, 0x00, 0x00, //0x00002def je           LBB0_561
	0x04, 0xf7, //0x00002df5 addb         $-9, %al
	0x3c, 0x01, //0x00002df7 cmpb         $1, %al
	0x0f, 0x86, 0x11, 0x00, 0x00, 0x00, //0x00002df9 jbe          LBB0_561
	0x4d, 0x89, 0xf9, //0x00002dff movq         %r15, %r9
	0xe9, 0x37, 0x01, 0x00, 0x00, //0x00002e02 jmp          LBB0_583
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002e07 .p2align 4, 0x90
	//0x00002e10 LBB0_561
	0x4d, 0x8d, 0x4f, 0x01, //0x00002e10 leaq         $1(%r15), %r9
	0x49, 0x39, 0xd1, //0x00002e14 cmpq         %rdx, %r9
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00002e17 jae          LBB0_565
	0x43, 0x8a, 0x1c, 0x0c, //0x00002e1d movb         (%r12,%r9), %bl
	0x80, 0xfb, 0x0d, //0x00002e21 cmpb         $13, %bl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00002e24 je           LBB0_565
	0x80, 0xfb, 0x20, //0x00002e2a cmpb         $32, %bl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x00002e2d je           LBB0_565
	0x80, 0xc3, 0xf7, //0x00002e33 addb         $-9, %bl
	0x80, 0xfb, 0x01, //0x00002e36 cmpb         $1, %bl
	0x0f, 0x87, 0xff, 0x00, 0x00, 0x00, //0x00002e39 ja           LBB0_583
	0x90, //0x00002e3f .p2align 4, 0x90
	//0x00002e40 LBB0_565
	0x4d, 0x8d, 0x4f, 0x02, //0x00002e40 leaq         $2(%r15), %r9
	0x49, 0x39, 0xd1, //0x00002e44 cmpq         %rdx, %r9
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00002e47 jae          LBB0_569
	0x43, 0x8a, 0x1c, 0x0c, //0x00002e4d movb         (%r12,%r9), %bl
	0x80, 0xfb, 0x0d, //0x00002e51 cmpb         $13, %bl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00002e54 je           LBB0_569
	0x80, 0xfb, 0x20, //0x00002e5a cmpb         $32, %bl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x00002e5d je           LBB0_569
	0x80, 0xc3, 0xf7, //0x00002e63 addb         $-9, %bl
	0x80, 0xfb, 0x01, //0x00002e66 cmpb         $1, %bl
	0x0f, 0x87, 0xcf, 0x00, 0x00, 0x00, //0x00002e69 ja           LBB0_583
	0x90, //0x00002e6f .p2align 4, 0x90
	//0x00002e70 LBB0_569
	0x4d, 0x8d, 0x4f, 0x03, //0x00002e70 leaq         $3(%r15), %r9
	0x49, 0x39, 0xd1, //0x00002e74 cmpq         %rdx, %r9
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00002e77 jae          LBB0_573
	0x43, 0x8a, 0x1c, 0x0c, //0x00002e7d movb         (%r12,%r9), %bl
	0x80, 0xfb, 0x0d, //0x00002e81 cmpb         $13, %bl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00002e84 je           LBB0_573
	0x80, 0xfb, 0x20, //0x00002e8a cmpb         $32, %bl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x00002e8d je           LBB0_573
	0x80, 0xc3, 0xf7, //0x00002e93 addb         $-9, %bl
	0x80, 0xfb, 0x01, //0x00002e96 cmpb         $1, %bl
	0x0f, 0x87, 0x9f, 0x00, 0x00, 0x00, //0x00002e99 ja           LBB0_583
	0x90, //0x00002e9f .p2align 4, 0x90
	//0x00002ea0 LBB0_573
	0x49, 0x8d, 0x7f, 0x04, //0x00002ea0 leaq         $4(%r15), %rdi
	0x48, 0x39, 0xfa, //0x00002ea4 cmpq         %rdi, %rdx
	0x0f, 0x86, 0x93, 0x14, 0x00, 0x00, //0x00002ea7 jbe          LBB0_831
	0x48, 0x39, 0xfa, //0x00002ead cmpq         %rdi, %rdx
	0x0f, 0x84, 0x5a, 0x00, 0x00, 0x00, //0x00002eb0 je           LBB0_579
	0x49, 0x8d, 0x3c, 0x14, //0x00002eb6 leaq         (%r12,%rdx), %rdi
	0x48, 0x83, 0xc6, 0x04, //0x00002eba addq         $4, %rsi
	0x4c, 0x03, 0xbc, 0x24, 0xb0, 0x00, 0x00, 0x00, //0x00002ebe addq         $176(%rsp), %r15
	0x4d, 0x89, 0xf9, //0x00002ec6 movq         %r15, %r9
	0x48, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00002ec9 movabsq      $4294977024, %rbx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002ed3 .p2align 4, 0x90
	//0x00002ee0 LBB0_576
	0x41, 0x0f, 0xbe, 0x41, 0xff, //0x00002ee0 movsbl       $-1(%r9), %eax
	0x83, 0xf8, 0x20, //0x00002ee5 cmpl         $32, %eax
	0x0f, 0x87, 0x42, 0x00, 0x00, 0x00, //0x00002ee8 ja           LBB0_582
	0x48, 0x0f, 0xa3, 0xc3, //0x00002eee btq          %rax, %rbx
	0x0f, 0x83, 0x38, 0x00, 0x00, 0x00, //0x00002ef2 jae          LBB0_582
	0x49, 0xff, 0xc1, //0x00002ef8 incq         %r9
	0x48, 0xff, 0xc6, //0x00002efb incq         %rsi
	0x0f, 0x85, 0xdc, 0xff, 0xff, 0xff, //0x00002efe jne          LBB0_576
	0xe9, 0x0a, 0x00, 0x00, 0x00, //0x00002f04 jmp          LBB0_580
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002f09 .p2align 4, 0x90
	//0x00002f10 LBB0_579
	0x4c, 0x01, 0xe7, //0x00002f10 addq         %r12, %rdi
	//0x00002f13 LBB0_580
	0x4c, 0x29, 0xe7, //0x00002f13 subq         %r12, %rdi
	0x49, 0x89, 0xf9, //0x00002f16 movq         %rdi, %r9
	0x49, 0x39, 0xd1, //0x00002f19 cmpq         %rdx, %r9
	0x0f, 0x82, 0x1c, 0x00, 0x00, 0x00, //0x00002f1c jb           LBB0_583
	0xe9, 0x7c, 0x14, 0x00, 0x00, //0x00002f22 jmp          LBB0_581
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002f27 .p2align 4, 0x90
	//0x00002f30 LBB0_582
	0x4c, 0x03, 0x4c, 0x24, 0x38, //0x00002f30 addq         $56(%rsp), %r9
	0x49, 0x39, 0xd1, //0x00002f35 cmpq         %rdx, %r9
	0x0f, 0x83, 0x65, 0x14, 0x00, 0x00, //0x00002f38 jae          LBB0_581
	//0x00002f3e LBB0_583
	0x4d, 0x8d, 0x79, 0x01, //0x00002f3e leaq         $1(%r9), %r15
	0x4d, 0x89, 0x38, //0x00002f42 movq         %r15, (%r8)
	0x43, 0x0f, 0xbe, 0x3c, 0x0c, //0x00002f45 movsbl       (%r12,%r9), %edi
	0x49, 0xc7, 0xc5, 0xff, 0xff, 0xff, 0xff, //0x00002f4a movq         $-1, %r13
	0x85, 0xff, //0x00002f51 testl        %edi, %edi
	0x0f, 0x84, 0x35, 0x14, 0x00, 0x00, //0x00002f53 je           LBB0_839
	0x49, 0x8d, 0x73, 0xff, //0x00002f59 leaq         $-1(%r11), %rsi
	0x42, 0x8b, 0x04, 0xd9, //0x00002f5d movl         (%rcx,%r11,8), %eax
	0x48, 0x8b, 0x54, 0x24, 0x48, //0x00002f61 movq         $72(%rsp), %rdx
	0x48, 0x83, 0xfa, 0xff, //0x00002f66 cmpq         $-1, %rdx
	0x49, 0x0f, 0x44, 0xd1, //0x00002f6a cmoveq       %r9, %rdx
	0x48, 0x89, 0x54, 0x24, 0x48, //0x00002f6e movq         %rdx, $72(%rsp)
	0xff, 0xc8, //0x00002f73 decl         %eax
	0x83, 0xf8, 0x05, //0x00002f75 cmpl         $5, %eax
	0x0f, 0x87, 0x27, 0x00, 0x00, 0x00, //0x00002f78 ja           LBB0_589
	0x48, 0x8d, 0x15, 0x13, 0x24, 0x00, 0x00, //0x00002f7e leaq         $9235(%rip), %rdx  /* LJTI0_2+0(%rip) */
	0x48, 0x63, 0x04, 0x82, //0x00002f85 movslq       (%rdx,%rax,4), %rax
	0x48, 0x01, 0xd0, //0x00002f89 addq         %rdx, %rax
	0xff, 0xe0, //0x00002f8c jmpq         *%rax
	//0x00002f8e LBB0_586
	0x83, 0xff, 0x2c, //0x00002f8e cmpl         $44, %edi
	0x0f, 0x84, 0xfe, 0xfd, 0xff, 0xff, //0x00002f91 je           LBB0_551
	0x83, 0xff, 0x5d, //0x00002f97 cmpl         $93, %edi
	0x0f, 0x84, 0x87, 0x04, 0x00, 0x00, //0x00002f9a je           LBB0_588
	0xe9, 0xe2, 0x13, 0x00, 0x00, //0x00002fa0 jmp          LBB0_838
	//0x00002fa5 LBB0_589
	0x48, 0x89, 0x31, //0x00002fa5 movq         %rsi, (%rcx)
	0x83, 0xff, 0x7b, //0x00002fa8 cmpl         $123, %edi
	0x0f, 0x86, 0x01, 0x02, 0x00, 0x00, //0x00002fab jbe          LBB0_615
	0xe9, 0xd1, 0x13, 0x00, 0x00, //0x00002fb1 jmp          LBB0_838
	//0x00002fb6 LBB0_590
	0x83, 0xff, 0x2c, //0x00002fb6 cmpl         $44, %edi
	0x0f, 0x85, 0x5f, 0x04, 0x00, 0x00, //0x00002fb9 jne          LBB0_591
	0x49, 0x81, 0xfb, 0xff, 0x0f, 0x00, 0x00, //0x00002fbf cmpq         $4095, %r11
	0x0f, 0x8f, 0x32, 0x15, 0x00, 0x00, //0x00002fc6 jg           LBB0_942
	0x49, 0x8d, 0x43, 0x01, //0x00002fcc leaq         $1(%r11), %rax
	0x48, 0x89, 0x01, //0x00002fd0 movq         %rax, (%rcx)
	0x4a, 0xc7, 0x44, 0xd9, 0x08, 0x03, 0x00, 0x00, 0x00, //0x00002fd3 movq         $3, $8(%rcx,%r11,8)
	0xe9, 0xd1, 0xfd, 0xff, 0xff, //0x00002fdc jmp          LBB0_553
	//0x00002fe1 LBB0_592
	0x40, 0x80, 0xff, 0x22, //0x00002fe1 cmpb         $34, %dil
	0x0f, 0x85, 0x9c, 0x13, 0x00, 0x00, //0x00002fe5 jne          LBB0_838
	0x4a, 0xc7, 0x04, 0xd9, 0x04, 0x00, 0x00, 0x00, //0x00002feb movq         $4, (%rcx,%r11,8)
	0x49, 0x8b, 0x42, 0x08, //0x00002ff3 movq         $8(%r10), %rax
	0x48, 0x89, 0xc3, //0x00002ff7 movq         %rax, %rbx
	0x4c, 0x29, 0xfb, //0x00002ffa subq         %r15, %rbx
	0x0f, 0x84, 0x78, 0x1e, 0x00, 0x00, //0x00002ffd je           LBB0_960
	0x4f, 0x8d, 0x14, 0x3c, //0x00003003 leaq         (%r12,%r15), %r10
	0x48, 0x83, 0xfb, 0x40, //0x00003007 cmpq         $64, %rbx
	0x48, 0x89, 0x44, 0x24, 0x20, //0x0000300b movq         %rax, $32(%rsp)
	0x0f, 0x82, 0xd6, 0x10, 0x00, 0x00, //0x00003010 jb           LBB0_809
	0x89, 0xd9, //0x00003016 movl         %ebx, %ecx
	0x83, 0xe1, 0x3f, //0x00003018 andl         $63, %ecx
	0x48, 0x89, 0x4c, 0x24, 0x50, //0x0000301b movq         %rcx, $80(%rsp)
	0x4c, 0x29, 0xc8, //0x00003020 subq         %r9, %rax
	0x48, 0x83, 0xc0, 0xbf, //0x00003023 addq         $-65, %rax
	0x48, 0x83, 0xe0, 0xc0, //0x00003027 andq         $-64, %rax
	0x4c, 0x01, 0xc8, //0x0000302b addq         %r9, %rax
	0x49, 0x8d, 0x44, 0x04, 0x41, //0x0000302e leaq         $65(%r12,%rax), %rax
	0x48, 0x89, 0x84, 0x24, 0xa8, 0x00, 0x00, 0x00, //0x00003033 movq         %rax, $168(%rsp)
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x0000303b movq         $-1, %r12
	0x45, 0x31, 0xed, //0x00003042 xorl         %r13d, %r13d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00003045 .p2align 4, 0x90
	//0x00003050 LBB0_596
	0xc4, 0xc1, 0x7a, 0x6f, 0x1a, //0x00003050 vmovdqu      (%r10), %xmm3
	0xc4, 0xc1, 0x7a, 0x6f, 0x62, 0x10, //0x00003055 vmovdqu      $16(%r10), %xmm4
	0xc4, 0xc1, 0x7a, 0x6f, 0x6a, 0x20, //0x0000305b vmovdqu      $32(%r10), %xmm5
	0xc4, 0xc1, 0x7a, 0x6f, 0x72, 0x30, //0x00003061 vmovdqu      $48(%r10), %xmm6
	0xc5, 0xe1, 0x74, 0xf8, //0x00003067 vpcmpeqb     %xmm0, %xmm3, %xmm7
	0xc5, 0xf9, 0xd7, 0xf7, //0x0000306b vpmovmskb    %xmm7, %esi
	0xc5, 0xd9, 0x74, 0xf8, //0x0000306f vpcmpeqb     %xmm0, %xmm4, %xmm7
	0xc5, 0xf9, 0xd7, 0xc7, //0x00003073 vpmovmskb    %xmm7, %eax
	0xc5, 0xd1, 0x74, 0xf8, //0x00003077 vpcmpeqb     %xmm0, %xmm5, %xmm7
	0xc5, 0xf9, 0xd7, 0xd7, //0x0000307b vpmovmskb    %xmm7, %edx
	0xc5, 0xc9, 0x74, 0xf8, //0x0000307f vpcmpeqb     %xmm0, %xmm6, %xmm7
	0xc5, 0xf9, 0xd7, 0xcf, //0x00003083 vpmovmskb    %xmm7, %ecx
	0xc5, 0xe1, 0x74, 0xd9, //0x00003087 vpcmpeqb     %xmm1, %xmm3, %xmm3
	0xc5, 0xf9, 0xd7, 0xfb, //0x0000308b vpmovmskb    %xmm3, %edi
	0xc5, 0xd9, 0x74, 0xd9, //0x0000308f vpcmpeqb     %xmm1, %xmm4, %xmm3
	0xc5, 0x79, 0xd7, 0xc3, //0x00003093 vpmovmskb    %xmm3, %r8d
	0xc5, 0xd1, 0x74, 0xd9, //0x00003097 vpcmpeqb     %xmm1, %xmm5, %xmm3
	0xc5, 0x79, 0xd7, 0xdb, //0x0000309b vpmovmskb    %xmm3, %r11d
	0xc5, 0xc9, 0x74, 0xd9, //0x0000309f vpcmpeqb     %xmm1, %xmm6, %xmm3
	0xc5, 0x79, 0xd7, 0xf3, //0x000030a3 vpmovmskb    %xmm3, %r14d
	0x48, 0xc1, 0xe1, 0x30, //0x000030a7 shlq         $48, %rcx
	0x48, 0xc1, 0xe2, 0x20, //0x000030ab shlq         $32, %rdx
	0x48, 0xc1, 0xe0, 0x10, //0x000030af shlq         $16, %rax
	0x48, 0x09, 0xc6, //0x000030b3 orq          %rax, %rsi
	0x48, 0x09, 0xd6, //0x000030b6 orq          %rdx, %rsi
	0x49, 0xc1, 0xe6, 0x30, //0x000030b9 shlq         $48, %r14
	0x49, 0xc1, 0xe3, 0x20, //0x000030bd shlq         $32, %r11
	0x49, 0xc1, 0xe0, 0x10, //0x000030c1 shlq         $16, %r8
	0x4c, 0x09, 0xc7, //0x000030c5 orq          %r8, %rdi
	0x4c, 0x09, 0xdf, //0x000030c8 orq          %r11, %rdi
	0x4c, 0x09, 0xf7, //0x000030cb orq          %r14, %rdi
	0x49, 0x83, 0xfc, 0xff, //0x000030ce cmpq         $-1, %r12
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x000030d2 jne          LBB0_598
	0x48, 0x85, 0xff, //0x000030d8 testq        %rdi, %rdi
	0x0f, 0x85, 0x8b, 0x00, 0x00, 0x00, //0x000030db jne          LBB0_610
	//0x000030e1 LBB0_598
	0x48, 0x09, 0xce, //0x000030e1 orq          %rcx, %rsi
	0x48, 0x89, 0xf8, //0x000030e4 movq         %rdi, %rax
	0x4c, 0x09, 0xe8, //0x000030e7 orq          %r13, %rax
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x000030ea movq         $24(%rsp), %r8
	0x0f, 0x85, 0x20, 0x00, 0x00, 0x00, //0x000030ef jne          LBB0_609
	0x48, 0x85, 0xf6, //0x000030f5 testq        %rsi, %rsi
	0x0f, 0x85, 0x73, 0x0b, 0x00, 0x00, //0x000030f8 jne          LBB0_606
	//0x000030fe LBB0_600
	0x48, 0x83, 0xc3, 0xc0, //0x000030fe addq         $-64, %rbx
	0x49, 0x83, 0xc2, 0x40, //0x00003102 addq         $64, %r10
	0x48, 0x83, 0xfb, 0x3f, //0x00003106 cmpq         $63, %rbx
	0x0f, 0x87, 0x40, 0xff, 0xff, 0xff, //0x0000310a ja           LBB0_596
	0xe9, 0xdf, 0x0a, 0x00, 0x00, //0x00003110 jmp          LBB0_601
	//0x00003115 LBB0_609
	0x4c, 0x89, 0xe8, //0x00003115 movq         %r13, %rax
	0x48, 0xf7, 0xd0, //0x00003118 notq         %rax
	0x48, 0x21, 0xf8, //0x0000311b andq         %rdi, %rax
	0x48, 0x8d, 0x0c, 0x00, //0x0000311e leaq         (%rax,%rax), %rcx
	0x4c, 0x09, 0xe9, //0x00003122 orq          %r13, %rcx
	0x48, 0x89, 0xca, //0x00003125 movq         %rcx, %rdx
	0x48, 0xf7, 0xd2, //0x00003128 notq         %rdx
	0x48, 0x21, 0xfa, //0x0000312b andq         %rdi, %rdx
	0x48, 0xbf, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x0000312e movabsq      $-6148914691236517206, %rdi
	0x48, 0x21, 0xfa, //0x00003138 andq         %rdi, %rdx
	0x45, 0x31, 0xed, //0x0000313b xorl         %r13d, %r13d
	0x48, 0x01, 0xc2, //0x0000313e addq         %rax, %rdx
	0x41, 0x0f, 0x92, 0xc5, //0x00003141 setb         %r13b
	0x48, 0x01, 0xd2, //0x00003145 addq         %rdx, %rdx
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00003148 movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc2, //0x00003152 xorq         %rax, %rdx
	0x48, 0x21, 0xca, //0x00003155 andq         %rcx, %rdx
	0x48, 0xf7, 0xd2, //0x00003158 notq         %rdx
	0x48, 0x21, 0xd6, //0x0000315b andq         %rdx, %rsi
	0x48, 0x85, 0xf6, //0x0000315e testq        %rsi, %rsi
	0x0f, 0x84, 0x97, 0xff, 0xff, 0xff, //0x00003161 je           LBB0_600
	0xe9, 0x05, 0x0b, 0x00, 0x00, //0x00003167 jmp          LBB0_606
	//0x0000316c LBB0_610
	0x4c, 0x89, 0xd0, //0x0000316c movq         %r10, %rax
	0x48, 0x2b, 0x44, 0x24, 0x30, //0x0000316f subq         $48(%rsp), %rax
	0x4c, 0x0f, 0xbc, 0xe7, //0x00003174 bsfq         %rdi, %r12
	0x49, 0x01, 0xc4, //0x00003178 addq         %rax, %r12
	0xe9, 0x61, 0xff, 0xff, 0xff, //0x0000317b jmp          LBB0_598
	//0x00003180 LBB0_611
	0x40, 0x80, 0xff, 0x3a, //0x00003180 cmpb         $58, %dil
	0x0f, 0x85, 0xfd, 0x11, 0x00, 0x00, //0x00003184 jne          LBB0_838
	0x4a, 0xc7, 0x04, 0xd9, 0x00, 0x00, 0x00, 0x00, //0x0000318a movq         $0, (%rcx,%r11,8)
	0xe9, 0x1b, 0xfc, 0xff, 0xff, //0x00003192 jmp          LBB0_553
	//0x00003197 LBB0_613
	0x40, 0x80, 0xff, 0x5d, //0x00003197 cmpb         $93, %dil
	0x0f, 0x84, 0x86, 0x02, 0x00, 0x00, //0x0000319b je           LBB0_588
	0x4a, 0xc7, 0x04, 0xd9, 0x01, 0x00, 0x00, 0x00, //0x000031a1 movq         $1, (%rcx,%r11,8)
	0x83, 0xff, 0x7b, //0x000031a9 cmpl         $123, %edi
	0x0f, 0x87, 0xd5, 0x11, 0x00, 0x00, //0x000031ac ja           LBB0_838
	//0x000031b2 LBB0_615
	0x4f, 0x8d, 0x14, 0x0c, //0x000031b2 leaq         (%r12,%r9), %r10
	0x89, 0xf8, //0x000031b6 movl         %edi, %eax
	0x48, 0x8d, 0x15, 0xf1, 0x21, 0x00, 0x00, //0x000031b8 leaq         $8689(%rip), %rdx  /* LJTI0_3+0(%rip) */
	0x48, 0x63, 0x04, 0x82, //0x000031bf movslq       (%rdx,%rax,4), %rax
	0x48, 0x01, 0xd0, //0x000031c3 addq         %rdx, %rax
	0xff, 0xe0, //0x000031c6 jmpq         *%rax
	//0x000031c8 LBB0_618
	0x48, 0x8b, 0x44, 0x24, 0x28, //0x000031c8 movq         $40(%rsp), %rax
	0x48, 0x8b, 0x78, 0x08, //0x000031cd movq         $8(%rax), %rdi
	0x4c, 0x29, 0xcf, //0x000031d1 subq         %r9, %rdi
	0x0f, 0x84, 0x59, 0x13, 0x00, 0x00, //0x000031d4 je           LBB0_865
	0x41, 0x80, 0x3a, 0x30, //0x000031da cmpb         $48, (%r10)
	0x0f, 0x85, 0x2f, 0x00, 0x00, 0x00, //0x000031de jne          LBB0_623
	0x48, 0x83, 0xff, 0x01, //0x000031e4 cmpq         $1, %rdi
	0x0f, 0x84, 0x6b, 0x04, 0x00, 0x00, //0x000031e8 je           LBB0_686
	0x43, 0x8a, 0x04, 0x3c, //0x000031ee movb         (%r12,%r15), %al
	0x04, 0xd2, //0x000031f2 addb         $-46, %al
	0x3c, 0x37, //0x000031f4 cmpb         $55, %al
	0x0f, 0x87, 0x5d, 0x04, 0x00, 0x00, //0x000031f6 ja           LBB0_686
	0x0f, 0xb6, 0xc0, //0x000031fc movzbl       %al, %eax
	0x48, 0xba, 0x01, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, //0x000031ff movabsq      $36028797027352577, %rdx
	0x48, 0x0f, 0xa3, 0xc2, //0x00003209 btq          %rax, %rdx
	0x0f, 0x83, 0x46, 0x04, 0x00, 0x00, //0x0000320d jae          LBB0_686
	//0x00003213 LBB0_623
	0x48, 0x83, 0xff, 0x10, //0x00003213 cmpq         $16, %rdi
	0x0f, 0x82, 0x54, 0x0f, 0x00, 0x00, //0x00003217 jb           LBB0_816
	0x48, 0x8d, 0x57, 0xf0, //0x0000321d leaq         $-16(%rdi), %rdx
	0x48, 0x89, 0xd0, //0x00003221 movq         %rdx, %rax
	0x48, 0x83, 0xe0, 0xf0, //0x00003224 andq         $-16, %rax
	0x4e, 0x8d, 0x7c, 0x10, 0x10, //0x00003228 leaq         $16(%rax,%r10), %r15
	0x83, 0xe2, 0x0f, //0x0000322d andl         $15, %edx
	0x49, 0xc7, 0xc5, 0xff, 0xff, 0xff, 0xff, //0x00003230 movq         $-1, %r13
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00003237 movq         $-1, %r11
	0x48, 0xc7, 0x44, 0x24, 0x20, 0xff, 0xff, 0xff, 0xff, //0x0000323e movq         $-1, $32(%rsp)
	0x4d, 0x89, 0xd4, //0x00003247 movq         %r10, %r12
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x0000324a .p2align 4, 0x90
	//0x00003250 LBB0_625
	0xc4, 0xc1, 0x7a, 0x6f, 0x1c, 0x24, //0x00003250 vmovdqu      (%r12), %xmm3
	0xc4, 0xc1, 0x61, 0x64, 0xe0, //0x00003256 vpcmpgtb     %xmm8, %xmm3, %xmm4
	0xc5, 0xb1, 0x64, 0xeb, //0x0000325b vpcmpgtb     %xmm3, %xmm9, %xmm5
	0xc5, 0xd9, 0xdb, 0xe5, //0x0000325f vpand        %xmm5, %xmm4, %xmm4
	0xc5, 0xa9, 0x74, 0xeb, //0x00003263 vpcmpeqb     %xmm3, %xmm10, %xmm5
	0xc5, 0xa1, 0x74, 0xf3, //0x00003267 vpcmpeqb     %xmm3, %xmm11, %xmm6
	0xc5, 0xc9, 0xeb, 0xed, //0x0000326b vpor         %xmm5, %xmm6, %xmm5
	0xc5, 0x99, 0xeb, 0xf3, //0x0000326f vpor         %xmm3, %xmm12, %xmm6
	0xc5, 0x91, 0x74, 0xdb, //0x00003273 vpcmpeqb     %xmm3, %xmm13, %xmm3
	0xc5, 0xc9, 0x74, 0xf2, //0x00003277 vpcmpeqb     %xmm2, %xmm6, %xmm6
	0xc5, 0xc9, 0xeb, 0xfb, //0x0000327b vpor         %xmm3, %xmm6, %xmm7
	0xc5, 0xd1, 0xeb, 0xe4, //0x0000327f vpor         %xmm4, %xmm5, %xmm4
	0xc5, 0xc1, 0xeb, 0xe4, //0x00003283 vpor         %xmm4, %xmm7, %xmm4
	0xc5, 0x79, 0xd7, 0xc3, //0x00003287 vpmovmskb    %xmm3, %r8d
	0xc5, 0x79, 0xd7, 0xf6, //0x0000328b vpmovmskb    %xmm6, %r14d
	0xc5, 0xf9, 0xd7, 0xf5, //0x0000328f vpmovmskb    %xmm5, %esi
	0xc5, 0xf9, 0xd7, 0xc4, //0x00003293 vpmovmskb    %xmm4, %eax
	0xb9, 0xff, 0xff, 0xff, 0xff, //0x00003297 movl         $4294967295, %ecx
	0x48, 0x31, 0xc8, //0x0000329c xorq         %rcx, %rax
	0x48, 0x0f, 0xbc, 0xc8, //0x0000329f bsfq         %rax, %rcx
	0x83, 0xf9, 0x10, //0x000032a3 cmpl         $16, %ecx
	0x0f, 0x84, 0x13, 0x00, 0x00, 0x00, //0x000032a6 je           LBB0_627
	0xb8, 0xff, 0xff, 0xff, 0xff, //0x000032ac movl         $-1, %eax
	0xd3, 0xe0, //0x000032b1 shll         %cl, %eax
	0xf7, 0xd0, //0x000032b3 notl         %eax
	0x41, 0x21, 0xc0, //0x000032b5 andl         %eax, %r8d
	0x41, 0x21, 0xc6, //0x000032b8 andl         %eax, %r14d
	0x21, 0xf0, //0x000032bb andl         %esi, %eax
	0x89, 0xc6, //0x000032bd movl         %eax, %esi
	//0x000032bf LBB0_627
	0x41, 0x8d, 0x40, 0xff, //0x000032bf leal         $-1(%r8), %eax
	0x44, 0x21, 0xc0, //0x000032c3 andl         %r8d, %eax
	0x0f, 0x85, 0xdd, 0x09, 0x00, 0x00, //0x000032c6 jne          LBB0_776
	0x41, 0x8d, 0x46, 0xff, //0x000032cc leal         $-1(%r14), %eax
	0x44, 0x21, 0xf0, //0x000032d0 andl         %r14d, %eax
	0x0f, 0x85, 0xd0, 0x09, 0x00, 0x00, //0x000032d3 jne          LBB0_776
	0x8d, 0x46, 0xff, //0x000032d9 leal         $-1(%rsi), %eax
	0x21, 0xf0, //0x000032dc andl         %esi, %eax
	0x0f, 0x85, 0xc5, 0x09, 0x00, 0x00, //0x000032de jne          LBB0_776
	0x45, 0x85, 0xc0, //0x000032e4 testl        %r8d, %r8d
	0x0f, 0x84, 0x1e, 0x00, 0x00, 0x00, //0x000032e7 je           LBB0_633
	0x4c, 0x89, 0xe3, //0x000032ed movq         %r12, %rbx
	0x4c, 0x29, 0xd3, //0x000032f0 subq         %r10, %rbx
	0x41, 0x0f, 0xbc, 0xc0, //0x000032f3 bsfl         %r8d, %eax
	0x48, 0x01, 0xd8, //0x000032f7 addq         %rbx, %rax
	0x48, 0x83, 0x7c, 0x24, 0x20, 0xff, //0x000032fa cmpq         $-1, $32(%rsp)
	0x0f, 0x85, 0xac, 0x0c, 0x00, 0x00, //0x00003300 jne          LBB0_804
	0x48, 0x89, 0x44, 0x24, 0x20, //0x00003306 movq         %rax, $32(%rsp)
	//0x0000330b LBB0_633
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x0000330b movq         $24(%rsp), %r8
	0x45, 0x85, 0xf6, //0x00003310 testl        %r14d, %r14d
	0x0f, 0x84, 0x1a, 0x00, 0x00, 0x00, //0x00003313 je           LBB0_636
	0x4c, 0x89, 0xe3, //0x00003319 movq         %r12, %rbx
	0x4c, 0x29, 0xd3, //0x0000331c subq         %r10, %rbx
	0x41, 0x0f, 0xbc, 0xc6, //0x0000331f bsfl         %r14d, %eax
	0x48, 0x01, 0xd8, //0x00003323 addq         %rbx, %rax
	0x49, 0x83, 0xfb, 0xff, //0x00003326 cmpq         $-1, %r11
	0x0f, 0x85, 0x76, 0x0b, 0x00, 0x00, //0x0000332a jne          LBB0_791
	0x49, 0x89, 0xc3, //0x00003330 movq         %rax, %r11
	//0x00003333 LBB0_636
	0x85, 0xf6, //0x00003333 testl        %esi, %esi
	0x0f, 0x84, 0x19, 0x00, 0x00, 0x00, //0x00003335 je           LBB0_639
	0x4c, 0x89, 0xe3, //0x0000333b movq         %r12, %rbx
	0x4c, 0x29, 0xd3, //0x0000333e subq         %r10, %rbx
	0x0f, 0xbc, 0xc6, //0x00003341 bsfl         %esi, %eax
	0x48, 0x01, 0xd8, //0x00003344 addq         %rbx, %rax
	0x49, 0x83, 0xfd, 0xff, //0x00003347 cmpq         $-1, %r13
	0x0f, 0x85, 0x55, 0x0b, 0x00, 0x00, //0x0000334b jne          LBB0_791
	0x49, 0x89, 0xc5, //0x00003351 movq         %rax, %r13
	//0x00003354 LBB0_639
	0x83, 0xf9, 0x10, //0x00003354 cmpl         $16, %ecx
	0x0f, 0x85, 0x73, 0x02, 0x00, 0x00, //0x00003357 jne          LBB0_673
	0x49, 0x83, 0xc4, 0x10, //0x0000335d addq         $16, %r12
	0x48, 0x83, 0xc7, 0xf0, //0x00003361 addq         $-16, %rdi
	0x48, 0x83, 0xff, 0x0f, //0x00003365 cmpq         $15, %rdi
	0x0f, 0x87, 0xe1, 0xfe, 0xff, 0xff, //0x00003369 ja           LBB0_625
	0x48, 0x85, 0xd2, //0x0000336f testq        %rdx, %rdx
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x00003372 movq         $24(%rsp), %r8
	0x48, 0x8b, 0x4c, 0x24, 0x40, //0x00003377 movq         $64(%rsp), %rcx
	0x4c, 0x8b, 0x64, 0x24, 0x30, //0x0000337c movq         $48(%rsp), %r12
	0x0f, 0x84, 0x59, 0x02, 0x00, 0x00, //0x00003381 je           LBB0_674
	//0x00003387 LBB0_642
	0x49, 0x8d, 0x3c, 0x17, //0x00003387 leaq         (%r15,%rdx), %rdi
	0xe9, 0x0c, 0x00, 0x00, 0x00, //0x0000338b jmp          LBB0_644
	//0x00003390 .p2align 4, 0x90
	//0x00003390 LBB0_643
	0x49, 0x89, 0xf7, //0x00003390 movq         %rsi, %r15
	0x48, 0xff, 0xca, //0x00003393 decq         %rdx
	0x0f, 0x84, 0x2c, 0x0b, 0x00, 0x00, //0x00003396 je           LBB0_794
	//0x0000339c LBB0_644
	0x41, 0x0f, 0xbe, 0x07, //0x0000339c movsbl       (%r15), %eax
	0x83, 0xc0, 0xd5, //0x000033a0 addl         $-43, %eax
	0x83, 0xf8, 0x3a, //0x000033a3 cmpl         $58, %eax
	0x0f, 0x87, 0x34, 0x02, 0x00, 0x00, //0x000033a6 ja           LBB0_674
	0x49, 0x8d, 0x77, 0x01, //0x000033ac leaq         $1(%r15), %rsi
	0x48, 0x8d, 0x1d, 0xd5, 0x22, 0x00, 0x00, //0x000033b0 leaq         $8917(%rip), %rbx  /* LJTI0_5+0(%rip) */
	0x48, 0x63, 0x04, 0x83, //0x000033b7 movslq       (%rbx,%rax,4), %rax
	0x48, 0x01, 0xd8, //0x000033bb addq         %rbx, %rax
	0xff, 0xe0, //0x000033be jmpq         *%rax
	//0x000033c0 LBB0_646
	0x49, 0x89, 0xf7, //0x000033c0 movq         %rsi, %r15
	0x4d, 0x29, 0xd7, //0x000033c3 subq         %r10, %r15
	0x49, 0x83, 0xfd, 0xff, //0x000033c6 cmpq         $-1, %r13
	0x0f, 0x85, 0x7a, 0x02, 0x00, 0x00, //0x000033ca jne          LBB0_683
	0x49, 0xff, 0xcf, //0x000033d0 decq         %r15
	0x4d, 0x89, 0xfd, //0x000033d3 movq         %r15, %r13
	0xe9, 0xb5, 0xff, 0xff, 0xff, //0x000033d6 jmp          LBB0_643
	//0x000033db LBB0_648
	0x49, 0x89, 0xf7, //0x000033db movq         %rsi, %r15
	0x4d, 0x29, 0xd7, //0x000033de subq         %r10, %r15
	0x49, 0x83, 0xfb, 0xff, //0x000033e1 cmpq         $-1, %r11
	0x0f, 0x85, 0x5f, 0x02, 0x00, 0x00, //0x000033e5 jne          LBB0_683
	0x49, 0xff, 0xcf, //0x000033eb decq         %r15
	0x4d, 0x89, 0xfb, //0x000033ee movq         %r15, %r11
	0xe9, 0x9a, 0xff, 0xff, 0xff, //0x000033f1 jmp          LBB0_643
	//0x000033f6 LBB0_650
	0x49, 0x89, 0xf7, //0x000033f6 movq         %rsi, %r15
	0x4d, 0x29, 0xd7, //0x000033f9 subq         %r10, %r15
	0x48, 0x83, 0x7c, 0x24, 0x20, 0xff, //0x000033fc cmpq         $-1, $32(%rsp)
	0x0f, 0x85, 0x42, 0x02, 0x00, 0x00, //0x00003402 jne          LBB0_683
	0x49, 0xff, 0xcf, //0x00003408 decq         %r15
	0x4c, 0x89, 0x7c, 0x24, 0x20, //0x0000340b movq         %r15, $32(%rsp)
	0xe9, 0x7b, 0xff, 0xff, 0xff, //0x00003410 jmp          LBB0_643
	//0x00003415 LBB0_616
	0x83, 0xff, 0x22, //0x00003415 cmpl         $34, %edi
	0x0f, 0x84, 0x22, 0x00, 0x00, 0x00, //0x00003418 je           LBB0_654
	//0x0000341e LBB0_591
	0x83, 0xff, 0x7d, //0x0000341e cmpl         $125, %edi
	0x0f, 0x85, 0x60, 0x0f, 0x00, 0x00, //0x00003421 jne          LBB0_838
	//0x00003427 LBB0_588
	0x48, 0x89, 0x31, //0x00003427 movq         %rsi, (%rcx)
	0x49, 0x89, 0xf3, //0x0000342a movq         %rsi, %r11
	0x4c, 0x8b, 0x6c, 0x24, 0x48, //0x0000342d movq         $72(%rsp), %r13
	0x48, 0x85, 0xf6, //0x00003432 testq        %rsi, %rsi
	0x0f, 0x85, 0x91, 0xf9, 0xff, 0xff, //0x00003435 jne          LBB0_556
	0xe9, 0x4e, 0x0f, 0x00, 0x00, //0x0000343b jmp          LBB0_839
	//0x00003440 LBB0_654
	0x4a, 0xc7, 0x04, 0xd9, 0x02, 0x00, 0x00, 0x00, //0x00003440 movq         $2, (%rcx,%r11,8)
	0x49, 0x8b, 0x42, 0x08, //0x00003448 movq         $8(%r10), %rax
	0x48, 0x89, 0xc3, //0x0000344c movq         %rax, %rbx
	0x4c, 0x29, 0xfb, //0x0000344f subq         %r15, %rbx
	0x0f, 0x84, 0x23, 0x1a, 0x00, 0x00, //0x00003452 je           LBB0_960
	0x4f, 0x8d, 0x14, 0x3c, //0x00003458 leaq         (%r12,%r15), %r10
	0x48, 0x83, 0xfb, 0x40, //0x0000345c cmpq         $64, %rbx
	0x48, 0x89, 0x44, 0x24, 0x20, //0x00003460 movq         %rax, $32(%rsp)
	0x0f, 0x82, 0x37, 0x0d, 0x00, 0x00, //0x00003465 jb           LBB0_817
	0x89, 0xd9, //0x0000346b movl         %ebx, %ecx
	0x83, 0xe1, 0x3f, //0x0000346d andl         $63, %ecx
	0x48, 0x89, 0x4c, 0x24, 0x50, //0x00003470 movq         %rcx, $80(%rsp)
	0x4c, 0x29, 0xc8, //0x00003475 subq         %r9, %rax
	0x48, 0x83, 0xc0, 0xbf, //0x00003478 addq         $-65, %rax
	0x48, 0x83, 0xe0, 0xc0, //0x0000347c andq         $-64, %rax
	0x4c, 0x01, 0xc8, //0x00003480 addq         %r9, %rax
	0x49, 0x8d, 0x44, 0x04, 0x41, //0x00003483 leaq         $65(%r12,%rax), %rax
	0x48, 0x89, 0x84, 0x24, 0xa8, 0x00, 0x00, 0x00, //0x00003488 movq         %rax, $168(%rsp)
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x00003490 movq         $-1, %r12
	0x45, 0x31, 0xed, //0x00003497 xorl         %r13d, %r13d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x0000349a .p2align 4, 0x90
	//0x000034a0 LBB0_657
	0xc4, 0xc1, 0x7a, 0x6f, 0x1a, //0x000034a0 vmovdqu      (%r10), %xmm3
	0xc4, 0xc1, 0x7a, 0x6f, 0x62, 0x10, //0x000034a5 vmovdqu      $16(%r10), %xmm4
	0xc4, 0xc1, 0x7a, 0x6f, 0x6a, 0x20, //0x000034ab vmovdqu      $32(%r10), %xmm5
	0xc4, 0xc1, 0x7a, 0x6f, 0x72, 0x30, //0x000034b1 vmovdqu      $48(%r10), %xmm6
	0xc5, 0xe1, 0x74, 0xf8, //0x000034b7 vpcmpeqb     %xmm0, %xmm3, %xmm7
	0xc5, 0xf9, 0xd7, 0xf7, //0x000034bb vpmovmskb    %xmm7, %esi
	0xc5, 0xd9, 0x74, 0xf8, //0x000034bf vpcmpeqb     %xmm0, %xmm4, %xmm7
	0xc5, 0xf9, 0xd7, 0xc7, //0x000034c3 vpmovmskb    %xmm7, %eax
	0xc5, 0xd1, 0x74, 0xf8, //0x000034c7 vpcmpeqb     %xmm0, %xmm5, %xmm7
	0xc5, 0xf9, 0xd7, 0xd7, //0x000034cb vpmovmskb    %xmm7, %edx
	0xc5, 0xc9, 0x74, 0xf8, //0x000034cf vpcmpeqb     %xmm0, %xmm6, %xmm7
	0xc5, 0xf9, 0xd7, 0xcf, //0x000034d3 vpmovmskb    %xmm7, %ecx
	0xc5, 0xe1, 0x74, 0xd9, //0x000034d7 vpcmpeqb     %xmm1, %xmm3, %xmm3
	0xc5, 0xf9, 0xd7, 0xfb, //0x000034db vpmovmskb    %xmm3, %edi
	0xc5, 0xd9, 0x74, 0xd9, //0x000034df vpcmpeqb     %xmm1, %xmm4, %xmm3
	0xc5, 0x79, 0xd7, 0xc3, //0x000034e3 vpmovmskb    %xmm3, %r8d
	0xc5, 0xd1, 0x74, 0xd9, //0x000034e7 vpcmpeqb     %xmm1, %xmm5, %xmm3
	0xc5, 0x79, 0xd7, 0xdb, //0x000034eb vpmovmskb    %xmm3, %r11d
	0xc5, 0xc9, 0x74, 0xd9, //0x000034ef vpcmpeqb     %xmm1, %xmm6, %xmm3
	0xc5, 0x79, 0xd7, 0xf3, //0x000034f3 vpmovmskb    %xmm3, %r14d
	0x48, 0xc1, 0xe1, 0x30, //0x000034f7 shlq         $48, %rcx
	0x48, 0xc1, 0xe2, 0x20, //0x000034fb shlq         $32, %rdx
	0x48, 0xc1, 0xe0, 0x10, //0x000034ff shlq         $16, %rax
	0x48, 0x09, 0xc6, //0x00003503 orq          %rax, %rsi
	0x48, 0x09, 0xd6, //0x00003506 orq          %rdx, %rsi
	0x49, 0xc1, 0xe6, 0x30, //0x00003509 shlq         $48, %r14
	0x49, 0xc1, 0xe3, 0x20, //0x0000350d shlq         $32, %r11
	0x49, 0xc1, 0xe0, 0x10, //0x00003511 shlq         $16, %r8
	0x4c, 0x09, 0xc7, //0x00003515 orq          %r8, %rdi
	0x4c, 0x09, 0xdf, //0x00003518 orq          %r11, %rdi
	0x4c, 0x09, 0xf7, //0x0000351b orq          %r14, %rdi
	0x49, 0x83, 0xfc, 0xff, //0x0000351e cmpq         $-1, %r12
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x00003522 jne          LBB0_659
	0x48, 0x85, 0xff, //0x00003528 testq        %rdi, %rdi
	0x0f, 0x85, 0x8b, 0x00, 0x00, 0x00, //0x0000352b jne          LBB0_672
	//0x00003531 LBB0_659
	0x48, 0x09, 0xce, //0x00003531 orq          %rcx, %rsi
	0x48, 0x89, 0xf8, //0x00003534 movq         %rdi, %rax
	0x4c, 0x09, 0xe8, //0x00003537 orq          %r13, %rax
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x0000353a movq         $24(%rsp), %r8
	0x0f, 0x85, 0x20, 0x00, 0x00, 0x00, //0x0000353f jne          LBB0_671
	0x48, 0x85, 0xf6, //0x00003545 testq        %rsi, %rsi
	0x0f, 0x85, 0xe4, 0x08, 0x00, 0x00, //0x00003548 jne          LBB0_667
	//0x0000354e LBB0_661
	0x48, 0x83, 0xc3, 0xc0, //0x0000354e addq         $-64, %rbx
	0x49, 0x83, 0xc2, 0x40, //0x00003552 addq         $64, %r10
	0x48, 0x83, 0xfb, 0x3f, //0x00003556 cmpq         $63, %rbx
	0x0f, 0x87, 0x40, 0xff, 0xff, 0xff, //0x0000355a ja           LBB0_657
	0xe9, 0x50, 0x08, 0x00, 0x00, //0x00003560 jmp          LBB0_662
	//0x00003565 LBB0_671
	0x4c, 0x89, 0xe8, //0x00003565 movq         %r13, %rax
	0x48, 0xf7, 0xd0, //0x00003568 notq         %rax
	0x48, 0x21, 0xf8, //0x0000356b andq         %rdi, %rax
	0x48, 0x8d, 0x0c, 0x00, //0x0000356e leaq         (%rax,%rax), %rcx
	0x4c, 0x09, 0xe9, //0x00003572 orq          %r13, %rcx
	0x48, 0x89, 0xca, //0x00003575 movq         %rcx, %rdx
	0x48, 0xf7, 0xd2, //0x00003578 notq         %rdx
	0x48, 0x21, 0xfa, //0x0000357b andq         %rdi, %rdx
	0x48, 0xbf, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x0000357e movabsq      $-6148914691236517206, %rdi
	0x48, 0x21, 0xfa, //0x00003588 andq         %rdi, %rdx
	0x45, 0x31, 0xed, //0x0000358b xorl         %r13d, %r13d
	0x48, 0x01, 0xc2, //0x0000358e addq         %rax, %rdx
	0x41, 0x0f, 0x92, 0xc5, //0x00003591 setb         %r13b
	0x48, 0x01, 0xd2, //0x00003595 addq         %rdx, %rdx
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00003598 movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc2, //0x000035a2 xorq         %rax, %rdx
	0x48, 0x21, 0xca, //0x000035a5 andq         %rcx, %rdx
	0x48, 0xf7, 0xd2, //0x000035a8 notq         %rdx
	0x48, 0x21, 0xd6, //0x000035ab andq         %rdx, %rsi
	0x48, 0x85, 0xf6, //0x000035ae testq        %rsi, %rsi
	0x0f, 0x84, 0x97, 0xff, 0xff, 0xff, //0x000035b1 je           LBB0_661
	0xe9, 0x76, 0x08, 0x00, 0x00, //0x000035b7 jmp          LBB0_667
	//0x000035bc LBB0_672
	0x4c, 0x89, 0xd0, //0x000035bc movq         %r10, %rax
	0x48, 0x2b, 0x44, 0x24, 0x30, //0x000035bf subq         $48(%rsp), %rax
	0x4c, 0x0f, 0xbc, 0xe7, //0x000035c4 bsfq         %rdi, %r12
	0x49, 0x01, 0xc4, //0x000035c8 addq         %rax, %r12
	0xe9, 0x61, 0xff, 0xff, 0xff, //0x000035cb jmp          LBB0_659
	//0x000035d0 LBB0_673
	0x49, 0x01, 0xcc, //0x000035d0 addq         %rcx, %r12
	0x4d, 0x89, 0xe7, //0x000035d3 movq         %r12, %r15
	0x48, 0x8b, 0x4c, 0x24, 0x40, //0x000035d6 movq         $64(%rsp), %rcx
	0x4c, 0x8b, 0x64, 0x24, 0x30, //0x000035db movq         $48(%rsp), %r12
	//0x000035e0 LBB0_674
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x000035e0 movq         $-1, %rax
	0x4d, 0x85, 0xdb, //0x000035e7 testq        %r11, %r11
	0x0f, 0x84, 0x31, 0x17, 0x00, 0x00, //0x000035ea je           LBB0_931
	//0x000035f0 LBB0_675
	0x4d, 0x85, 0xed, //0x000035f0 testq        %r13, %r13
	0x0f, 0x84, 0x28, 0x17, 0x00, 0x00, //0x000035f3 je           LBB0_931
	0x48, 0x8b, 0x54, 0x24, 0x20, //0x000035f9 movq         $32(%rsp), %rdx
	0x48, 0x85, 0xd2, //0x000035fe testq        %rdx, %rdx
	0x0f, 0x84, 0x1a, 0x17, 0x00, 0x00, //0x00003601 je           LBB0_931
	0x4d, 0x29, 0xd7, //0x00003607 subq         %r10, %r15
	0x49, 0x8d, 0x47, 0xff, //0x0000360a leaq         $-1(%r15), %rax
	0x49, 0x39, 0xc3, //0x0000360e cmpq         %rax, %r11
	0x0f, 0x84, 0x33, 0x00, 0x00, 0x00, //0x00003611 je           LBB0_683
	0x48, 0x39, 0xc2, //0x00003617 cmpq         %rax, %rdx
	0x0f, 0x84, 0x2a, 0x00, 0x00, 0x00, //0x0000361a je           LBB0_683
	0x49, 0x39, 0xc5, //0x00003620 cmpq         %rax, %r13
	0x0f, 0x84, 0x21, 0x00, 0x00, 0x00, //0x00003623 je           LBB0_683
	0x4d, 0x85, 0xed, //0x00003629 testq        %r13, %r13
	0x0f, 0x8e, 0x3e, 0x00, 0x00, 0x00, //0x0000362c jle          LBB0_687
	0x49, 0x8d, 0x45, 0xff, //0x00003632 leaq         $-1(%r13), %rax
	0x49, 0x39, 0xc3, //0x00003636 cmpq         %rax, %r11
	0x0f, 0x84, 0x31, 0x00, 0x00, 0x00, //0x00003639 je           LBB0_687
	0x49, 0xf7, 0xd5, //0x0000363f notq         %r13
	0x4d, 0x89, 0xef, //0x00003642 movq         %r13, %r15
	0xe9, 0x03, 0x00, 0x00, 0x00, //0x00003645 jmp          LBB0_684
	//0x0000364a LBB0_683
	0x49, 0xf7, 0xdf, //0x0000364a negq         %r15
	//0x0000364d LBB0_684
	0x4d, 0x85, 0xff, //0x0000364d testq        %r15, %r15
	0x0f, 0x88, 0xc8, 0x16, 0x00, 0x00, //0x00003650 js           LBB0_930
	0x4d, 0x01, 0xcf, //0x00003656 addq         %r9, %r15
	//0x00003659 LBB0_686
	0x4d, 0x89, 0x38, //0x00003659 movq         %r15, (%r8)
	0x4d, 0x89, 0xfa, //0x0000365c movq         %r15, %r10
	0x4d, 0x89, 0xcd, //0x0000365f movq         %r9, %r13
	0x4d, 0x85, 0xc9, //0x00003662 testq        %r9, %r9
	0x0f, 0x89, 0x4a, 0xf7, 0xff, 0xff, //0x00003665 jns          LBB0_554
	0xe9, 0x1e, 0x0d, 0x00, 0x00, //0x0000366b jmp          LBB0_839
	//0x00003670 LBB0_687
	0x48, 0x89, 0xd0, //0x00003670 movq         %rdx, %rax
	0x4c, 0x09, 0xd8, //0x00003673 orq          %r11, %rax
	0x4c, 0x39, 0xda, //0x00003676 cmpq         %r11, %rdx
	0x0f, 0x8c, 0xb1, 0x04, 0x00, 0x00, //0x00003679 jl           LBB0_761
	0x48, 0x85, 0xc0, //0x0000367f testq        %rax, %rax
	0x0f, 0x88, 0xa8, 0x04, 0x00, 0x00, //0x00003682 js           LBB0_761
	0x48, 0xf7, 0xd2, //0x00003688 notq         %rdx
	0x49, 0x89, 0xd7, //0x0000368b movq         %rdx, %r15
	0xe9, 0xba, 0xff, 0xff, 0xff, //0x0000368e jmp          LBB0_684
	//0x00003693 LBB0_690
	0x48, 0x8b, 0x44, 0x24, 0x28, //0x00003693 movq         $40(%rsp), %rax
	0x48, 0x8b, 0x40, 0x08, //0x00003698 movq         $8(%rax), %rax
	0x48, 0x89, 0xc3, //0x0000369c movq         %rax, %rbx
	0x4c, 0x29, 0xfb, //0x0000369f subq         %r15, %rbx
	0x0f, 0x84, 0xd3, 0x17, 0x00, 0x00, //0x000036a2 je           LBB0_960
	0x4f, 0x8d, 0x14, 0x3c, //0x000036a8 leaq         (%r12,%r15), %r10
	0x48, 0x83, 0xfb, 0x40, //0x000036ac cmpq         $64, %rbx
	0x48, 0x89, 0x44, 0x24, 0x20, //0x000036b0 movq         %rax, $32(%rsp)
	0x0f, 0x82, 0x4a, 0x0b, 0x00, 0x00, //0x000036b5 jb           LBB0_821
	0x89, 0xd9, //0x000036bb movl         %ebx, %ecx
	0x83, 0xe1, 0x3f, //0x000036bd andl         $63, %ecx
	0x48, 0x89, 0x4c, 0x24, 0x50, //0x000036c0 movq         %rcx, $80(%rsp)
	0x4c, 0x29, 0xc8, //0x000036c5 subq         %r9, %rax
	0x48, 0x83, 0xc0, 0xbf, //0x000036c8 addq         $-65, %rax
	0x48, 0x83, 0xe0, 0xc0, //0x000036cc andq         $-64, %rax
	0x4c, 0x01, 0xc8, //0x000036d0 addq         %r9, %rax
	0x49, 0x8d, 0x44, 0x04, 0x41, //0x000036d3 leaq         $65(%r12,%rax), %rax
	0x48, 0x89, 0x84, 0x24, 0xa8, 0x00, 0x00, 0x00, //0x000036d8 movq         %rax, $168(%rsp)
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x000036e0 movq         $-1, %r12
	0x45, 0x31, 0xed, //0x000036e7 xorl         %r13d, %r13d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000036ea .p2align 4, 0x90
	//0x000036f0 LBB0_693
	0xc4, 0xc1, 0x7a, 0x6f, 0x1a, //0x000036f0 vmovdqu      (%r10), %xmm3
	0xc4, 0xc1, 0x7a, 0x6f, 0x62, 0x10, //0x000036f5 vmovdqu      $16(%r10), %xmm4
	0xc4, 0xc1, 0x7a, 0x6f, 0x6a, 0x20, //0x000036fb vmovdqu      $32(%r10), %xmm5
	0xc4, 0xc1, 0x7a, 0x6f, 0x72, 0x30, //0x00003701 vmovdqu      $48(%r10), %xmm6
	0xc5, 0xe1, 0x74, 0xf8, //0x00003707 vpcmpeqb     %xmm0, %xmm3, %xmm7
	0xc5, 0xf9, 0xd7, 0xf7, //0x0000370b vpmovmskb    %xmm7, %esi
	0xc5, 0xd9, 0x74, 0xf8, //0x0000370f vpcmpeqb     %xmm0, %xmm4, %xmm7
	0xc5, 0xf9, 0xd7, 0xc7, //0x00003713 vpmovmskb    %xmm7, %eax
	0xc5, 0xd1, 0x74, 0xf8, //0x00003717 vpcmpeqb     %xmm0, %xmm5, %xmm7
	0xc5, 0xf9, 0xd7, 0xd7, //0x0000371b vpmovmskb    %xmm7, %edx
	0xc5, 0xc9, 0x74, 0xf8, //0x0000371f vpcmpeqb     %xmm0, %xmm6, %xmm7
	0xc5, 0xf9, 0xd7, 0xcf, //0x00003723 vpmovmskb    %xmm7, %ecx
	0xc5, 0xe1, 0x74, 0xd9, //0x00003727 vpcmpeqb     %xmm1, %xmm3, %xmm3
	0xc5, 0xf9, 0xd7, 0xfb, //0x0000372b vpmovmskb    %xmm3, %edi
	0xc5, 0xd9, 0x74, 0xd9, //0x0000372f vpcmpeqb     %xmm1, %xmm4, %xmm3
	0xc5, 0x79, 0xd7, 0xc3, //0x00003733 vpmovmskb    %xmm3, %r8d
	0xc5, 0xd1, 0x74, 0xd9, //0x00003737 vpcmpeqb     %xmm1, %xmm5, %xmm3
	0xc5, 0x79, 0xd7, 0xdb, //0x0000373b vpmovmskb    %xmm3, %r11d
	0xc5, 0xc9, 0x74, 0xd9, //0x0000373f vpcmpeqb     %xmm1, %xmm6, %xmm3
	0xc5, 0x79, 0xd7, 0xf3, //0x00003743 vpmovmskb    %xmm3, %r14d
	0x48, 0xc1, 0xe1, 0x30, //0x00003747 shlq         $48, %rcx
	0x48, 0xc1, 0xe2, 0x20, //0x0000374b shlq         $32, %rdx
	0x48, 0xc1, 0xe0, 0x10, //0x0000374f shlq         $16, %rax
	0x48, 0x09, 0xc6, //0x00003753 orq          %rax, %rsi
	0x48, 0x09, 0xd6, //0x00003756 orq          %rdx, %rsi
	0x49, 0xc1, 0xe6, 0x30, //0x00003759 shlq         $48, %r14
	0x49, 0xc1, 0xe3, 0x20, //0x0000375d shlq         $32, %r11
	0x49, 0xc1, 0xe0, 0x10, //0x00003761 shlq         $16, %r8
	0x4c, 0x09, 0xc7, //0x00003765 orq          %r8, %rdi
	0x4c, 0x09, 0xdf, //0x00003768 orq          %r11, %rdi
	0x4c, 0x09, 0xf7, //0x0000376b orq          %r14, %rdi
	0x49, 0x83, 0xfc, 0xff, //0x0000376e cmpq         $-1, %r12
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x00003772 jne          LBB0_695
	0x48, 0x85, 0xff, //0x00003778 testq        %rdi, %rdi
	0x0f, 0x85, 0x8b, 0x00, 0x00, 0x00, //0x0000377b jne          LBB0_710
	//0x00003781 LBB0_695
	0x48, 0x09, 0xce, //0x00003781 orq          %rcx, %rsi
	0x48, 0x89, 0xf8, //0x00003784 movq         %rdi, %rax
	0x4c, 0x09, 0xe8, //0x00003787 orq          %r13, %rax
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x0000378a movq         $24(%rsp), %r8
	0x0f, 0x85, 0x20, 0x00, 0x00, 0x00, //0x0000378f jne          LBB0_709
	0x48, 0x85, 0xf6, //0x00003795 testq        %rsi, %rsi
	0x0f, 0x85, 0xd3, 0x04, 0x00, 0x00, //0x00003798 jne          LBB0_606
	//0x0000379e LBB0_697
	0x48, 0x83, 0xc3, 0xc0, //0x0000379e addq         $-64, %rbx
	0x49, 0x83, 0xc2, 0x40, //0x000037a2 addq         $64, %r10
	0x48, 0x83, 0xfb, 0x3f, //0x000037a6 cmpq         $63, %rbx
	0x0f, 0x87, 0x40, 0xff, 0xff, 0xff, //0x000037aa ja           LBB0_693
	0xe9, 0x3f, 0x08, 0x00, 0x00, //0x000037b0 jmp          LBB0_698
	//0x000037b5 LBB0_709
	0x4c, 0x89, 0xe8, //0x000037b5 movq         %r13, %rax
	0x48, 0xf7, 0xd0, //0x000037b8 notq         %rax
	0x48, 0x21, 0xf8, //0x000037bb andq         %rdi, %rax
	0x48, 0x8d, 0x0c, 0x00, //0x000037be leaq         (%rax,%rax), %rcx
	0x4c, 0x09, 0xe9, //0x000037c2 orq          %r13, %rcx
	0x48, 0x89, 0xca, //0x000037c5 movq         %rcx, %rdx
	0x48, 0xf7, 0xd2, //0x000037c8 notq         %rdx
	0x48, 0x21, 0xfa, //0x000037cb andq         %rdi, %rdx
	0x48, 0xbf, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x000037ce movabsq      $-6148914691236517206, %rdi
	0x48, 0x21, 0xfa, //0x000037d8 andq         %rdi, %rdx
	0x45, 0x31, 0xed, //0x000037db xorl         %r13d, %r13d
	0x48, 0x01, 0xc2, //0x000037de addq         %rax, %rdx
	0x41, 0x0f, 0x92, 0xc5, //0x000037e1 setb         %r13b
	0x48, 0x01, 0xd2, //0x000037e5 addq         %rdx, %rdx
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x000037e8 movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc2, //0x000037f2 xorq         %rax, %rdx
	0x48, 0x21, 0xca, //0x000037f5 andq         %rcx, %rdx
	0x48, 0xf7, 0xd2, //0x000037f8 notq         %rdx
	0x48, 0x21, 0xd6, //0x000037fb andq         %rdx, %rsi
	0x48, 0x85, 0xf6, //0x000037fe testq        %rsi, %rsi
	0x0f, 0x84, 0x97, 0xff, 0xff, 0xff, //0x00003801 je           LBB0_697
	0xe9, 0x65, 0x04, 0x00, 0x00, //0x00003807 jmp          LBB0_606
	//0x0000380c LBB0_710
	0x4c, 0x89, 0xd0, //0x0000380c movq         %r10, %rax
	0x48, 0x2b, 0x44, 0x24, 0x30, //0x0000380f subq         $48(%rsp), %rax
	0x4c, 0x0f, 0xbc, 0xe7, //0x00003814 bsfq         %rdi, %r12
	0x49, 0x01, 0xc4, //0x00003818 addq         %rax, %r12
	0xe9, 0x61, 0xff, 0xff, 0xff, //0x0000381b jmp          LBB0_695
	//0x00003820 LBB0_711
	0x48, 0x8b, 0x44, 0x24, 0x28, //0x00003820 movq         $40(%rsp), %rax
	0x48, 0x8b, 0x78, 0x08, //0x00003825 movq         $8(%rax), %rdi
	0x4c, 0x29, 0xff, //0x00003829 subq         %r15, %rdi
	0x0f, 0x84, 0x31, 0x15, 0x00, 0x00, //0x0000382c je           LBB0_939
	0x4f, 0x8d, 0x34, 0x3c, //0x00003832 leaq         (%r12,%r15), %r14
	0x41, 0x80, 0x3e, 0x30, //0x00003836 cmpb         $48, (%r14)
	0x0f, 0x85, 0x35, 0x00, 0x00, 0x00, //0x0000383a jne          LBB0_716
	0x41, 0xba, 0x01, 0x00, 0x00, 0x00, //0x00003840 movl         $1, %r10d
	0x48, 0x83, 0xff, 0x01, //0x00003846 cmpq         $1, %rdi
	0x0f, 0x84, 0x88, 0x03, 0x00, 0x00, //0x0000384a je           LBB0_774
	0x41, 0x8a, 0x46, 0x01, //0x00003850 movb         $1(%r14), %al
	0x04, 0xd2, //0x00003854 addb         $-46, %al
	0x3c, 0x37, //0x00003856 cmpb         $55, %al
	0x0f, 0x87, 0x7a, 0x03, 0x00, 0x00, //0x00003858 ja           LBB0_774
	0x0f, 0xb6, 0xc0, //0x0000385e movzbl       %al, %eax
	0x48, 0xba, 0x01, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, //0x00003861 movabsq      $36028797027352577, %rdx
	0x48, 0x0f, 0xa3, 0xc2, //0x0000386b btq          %rax, %rdx
	0x0f, 0x83, 0x63, 0x03, 0x00, 0x00, //0x0000386f jae          LBB0_774
	//0x00003875 LBB0_716
	0x48, 0x83, 0xff, 0x10, //0x00003875 cmpq         $16, %rdi
	0x0f, 0x82, 0x9f, 0x09, 0x00, 0x00, //0x00003879 jb           LBB0_822
	0x4c, 0x8d, 0x5f, 0xf0, //0x0000387f leaq         $-16(%rdi), %r11
	0x4c, 0x89, 0xd8, //0x00003883 movq         %r11, %rax
	0x48, 0x83, 0xe0, 0xf0, //0x00003886 andq         $-16, %rax
	0x4e, 0x8d, 0x44, 0x30, 0x10, //0x0000388a leaq         $16(%rax,%r14), %r8
	0x41, 0x83, 0xe3, 0x0f, //0x0000388f andl         $15, %r11d
	0x48, 0xc7, 0x44, 0x24, 0x20, 0xff, 0xff, 0xff, 0xff, //0x00003893 movq         $-1, $32(%rsp)
	0x49, 0xc7, 0xc5, 0xff, 0xff, 0xff, 0xff, //0x0000389c movq         $-1, %r13
	0x48, 0xc7, 0x44, 0x24, 0x50, 0xff, 0xff, 0xff, 0xff, //0x000038a3 movq         $-1, $80(%rsp)
	0x4d, 0x89, 0xf4, //0x000038ac movq         %r14, %r12
	//0x000038af LBB0_718
	0xc4, 0xc1, 0x7a, 0x6f, 0x1c, 0x24, //0x000038af vmovdqu      (%r12), %xmm3
	0xc4, 0xc1, 0x61, 0x64, 0xe0, //0x000038b5 vpcmpgtb     %xmm8, %xmm3, %xmm4
	0xc5, 0xb1, 0x64, 0xeb, //0x000038ba vpcmpgtb     %xmm3, %xmm9, %xmm5
	0xc5, 0xd9, 0xdb, 0xe5, //0x000038be vpand        %xmm5, %xmm4, %xmm4
	0xc5, 0xa9, 0x74, 0xeb, //0x000038c2 vpcmpeqb     %xmm3, %xmm10, %xmm5
	0xc5, 0xa1, 0x74, 0xf3, //0x000038c6 vpcmpeqb     %xmm3, %xmm11, %xmm6
	0xc5, 0xc9, 0xeb, 0xed, //0x000038ca vpor         %xmm5, %xmm6, %xmm5
	0xc5, 0x99, 0xeb, 0xf3, //0x000038ce vpor         %xmm3, %xmm12, %xmm6
	0xc5, 0x91, 0x74, 0xdb, //0x000038d2 vpcmpeqb     %xmm3, %xmm13, %xmm3
	0xc5, 0xc9, 0x74, 0xf2, //0x000038d6 vpcmpeqb     %xmm2, %xmm6, %xmm6
	0xc5, 0xc9, 0xeb, 0xfb, //0x000038da vpor         %xmm3, %xmm6, %xmm7
	0xc5, 0xd1, 0xeb, 0xe4, //0x000038de vpor         %xmm4, %xmm5, %xmm4
	0xc5, 0xc1, 0xeb, 0xe4, //0x000038e2 vpor         %xmm4, %xmm7, %xmm4
	0xc5, 0xf9, 0xd7, 0xdb, //0x000038e6 vpmovmskb    %xmm3, %ebx
	0xc5, 0xf9, 0xd7, 0xd6, //0x000038ea vpmovmskb    %xmm6, %edx
	0xc5, 0xf9, 0xd7, 0xf5, //0x000038ee vpmovmskb    %xmm5, %esi
	0xc5, 0xf9, 0xd7, 0xc4, //0x000038f2 vpmovmskb    %xmm4, %eax
	0xb9, 0xff, 0xff, 0xff, 0xff, //0x000038f6 movl         $4294967295, %ecx
	0x48, 0x31, 0xc8, //0x000038fb xorq         %rcx, %rax
	0x48, 0x0f, 0xbc, 0xc8, //0x000038fe bsfq         %rax, %rcx
	0x83, 0xf9, 0x10, //0x00003902 cmpl         $16, %ecx
	0x0f, 0x84, 0x11, 0x00, 0x00, 0x00, //0x00003905 je           LBB0_720
	0xb8, 0xff, 0xff, 0xff, 0xff, //0x0000390b movl         $-1, %eax
	0xd3, 0xe0, //0x00003910 shll         %cl, %eax
	0xf7, 0xd0, //0x00003912 notl         %eax
	0x21, 0xc3, //0x00003914 andl         %eax, %ebx
	0x21, 0xc2, //0x00003916 andl         %eax, %edx
	0x21, 0xf0, //0x00003918 andl         %esi, %eax
	0x89, 0xc6, //0x0000391a movl         %eax, %esi
	//0x0000391c LBB0_720
	0x8d, 0x43, 0xff, //0x0000391c leal         $-1(%rbx), %eax
	0x21, 0xd8, //0x0000391f andl         %ebx, %eax
	0x0f, 0x85, 0xb1, 0x06, 0x00, 0x00, //0x00003921 jne          LBB0_805
	0x8d, 0x42, 0xff, //0x00003927 leal         $-1(%rdx), %eax
	0x21, 0xd0, //0x0000392a andl         %edx, %eax
	0x0f, 0x85, 0xa6, 0x06, 0x00, 0x00, //0x0000392c jne          LBB0_805
	0x8d, 0x46, 0xff, //0x00003932 leal         $-1(%rsi), %eax
	0x21, 0xf0, //0x00003935 andl         %esi, %eax
	0x0f, 0x85, 0x9b, 0x06, 0x00, 0x00, //0x00003937 jne          LBB0_805
	0x85, 0xdb, //0x0000393d testl        %ebx, %ebx
	0x0f, 0x84, 0x1e, 0x00, 0x00, 0x00, //0x0000393f je           LBB0_726
	0x4c, 0x89, 0xe0, //0x00003945 movq         %r12, %rax
	0x4c, 0x29, 0xf0, //0x00003948 subq         %r14, %rax
	0x44, 0x0f, 0xbc, 0xd3, //0x0000394b bsfl         %ebx, %r10d
	0x49, 0x01, 0xc2, //0x0000394f addq         %rax, %r10
	0x48, 0x83, 0x7c, 0x24, 0x50, 0xff, //0x00003952 cmpq         $-1, $80(%rsp)
	0x0f, 0x85, 0x84, 0x06, 0x00, 0x00, //0x00003958 jne          LBB0_806
	0x4c, 0x89, 0x54, 0x24, 0x50, //0x0000395e movq         %r10, $80(%rsp)
	//0x00003963 LBB0_726
	0x85, 0xd2, //0x00003963 testl        %edx, %edx
	0x0f, 0x84, 0x1a, 0x00, 0x00, 0x00, //0x00003965 je           LBB0_729
	0x4c, 0x89, 0xe0, //0x0000396b movq         %r12, %rax
	0x4c, 0x29, 0xf0, //0x0000396e subq         %r14, %rax
	0x44, 0x0f, 0xbc, 0xd2, //0x00003971 bsfl         %edx, %r10d
	0x49, 0x01, 0xc2, //0x00003975 addq         %rax, %r10
	0x49, 0x83, 0xfd, 0xff, //0x00003978 cmpq         $-1, %r13
	0x0f, 0x85, 0x60, 0x06, 0x00, 0x00, //0x0000397c jne          LBB0_806
	0x4d, 0x89, 0xd5, //0x00003982 movq         %r10, %r13
	//0x00003985 LBB0_729
	0x85, 0xf6, //0x00003985 testl        %esi, %esi
	0x0f, 0x84, 0x1e, 0x00, 0x00, 0x00, //0x00003987 je           LBB0_732
	0x4c, 0x89, 0xe0, //0x0000398d movq         %r12, %rax
	0x4c, 0x29, 0xf0, //0x00003990 subq         %r14, %rax
	0x44, 0x0f, 0xbc, 0xd6, //0x00003993 bsfl         %esi, %r10d
	0x49, 0x01, 0xc2, //0x00003997 addq         %rax, %r10
	0x48, 0x83, 0x7c, 0x24, 0x20, 0xff, //0x0000399a cmpq         $-1, $32(%rsp)
	0x0f, 0x85, 0x3c, 0x06, 0x00, 0x00, //0x000039a0 jne          LBB0_806
	0x4c, 0x89, 0x54, 0x24, 0x20, //0x000039a6 movq         %r10, $32(%rsp)
	//0x000039ab LBB0_732
	0x83, 0xf9, 0x10, //0x000039ab cmpl         $16, %ecx
	0x0f, 0x85, 0x96, 0x01, 0x00, 0x00, //0x000039ae jne          LBB0_762
	0x49, 0x83, 0xc4, 0x10, //0x000039b4 addq         $16, %r12
	0x48, 0x83, 0xc7, 0xf0, //0x000039b8 addq         $-16, %rdi
	0x48, 0x83, 0xff, 0x0f, //0x000039bc cmpq         $15, %rdi
	0x0f, 0x87, 0xe9, 0xfe, 0xff, 0xff, //0x000039c0 ja           LBB0_718
	0x4d, 0x85, 0xdb, //0x000039c6 testq        %r11, %r11
	0x48, 0x8b, 0x4c, 0x24, 0x40, //0x000039c9 movq         $64(%rsp), %rcx
	0x4c, 0x8b, 0x64, 0x24, 0x30, //0x000039ce movq         $48(%rsp), %r12
	0x48, 0x8d, 0x3d, 0xc6, 0x1b, 0x00, 0x00, //0x000039d3 leaq         $7110(%rip), %rdi  /* LJTI0_4+0(%rip) */
	0x0f, 0x84, 0x7a, 0x01, 0x00, 0x00, //0x000039da je           LBB0_763
	//0x000039e0 LBB0_735
	0x4b, 0x8d, 0x34, 0x18, //0x000039e0 leaq         (%r8,%r11), %rsi
	0xe9, 0x26, 0x00, 0x00, 0x00, //0x000039e4 jmp          LBB0_739
	//0x000039e9 LBB0_736
	0x49, 0x89, 0xd2, //0x000039e9 movq         %rdx, %r10
	0x4d, 0x29, 0xf2, //0x000039ec subq         %r14, %r10
	0x48, 0x83, 0x7c, 0x24, 0x20, 0xff, //0x000039ef cmpq         $-1, $32(%rsp)
	0x0f, 0x85, 0x02, 0x08, 0x00, 0x00, //0x000039f5 jne          LBB0_823
	0x49, 0xff, 0xca, //0x000039fb decq         %r10
	0x4c, 0x89, 0x54, 0x24, 0x20, //0x000039fe movq         %r10, $32(%rsp)
	//0x00003a03 LBB0_738
	0x49, 0x89, 0xd0, //0x00003a03 movq         %rdx, %r8
	0x49, 0xff, 0xcb, //0x00003a06 decq         %r11
	0x0f, 0x84, 0x4a, 0x07, 0x00, 0x00, //0x00003a09 je           LBB0_815
	//0x00003a0f LBB0_739
	0x41, 0x0f, 0xbe, 0x00, //0x00003a0f movsbl       (%r8), %eax
	0x83, 0xc0, 0xd5, //0x00003a13 addl         $-43, %eax
	0x83, 0xf8, 0x3a, //0x00003a16 cmpl         $58, %eax
	0x0f, 0x87, 0x3b, 0x01, 0x00, 0x00, //0x00003a19 ja           LBB0_763
	0x49, 0x8d, 0x50, 0x01, //0x00003a1f leaq         $1(%r8), %rdx
	0x48, 0x63, 0x04, 0x87, //0x00003a23 movslq       (%rdi,%rax,4), %rax
	0x48, 0x01, 0xf8, //0x00003a27 addq         %rdi, %rax
	0xff, 0xe0, //0x00003a2a jmpq         *%rax
	//0x00003a2c LBB0_741
	0x49, 0x89, 0xd2, //0x00003a2c movq         %rdx, %r10
	0x4d, 0x29, 0xf2, //0x00003a2f subq         %r14, %r10
	0x49, 0x83, 0xfd, 0xff, //0x00003a32 cmpq         $-1, %r13
	0x0f, 0x85, 0xc1, 0x07, 0x00, 0x00, //0x00003a36 jne          LBB0_823
	0x49, 0xff, 0xca, //0x00003a3c decq         %r10
	0x4d, 0x89, 0xd5, //0x00003a3f movq         %r10, %r13
	0xe9, 0xbc, 0xff, 0xff, 0xff, //0x00003a42 jmp          LBB0_738
	//0x00003a47 LBB0_743
	0x49, 0x89, 0xd2, //0x00003a47 movq         %rdx, %r10
	0x4d, 0x29, 0xf2, //0x00003a4a subq         %r14, %r10
	0x48, 0x83, 0x7c, 0x24, 0x50, 0xff, //0x00003a4d cmpq         $-1, $80(%rsp)
	0x0f, 0x85, 0xa4, 0x07, 0x00, 0x00, //0x00003a53 jne          LBB0_823
	0x49, 0xff, 0xca, //0x00003a59 decq         %r10
	0x4c, 0x89, 0x54, 0x24, 0x50, //0x00003a5c movq         %r10, $80(%rsp)
	0xe9, 0x9d, 0xff, 0xff, 0xff, //0x00003a61 jmp          LBB0_738
	//0x00003a66 LBB0_745
	0x48, 0x8b, 0x01, //0x00003a66 movq         (%rcx), %rax
	0x48, 0x3d, 0xff, 0x0f, 0x00, 0x00, //0x00003a69 cmpq         $4095, %rax
	0x0f, 0x8f, 0x89, 0x0a, 0x00, 0x00, //0x00003a6f jg           LBB0_942
	0x48, 0x8d, 0x50, 0x01, //0x00003a75 leaq         $1(%rax), %rdx
	0x48, 0x89, 0x11, //0x00003a79 movq         %rdx, (%rcx)
	0x48, 0xc7, 0x44, 0xc1, 0x08, 0x05, 0x00, 0x00, 0x00, //0x00003a7c movq         $5, $8(%rcx,%rax,8)
	0xe9, 0x28, 0xf3, 0xff, 0xff, //0x00003a85 jmp          LBB0_553
	//0x00003a8a LBB0_747
	0x48, 0x8b, 0x44, 0x24, 0x28, //0x00003a8a movq         $40(%rsp), %rax
	0x48, 0x8b, 0x40, 0x08, //0x00003a8f movq         $8(%rax), %rax
	0x48, 0x8d, 0x50, 0xfc, //0x00003a93 leaq         $-4(%rax), %rdx
	0x49, 0x39, 0xd1, //0x00003a97 cmpq         %rdx, %r9
	0x0f, 0x83, 0x8f, 0x12, 0x00, 0x00, //0x00003a9a jae          LBB0_941
	0x43, 0x8b, 0x04, 0x3c, //0x00003aa0 movl         (%r12,%r15), %eax
	0x3d, 0x61, 0x6c, 0x73, 0x65, //0x00003aa4 cmpl         $1702063201, %eax
	0x0f, 0x85, 0xcb, 0x12, 0x00, 0x00, //0x00003aa9 jne          LBB0_943
	0x4d, 0x8d, 0x51, 0x05, //0x00003aaf leaq         $5(%r9), %r10
	0xe9, 0x28, 0x01, 0x00, 0x00, //0x00003ab3 jmp          LBB0_775
	//0x00003ab8 LBB0_750
	0x48, 0x8b, 0x44, 0x24, 0x28, //0x00003ab8 movq         $40(%rsp), %rax
	0x48, 0x8b, 0x40, 0x08, //0x00003abd movq         $8(%rax), %rax
	0x48, 0x8d, 0x50, 0xfd, //0x00003ac1 leaq         $-3(%rax), %rdx
	0x49, 0x39, 0xd1, //0x00003ac5 cmpq         %rdx, %r9
	0x0f, 0x83, 0x61, 0x12, 0x00, 0x00, //0x00003ac8 jae          LBB0_941
	0x41, 0x81, 0x3a, 0x6e, 0x75, 0x6c, 0x6c, //0x00003ace cmpl         $1819047278, (%r10)
	0x0f, 0x84, 0x28, 0x00, 0x00, 0x00, //0x00003ad5 je           LBB0_758
	0xe9, 0xeb, 0x12, 0x00, 0x00, //0x00003adb jmp          LBB0_752
	//0x00003ae0 LBB0_756
	0x48, 0x8b, 0x44, 0x24, 0x28, //0x00003ae0 movq         $40(%rsp), %rax
	0x48, 0x8b, 0x40, 0x08, //0x00003ae5 movq         $8(%rax), %rax
	0x48, 0x8d, 0x50, 0xfd, //0x00003ae9 leaq         $-3(%rax), %rdx
	0x49, 0x39, 0xd1, //0x00003aed cmpq         %rdx, %r9
	0x0f, 0x83, 0x39, 0x12, 0x00, 0x00, //0x00003af0 jae          LBB0_941
	0x41, 0x81, 0x3a, 0x74, 0x72, 0x75, 0x65, //0x00003af6 cmpl         $1702195828, (%r10)
	0x0f, 0x85, 0x1a, 0x13, 0x00, 0x00, //0x00003afd jne          LBB0_948
	//0x00003b03 LBB0_758
	0x4d, 0x8d, 0x51, 0x04, //0x00003b03 leaq         $4(%r9), %r10
	0xe9, 0xd4, 0x00, 0x00, 0x00, //0x00003b07 jmp          LBB0_775
	//0x00003b0c LBB0_759
	0x48, 0x8b, 0x01, //0x00003b0c movq         (%rcx), %rax
	0x48, 0x3d, 0xff, 0x0f, 0x00, 0x00, //0x00003b0f cmpq         $4095, %rax
	0x0f, 0x8f, 0xe3, 0x09, 0x00, 0x00, //0x00003b15 jg           LBB0_942
	0x48, 0x8d, 0x50, 0x01, //0x00003b1b leaq         $1(%rax), %rdx
	0x48, 0x89, 0x11, //0x00003b1f movq         %rdx, (%rcx)
	0x48, 0xc7, 0x44, 0xc1, 0x08, 0x06, 0x00, 0x00, 0x00, //0x00003b22 movq         $6, $8(%rcx,%rax,8)
	0xe9, 0x82, 0xf2, 0xff, 0xff, //0x00003b2b jmp          LBB0_553
	//0x00003b30 LBB0_761
	0x48, 0x85, 0xc0, //0x00003b30 testq        %rax, %rax
	0x49, 0x8d, 0x43, 0xff, //0x00003b33 leaq         $-1(%r11), %rax
	0x49, 0xf7, 0xd3, //0x00003b37 notq         %r11
	0x4d, 0x0f, 0x48, 0xdf, //0x00003b3a cmovsq       %r15, %r11
	0x48, 0x39, 0xc2, //0x00003b3e cmpq         %rax, %rdx
	0x4d, 0x0f, 0x44, 0xfb, //0x00003b41 cmoveq       %r11, %r15
	0xe9, 0x03, 0xfb, 0xff, 0xff, //0x00003b45 jmp          LBB0_684
	//0x00003b4a LBB0_762
	0x49, 0x01, 0xcc, //0x00003b4a addq         %rcx, %r12
	0x4d, 0x89, 0xe0, //0x00003b4d movq         %r12, %r8
	0x48, 0x8b, 0x4c, 0x24, 0x40, //0x00003b50 movq         $64(%rsp), %rcx
	0x4c, 0x8b, 0x64, 0x24, 0x30, //0x00003b55 movq         $48(%rsp), %r12
	//0x00003b5a LBB0_763
	0x49, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x00003b5a movq         $-1, %r10
	0x4d, 0x85, 0xed, //0x00003b61 testq        %r13, %r13
	0x0f, 0x84, 0x00, 0x12, 0x00, 0x00, //0x00003b64 je           LBB0_940
	//0x00003b6a LBB0_764
	0x48, 0x8b, 0x74, 0x24, 0x20, //0x00003b6a movq         $32(%rsp), %rsi
	0x48, 0x85, 0xf6, //0x00003b6f testq        %rsi, %rsi
	0x0f, 0x84, 0xf2, 0x11, 0x00, 0x00, //0x00003b72 je           LBB0_940
	0x48, 0x8b, 0x54, 0x24, 0x50, //0x00003b78 movq         $80(%rsp), %rdx
	0x48, 0x85, 0xd2, //0x00003b7d testq        %rdx, %rdx
	0x0f, 0x84, 0xe4, 0x11, 0x00, 0x00, //0x00003b80 je           LBB0_940
	0x4d, 0x29, 0xf0, //0x00003b86 subq         %r14, %r8
	0x49, 0x8d, 0x40, 0xff, //0x00003b89 leaq         $-1(%r8), %rax
	0x49, 0x39, 0xc5, //0x00003b8d cmpq         %rax, %r13
	0x0f, 0x84, 0x33, 0x00, 0x00, 0x00, //0x00003b90 je           LBB0_772
	0x48, 0x39, 0xc2, //0x00003b96 cmpq         %rax, %rdx
	0x0f, 0x84, 0x2a, 0x00, 0x00, 0x00, //0x00003b99 je           LBB0_772
	0x48, 0x39, 0xc6, //0x00003b9f cmpq         %rax, %rsi
	0x0f, 0x84, 0x21, 0x00, 0x00, 0x00, //0x00003ba2 je           LBB0_772
	0x48, 0x85, 0xf6, //0x00003ba8 testq        %rsi, %rsi
	0x0f, 0x8e, 0x0f, 0x01, 0x00, 0x00, //0x00003bab jle          LBB0_778
	0x48, 0x8d, 0x46, 0xff, //0x00003bb1 leaq         $-1(%rsi), %rax
	0x49, 0x39, 0xc5, //0x00003bb5 cmpq         %rax, %r13
	0x0f, 0x84, 0x02, 0x01, 0x00, 0x00, //0x00003bb8 je           LBB0_778
	0x48, 0xf7, 0xd6, //0x00003bbe notq         %rsi
	0x49, 0x89, 0xf2, //0x00003bc1 movq         %rsi, %r10
	0xe9, 0x06, 0x00, 0x00, 0x00, //0x00003bc4 jmp          LBB0_773
	//0x00003bc9 LBB0_772
	0x49, 0xf7, 0xd8, //0x00003bc9 negq         %r8
	0x4d, 0x89, 0xc2, //0x00003bcc movq         %r8, %r10
	//0x00003bcf LBB0_773
	0x4d, 0x85, 0xd2, //0x00003bcf testq        %r10, %r10
	0x0f, 0x88, 0x92, 0x11, 0x00, 0x00, //0x00003bd2 js           LBB0_940
	//0x00003bd8 LBB0_774
	0x4d, 0x01, 0xfa, //0x00003bd8 addq         %r15, %r10
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x00003bdb movq         $24(%rsp), %r8
	//0x00003be0 LBB0_775
	0x4d, 0x89, 0x10, //0x00003be0 movq         %r10, (%r8)
	0x4d, 0x89, 0xcd, //0x00003be3 movq         %r9, %r13
	0x4d, 0x85, 0xff, //0x00003be6 testq        %r15, %r15
	0x0f, 0x8f, 0xc6, 0xf1, 0xff, 0xff, //0x00003be9 jg           LBB0_554
	0xe9, 0x9a, 0x07, 0x00, 0x00, //0x00003bef jmp          LBB0_839
	//0x00003bf4 LBB0_601
	0x4c, 0x8b, 0x94, 0x24, 0xa8, 0x00, 0x00, 0x00, //0x00003bf4 movq         $168(%rsp), %r10
	0x48, 0x8b, 0x5c, 0x24, 0x50, //0x00003bfc movq         $80(%rsp), %rbx
	0x48, 0x83, 0xfb, 0x20, //0x00003c01 cmpq         $32, %rbx
	0x0f, 0x82, 0xf5, 0x04, 0x00, 0x00, //0x00003c05 jb           LBB0_810
	//0x00003c0b LBB0_602
	0xc4, 0xc1, 0x7a, 0x6f, 0x1a, //0x00003c0b vmovdqu      (%r10), %xmm3
	0xc4, 0xc1, 0x7a, 0x6f, 0x62, 0x10, //0x00003c10 vmovdqu      $16(%r10), %xmm4
	0xc5, 0xe1, 0x74, 0xe8, //0x00003c16 vpcmpeqb     %xmm0, %xmm3, %xmm5
	0xc5, 0xf9, 0xd7, 0xfd, //0x00003c1a vpmovmskb    %xmm5, %edi
	0xc5, 0xd9, 0x74, 0xe8, //0x00003c1e vpcmpeqb     %xmm0, %xmm4, %xmm5
	0xc5, 0xf9, 0xd7, 0xf5, //0x00003c22 vpmovmskb    %xmm5, %esi
	0xc5, 0xe1, 0x74, 0xd9, //0x00003c26 vpcmpeqb     %xmm1, %xmm3, %xmm3
	0xc5, 0xf9, 0xd7, 0xcb, //0x00003c2a vpmovmskb    %xmm3, %ecx
	0xc5, 0xd9, 0x74, 0xd9, //0x00003c2e vpcmpeqb     %xmm1, %xmm4, %xmm3
	0xc5, 0xf9, 0xd7, 0xc3, //0x00003c32 vpmovmskb    %xmm3, %eax
	0x48, 0xc1, 0xe6, 0x10, //0x00003c36 shlq         $16, %rsi
	0x48, 0xc1, 0xe0, 0x10, //0x00003c3a shlq         $16, %rax
	0x48, 0x09, 0xc1, //0x00003c3e orq          %rax, %rcx
	0x49, 0x83, 0xfc, 0xff, //0x00003c41 cmpq         $-1, %r12
	0x0f, 0x85, 0x98, 0x00, 0x00, 0x00, //0x00003c45 jne          LBB0_781
	0x48, 0x85, 0xc9, //0x00003c4b testq        %rcx, %rcx
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x00003c4e movq         $24(%rsp), %r8
	0x0f, 0x85, 0xfa, 0x05, 0x00, 0x00, //0x00003c53 jne          LBB0_824
	0x48, 0x09, 0xfe, //0x00003c59 orq          %rdi, %rsi
	0x48, 0x89, 0xc8, //0x00003c5c movq         %rcx, %rax
	0x4c, 0x09, 0xe8, //0x00003c5f orq          %r13, %rax
	0x0f, 0x85, 0x8f, 0x00, 0x00, 0x00, //0x00003c62 jne          LBB0_782
	//0x00003c68 LBB0_605
	0x48, 0x85, 0xf6, //0x00003c68 testq        %rsi, %rsi
	0x0f, 0x84, 0xbf, 0x00, 0x00, 0x00, //0x00003c6b je           LBB0_783
	//0x00003c71 LBB0_606
	0x48, 0x0f, 0xbc, 0xc6, //0x00003c71 bsfq         %rsi, %rax
	0x4c, 0x03, 0x54, 0x24, 0x58, //0x00003c75 addq         $88(%rsp), %r10
	0x49, 0x01, 0xc2, //0x00003c7a addq         %rax, %r10
	//0x00003c7d LBB0_607
	0x4d, 0x85, 0xd2, //0x00003c7d testq        %r10, %r10
	0x48, 0x8b, 0x44, 0x24, 0x20, //0x00003c80 movq         $32(%rsp), %rax
	0x0f, 0x88, 0x7f, 0x08, 0x00, 0x00, //0x00003c85 js           LBB0_861
	0x4d, 0x89, 0x10, //0x00003c8b movq         %r10, (%r8)
	0x4d, 0x89, 0xcd, //0x00003c8e movq         %r9, %r13
	0x4d, 0x85, 0xff, //0x00003c91 testq        %r15, %r15
	0x48, 0x8b, 0x4c, 0x24, 0x40, //0x00003c94 movq         $64(%rsp), %rcx
	0x4c, 0x8b, 0x64, 0x24, 0x30, //0x00003c99 movq         $48(%rsp), %r12
	0x0f, 0x8f, 0x11, 0xf1, 0xff, 0xff, //0x00003c9e jg           LBB0_554
	0xe9, 0xe5, 0x06, 0x00, 0x00, //0x00003ca4 jmp          LBB0_839
	//0x00003ca9 LBB0_776
	0x4d, 0x29, 0xd4, //0x00003ca9 subq         %r10, %r12
	0x44, 0x0f, 0xbc, 0xf8, //0x00003cac bsfl         %eax, %r15d
	0x4d, 0x01, 0xe7, //0x00003cb0 addq         %r12, %r15
	0x49, 0xf7, 0xd7, //0x00003cb3 notq         %r15
	//0x00003cb6 LBB0_777
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x00003cb6 movq         $24(%rsp), %r8
	0xe9, 0xec, 0x01, 0x00, 0x00, //0x00003cbb jmp          LBB0_792
	//0x00003cc0 LBB0_778
	0x48, 0x89, 0xd0, //0x00003cc0 movq         %rdx, %rax
	0x4c, 0x09, 0xe8, //0x00003cc3 orq          %r13, %rax
	0x4c, 0x39, 0xea, //0x00003cc6 cmpq         %r13, %rdx
	0x0f, 0x8c, 0xba, 0x01, 0x00, 0x00, //0x00003cc9 jl           LBB0_790
	0x48, 0x85, 0xc0, //0x00003ccf testq        %rax, %rax
	0x0f, 0x88, 0xb1, 0x01, 0x00, 0x00, //0x00003cd2 js           LBB0_790
	0x48, 0xf7, 0xd2, //0x00003cd8 notq         %rdx
	0x49, 0x89, 0xd2, //0x00003cdb movq         %rdx, %r10
	0xe9, 0xec, 0xfe, 0xff, 0xff, //0x00003cde jmp          LBB0_773
	//0x00003ce3 LBB0_781
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x00003ce3 movq         $24(%rsp), %r8
	0x48, 0x09, 0xfe, //0x00003ce8 orq          %rdi, %rsi
	0x48, 0x89, 0xc8, //0x00003ceb movq         %rcx, %rax
	0x4c, 0x09, 0xe8, //0x00003cee orq          %r13, %rax
	0x0f, 0x84, 0x71, 0xff, 0xff, 0xff, //0x00003cf1 je           LBB0_605
	//0x00003cf7 LBB0_782
	0x44, 0x89, 0xe8, //0x00003cf7 movl         %r13d, %eax
	0xf7, 0xd0, //0x00003cfa notl         %eax
	0x21, 0xc8, //0x00003cfc andl         %ecx, %eax
	0x8d, 0x14, 0x00, //0x00003cfe leal         (%rax,%rax), %edx
	0x44, 0x09, 0xea, //0x00003d01 orl          %r13d, %edx
	0x89, 0xd7, //0x00003d04 movl         %edx, %edi
	0xf7, 0xd7, //0x00003d06 notl         %edi
	0x21, 0xcf, //0x00003d08 andl         %ecx, %edi
	0x81, 0xe7, 0xaa, 0xaa, 0xaa, 0xaa, //0x00003d0a andl         $-1431655766, %edi
	0x45, 0x31, 0xed, //0x00003d10 xorl         %r13d, %r13d
	0x01, 0xc7, //0x00003d13 addl         %eax, %edi
	0x41, 0x0f, 0x92, 0xc5, //0x00003d15 setb         %r13b
	0x01, 0xff, //0x00003d19 addl         %edi, %edi
	0x81, 0xf7, 0x55, 0x55, 0x55, 0x55, //0x00003d1b xorl         $1431655765, %edi
	0x21, 0xd7, //0x00003d21 andl         %edx, %edi
	0xf7, 0xd7, //0x00003d23 notl         %edi
	0x21, 0xfe, //0x00003d25 andl         %edi, %esi
	0x48, 0x85, 0xf6, //0x00003d27 testq        %rsi, %rsi
	0x0f, 0x85, 0x41, 0xff, 0xff, 0xff, //0x00003d2a jne          LBB0_606
	//0x00003d30 LBB0_783
	0x49, 0x83, 0xc2, 0x20, //0x00003d30 addq         $32, %r10
	0x48, 0x83, 0xc3, 0xe0, //0x00003d34 addq         $-32, %rbx
	0x4d, 0x85, 0xed, //0x00003d38 testq        %r13, %r13
	0x0f, 0x85, 0xcd, 0x03, 0x00, 0x00, //0x00003d3b jne          LBB0_811
	//0x00003d41 LBB0_784
	0x4c, 0x89, 0xe2, //0x00003d41 movq         %r12, %rdx
	0x48, 0x85, 0xdb, //0x00003d44 testq        %rbx, %rbx
	0x0f, 0x84, 0x36, 0x11, 0x00, 0x00, //0x00003d47 je           LBB0_813
	//0x00003d4d LBB0_785
	0x49, 0x8d, 0x4a, 0x01, //0x00003d4d leaq         $1(%r10), %rcx
	0x41, 0x0f, 0xb6, 0x02, //0x00003d51 movzbl       (%r10), %eax
	0x3c, 0x22, //0x00003d55 cmpb         $34, %al
	0x0f, 0x84, 0x5e, 0x01, 0x00, 0x00, //0x00003d57 je           LBB0_793
	0x48, 0x8d, 0x73, 0xff, //0x00003d5d leaq         $-1(%rbx), %rsi
	0x3c, 0x5c, //0x00003d61 cmpb         $92, %al
	0x0f, 0x84, 0x14, 0x00, 0x00, 0x00, //0x00003d63 je           LBB0_788
	0x48, 0x89, 0xf3, //0x00003d69 movq         %rsi, %rbx
	0x49, 0x89, 0xca, //0x00003d6c movq         %rcx, %r10
	0x48, 0x85, 0xf6, //0x00003d6f testq        %rsi, %rsi
	0x0f, 0x85, 0xd5, 0xff, 0xff, 0xff, //0x00003d72 jne          LBB0_785
	0xe9, 0x06, 0x11, 0x00, 0x00, //0x00003d78 jmp          LBB0_813
	//0x00003d7d LBB0_788
	0x48, 0x85, 0xf6, //0x00003d7d testq        %rsi, %rsi
	0x0f, 0x84, 0x9e, 0x05, 0x00, 0x00, //0x00003d80 je           LBB0_830
	0x48, 0x03, 0x4c, 0x24, 0x38, //0x00003d86 addq         $56(%rsp), %rcx
	0x48, 0x83, 0xfa, 0xff, //0x00003d8b cmpq         $-1, %rdx
	0x4c, 0x0f, 0x44, 0xe1, //0x00003d8f cmoveq       %rcx, %r12
	0x48, 0x0f, 0x44, 0xd1, //0x00003d93 cmoveq       %rcx, %rdx
	0x49, 0x83, 0xc2, 0x02, //0x00003d97 addq         $2, %r10
	0x48, 0x83, 0xc3, 0xfe, //0x00003d9b addq         $-2, %rbx
	0x48, 0x89, 0xde, //0x00003d9f movq         %rbx, %rsi
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x00003da2 movq         $24(%rsp), %r8
	0x48, 0x85, 0xf6, //0x00003da7 testq        %rsi, %rsi
	0x0f, 0x85, 0x9d, 0xff, 0xff, 0xff, //0x00003daa jne          LBB0_785
	0xe9, 0xce, 0x10, 0x00, 0x00, //0x00003db0 jmp          LBB0_813
	//0x00003db5 LBB0_662
	0x4c, 0x8b, 0x94, 0x24, 0xa8, 0x00, 0x00, 0x00, //0x00003db5 movq         $168(%rsp), %r10
	0x48, 0x8b, 0x5c, 0x24, 0x50, //0x00003dbd movq         $80(%rsp), %rbx
	0x48, 0x83, 0xfb, 0x20, //0x00003dc2 cmpq         $32, %rbx
	0x0f, 0x82, 0xea, 0x03, 0x00, 0x00, //0x00003dc6 jb           LBB0_818
	//0x00003dcc LBB0_663
	0xc4, 0xc1, 0x7a, 0x6f, 0x1a, //0x00003dcc vmovdqu      (%r10), %xmm3
	0xc4, 0xc1, 0x7a, 0x6f, 0x62, 0x10, //0x00003dd1 vmovdqu      $16(%r10), %xmm4
	0xc5, 0xe1, 0x74, 0xe8, //0x00003dd7 vpcmpeqb     %xmm0, %xmm3, %xmm5
	0xc5, 0xf9, 0xd7, 0xfd, //0x00003ddb vpmovmskb    %xmm5, %edi
	0xc5, 0xd9, 0x74, 0xe8, //0x00003ddf vpcmpeqb     %xmm0, %xmm4, %xmm5
	0xc5, 0xf9, 0xd7, 0xf5, //0x00003de3 vpmovmskb    %xmm5, %esi
	0xc5, 0xe1, 0x74, 0xd9, //0x00003de7 vpcmpeqb     %xmm1, %xmm3, %xmm3
	0xc5, 0xf9, 0xd7, 0xcb, //0x00003deb vpmovmskb    %xmm3, %ecx
	0xc5, 0xd9, 0x74, 0xd9, //0x00003def vpcmpeqb     %xmm1, %xmm4, %xmm3
	0xc5, 0xf9, 0xd7, 0xc3, //0x00003df3 vpmovmskb    %xmm3, %eax
	0x48, 0xc1, 0xe6, 0x10, //0x00003df7 shlq         $16, %rsi
	0x48, 0xc1, 0xe0, 0x10, //0x00003dfb shlq         $16, %rax
	0x48, 0x09, 0xc1, //0x00003dff orq          %rax, %rcx
	0x49, 0x83, 0xfc, 0xff, //0x00003e02 cmpq         $-1, %r12
	0x0f, 0x85, 0xd4, 0x00, 0x00, 0x00, //0x00003e06 jne          LBB0_795
	0x48, 0x85, 0xc9, //0x00003e0c testq        %rcx, %rcx
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x00003e0f movq         $24(%rsp), %r8
	0x0f, 0x85, 0x5c, 0x04, 0x00, 0x00, //0x00003e14 jne          LBB0_825
	0x48, 0x09, 0xfe, //0x00003e1a orq          %rdi, %rsi
	0x48, 0x89, 0xc8, //0x00003e1d movq         %rcx, %rax
	0x4c, 0x09, 0xe8, //0x00003e20 orq          %r13, %rax
	0x0f, 0x85, 0xcb, 0x00, 0x00, 0x00, //0x00003e23 jne          LBB0_796
	//0x00003e29 LBB0_666
	0x48, 0x85, 0xf6, //0x00003e29 testq        %rsi, %rsi
	0x0f, 0x84, 0xfb, 0x00, 0x00, 0x00, //0x00003e2c je           LBB0_797
	//0x00003e32 LBB0_667
	0x48, 0x0f, 0xbc, 0xc6, //0x00003e32 bsfq         %rsi, %rax
	0x4c, 0x03, 0x54, 0x24, 0x58, //0x00003e36 addq         $88(%rsp), %r10
	0x49, 0x01, 0xc2, //0x00003e3b addq         %rax, %r10
	0x4d, 0x85, 0xd2, //0x00003e3e testq        %r10, %r10
	0x48, 0x8b, 0x44, 0x24, 0x20, //0x00003e41 movq         $32(%rsp), %rax
	0x0f, 0x88, 0xbe, 0x06, 0x00, 0x00, //0x00003e46 js           LBB0_861
	//0x00003e4c LBB0_668
	0x4d, 0x89, 0x10, //0x00003e4c movq         %r10, (%r8)
	0x4d, 0x89, 0xcd, //0x00003e4f movq         %r9, %r13
	0x4d, 0x85, 0xff, //0x00003e52 testq        %r15, %r15
	0x48, 0x8b, 0x4c, 0x24, 0x40, //0x00003e55 movq         $64(%rsp), %rcx
	0x4c, 0x8b, 0x64, 0x24, 0x30, //0x00003e5a movq         $48(%rsp), %r12
	0x0f, 0x8e, 0x29, 0x05, 0x00, 0x00, //0x00003e5f jle          LBB0_839
	0x48, 0x8b, 0x01, //0x00003e65 movq         (%rcx), %rax
	0x48, 0x3d, 0xff, 0x0f, 0x00, 0x00, //0x00003e68 cmpq         $4095, %rax
	0x0f, 0x8f, 0x8a, 0x06, 0x00, 0x00, //0x00003e6e jg           LBB0_942
	0x48, 0x8d, 0x50, 0x01, //0x00003e74 leaq         $1(%rax), %rdx
	0x48, 0x89, 0x11, //0x00003e78 movq         %rdx, (%rcx)
	0x48, 0xc7, 0x44, 0xc1, 0x08, 0x04, 0x00, 0x00, 0x00, //0x00003e7b movq         $4, $8(%rcx,%rax,8)
	0xe9, 0x2c, 0xef, 0xff, 0xff, //0x00003e84 jmp          LBB0_554
	//0x00003e89 LBB0_790
	0x48, 0x85, 0xc0, //0x00003e89 testq        %rax, %rax
	0x49, 0x8d, 0x45, 0xff, //0x00003e8c leaq         $-1(%r13), %rax
	0x49, 0xf7, 0xd5, //0x00003e90 notq         %r13
	0x4d, 0x0f, 0x48, 0xe8, //0x00003e93 cmovsq       %r8, %r13
	0x48, 0x39, 0xc2, //0x00003e97 cmpq         %rax, %rdx
	0x4d, 0x0f, 0x45, 0xe8, //0x00003e9a cmovneq      %r8, %r13
	0x4d, 0x89, 0xea, //0x00003e9e movq         %r13, %r10
	0xe9, 0x29, 0xfd, 0xff, 0xff, //0x00003ea1 jmp          LBB0_773
	//0x00003ea6 LBB0_791
	0x48, 0xf7, 0xd0, //0x00003ea6 notq         %rax
	0x49, 0x89, 0xc7, //0x00003ea9 movq         %rax, %r15
	//0x00003eac LBB0_792
	0x48, 0x8b, 0x4c, 0x24, 0x40, //0x00003eac movq         $64(%rsp), %rcx
	0x4c, 0x8b, 0x64, 0x24, 0x30, //0x00003eb1 movq         $48(%rsp), %r12
	0xe9, 0x92, 0xf7, 0xff, 0xff, //0x00003eb6 jmp          LBB0_684
	//0x00003ebb LBB0_793
	0x48, 0x2b, 0x4c, 0x24, 0x30, //0x00003ebb subq         $48(%rsp), %rcx
	0x49, 0x89, 0xca, //0x00003ec0 movq         %rcx, %r10
	0xe9, 0xb5, 0xfd, 0xff, 0xff, //0x00003ec3 jmp          LBB0_607
	//0x00003ec8 LBB0_794
	0x49, 0x89, 0xff, //0x00003ec8 movq         %rdi, %r15
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00003ecb movq         $-1, %rax
	0x4d, 0x85, 0xdb, //0x00003ed2 testq        %r11, %r11
	0x0f, 0x85, 0x15, 0xf7, 0xff, 0xff, //0x00003ed5 jne          LBB0_675
	0xe9, 0x41, 0x0e, 0x00, 0x00, //0x00003edb jmp          LBB0_931
	//0x00003ee0 LBB0_795
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x00003ee0 movq         $24(%rsp), %r8
	0x48, 0x09, 0xfe, //0x00003ee5 orq          %rdi, %rsi
	0x48, 0x89, 0xc8, //0x00003ee8 movq         %rcx, %rax
	0x4c, 0x09, 0xe8, //0x00003eeb orq          %r13, %rax
	0x0f, 0x84, 0x35, 0xff, 0xff, 0xff, //0x00003eee je           LBB0_666
	//0x00003ef4 LBB0_796
	0x44, 0x89, 0xe8, //0x00003ef4 movl         %r13d, %eax
	0xf7, 0xd0, //0x00003ef7 notl         %eax
	0x21, 0xc8, //0x00003ef9 andl         %ecx, %eax
	0x8d, 0x14, 0x00, //0x00003efb leal         (%rax,%rax), %edx
	0x44, 0x09, 0xea, //0x00003efe orl          %r13d, %edx
	0x89, 0xd7, //0x00003f01 movl         %edx, %edi
	0xf7, 0xd7, //0x00003f03 notl         %edi
	0x21, 0xcf, //0x00003f05 andl         %ecx, %edi
	0x81, 0xe7, 0xaa, 0xaa, 0xaa, 0xaa, //0x00003f07 andl         $-1431655766, %edi
	0x45, 0x31, 0xed, //0x00003f0d xorl         %r13d, %r13d
	0x01, 0xc7, //0x00003f10 addl         %eax, %edi
	0x41, 0x0f, 0x92, 0xc5, //0x00003f12 setb         %r13b
	0x01, 0xff, //0x00003f16 addl         %edi, %edi
	0x81, 0xf7, 0x55, 0x55, 0x55, 0x55, //0x00003f18 xorl         $1431655765, %edi
	0x21, 0xd7, //0x00003f1e andl         %edx, %edi
	0xf7, 0xd7, //0x00003f20 notl         %edi
	0x21, 0xfe, //0x00003f22 andl         %edi, %esi
	0x48, 0x85, 0xf6, //0x00003f24 testq        %rsi, %rsi
	0x0f, 0x85, 0x05, 0xff, 0xff, 0xff, //0x00003f27 jne          LBB0_667
	//0x00003f2d LBB0_797
	0x49, 0x83, 0xc2, 0x20, //0x00003f2d addq         $32, %r10
	0x48, 0x83, 0xc3, 0xe0, //0x00003f31 addq         $-32, %rbx
	0x4d, 0x85, 0xed, //0x00003f35 testq        %r13, %r13
	0x0f, 0x85, 0x86, 0x02, 0x00, 0x00, //0x00003f38 jne          LBB0_819
	//0x00003f3e LBB0_798
	0x4c, 0x89, 0xe2, //0x00003f3e movq         %r12, %rdx
	0x48, 0x85, 0xdb, //0x00003f41 testq        %rbx, %rbx
	0x0f, 0x84, 0x39, 0x0f, 0x00, 0x00, //0x00003f44 je           LBB0_813
	//0x00003f4a LBB0_799
	0x49, 0x8d, 0x4a, 0x01, //0x00003f4a leaq         $1(%r10), %rcx
	0x41, 0x0f, 0xb6, 0x02, //0x00003f4e movzbl       (%r10), %eax
	0x3c, 0x22, //0x00003f52 cmpb         $34, %al
	0x0f, 0x84, 0x63, 0x00, 0x00, 0x00, //0x00003f54 je           LBB0_860
	0x48, 0x8d, 0x73, 0xff, //0x00003f5a leaq         $-1(%rbx), %rsi
	0x3c, 0x5c, //0x00003f5e cmpb         $92, %al
	0x0f, 0x84, 0x14, 0x00, 0x00, 0x00, //0x00003f60 je           LBB0_802
	0x48, 0x89, 0xf3, //0x00003f66 movq         %rsi, %rbx
	0x49, 0x89, 0xca, //0x00003f69 movq         %rcx, %r10
	0x48, 0x85, 0xf6, //0x00003f6c testq        %rsi, %rsi
	0x0f, 0x85, 0xd5, 0xff, 0xff, 0xff, //0x00003f6f jne          LBB0_799
	0xe9, 0x09, 0x0f, 0x00, 0x00, //0x00003f75 jmp          LBB0_813
	//0x00003f7a LBB0_802
	0x48, 0x85, 0xf6, //0x00003f7a testq        %rsi, %rsi
	0x0f, 0x84, 0xa1, 0x03, 0x00, 0x00, //0x00003f7d je           LBB0_830
	0x48, 0x03, 0x4c, 0x24, 0x38, //0x00003f83 addq         $56(%rsp), %rcx
	0x48, 0x83, 0xfa, 0xff, //0x00003f88 cmpq         $-1, %rdx
	0x4c, 0x0f, 0x44, 0xe1, //0x00003f8c cmoveq       %rcx, %r12
	0x48, 0x0f, 0x44, 0xd1, //0x00003f90 cmoveq       %rcx, %rdx
	0x49, 0x83, 0xc2, 0x02, //0x00003f94 addq         $2, %r10
	0x48, 0x83, 0xc3, 0xfe, //0x00003f98 addq         $-2, %rbx
	0x48, 0x89, 0xde, //0x00003f9c movq         %rbx, %rsi
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x00003f9f movq         $24(%rsp), %r8
	0x48, 0x85, 0xf6, //0x00003fa4 testq        %rsi, %rsi
	0x0f, 0x85, 0x9d, 0xff, 0xff, 0xff, //0x00003fa7 jne          LBB0_799
	0xe9, 0xd1, 0x0e, 0x00, 0x00, //0x00003fad jmp          LBB0_813
	//0x00003fb2 LBB0_804
	0x48, 0xf7, 0xd0, //0x00003fb2 notq         %rax
	0x49, 0x89, 0xc7, //0x00003fb5 movq         %rax, %r15
	0xe9, 0xf9, 0xfc, 0xff, 0xff, //0x00003fb8 jmp          LBB0_777
	//0x00003fbd LBB0_860
	0x48, 0x2b, 0x4c, 0x24, 0x30, //0x00003fbd subq         $48(%rsp), %rcx
	0x49, 0x89, 0xca, //0x00003fc2 movq         %rcx, %r10
	0x4d, 0x85, 0xd2, //0x00003fc5 testq        %r10, %r10
	0x48, 0x8b, 0x44, 0x24, 0x20, //0x00003fc8 movq         $32(%rsp), %rax
	0x0f, 0x89, 0x79, 0xfe, 0xff, 0xff, //0x00003fcd jns          LBB0_668
	0xe9, 0x32, 0x05, 0x00, 0x00, //0x00003fd3 jmp          LBB0_861
	//0x00003fd8 LBB0_805
	0x4d, 0x29, 0xf4, //0x00003fd8 subq         %r14, %r12
	0x44, 0x0f, 0xbc, 0xd0, //0x00003fdb bsfl         %eax, %r10d
	0x4d, 0x01, 0xe2, //0x00003fdf addq         %r12, %r10
	//0x00003fe2 LBB0_806
	0x49, 0xf7, 0xd2, //0x00003fe2 notq         %r10
	0x48, 0x8b, 0x4c, 0x24, 0x40, //0x00003fe5 movq         $64(%rsp), %rcx
	0x4c, 0x8b, 0x64, 0x24, 0x30, //0x00003fea movq         $48(%rsp), %r12
	0xe9, 0xdb, 0xfb, 0xff, 0xff, //0x00003fef jmp          LBB0_773
	//0x00003ff4 LBB0_698
	0x4c, 0x8b, 0x94, 0x24, 0xa8, 0x00, 0x00, 0x00, //0x00003ff4 movq         $168(%rsp), %r10
	0x48, 0x8b, 0x5c, 0x24, 0x50, //0x00003ffc movq         $80(%rsp), %rbx
	0x48, 0x83, 0xfb, 0x20, //0x00004001 cmpq         $32, %rbx
	0x0f, 0x82, 0x69, 0x00, 0x00, 0x00, //0x00004005 jb           LBB0_704
	//0x0000400b LBB0_699
	0xc4, 0xc1, 0x7a, 0x6f, 0x1a, //0x0000400b vmovdqu      (%r10), %xmm3
	0xc4, 0xc1, 0x7a, 0x6f, 0x62, 0x10, //0x00004010 vmovdqu      $16(%r10), %xmm4
	0xc5, 0xe1, 0x74, 0xe8, //0x00004016 vpcmpeqb     %xmm0, %xmm3, %xmm5
	0xc5, 0xf9, 0xd7, 0xfd, //0x0000401a vpmovmskb    %xmm5, %edi
	0xc5, 0xd9, 0x74, 0xe8, //0x0000401e vpcmpeqb     %xmm0, %xmm4, %xmm5
	0xc5, 0xf9, 0xd7, 0xf5, //0x00004022 vpmovmskb    %xmm5, %esi
	0xc5, 0xe1, 0x74, 0xd9, //0x00004026 vpcmpeqb     %xmm1, %xmm3, %xmm3
	0xc5, 0xf9, 0xd7, 0xcb, //0x0000402a vpmovmskb    %xmm3, %ecx
	0xc5, 0xd9, 0x74, 0xd9, //0x0000402e vpcmpeqb     %xmm1, %xmm4, %xmm3
	0xc5, 0xf9, 0xd7, 0xc3, //0x00004032 vpmovmskb    %xmm3, %eax
	0x48, 0xc1, 0xe6, 0x10, //0x00004036 shlq         $16, %rsi
	0x48, 0xc1, 0xe0, 0x10, //0x0000403a shlq         $16, %rax
	0x48, 0x09, 0xc1, //0x0000403e orq          %rax, %rcx
	0x49, 0x83, 0xfc, 0xff, //0x00004041 cmpq         $-1, %r12
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x00004045 jne          LBB0_701
	0x48, 0x85, 0xc9, //0x0000404b testq        %rcx, %rcx
	0x0f, 0x85, 0x45, 0x02, 0x00, 0x00, //0x0000404e jne          LBB0_826
	//0x00004054 LBB0_701
	0x48, 0x09, 0xfe, //0x00004054 orq          %rdi, %rsi
	0x48, 0x89, 0xc8, //0x00004057 movq         %rcx, %rax
	0x4c, 0x09, 0xe8, //0x0000405a orq          %r13, %rax
	0x0f, 0x85, 0x54, 0x02, 0x00, 0x00, //0x0000405d jne          LBB0_827
	//0x00004063 LBB0_702
	0x48, 0x85, 0xf6, //0x00004063 testq        %rsi, %rsi
	0x0f, 0x85, 0x05, 0xfc, 0xff, 0xff, //0x00004066 jne          LBB0_606
	//0x0000406c LBB0_703
	0x49, 0x83, 0xc2, 0x20, //0x0000406c addq         $32, %r10
	0x48, 0x83, 0xc3, 0xe0, //0x00004070 addq         $-32, %rbx
	//0x00004074 LBB0_704
	0x4d, 0x85, 0xed, //0x00004074 testq        %r13, %r13
	0x0f, 0x85, 0x78, 0x02, 0x00, 0x00, //0x00004077 jne          LBB0_828
	0x4c, 0x89, 0xe2, //0x0000407d movq         %r12, %rdx
	0x48, 0x85, 0xdb, //0x00004080 testq        %rbx, %rbx
	0x0f, 0x84, 0x9b, 0x02, 0x00, 0x00, //0x00004083 je           LBB0_830
	//0x00004089 LBB0_706
	0x49, 0x8d, 0x4a, 0x01, //0x00004089 leaq         $1(%r10), %rcx
	0x41, 0x0f, 0xb6, 0x02, //0x0000408d movzbl       (%r10), %eax
	0x3c, 0x22, //0x00004091 cmpb         $34, %al
	0x0f, 0x84, 0xae, 0x00, 0x00, 0x00, //0x00004093 je           LBB0_814
	0x48, 0x8d, 0x73, 0xff, //0x00004099 leaq         $-1(%rbx), %rsi
	0x3c, 0x5c, //0x0000409d cmpb         $92, %al
	0x0f, 0x84, 0x14, 0x00, 0x00, 0x00, //0x0000409f je           LBB0_807
	0x48, 0x89, 0xf3, //0x000040a5 movq         %rsi, %rbx
	0x49, 0x89, 0xca, //0x000040a8 movq         %rcx, %r10
	0x48, 0x85, 0xf6, //0x000040ab testq        %rsi, %rsi
	0x0f, 0x85, 0xd5, 0xff, 0xff, 0xff, //0x000040ae jne          LBB0_706
	0xe9, 0x6b, 0x02, 0x00, 0x00, //0x000040b4 jmp          LBB0_830
	//0x000040b9 LBB0_807
	0x48, 0x85, 0xf6, //0x000040b9 testq        %rsi, %rsi
	0x0f, 0x84, 0x62, 0x02, 0x00, 0x00, //0x000040bc je           LBB0_830
	0x48, 0x03, 0x4c, 0x24, 0x38, //0x000040c2 addq         $56(%rsp), %rcx
	0x48, 0x83, 0xfa, 0xff, //0x000040c7 cmpq         $-1, %rdx
	0x4c, 0x0f, 0x44, 0xe1, //0x000040cb cmoveq       %rcx, %r12
	0x48, 0x0f, 0x44, 0xd1, //0x000040cf cmoveq       %rcx, %rdx
	0x49, 0x83, 0xc2, 0x02, //0x000040d3 addq         $2, %r10
	0x48, 0x83, 0xc3, 0xfe, //0x000040d7 addq         $-2, %rbx
	0x48, 0x89, 0xde, //0x000040db movq         %rbx, %rsi
	0x48, 0x85, 0xf6, //0x000040de testq        %rsi, %rsi
	0x0f, 0x85, 0xa2, 0xff, 0xff, 0xff, //0x000040e1 jne          LBB0_706
	0xe9, 0x38, 0x02, 0x00, 0x00, //0x000040e7 jmp          LBB0_830
	//0x000040ec LBB0_809
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x000040ec movq         $-1, %r12
	0x45, 0x31, 0xed, //0x000040f3 xorl         %r13d, %r13d
	0x48, 0x83, 0xfb, 0x20, //0x000040f6 cmpq         $32, %rbx
	0x0f, 0x83, 0x0b, 0xfb, 0xff, 0xff, //0x000040fa jae          LBB0_602
	//0x00004100 LBB0_810
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x00004100 movq         $24(%rsp), %r8
	0x4d, 0x85, 0xed, //0x00004105 testq        %r13, %r13
	0x0f, 0x84, 0x33, 0xfc, 0xff, 0xff, //0x00004108 je           LBB0_784
	//0x0000410e LBB0_811
	0x48, 0x85, 0xdb, //0x0000410e testq        %rbx, %rbx
	0x0f, 0x84, 0x0d, 0x02, 0x00, 0x00, //0x00004111 je           LBB0_830
	0x48, 0x8b, 0x44, 0x24, 0x38, //0x00004117 movq         $56(%rsp), %rax
	0x4c, 0x01, 0xd0, //0x0000411c addq         %r10, %rax
	0x49, 0x83, 0xfc, 0xff, //0x0000411f cmpq         $-1, %r12
	0x4c, 0x89, 0xe2, //0x00004123 movq         %r12, %rdx
	0x4c, 0x0f, 0x44, 0xe0, //0x00004126 cmoveq       %rax, %r12
	0x48, 0x0f, 0x44, 0xd0, //0x0000412a cmoveq       %rax, %rdx
	0x49, 0xff, 0xc2, //0x0000412e incq         %r10
	0x48, 0xff, 0xcb, //0x00004131 decq         %rbx
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x00004134 movq         $24(%rsp), %r8
	0x48, 0x85, 0xdb, //0x00004139 testq        %rbx, %rbx
	0x0f, 0x85, 0x0b, 0xfc, 0xff, 0xff, //0x0000413c jne          LBB0_785
	0xe9, 0x3c, 0x0d, 0x00, 0x00, //0x00004142 jmp          LBB0_813
	//0x00004147 LBB0_814
	0x48, 0x2b, 0x4c, 0x24, 0x30, //0x00004147 subq         $48(%rsp), %rcx
	0x49, 0x89, 0xca, //0x0000414c movq         %rcx, %r10
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x0000414f movq         $24(%rsp), %r8
	0xe9, 0x24, 0xfb, 0xff, 0xff, //0x00004154 jmp          LBB0_607
	//0x00004159 LBB0_815
	0x49, 0x89, 0xf0, //0x00004159 movq         %rsi, %r8
	0x49, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x0000415c movq         $-1, %r10
	0x4d, 0x85, 0xed, //0x00004163 testq        %r13, %r13
	0x0f, 0x85, 0xfe, 0xf9, 0xff, 0xff, //0x00004166 jne          LBB0_764
	0xe9, 0xf9, 0x0b, 0x00, 0x00, //0x0000416c jmp          LBB0_940
	//0x00004171 LBB0_816
	0x48, 0xc7, 0x44, 0x24, 0x20, 0xff, 0xff, 0xff, 0xff, //0x00004171 movq         $-1, $32(%rsp)
	0x4d, 0x89, 0xd7, //0x0000417a movq         %r10, %r15
	0x48, 0x89, 0xfa, //0x0000417d movq         %rdi, %rdx
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00004180 movq         $-1, %r11
	0x49, 0xc7, 0xc5, 0xff, 0xff, 0xff, 0xff, //0x00004187 movq         $-1, %r13
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x0000418e movq         $24(%rsp), %r8
	0x48, 0x8b, 0x4c, 0x24, 0x40, //0x00004193 movq         $64(%rsp), %rcx
	0x4c, 0x8b, 0x64, 0x24, 0x30, //0x00004198 movq         $48(%rsp), %r12
	0xe9, 0xe5, 0xf1, 0xff, 0xff, //0x0000419d jmp          LBB0_642
	//0x000041a2 LBB0_817
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x000041a2 movq         $-1, %r12
	0x45, 0x31, 0xed, //0x000041a9 xorl         %r13d, %r13d
	0x48, 0x83, 0xfb, 0x20, //0x000041ac cmpq         $32, %rbx
	0x0f, 0x83, 0x16, 0xfc, 0xff, 0xff, //0x000041b0 jae          LBB0_663
	//0x000041b6 LBB0_818
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x000041b6 movq         $24(%rsp), %r8
	0x4d, 0x85, 0xed, //0x000041bb testq        %r13, %r13
	0x0f, 0x84, 0x7a, 0xfd, 0xff, 0xff, //0x000041be je           LBB0_798
	//0x000041c4 LBB0_819
	0x48, 0x85, 0xdb, //0x000041c4 testq        %rbx, %rbx
	0x0f, 0x84, 0x57, 0x01, 0x00, 0x00, //0x000041c7 je           LBB0_830
	0x48, 0x8b, 0x44, 0x24, 0x38, //0x000041cd movq         $56(%rsp), %rax
	0x4c, 0x01, 0xd0, //0x000041d2 addq         %r10, %rax
	0x49, 0x83, 0xfc, 0xff, //0x000041d5 cmpq         $-1, %r12
	0x4c, 0x89, 0xe2, //0x000041d9 movq         %r12, %rdx
	0x4c, 0x0f, 0x44, 0xe0, //0x000041dc cmoveq       %rax, %r12
	0x48, 0x0f, 0x44, 0xd0, //0x000041e0 cmoveq       %rax, %rdx
	0x49, 0xff, 0xc2, //0x000041e4 incq         %r10
	0x48, 0xff, 0xcb, //0x000041e7 decq         %rbx
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x000041ea movq         $24(%rsp), %r8
	0x48, 0x85, 0xdb, //0x000041ef testq        %rbx, %rbx
	0x0f, 0x85, 0x52, 0xfd, 0xff, 0xff, //0x000041f2 jne          LBB0_799
	0xe9, 0x86, 0x0c, 0x00, 0x00, //0x000041f8 jmp          LBB0_813
	//0x000041fd LBB0_823
	0x49, 0xf7, 0xda, //0x000041fd negq         %r10
	0xe9, 0xca, 0xf9, 0xff, 0xff, //0x00004200 jmp          LBB0_773
	//0x00004205 LBB0_821
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x00004205 movq         $-1, %r12
	0x45, 0x31, 0xed, //0x0000420c xorl         %r13d, %r13d
	0x48, 0x83, 0xfb, 0x20, //0x0000420f cmpq         $32, %rbx
	0x0f, 0x83, 0xf2, 0xfd, 0xff, 0xff, //0x00004213 jae          LBB0_699
	0xe9, 0x56, 0xfe, 0xff, 0xff, //0x00004219 jmp          LBB0_704
	//0x0000421e LBB0_822
	0x48, 0xc7, 0x44, 0x24, 0x50, 0xff, 0xff, 0xff, 0xff, //0x0000421e movq         $-1, $80(%rsp)
	0x4d, 0x89, 0xf0, //0x00004227 movq         %r14, %r8
	0x49, 0x89, 0xfb, //0x0000422a movq         %rdi, %r11
	0x49, 0xc7, 0xc5, 0xff, 0xff, 0xff, 0xff, //0x0000422d movq         $-1, %r13
	0x48, 0xc7, 0x44, 0x24, 0x20, 0xff, 0xff, 0xff, 0xff, //0x00004234 movq         $-1, $32(%rsp)
	0x48, 0x8b, 0x4c, 0x24, 0x40, //0x0000423d movq         $64(%rsp), %rcx
	0x4c, 0x8b, 0x64, 0x24, 0x30, //0x00004242 movq         $48(%rsp), %r12
	0x48, 0x8d, 0x3d, 0x52, 0x13, 0x00, 0x00, //0x00004247 leaq         $4946(%rip), %rdi  /* LJTI0_4+0(%rip) */
	0xe9, 0x8d, 0xf7, 0xff, 0xff, //0x0000424e jmp          LBB0_735
	//0x00004253 LBB0_824
	0x4c, 0x89, 0xd0, //0x00004253 movq         %r10, %rax
	0x48, 0x2b, 0x44, 0x24, 0x30, //0x00004256 subq         $48(%rsp), %rax
	0x4c, 0x0f, 0xbc, 0xe1, //0x0000425b bsfq         %rcx, %r12
	0x49, 0x01, 0xc4, //0x0000425f addq         %rax, %r12
	0x48, 0x09, 0xfe, //0x00004262 orq          %rdi, %rsi
	0x48, 0x89, 0xc8, //0x00004265 movq         %rcx, %rax
	0x4c, 0x09, 0xe8, //0x00004268 orq          %r13, %rax
	0x0f, 0x84, 0xf7, 0xf9, 0xff, 0xff, //0x0000426b je           LBB0_605
	0xe9, 0x81, 0xfa, 0xff, 0xff, //0x00004271 jmp          LBB0_782
	//0x00004276 LBB0_825
	0x4c, 0x89, 0xd0, //0x00004276 movq         %r10, %rax
	0x48, 0x2b, 0x44, 0x24, 0x30, //0x00004279 subq         $48(%rsp), %rax
	0x4c, 0x0f, 0xbc, 0xe1, //0x0000427e bsfq         %rcx, %r12
	0x49, 0x01, 0xc4, //0x00004282 addq         %rax, %r12
	0x48, 0x09, 0xfe, //0x00004285 orq          %rdi, %rsi
	0x48, 0x89, 0xc8, //0x00004288 movq         %rcx, %rax
	0x4c, 0x09, 0xe8, //0x0000428b orq          %r13, %rax
	0x0f, 0x84, 0x95, 0xfb, 0xff, 0xff, //0x0000428e je           LBB0_666
	0xe9, 0x5b, 0xfc, 0xff, 0xff, //0x00004294 jmp          LBB0_796
	//0x00004299 LBB0_826
	0x4c, 0x89, 0xd0, //0x00004299 movq         %r10, %rax
	0x48, 0x2b, 0x44, 0x24, 0x30, //0x0000429c subq         $48(%rsp), %rax
	0x4c, 0x0f, 0xbc, 0xe1, //0x000042a1 bsfq         %rcx, %r12
	0x49, 0x01, 0xc4, //0x000042a5 addq         %rax, %r12
	0x48, 0x09, 0xfe, //0x000042a8 orq          %rdi, %rsi
	0x48, 0x89, 0xc8, //0x000042ab movq         %rcx, %rax
	0x4c, 0x09, 0xe8, //0x000042ae orq          %r13, %rax
	0x0f, 0x84, 0xac, 0xfd, 0xff, 0xff, //0x000042b1 je           LBB0_702
	//0x000042b7 LBB0_827
	0x44, 0x89, 0xe8, //0x000042b7 movl         %r13d, %eax
	0xf7, 0xd0, //0x000042ba notl         %eax
	0x21, 0xc8, //0x000042bc andl         %ecx, %eax
	0x8d, 0x14, 0x00, //0x000042be leal         (%rax,%rax), %edx
	0x44, 0x09, 0xea, //0x000042c1 orl          %r13d, %edx
	0x89, 0xd7, //0x000042c4 movl         %edx, %edi
	0xf7, 0xd7, //0x000042c6 notl         %edi
	0x21, 0xcf, //0x000042c8 andl         %ecx, %edi
	0x81, 0xe7, 0xaa, 0xaa, 0xaa, 0xaa, //0x000042ca andl         $-1431655766, %edi
	0x45, 0x31, 0xed, //0x000042d0 xorl         %r13d, %r13d
	0x01, 0xc7, //0x000042d3 addl         %eax, %edi
	0x41, 0x0f, 0x92, 0xc5, //0x000042d5 setb         %r13b
	0x01, 0xff, //0x000042d9 addl         %edi, %edi
	0x81, 0xf7, 0x55, 0x55, 0x55, 0x55, //0x000042db xorl         $1431655765, %edi
	0x21, 0xd7, //0x000042e1 andl         %edx, %edi
	0xf7, 0xd7, //0x000042e3 notl         %edi
	0x21, 0xfe, //0x000042e5 andl         %edi, %esi
	0x48, 0x85, 0xf6, //0x000042e7 testq        %rsi, %rsi
	0x0f, 0x85, 0x81, 0xf9, 0xff, 0xff, //0x000042ea jne          LBB0_606
	0xe9, 0x77, 0xfd, 0xff, 0xff, //0x000042f0 jmp          LBB0_703
	//0x000042f5 LBB0_828
	0x48, 0x85, 0xdb, //0x000042f5 testq        %rbx, %rbx
	0x0f, 0x84, 0x26, 0x00, 0x00, 0x00, //0x000042f8 je           LBB0_830
	0x48, 0x8b, 0x44, 0x24, 0x38, //0x000042fe movq         $56(%rsp), %rax
	0x4c, 0x01, 0xd0, //0x00004303 addq         %r10, %rax
	0x49, 0x83, 0xfc, 0xff, //0x00004306 cmpq         $-1, %r12
	0x4c, 0x89, 0xe2, //0x0000430a movq         %r12, %rdx
	0x4c, 0x0f, 0x44, 0xe0, //0x0000430d cmoveq       %rax, %r12
	0x48, 0x0f, 0x44, 0xd0, //0x00004311 cmoveq       %rax, %rdx
	0x49, 0xff, 0xc2, //0x00004315 incq         %r10
	0x48, 0xff, 0xcb, //0x00004318 decq         %rbx
	0x48, 0x85, 0xdb, //0x0000431b testq        %rbx, %rbx
	0x0f, 0x85, 0x65, 0xfd, 0xff, 0xff, //0x0000431e jne          LBB0_706
	//0x00004324 LBB0_830
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x00004324 movq         $24(%rsp), %r8
	0x48, 0x8b, 0x44, 0x24, 0x20, //0x00004329 movq         $32(%rsp), %rax
	0xe9, 0xe1, 0x01, 0x00, 0x00, //0x0000432e jmp          LBB0_862
	//0x00004333 LBB0_249
	0x3c, 0x7d, //0x00004333 cmpb         $125, %al
	0x0f, 0x84, 0x1c, 0x00, 0x00, 0x00, //0x00004335 je           LBB0_833
	0xe9, 0x41, 0x00, 0x00, 0x00, //0x0000433b jmp          LBB0_837
	//0x00004340 LBB0_831
	0x49, 0x89, 0x38, //0x00004340 movq         %rdi, (%r8)
	0x49, 0xc7, 0xc5, 0xff, 0xff, 0xff, 0xff, //0x00004343 movq         $-1, %r13
	0xe9, 0x3f, 0x00, 0x00, 0x00, //0x0000434a jmp          LBB0_839
	//0x0000434f LBB0_832
	0x3c, 0x5d, //0x0000434f cmpb         $93, %al
	0x0f, 0x85, 0x2a, 0x00, 0x00, 0x00, //0x00004351 jne          LBB0_837
	//0x00004357 LBB0_833
	0x49, 0xff, 0xca, //0x00004357 decq         %r10
	0x4d, 0x89, 0x10, //0x0000435a movq         %r10, (%r8)
	0x49, 0xc7, 0xc5, 0xdf, 0xff, 0xff, 0xff, //0x0000435d movq         $-33, %r13
	0xe9, 0x25, 0x00, 0x00, 0x00, //0x00004364 jmp          LBB0_839
	//0x00004369 LBB0_835
	0x49, 0xff, 0xca, //0x00004369 decq         %r10
	0x4d, 0x89, 0x10, //0x0000436c movq         %r10, (%r8)
	0x49, 0xc7, 0xc5, 0xde, 0xff, 0xff, 0xff, //0x0000436f movq         $-34, %r13
	0xe9, 0x13, 0x00, 0x00, 0x00, //0x00004376 jmp          LBB0_839
	//0x0000437b LBB0_836
	0x49, 0x89, 0x38, //0x0000437b movq         %rdi, (%r8)
	0x49, 0x89, 0xfa, //0x0000437e movq         %rdi, %r10
	//0x00004381 LBB0_837
	0x49, 0xff, 0xca, //0x00004381 decq         %r10
	0x4d, 0x89, 0x10, //0x00004384 movq         %r10, (%r8)
	//0x00004387 LBB0_838
	0x49, 0xc7, 0xc5, 0xfe, 0xff, 0xff, 0xff, //0x00004387 movq         $-2, %r13
	//0x0000438e LBB0_839
	0x4c, 0x89, 0xe8, //0x0000438e movq         %r13, %rax
	0x48, 0x8d, 0x65, 0xd8, //0x00004391 leaq         $-40(%rbp), %rsp
	0x5b, //0x00004395 popq         %rbx
	0x41, 0x5c, //0x00004396 popq         %r12
	0x41, 0x5d, //0x00004398 popq         %r13
	0x41, 0x5e, //0x0000439a popq         %r14
	0x41, 0x5f, //0x0000439c popq         %r15
	0x5d, //0x0000439e popq         %rbp
	0xc5, 0xf8, 0x77, //0x0000439f vzeroupper   
	0xc3, //0x000043a2 retq         
	//0x000043a3 LBB0_581
	0x49, 0xc7, 0xc5, 0xff, 0xff, 0xff, 0xff, //0x000043a3 movq         $-1, %r13
	0xe9, 0xdf, 0xff, 0xff, 0xff, //0x000043aa jmp          LBB0_839
	//0x000043af LBB0_840
	0x4c, 0x01, 0xdf, //0x000043af addq         %r11, %rdi
	//0x000043b2 LBB0_841
	0x4c, 0x29, 0xdf, //0x000043b2 subq         %r11, %rdi
	0x49, 0x89, 0xfc, //0x000043b5 movq         %rdi, %r12
	0x49, 0xc7, 0xc5, 0xff, 0xff, 0xff, 0xff, //0x000043b8 movq         $-1, %r13
	0x49, 0x39, 0xf4, //0x000043bf cmpq         %rsi, %r12
	0x0f, 0x83, 0xc6, 0xff, 0xff, 0xff, //0x000043c2 jae          LBB0_839
	0xe9, 0x19, 0x00, 0x00, 0x00, //0x000043c8 jmp          LBB0_843
	//0x000043cd LBB0_842
	0x4c, 0x89, 0xd8, //0x000043cd movq         %r11, %rax
	0x48, 0xf7, 0xd0, //0x000043d0 notq         %rax
	0x49, 0x01, 0xc4, //0x000043d3 addq         %rax, %r12
	0x49, 0xc7, 0xc5, 0xff, 0xff, 0xff, 0xff, //0x000043d6 movq         $-1, %r13
	0x49, 0x39, 0xf4, //0x000043dd cmpq         %rsi, %r12
	0x0f, 0x83, 0xa8, 0xff, 0xff, 0xff, //0x000043e0 jae          LBB0_839
	//0x000043e6 LBB0_843
	0x49, 0x8d, 0x7c, 0x24, 0x01, //0x000043e6 leaq         $1(%r12), %rdi
	0x49, 0x89, 0x38, //0x000043eb movq         %rdi, (%r8)
	0x43, 0x0f, 0xbe, 0x04, 0x23, //0x000043ee movsbl       (%r11,%r12), %eax
	0x83, 0xf8, 0x7b, //0x000043f3 cmpl         $123, %eax
	0x0f, 0x87, 0x43, 0x01, 0x00, 0x00, //0x000043f6 ja           LBB0_866
	0x49, 0xc7, 0xc5, 0xff, 0xff, 0xff, 0xff, //0x000043fc movq         $-1, %r13
	0x48, 0x8d, 0x15, 0x6e, 0x13, 0x00, 0x00, //0x00004403 leaq         $4974(%rip), %rdx  /* LJTI0_6+0(%rip) */
	0x48, 0x63, 0x04, 0x82, //0x0000440a movslq       (%rdx,%rax,4), %rax
	0x48, 0x01, 0xd0, //0x0000440e addq         %rdx, %rax
	0xff, 0xe0, //0x00004411 jmpq         *%rax
	//0x00004413 LBB0_845
	0x48, 0x8b, 0x44, 0x24, 0x28, //0x00004413 movq         $40(%rsp), %rax
	0x48, 0x8b, 0x50, 0x08, //0x00004418 movq         $8(%rax), %rdx
	0x48, 0x89, 0xd1, //0x0000441c movq         %rdx, %rcx
	0x48, 0x29, 0xf9, //0x0000441f subq         %rdi, %rcx
	0x4c, 0x01, 0xdf, //0x00004422 addq         %r11, %rdi
	0x48, 0x83, 0xf9, 0x10, //0x00004425 cmpq         $16, %rcx
	0x0f, 0x82, 0x6a, 0x00, 0x00, 0x00, //0x00004429 jb           LBB0_850
	0x4c, 0x29, 0xe2, //0x0000442f subq         %r12, %rdx
	0x48, 0x83, 0xc2, 0xef, //0x00004432 addq         $-17, %rdx
	0x48, 0x89, 0xd0, //0x00004436 movq         %rdx, %rax
	0x48, 0x83, 0xe0, 0xf0, //0x00004439 andq         $-16, %rax
	0x4c, 0x01, 0xe0, //0x0000443d addq         %r12, %rax
	0x49, 0x8d, 0x74, 0x03, 0x11, //0x00004440 leaq         $17(%r11,%rax), %rsi
	0x83, 0xe2, 0x0f, //0x00004445 andl         $15, %edx
	0xc5, 0xfa, 0x6f, 0x05, 0xb0, 0xbb, 0xff, 0xff, //0x00004448 vmovdqu      $-17488(%rip), %xmm0  /* LCPI0_0+0(%rip) */
	0xc5, 0xfa, 0x6f, 0x0d, 0xb8, 0xbb, 0xff, 0xff, //0x00004450 vmovdqu      $-17480(%rip), %xmm1  /* LCPI0_1+0(%rip) */
	0xc5, 0xfa, 0x6f, 0x15, 0xc0, 0xbb, 0xff, 0xff, //0x00004458 vmovdqu      $-17472(%rip), %xmm2  /* LCPI0_2+0(%rip) */
	//0x00004460 .p2align 4, 0x90
	//0x00004460 LBB0_847
	0xc5, 0xfa, 0x6f, 0x1f, //0x00004460 vmovdqu      (%rdi), %xmm3
	0xc5, 0xe1, 0x74, 0xe0, //0x00004464 vpcmpeqb     %xmm0, %xmm3, %xmm4
	0xc5, 0xe1, 0xeb, 0xd9, //0x00004468 vpor         %xmm1, %xmm3, %xmm3
	0xc5, 0xe1, 0x74, 0xda, //0x0000446c vpcmpeqb     %xmm2, %xmm3, %xmm3
	0xc5, 0xe1, 0xeb, 0xdc, //0x00004470 vpor         %xmm4, %xmm3, %xmm3
	0xc5, 0xf9, 0xd7, 0xc3, //0x00004474 vpmovmskb    %xmm3, %eax
	0x66, 0x85, 0xc0, //0x00004478 testw        %ax, %ax
	0x0f, 0x85, 0x65, 0x00, 0x00, 0x00, //0x0000447b jne          LBB0_858
	0x48, 0x83, 0xc7, 0x10, //0x00004481 addq         $16, %rdi
	0x48, 0x83, 0xc1, 0xf0, //0x00004485 addq         $-16, %rcx
	0x48, 0x83, 0xf9, 0x0f, //0x00004489 cmpq         $15, %rcx
	0x0f, 0x87, 0xcd, 0xff, 0xff, 0xff, //0x0000448d ja           LBB0_847
	0x48, 0x89, 0xd1, //0x00004493 movq         %rdx, %rcx
	0x48, 0x89, 0xf7, //0x00004496 movq         %rsi, %rdi
	//0x00004499 LBB0_850
	0x48, 0x85, 0xc9, //0x00004499 testq        %rcx, %rcx
	0x0f, 0x84, 0x31, 0x00, 0x00, 0x00, //0x0000449c je           LBB0_857
	0x48, 0x8d, 0x04, 0x0f, //0x000044a2 leaq         (%rdi,%rcx), %rax
	//0x000044a6 LBB0_852
	0x0f, 0xb6, 0x17, //0x000044a6 movzbl       (%rdi), %edx
	0x80, 0xfa, 0x2c, //0x000044a9 cmpb         $44, %dl
	0x0f, 0x84, 0xa9, 0x08, 0x00, 0x00, //0x000044ac je           LBB0_938
	0x80, 0xfa, 0x7d, //0x000044b2 cmpb         $125, %dl
	0x0f, 0x84, 0xa0, 0x08, 0x00, 0x00, //0x000044b5 je           LBB0_938
	0x80, 0xfa, 0x5d, //0x000044bb cmpb         $93, %dl
	0x0f, 0x84, 0x97, 0x08, 0x00, 0x00, //0x000044be je           LBB0_938
	0x48, 0xff, 0xc7, //0x000044c4 incq         %rdi
	0x48, 0xff, 0xc9, //0x000044c7 decq         %rcx
	0x0f, 0x85, 0xd6, 0xff, 0xff, 0xff, //0x000044ca jne          LBB0_852
	0x48, 0x89, 0xc7, //0x000044d0 movq         %rax, %rdi
	//0x000044d3 LBB0_857
	0x4c, 0x29, 0xdf, //0x000044d3 subq         %r11, %rdi
	0x48, 0x8b, 0x44, 0x24, 0x18, //0x000044d6 movq         $24(%rsp), %rax
	0x48, 0x89, 0x38, //0x000044db movq         %rdi, (%rax)
	0x4d, 0x89, 0xe5, //0x000044de movq         %r12, %r13
	0xe9, 0xa8, 0xfe, 0xff, 0xff, //0x000044e1 jmp          LBB0_839
	//0x000044e6 LBB0_858
	0x0f, 0xb7, 0xc0, //0x000044e6 movzwl       %ax, %eax
	0x48, 0x0f, 0xbc, 0xc0, //0x000044e9 bsfq         %rax, %rax
	0x4c, 0x29, 0xdf, //0x000044ed subq         %r11, %rdi
	0x48, 0x01, 0xc7, //0x000044f0 addq         %rax, %rdi
	//0x000044f3 LBB0_859
	0x49, 0x89, 0x38, //0x000044f3 movq         %rdi, (%r8)
	0x4d, 0x89, 0xe5, //0x000044f6 movq         %r12, %r13
	0xe9, 0x90, 0xfe, 0xff, 0xff, //0x000044f9 jmp          LBB0_839
	//0x000044fe LBB0_942
	0x49, 0xc7, 0xc5, 0xf9, 0xff, 0xff, 0xff, //0x000044fe movq         $-7, %r13
	0xe9, 0x84, 0xfe, 0xff, 0xff, //0x00004505 jmp          LBB0_839
	//0x0000450a LBB0_861
	0x49, 0x83, 0xfa, 0xff, //0x0000450a cmpq         $-1, %r10
	0x0f, 0x85, 0x0a, 0x00, 0x00, 0x00, //0x0000450e jne          LBB0_863
	//0x00004514 LBB0_862
	0x49, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x00004514 movq         $-1, %r10
	0x49, 0x89, 0xc4, //0x0000451b movq         %rax, %r12
	//0x0000451e LBB0_863
	0x4d, 0x89, 0x20, //0x0000451e movq         %r12, (%r8)
	0x4d, 0x89, 0xd5, //0x00004521 movq         %r10, %r13
	0xe9, 0x65, 0xfe, 0xff, 0xff, //0x00004524 jmp          LBB0_839
	//0x00004529 LBB0_864
	0x49, 0x8d, 0x44, 0x24, 0x04, //0x00004529 leaq         $4(%r12), %rax
	0xe9, 0x5e, 0x04, 0x00, 0x00, //0x0000452e jmp          LBB0_903
	//0x00004533 LBB0_865
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00004533 movq         $-1, %rax
	0xe9, 0xe2, 0x07, 0x00, 0x00, //0x0000453a jmp          LBB0_931
	//0x0000453f LBB0_866
	0x4d, 0x89, 0x20, //0x0000453f movq         %r12, (%r8)
	0xe9, 0x40, 0xfe, 0xff, 0xff, //0x00004542 jmp          LBB0_838
	//0x00004547 LBB0_867
	0x48, 0x8b, 0x44, 0x24, 0x28, //0x00004547 movq         $40(%rsp), %rax
	0x4c, 0x8b, 0x40, 0x08, //0x0000454c movq         $8(%rax), %r8
	0x4d, 0x89, 0xc6, //0x00004550 movq         %r8, %r14
	0x49, 0x29, 0xfe, //0x00004553 subq         %rdi, %r14
	0x49, 0x83, 0xfe, 0x20, //0x00004556 cmpq         $32, %r14
	0x0f, 0x8c, 0xf6, 0x09, 0x00, 0x00, //0x0000455a jl           LBB0_961
	0x41, 0xb9, 0xff, 0xff, 0xff, 0xff, //0x00004560 movl         $4294967295, %r9d
	0x4f, 0x8d, 0x14, 0x23, //0x00004566 leaq         (%r11,%r12), %r10
	0x4d, 0x29, 0xe0, //0x0000456a subq         %r12, %r8
	0xbe, 0x1f, 0x00, 0x00, 0x00, //0x0000456d movl         $31, %esi
	0x45, 0x31, 0xf6, //0x00004572 xorl         %r14d, %r14d
	0xc5, 0xfa, 0x6f, 0x05, 0xb3, 0xba, 0xff, 0xff, //0x00004575 vmovdqu      $-17741(%rip), %xmm0  /* LCPI0_3+0(%rip) */
	0xc5, 0xfa, 0x6f, 0x0d, 0xbb, 0xba, 0xff, 0xff, //0x0000457d vmovdqu      $-17733(%rip), %xmm1  /* LCPI0_4+0(%rip) */
	0x45, 0x31, 0xff, //0x00004585 xorl         %r15d, %r15d
	0xe9, 0x22, 0x00, 0x00, 0x00, //0x00004588 jmp          LBB0_869
	//0x0000458d LBB0_872
	0x45, 0x31, 0xff, //0x0000458d xorl         %r15d, %r15d
	0x85, 0xff, //0x00004590 testl        %edi, %edi
	0x0f, 0x85, 0x9e, 0x00, 0x00, 0x00, //0x00004592 jne          LBB0_871
	//0x00004598 LBB0_873
	0x49, 0x83, 0xc6, 0x20, //0x00004598 addq         $32, %r14
	0x49, 0x8d, 0x44, 0x30, 0xe0, //0x0000459c leaq         $-32(%r8,%rsi), %rax
	0x48, 0x83, 0xc6, 0xe0, //0x000045a1 addq         $-32, %rsi
	0x48, 0x83, 0xf8, 0x3f, //0x000045a5 cmpq         $63, %rax
	0x0f, 0x8e, 0xf1, 0x08, 0x00, 0x00, //0x000045a9 jle          LBB0_874
	//0x000045af LBB0_869
	0xc4, 0x81, 0x7a, 0x6f, 0x54, 0x32, 0x01, //0x000045af vmovdqu      $1(%r10,%r14), %xmm2
	0xc4, 0x81, 0x7a, 0x6f, 0x5c, 0x32, 0x11, //0x000045b6 vmovdqu      $17(%r10,%r14), %xmm3
	0xc5, 0xe9, 0x74, 0xe0, //0x000045bd vpcmpeqb     %xmm0, %xmm2, %xmm4
	0xc5, 0xf9, 0xd7, 0xc4, //0x000045c1 vpmovmskb    %xmm4, %eax
	0xc5, 0xe1, 0x74, 0xe0, //0x000045c5 vpcmpeqb     %xmm0, %xmm3, %xmm4
	0xc5, 0xf9, 0xd7, 0xfc, //0x000045c9 vpmovmskb    %xmm4, %edi
	0x48, 0xc1, 0xe7, 0x10, //0x000045cd shlq         $16, %rdi
	0x48, 0x09, 0xc7, //0x000045d1 orq          %rax, %rdi
	0xc5, 0xe9, 0x74, 0xd1, //0x000045d4 vpcmpeqb     %xmm1, %xmm2, %xmm2
	0xc5, 0xf9, 0xd7, 0xda, //0x000045d8 vpmovmskb    %xmm2, %ebx
	0xc5, 0xe1, 0x74, 0xd1, //0x000045dc vpcmpeqb     %xmm1, %xmm3, %xmm2
	0xc5, 0xf9, 0xd7, 0xc2, //0x000045e0 vpmovmskb    %xmm2, %eax
	0x48, 0xc1, 0xe0, 0x10, //0x000045e4 shlq         $16, %rax
	0x48, 0x09, 0xd8, //0x000045e8 orq          %rbx, %rax
	0x48, 0x89, 0xc3, //0x000045eb movq         %rax, %rbx
	0x4c, 0x09, 0xfb, //0x000045ee orq          %r15, %rbx
	0x0f, 0x84, 0x96, 0xff, 0xff, 0xff, //0x000045f1 je           LBB0_872
	0x44, 0x89, 0xfb, //0x000045f7 movl         %r15d, %ebx
	0x44, 0x31, 0xcb, //0x000045fa xorl         %r9d, %ebx
	0x21, 0xd8, //0x000045fd andl         %ebx, %eax
	0x8d, 0x1c, 0x00, //0x000045ff leal         (%rax,%rax), %ebx
	0x44, 0x09, 0xfb, //0x00004602 orl          %r15d, %ebx
	0x41, 0x8d, 0x91, 0xab, 0xaa, 0xaa, 0xaa, //0x00004605 leal         $-1431655765(%r9), %edx
	0x31, 0xda, //0x0000460c xorl         %ebx, %edx
	0x21, 0xc2, //0x0000460e andl         %eax, %edx
	0x81, 0xe2, 0xaa, 0xaa, 0xaa, 0xaa, //0x00004610 andl         $-1431655766, %edx
	0x45, 0x31, 0xff, //0x00004616 xorl         %r15d, %r15d
	0x01, 0xc2, //0x00004619 addl         %eax, %edx
	0x41, 0x0f, 0x92, 0xc7, //0x0000461b setb         %r15b
	0x01, 0xd2, //0x0000461f addl         %edx, %edx
	0x81, 0xf2, 0x55, 0x55, 0x55, 0x55, //0x00004621 xorl         $1431655765, %edx
	0x21, 0xda, //0x00004627 andl         %ebx, %edx
	0x44, 0x31, 0xca, //0x00004629 xorl         %r9d, %edx
	0x21, 0xd7, //0x0000462c andl         %edx, %edi
	0x85, 0xff, //0x0000462e testl        %edi, %edi
	0x0f, 0x84, 0x62, 0xff, 0xff, 0xff, //0x00004630 je           LBB0_873
	//0x00004636 LBB0_871
	0x48, 0x0f, 0xbc, 0xc7, //0x00004636 bsfq         %rdi, %rax
	0x49, 0x01, 0xc2, //0x0000463a addq         %rax, %r10
	0x4d, 0x01, 0xf2, //0x0000463d addq         %r14, %r10
	0x4d, 0x29, 0xda, //0x00004640 subq         %r11, %r10
	0x49, 0x83, 0xc2, 0x02, //0x00004643 addq         $2, %r10
	0x48, 0x8b, 0x44, 0x24, 0x18, //0x00004647 movq         $24(%rsp), %rax
	0x4c, 0x89, 0x10, //0x0000464c movq         %r10, (%rax)
	0x4d, 0x89, 0xe5, //0x0000464f movq         %r12, %r13
	0xe9, 0x37, 0xfd, 0xff, 0xff, //0x00004652 jmp          LBB0_839
	//0x00004657 LBB0_878
	0x48, 0x8b, 0x44, 0x24, 0x28, //0x00004657 movq         $40(%rsp), %rax
	0x4c, 0x8b, 0x70, 0x08, //0x0000465c movq         $8(%rax), %r14
	0x49, 0x29, 0xfe, //0x00004660 subq         %rdi, %r14
	0x49, 0x01, 0xfb, //0x00004663 addq         %rdi, %r11
	0x45, 0x31, 0xc9, //0x00004666 xorl         %r9d, %r9d
	0xc5, 0x7a, 0x6f, 0x15, 0xbf, 0xb9, 0xff, 0xff, //0x00004669 vmovdqu      $-17985(%rip), %xmm10  /* LCPI0_3+0(%rip) */
	0xc5, 0xfa, 0x6f, 0x0d, 0xc7, 0xb9, 0xff, 0xff, //0x00004671 vmovdqu      $-17977(%rip), %xmm1  /* LCPI0_4+0(%rip) */
	0xc4, 0x41, 0x31, 0x76, 0xc9, //0x00004679 vpcmpeqd     %xmm9, %xmm9, %xmm9
	0xc5, 0xfa, 0x6f, 0x1d, 0xda, 0xb9, 0xff, 0xff, //0x0000467e vmovdqu      $-17958(%rip), %xmm3  /* LCPI0_6+0(%rip) */
	0xc5, 0xfa, 0x6f, 0x25, 0xe2, 0xb9, 0xff, 0xff, //0x00004686 vmovdqu      $-17950(%rip), %xmm4  /* LCPI0_7+0(%rip) */
	0xc4, 0x41, 0x39, 0xef, 0xc0, //0x0000468e vpxor        %xmm8, %xmm8, %xmm8
	0x31, 0xc9, //0x00004693 xorl         %ecx, %ecx
	0x45, 0x31, 0xff, //0x00004695 xorl         %r15d, %r15d
	0x31, 0xdb, //0x00004698 xorl         %ebx, %ebx
	0xe9, 0x17, 0x00, 0x00, 0x00, //0x0000469a jmp          LBB0_880
	//0x0000469f LBB0_879
	0x49, 0xc1, 0xf8, 0x3f, //0x0000469f sarq         $63, %r8
	0xf3, 0x48, 0x0f, 0xb8, 0xc7, //0x000046a3 popcntq      %rdi, %rax
	0x49, 0x01, 0xc7, //0x000046a8 addq         %rax, %r15
	0x49, 0x83, 0xc3, 0x40, //0x000046ab addq         $64, %r11
	0x49, 0x83, 0xc6, 0xc0, //0x000046af addq         $-64, %r14
	0x4d, 0x89, 0xc1, //0x000046b3 movq         %r8, %r9
	//0x000046b6 LBB0_880
	0x49, 0x83, 0xfe, 0x40, //0x000046b6 cmpq         $64, %r14
	0x0f, 0x8c, 0x9d, 0x01, 0x00, 0x00, //0x000046ba jl           LBB0_887
	//0x000046c0 LBB0_881
	0xc4, 0xc1, 0x7a, 0x6f, 0x13, //0x000046c0 vmovdqu      (%r11), %xmm2
	0xc4, 0xc1, 0x7a, 0x6f, 0x6b, 0x10, //0x000046c5 vmovdqu      $16(%r11), %xmm5
	0xc4, 0xc1, 0x7a, 0x6f, 0x7b, 0x20, //0x000046cb vmovdqu      $32(%r11), %xmm7
	0xc4, 0xc1, 0x7a, 0x6f, 0x73, 0x30, //0x000046d1 vmovdqu      $48(%r11), %xmm6
	0xc5, 0xa9, 0x74, 0xc2, //0x000046d7 vpcmpeqb     %xmm2, %xmm10, %xmm0
	0xc5, 0x79, 0xd7, 0xc0, //0x000046db vpmovmskb    %xmm0, %r8d
	0xc5, 0xa9, 0x74, 0xc5, //0x000046df vpcmpeqb     %xmm5, %xmm10, %xmm0
	0xc5, 0xf9, 0xd7, 0xd0, //0x000046e3 vpmovmskb    %xmm0, %edx
	0xc5, 0xa9, 0x74, 0xc7, //0x000046e7 vpcmpeqb     %xmm7, %xmm10, %xmm0
	0xc5, 0xf9, 0xd7, 0xf8, //0x000046eb vpmovmskb    %xmm0, %edi
	0xc5, 0xa9, 0x74, 0xc6, //0x000046ef vpcmpeqb     %xmm6, %xmm10, %xmm0
	0xc5, 0x79, 0xd7, 0xd0, //0x000046f3 vpmovmskb    %xmm0, %r10d
	0x49, 0xc1, 0xe2, 0x30, //0x000046f7 shlq         $48, %r10
	0x48, 0xc1, 0xe7, 0x20, //0x000046fb shlq         $32, %rdi
	0x48, 0xc1, 0xe2, 0x10, //0x000046ff shlq         $16, %rdx
	0x49, 0x09, 0xd0, //0x00004703 orq          %rdx, %r8
	0x49, 0x09, 0xf8, //0x00004706 orq          %rdi, %r8
	0x4d, 0x09, 0xd0, //0x00004709 orq          %r10, %r8
	0xc5, 0xe9, 0x74, 0xc1, //0x0000470c vpcmpeqb     %xmm1, %xmm2, %xmm0
	0xc5, 0xf9, 0xd7, 0xf8, //0x00004710 vpmovmskb    %xmm0, %edi
	0xc5, 0xd1, 0x74, 0xc1, //0x00004714 vpcmpeqb     %xmm1, %xmm5, %xmm0
	0xc5, 0xf9, 0xd7, 0xd0, //0x00004718 vpmovmskb    %xmm0, %edx
	0xc5, 0xc1, 0x74, 0xc1, //0x0000471c vpcmpeqb     %xmm1, %xmm7, %xmm0
	0xc5, 0xf9, 0xd7, 0xc0, //0x00004720 vpmovmskb    %xmm0, %eax
	0xc5, 0xc9, 0x74, 0xc1, //0x00004724 vpcmpeqb     %xmm1, %xmm6, %xmm0
	0xc5, 0xf9, 0xd7, 0xf0, //0x00004728 vpmovmskb    %xmm0, %esi
	0x48, 0xc1, 0xe6, 0x30, //0x0000472c shlq         $48, %rsi
	0x48, 0xc1, 0xe0, 0x20, //0x00004730 shlq         $32, %rax
	0x48, 0xc1, 0xe2, 0x10, //0x00004734 shlq         $16, %rdx
	0x48, 0x09, 0xd7, //0x00004738 orq          %rdx, %rdi
	0x48, 0x09, 0xc7, //0x0000473b orq          %rax, %rdi
	0x48, 0x09, 0xf7, //0x0000473e orq          %rsi, %rdi
	0x48, 0x89, 0xf8, //0x00004741 movq         %rdi, %rax
	0x48, 0x09, 0xc8, //0x00004744 orq          %rcx, %rax
	0x0f, 0x84, 0x49, 0x00, 0x00, 0x00, //0x00004747 je           LBB0_883
	0x48, 0x89, 0xc8, //0x0000474d movq         %rcx, %rax
	0x48, 0xf7, 0xd0, //0x00004750 notq         %rax
	0x48, 0x21, 0xf8, //0x00004753 andq         %rdi, %rax
	0x48, 0x8d, 0x14, 0x00, //0x00004756 leaq         (%rax,%rax), %rdx
	0x48, 0x09, 0xca, //0x0000475a orq          %rcx, %rdx
	0x48, 0x89, 0xd6, //0x0000475d movq         %rdx, %rsi
	0x48, 0xb9, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00004760 movabsq      $-6148914691236517206, %rcx
	0x48, 0x31, 0xce, //0x0000476a xorq         %rcx, %rsi
	0x48, 0x21, 0xcf, //0x0000476d andq         %rcx, %rdi
	0x48, 0x21, 0xf7, //0x00004770 andq         %rsi, %rdi
	0x31, 0xc9, //0x00004773 xorl         %ecx, %ecx
	0x48, 0x01, 0xc7, //0x00004775 addq         %rax, %rdi
	0x0f, 0x92, 0xc1, //0x00004778 setb         %cl
	0x48, 0x01, 0xff, //0x0000477b addq         %rdi, %rdi
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x0000477e movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc7, //0x00004788 xorq         %rax, %rdi
	0x48, 0x21, 0xd7, //0x0000478b andq         %rdx, %rdi
	0x48, 0xf7, 0xd7, //0x0000478e notq         %rdi
	0xe9, 0x09, 0x00, 0x00, 0x00, //0x00004791 jmp          LBB0_884
	//0x00004796 LBB0_883
	0x48, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x00004796 movq         $-1, %rdi
	0x31, 0xc9, //0x0000479d xorl         %ecx, %ecx
	//0x0000479f LBB0_884
	0x4c, 0x21, 0xc7, //0x0000479f andq         %r8, %rdi
	0xc4, 0xe1, 0xf9, 0x6e, 0xc7, //0x000047a2 vmovq        %rdi, %xmm0
	0xc4, 0xc3, 0x79, 0x44, 0xc1, 0x00, //0x000047a7 vpclmulqdq   $0, %xmm9, %xmm0, %xmm0
	0xc4, 0xc1, 0xf9, 0x7e, 0xc0, //0x000047ad vmovq        %xmm0, %r8
	0x4d, 0x31, 0xc8, //0x000047b2 xorq         %r9, %r8
	0xc5, 0xe9, 0x74, 0xc3, //0x000047b5 vpcmpeqb     %xmm3, %xmm2, %xmm0
	0xc5, 0xf9, 0xd7, 0xf8, //0x000047b9 vpmovmskb    %xmm0, %edi
	0xc5, 0xd1, 0x74, 0xc3, //0x000047bd vpcmpeqb     %xmm3, %xmm5, %xmm0
	0xc5, 0xf9, 0xd7, 0xc0, //0x000047c1 vpmovmskb    %xmm0, %eax
	0xc5, 0xc1, 0x74, 0xc3, //0x000047c5 vpcmpeqb     %xmm3, %xmm7, %xmm0
	0xc5, 0xf9, 0xd7, 0xd0, //0x000047c9 vpmovmskb    %xmm0, %edx
	0xc5, 0xc9, 0x74, 0xc3, //0x000047cd vpcmpeqb     %xmm3, %xmm6, %xmm0
	0xc5, 0xf9, 0xd7, 0xf0, //0x000047d1 vpmovmskb    %xmm0, %esi
	0x48, 0xc1, 0xe6, 0x30, //0x000047d5 shlq         $48, %rsi
	0x48, 0xc1, 0xe2, 0x20, //0x000047d9 shlq         $32, %rdx
	0x48, 0xc1, 0xe0, 0x10, //0x000047dd shlq         $16, %rax
	0x48, 0x09, 0xc7, //0x000047e1 orq          %rax, %rdi
	0x48, 0x09, 0xd7, //0x000047e4 orq          %rdx, %rdi
	0x48, 0x09, 0xf7, //0x000047e7 orq          %rsi, %rdi
	0x4d, 0x89, 0xc1, //0x000047ea movq         %r8, %r9
	0x49, 0xf7, 0xd1, //0x000047ed notq         %r9
	0x4c, 0x21, 0xcf, //0x000047f0 andq         %r9, %rdi
	0xc5, 0xe9, 0x74, 0xc4, //0x000047f3 vpcmpeqb     %xmm4, %xmm2, %xmm0
	0xc5, 0xf9, 0xd7, 0xd0, //0x000047f7 vpmovmskb    %xmm0, %edx
	0xc5, 0xd1, 0x74, 0xc4, //0x000047fb vpcmpeqb     %xmm4, %xmm5, %xmm0
	0xc5, 0xf9, 0xd7, 0xf0, //0x000047ff vpmovmskb    %xmm0, %esi
	0xc5, 0xc1, 0x74, 0xc4, //0x00004803 vpcmpeqb     %xmm4, %xmm7, %xmm0
	0xc5, 0xf9, 0xd7, 0xc0, //0x00004807 vpmovmskb    %xmm0, %eax
	0xc5, 0xc9, 0x74, 0xc4, //0x0000480b vpcmpeqb     %xmm4, %xmm6, %xmm0
	0xc5, 0x79, 0xd7, 0xd0, //0x0000480f vpmovmskb    %xmm0, %r10d
	0x49, 0xc1, 0xe2, 0x30, //0x00004813 shlq         $48, %r10
	0x48, 0xc1, 0xe0, 0x20, //0x00004817 shlq         $32, %rax
	0x48, 0xc1, 0xe6, 0x10, //0x0000481b shlq         $16, %rsi
	0x48, 0x09, 0xf2, //0x0000481f orq          %rsi, %rdx
	0x48, 0x09, 0xc2, //0x00004822 orq          %rax, %rdx
	0x4c, 0x09, 0xd2, //0x00004825 orq          %r10, %rdx
	0x4c, 0x21, 0xca, //0x00004828 andq         %r9, %rdx
	0x0f, 0x84, 0x6e, 0xfe, 0xff, 0xff, //0x0000482b je           LBB0_879
	//0x00004831 LBB0_885
	0x48, 0x8d, 0x42, 0xff, //0x00004831 leaq         $-1(%rdx), %rax
	0x48, 0x89, 0xc6, //0x00004835 movq         %rax, %rsi
	0x48, 0x21, 0xfe, //0x00004838 andq         %rdi, %rsi
	0xf3, 0x48, 0x0f, 0xb8, 0xf6, //0x0000483b popcntq      %rsi, %rsi
	0x4c, 0x01, 0xfe, //0x00004840 addq         %r15, %rsi
	0x48, 0x39, 0xde, //0x00004843 cmpq         %rbx, %rsi
	0x0f, 0x86, 0x94, 0x04, 0x00, 0x00, //0x00004846 jbe          LBB0_929
	0x48, 0xff, 0xc3, //0x0000484c incq         %rbx
	0x48, 0x21, 0xc2, //0x0000484f andq         %rax, %rdx
	0x0f, 0x85, 0xd9, 0xff, 0xff, 0xff, //0x00004852 jne          LBB0_885
	0xe9, 0x42, 0xfe, 0xff, 0xff, //0x00004858 jmp          LBB0_879
	//0x0000485d LBB0_887
	0x4d, 0x85, 0xf6, //0x0000485d testq        %r14, %r14
	0x0f, 0x8e, 0xf8, 0x06, 0x00, 0x00, //0x00004860 jle          LBB0_962
	0xc5, 0x7e, 0x7f, 0x84, 0x24, 0x80, 0x00, 0x00, 0x00, //0x00004866 vmovdqu      %ymm8, $128(%rsp)
	0xc5, 0x7e, 0x7f, 0x44, 0x24, 0x60, //0x0000486f vmovdqu      %ymm8, $96(%rsp)
	0x44, 0x89, 0xd8, //0x00004875 movl         %r11d, %eax
	0x25, 0xff, 0x0f, 0x00, 0x00, //0x00004878 andl         $4095, %eax
	0x3d, 0xc1, 0x0f, 0x00, 0x00, //0x0000487d cmpl         $4033, %eax
	0x0f, 0x82, 0x38, 0xfe, 0xff, 0xff, //0x00004882 jb           LBB0_881
	0x49, 0x83, 0xfe, 0x20, //0x00004888 cmpq         $32, %r14
	0x0f, 0x82, 0x2c, 0x00, 0x00, 0x00, //0x0000488c jb           LBB0_891
	0xc4, 0xc1, 0x78, 0x10, 0x03, //0x00004892 vmovups      (%r11), %xmm0
	0xc5, 0xf8, 0x11, 0x44, 0x24, 0x60, //0x00004897 vmovups      %xmm0, $96(%rsp)
	0xc4, 0xc1, 0x7a, 0x6f, 0x43, 0x10, //0x0000489d vmovdqu      $16(%r11), %xmm0
	0xc5, 0xfa, 0x7f, 0x44, 0x24, 0x70, //0x000048a3 vmovdqu      %xmm0, $112(%rsp)
	0x49, 0x83, 0xc3, 0x20, //0x000048a9 addq         $32, %r11
	0x49, 0x8d, 0x7e, 0xe0, //0x000048ad leaq         $-32(%r14), %rdi
	0x48, 0x8d, 0xb4, 0x24, 0x80, 0x00, 0x00, 0x00, //0x000048b1 leaq         $128(%rsp), %rsi
	0xe9, 0x08, 0x00, 0x00, 0x00, //0x000048b9 jmp          LBB0_892
	//0x000048be LBB0_891
	0x48, 0x8d, 0x74, 0x24, 0x60, //0x000048be leaq         $96(%rsp), %rsi
	0x4c, 0x89, 0xf7, //0x000048c3 movq         %r14, %rdi
	//0x000048c6 LBB0_892
	0x48, 0x83, 0xff, 0x10, //0x000048c6 cmpq         $16, %rdi
	0x0f, 0x82, 0x5a, 0x00, 0x00, 0x00, //0x000048ca jb           LBB0_893
	0xc4, 0xc1, 0x7a, 0x6f, 0x03, //0x000048d0 vmovdqu      (%r11), %xmm0
	0xc5, 0xfa, 0x7f, 0x06, //0x000048d5 vmovdqu      %xmm0, (%rsi)
	0x49, 0x83, 0xc3, 0x10, //0x000048d9 addq         $16, %r11
	0x48, 0x83, 0xc6, 0x10, //0x000048dd addq         $16, %rsi
	0x48, 0x83, 0xc7, 0xf0, //0x000048e1 addq         $-16, %rdi
	0x48, 0x83, 0xff, 0x08, //0x000048e5 cmpq         $8, %rdi
	0x0f, 0x83, 0x45, 0x00, 0x00, 0x00, //0x000048e9 jae          LBB0_898
	//0x000048ef LBB0_894
	0x48, 0x83, 0xff, 0x04, //0x000048ef cmpq         $4, %rdi
	0x0f, 0x8c, 0x57, 0x00, 0x00, 0x00, //0x000048f3 jl           LBB0_895
	//0x000048f9 LBB0_899
	0x41, 0x8b, 0x03, //0x000048f9 movl         (%r11), %eax
	0x89, 0x06, //0x000048fc movl         %eax, (%rsi)
	0x49, 0x83, 0xc3, 0x04, //0x000048fe addq         $4, %r11
	0x48, 0x83, 0xc6, 0x04, //0x00004902 addq         $4, %rsi
	0x48, 0x83, 0xc7, 0xfc, //0x00004906 addq         $-4, %rdi
	0x48, 0x83, 0xff, 0x02, //0x0000490a cmpq         $2, %rdi
	0x0f, 0x83, 0x46, 0x00, 0x00, 0x00, //0x0000490e jae          LBB0_900
	//0x00004914 LBB0_896
	0x4c, 0x89, 0xda, //0x00004914 movq         %r11, %rdx
	0x4c, 0x8d, 0x5c, 0x24, 0x60, //0x00004917 leaq         $96(%rsp), %r11
	0x48, 0x85, 0xff, //0x0000491c testq        %rdi, %rdi
	0x0f, 0x85, 0x59, 0x00, 0x00, 0x00, //0x0000491f jne          LBB0_901
	0xe9, 0x96, 0xfd, 0xff, 0xff, //0x00004925 jmp          LBB0_881
	//0x0000492a LBB0_893
	0x48, 0x83, 0xff, 0x08, //0x0000492a cmpq         $8, %rdi
	0x0f, 0x82, 0xbb, 0xff, 0xff, 0xff, //0x0000492e jb           LBB0_894
	//0x00004934 LBB0_898
	0x49, 0x8b, 0x03, //0x00004934 movq         (%r11), %rax
	0x48, 0x89, 0x06, //0x00004937 movq         %rax, (%rsi)
	0x49, 0x83, 0xc3, 0x08, //0x0000493a addq         $8, %r11
	0x48, 0x83, 0xc6, 0x08, //0x0000493e addq         $8, %rsi
	0x48, 0x83, 0xc7, 0xf8, //0x00004942 addq         $-8, %rdi
	0x48, 0x83, 0xff, 0x04, //0x00004946 cmpq         $4, %rdi
	0x0f, 0x8d, 0xa9, 0xff, 0xff, 0xff, //0x0000494a jge          LBB0_899
	//0x00004950 LBB0_895
	0x48, 0x83, 0xff, 0x02, //0x00004950 cmpq         $2, %rdi
	0x0f, 0x82, 0xba, 0xff, 0xff, 0xff, //0x00004954 jb           LBB0_896
	//0x0000495a LBB0_900
	0x41, 0x0f, 0xb7, 0x03, //0x0000495a movzwl       (%r11), %eax
	0x66, 0x89, 0x06, //0x0000495e movw         %ax, (%rsi)
	0x49, 0x83, 0xc3, 0x02, //0x00004961 addq         $2, %r11
	0x48, 0x83, 0xc6, 0x02, //0x00004965 addq         $2, %rsi
	0x48, 0x83, 0xc7, 0xfe, //0x00004969 addq         $-2, %rdi
	0x4c, 0x89, 0xda, //0x0000496d movq         %r11, %rdx
	0x4c, 0x8d, 0x5c, 0x24, 0x60, //0x00004970 leaq         $96(%rsp), %r11
	0x48, 0x85, 0xff, //0x00004975 testq        %rdi, %rdi
	0x0f, 0x84, 0x42, 0xfd, 0xff, 0xff, //0x00004978 je           LBB0_881
	//0x0000497e LBB0_901
	0x8a, 0x02, //0x0000497e movb         (%rdx), %al
	0x88, 0x06, //0x00004980 movb         %al, (%rsi)
	0x4c, 0x8d, 0x5c, 0x24, 0x60, //0x00004982 leaq         $96(%rsp), %r11
	0xe9, 0x34, 0xfd, 0xff, 0xff, //0x00004987 jmp          LBB0_881
	//0x0000498c LBB0_902
	0x49, 0x8d, 0x44, 0x24, 0x05, //0x0000498c leaq         $5(%r12), %rax
	//0x00004991 LBB0_903
	0x48, 0x8b, 0x54, 0x24, 0x28, //0x00004991 movq         $40(%rsp), %rdx
	0x48, 0x3b, 0x42, 0x08, //0x00004996 cmpq         $8(%rdx), %rax
	0x0f, 0x87, 0xee, 0xf9, 0xff, 0xff, //0x0000499a ja           LBB0_839
	0x49, 0x89, 0x00, //0x000049a0 movq         %rax, (%r8)
	0x4d, 0x89, 0xe5, //0x000049a3 movq         %r12, %r13
	0xe9, 0xe3, 0xf9, 0xff, 0xff, //0x000049a6 jmp          LBB0_839
	//0x000049ab LBB0_905
	0x48, 0x8b, 0x44, 0x24, 0x28, //0x000049ab movq         $40(%rsp), %rax
	0x4c, 0x8b, 0x70, 0x08, //0x000049b0 movq         $8(%rax), %r14
	0x49, 0x29, 0xfe, //0x000049b4 subq         %rdi, %r14
	0x49, 0x01, 0xfb, //0x000049b7 addq         %rdi, %r11
	0x45, 0x31, 0xc9, //0x000049ba xorl         %r9d, %r9d
	0xc5, 0x7a, 0x6f, 0x15, 0x6b, 0xb6, 0xff, 0xff, //0x000049bd vmovdqu      $-18837(%rip), %xmm10  /* LCPI0_3+0(%rip) */
	0xc5, 0xfa, 0x6f, 0x0d, 0x73, 0xb6, 0xff, 0xff, //0x000049c5 vmovdqu      $-18829(%rip), %xmm1  /* LCPI0_4+0(%rip) */
	0xc4, 0x41, 0x31, 0x76, 0xc9, //0x000049cd vpcmpeqd     %xmm9, %xmm9, %xmm9
	0xc5, 0xfa, 0x6f, 0x1d, 0x76, 0xb6, 0xff, 0xff, //0x000049d2 vmovdqu      $-18826(%rip), %xmm3  /* LCPI0_5+0(%rip) */
	0xc5, 0xfa, 0x6f, 0x25, 0x3e, 0xb6, 0xff, 0xff, //0x000049da vmovdqu      $-18882(%rip), %xmm4  /* LCPI0_2+0(%rip) */
	0xc4, 0x41, 0x39, 0xef, 0xc0, //0x000049e2 vpxor        %xmm8, %xmm8, %xmm8
	0x31, 0xc9, //0x000049e7 xorl         %ecx, %ecx
	0x45, 0x31, 0xff, //0x000049e9 xorl         %r15d, %r15d
	0x31, 0xdb, //0x000049ec xorl         %ebx, %ebx
	0xe9, 0x17, 0x00, 0x00, 0x00, //0x000049ee jmp          LBB0_907
	//0x000049f3 LBB0_906
	0x49, 0xc1, 0xf8, 0x3f, //0x000049f3 sarq         $63, %r8
	0xf3, 0x48, 0x0f, 0xb8, 0xc7, //0x000049f7 popcntq      %rdi, %rax
	0x49, 0x01, 0xc7, //0x000049fc addq         %rax, %r15
	0x49, 0x83, 0xc3, 0x40, //0x000049ff addq         $64, %r11
	0x49, 0x83, 0xc6, 0xc0, //0x00004a03 addq         $-64, %r14
	0x4d, 0x89, 0xc1, //0x00004a07 movq         %r8, %r9
	//0x00004a0a LBB0_907
	0x49, 0x83, 0xfe, 0x40, //0x00004a0a cmpq         $64, %r14
	0x0f, 0x8c, 0x9d, 0x01, 0x00, 0x00, //0x00004a0e jl           LBB0_914
	//0x00004a14 LBB0_908
	0xc4, 0xc1, 0x7a, 0x6f, 0x13, //0x00004a14 vmovdqu      (%r11), %xmm2
	0xc4, 0xc1, 0x7a, 0x6f, 0x6b, 0x10, //0x00004a19 vmovdqu      $16(%r11), %xmm5
	0xc4, 0xc1, 0x7a, 0x6f, 0x7b, 0x20, //0x00004a1f vmovdqu      $32(%r11), %xmm7
	0xc4, 0xc1, 0x7a, 0x6f, 0x73, 0x30, //0x00004a25 vmovdqu      $48(%r11), %xmm6
	0xc5, 0xa9, 0x74, 0xc2, //0x00004a2b vpcmpeqb     %xmm2, %xmm10, %xmm0
	0xc5, 0x79, 0xd7, 0xc0, //0x00004a2f vpmovmskb    %xmm0, %r8d
	0xc5, 0xa9, 0x74, 0xc5, //0x00004a33 vpcmpeqb     %xmm5, %xmm10, %xmm0
	0xc5, 0xf9, 0xd7, 0xd0, //0x00004a37 vpmovmskb    %xmm0, %edx
	0xc5, 0xa9, 0x74, 0xc7, //0x00004a3b vpcmpeqb     %xmm7, %xmm10, %xmm0
	0xc5, 0xf9, 0xd7, 0xf8, //0x00004a3f vpmovmskb    %xmm0, %edi
	0xc5, 0xa9, 0x74, 0xc6, //0x00004a43 vpcmpeqb     %xmm6, %xmm10, %xmm0
	0xc5, 0x79, 0xd7, 0xd0, //0x00004a47 vpmovmskb    %xmm0, %r10d
	0x49, 0xc1, 0xe2, 0x30, //0x00004a4b shlq         $48, %r10
	0x48, 0xc1, 0xe7, 0x20, //0x00004a4f shlq         $32, %rdi
	0x48, 0xc1, 0xe2, 0x10, //0x00004a53 shlq         $16, %rdx
	0x49, 0x09, 0xd0, //0x00004a57 orq          %rdx, %r8
	0x49, 0x09, 0xf8, //0x00004a5a orq          %rdi, %r8
	0x4d, 0x09, 0xd0, //0x00004a5d orq          %r10, %r8
	0xc5, 0xe9, 0x74, 0xc1, //0x00004a60 vpcmpeqb     %xmm1, %xmm2, %xmm0
	0xc5, 0xf9, 0xd7, 0xf8, //0x00004a64 vpmovmskb    %xmm0, %edi
	0xc5, 0xd1, 0x74, 0xc1, //0x00004a68 vpcmpeqb     %xmm1, %xmm5, %xmm0
	0xc5, 0xf9, 0xd7, 0xd0, //0x00004a6c vpmovmskb    %xmm0, %edx
	0xc5, 0xc1, 0x74, 0xc1, //0x00004a70 vpcmpeqb     %xmm1, %xmm7, %xmm0
	0xc5, 0xf9, 0xd7, 0xc0, //0x00004a74 vpmovmskb    %xmm0, %eax
	0xc5, 0xc9, 0x74, 0xc1, //0x00004a78 vpcmpeqb     %xmm1, %xmm6, %xmm0
	0xc5, 0xf9, 0xd7, 0xf0, //0x00004a7c vpmovmskb    %xmm0, %esi
	0x48, 0xc1, 0xe6, 0x30, //0x00004a80 shlq         $48, %rsi
	0x48, 0xc1, 0xe0, 0x20, //0x00004a84 shlq         $32, %rax
	0x48, 0xc1, 0xe2, 0x10, //0x00004a88 shlq         $16, %rdx
	0x48, 0x09, 0xd7, //0x00004a8c orq          %rdx, %rdi
	0x48, 0x09, 0xc7, //0x00004a8f orq          %rax, %rdi
	0x48, 0x09, 0xf7, //0x00004a92 orq          %rsi, %rdi
	0x48, 0x89, 0xf8, //0x00004a95 movq         %rdi, %rax
	0x48, 0x09, 0xc8, //0x00004a98 orq          %rcx, %rax
	0x0f, 0x84, 0x49, 0x00, 0x00, 0x00, //0x00004a9b je           LBB0_910
	0x48, 0x89, 0xc8, //0x00004aa1 movq         %rcx, %rax
	0x48, 0xf7, 0xd0, //0x00004aa4 notq         %rax
	0x48, 0x21, 0xf8, //0x00004aa7 andq         %rdi, %rax
	0x48, 0x8d, 0x14, 0x00, //0x00004aaa leaq         (%rax,%rax), %rdx
	0x48, 0x09, 0xca, //0x00004aae orq          %rcx, %rdx
	0x48, 0x89, 0xd6, //0x00004ab1 movq         %rdx, %rsi
	0x48, 0xb9, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00004ab4 movabsq      $-6148914691236517206, %rcx
	0x48, 0x31, 0xce, //0x00004abe xorq         %rcx, %rsi
	0x48, 0x21, 0xcf, //0x00004ac1 andq         %rcx, %rdi
	0x48, 0x21, 0xf7, //0x00004ac4 andq         %rsi, %rdi
	0x31, 0xc9, //0x00004ac7 xorl         %ecx, %ecx
	0x48, 0x01, 0xc7, //0x00004ac9 addq         %rax, %rdi
	0x0f, 0x92, 0xc1, //0x00004acc setb         %cl
	0x48, 0x01, 0xff, //0x00004acf addq         %rdi, %rdi
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00004ad2 movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc7, //0x00004adc xorq         %rax, %rdi
	0x48, 0x21, 0xd7, //0x00004adf andq         %rdx, %rdi
	0x48, 0xf7, 0xd7, //0x00004ae2 notq         %rdi
	0xe9, 0x09, 0x00, 0x00, 0x00, //0x00004ae5 jmp          LBB0_911
	//0x00004aea LBB0_910
	0x48, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x00004aea movq         $-1, %rdi
	0x31, 0xc9, //0x00004af1 xorl         %ecx, %ecx
	//0x00004af3 LBB0_911
	0x4c, 0x21, 0xc7, //0x00004af3 andq         %r8, %rdi
	0xc4, 0xe1, 0xf9, 0x6e, 0xc7, //0x00004af6 vmovq        %rdi, %xmm0
	0xc4, 0xc3, 0x79, 0x44, 0xc1, 0x00, //0x00004afb vpclmulqdq   $0, %xmm9, %xmm0, %xmm0
	0xc4, 0xc1, 0xf9, 0x7e, 0xc0, //0x00004b01 vmovq        %xmm0, %r8
	0x4d, 0x31, 0xc8, //0x00004b06 xorq         %r9, %r8
	0xc5, 0xe9, 0x74, 0xc3, //0x00004b09 vpcmpeqb     %xmm3, %xmm2, %xmm0
	0xc5, 0xf9, 0xd7, 0xf8, //0x00004b0d vpmovmskb    %xmm0, %edi
	0xc5, 0xd1, 0x74, 0xc3, //0x00004b11 vpcmpeqb     %xmm3, %xmm5, %xmm0
	0xc5, 0xf9, 0xd7, 0xc0, //0x00004b15 vpmovmskb    %xmm0, %eax
	0xc5, 0xc1, 0x74, 0xc3, //0x00004b19 vpcmpeqb     %xmm3, %xmm7, %xmm0
	0xc5, 0xf9, 0xd7, 0xd0, //0x00004b1d vpmovmskb    %xmm0, %edx
	0xc5, 0xc9, 0x74, 0xc3, //0x00004b21 vpcmpeqb     %xmm3, %xmm6, %xmm0
	0xc5, 0xf9, 0xd7, 0xf0, //0x00004b25 vpmovmskb    %xmm0, %esi
	0x48, 0xc1, 0xe6, 0x30, //0x00004b29 shlq         $48, %rsi
	0x48, 0xc1, 0xe2, 0x20, //0x00004b2d shlq         $32, %rdx
	0x48, 0xc1, 0xe0, 0x10, //0x00004b31 shlq         $16, %rax
	0x48, 0x09, 0xc7, //0x00004b35 orq          %rax, %rdi
	0x48, 0x09, 0xd7, //0x00004b38 orq          %rdx, %rdi
	0x48, 0x09, 0xf7, //0x00004b3b orq          %rsi, %rdi
	0x4d, 0x89, 0xc1, //0x00004b3e movq         %r8, %r9
	0x49, 0xf7, 0xd1, //0x00004b41 notq         %r9
	0x4c, 0x21, 0xcf, //0x00004b44 andq         %r9, %rdi
	0xc5, 0xe9, 0x74, 0xc4, //0x00004b47 vpcmpeqb     %xmm4, %xmm2, %xmm0
	0xc5, 0xf9, 0xd7, 0xd0, //0x00004b4b vpmovmskb    %xmm0, %edx
	0xc5, 0xd1, 0x74, 0xc4, //0x00004b4f vpcmpeqb     %xmm4, %xmm5, %xmm0
	0xc5, 0xf9, 0xd7, 0xf0, //0x00004b53 vpmovmskb    %xmm0, %esi
	0xc5, 0xc1, 0x74, 0xc4, //0x00004b57 vpcmpeqb     %xmm4, %xmm7, %xmm0
	0xc5, 0xf9, 0xd7, 0xc0, //0x00004b5b vpmovmskb    %xmm0, %eax
	0xc5, 0xc9, 0x74, 0xc4, //0x00004b5f vpcmpeqb     %xmm4, %xmm6, %xmm0
	0xc5, 0x79, 0xd7, 0xd0, //0x00004b63 vpmovmskb    %xmm0, %r10d
	0x49, 0xc1, 0xe2, 0x30, //0x00004b67 shlq         $48, %r10
	0x48, 0xc1, 0xe0, 0x20, //0x00004b6b shlq         $32, %rax
	0x48, 0xc1, 0xe6, 0x10, //0x00004b6f shlq         $16, %rsi
	0x48, 0x09, 0xf2, //0x00004b73 orq          %rsi, %rdx
	0x48, 0x09, 0xc2, //0x00004b76 orq          %rax, %rdx
	0x4c, 0x09, 0xd2, //0x00004b79 orq          %r10, %rdx
	0x4c, 0x21, 0xca, //0x00004b7c andq         %r9, %rdx
	0x0f, 0x84, 0x6e, 0xfe, 0xff, 0xff, //0x00004b7f je           LBB0_906
	//0x00004b85 LBB0_912
	0x48, 0x8d, 0x42, 0xff, //0x00004b85 leaq         $-1(%rdx), %rax
	0x48, 0x89, 0xc6, //0x00004b89 movq         %rax, %rsi
	0x48, 0x21, 0xfe, //0x00004b8c andq         %rdi, %rsi
	0xf3, 0x48, 0x0f, 0xb8, 0xf6, //0x00004b8f popcntq      %rsi, %rsi
	0x4c, 0x01, 0xfe, //0x00004b94 addq         %r15, %rsi
	0x48, 0x39, 0xde, //0x00004b97 cmpq         %rbx, %rsi
	0x0f, 0x86, 0x40, 0x01, 0x00, 0x00, //0x00004b9a jbe          LBB0_929
	0x48, 0xff, 0xc3, //0x00004ba0 incq         %rbx
	0x48, 0x21, 0xc2, //0x00004ba3 andq         %rax, %rdx
	0x0f, 0x85, 0xd9, 0xff, 0xff, 0xff, //0x00004ba6 jne          LBB0_912
	0xe9, 0x42, 0xfe, 0xff, 0xff, //0x00004bac jmp          LBB0_906
	//0x00004bb1 LBB0_914
	0x4d, 0x85, 0xf6, //0x00004bb1 testq        %r14, %r14
	0x0f, 0x8e, 0xa4, 0x03, 0x00, 0x00, //0x00004bb4 jle          LBB0_962
	0xc5, 0x7e, 0x7f, 0x84, 0x24, 0x80, 0x00, 0x00, 0x00, //0x00004bba vmovdqu      %ymm8, $128(%rsp)
	0xc5, 0x7e, 0x7f, 0x44, 0x24, 0x60, //0x00004bc3 vmovdqu      %ymm8, $96(%rsp)
	0x44, 0x89, 0xd8, //0x00004bc9 movl         %r11d, %eax
	0x25, 0xff, 0x0f, 0x00, 0x00, //0x00004bcc andl         $4095, %eax
	0x3d, 0xc1, 0x0f, 0x00, 0x00, //0x00004bd1 cmpl         $4033, %eax
	0x0f, 0x82, 0x38, 0xfe, 0xff, 0xff, //0x00004bd6 jb           LBB0_908
	0x49, 0x83, 0xfe, 0x20, //0x00004bdc cmpq         $32, %r14
	0x0f, 0x82, 0x2c, 0x00, 0x00, 0x00, //0x00004be0 jb           LBB0_918
	0xc4, 0xc1, 0x78, 0x10, 0x03, //0x00004be6 vmovups      (%r11), %xmm0
	0xc5, 0xf8, 0x11, 0x44, 0x24, 0x60, //0x00004beb vmovups      %xmm0, $96(%rsp)
	0xc4, 0xc1, 0x7a, 0x6f, 0x43, 0x10, //0x00004bf1 vmovdqu      $16(%r11), %xmm0
	0xc5, 0xfa, 0x7f, 0x44, 0x24, 0x70, //0x00004bf7 vmovdqu      %xmm0, $112(%rsp)
	0x49, 0x83, 0xc3, 0x20, //0x00004bfd addq         $32, %r11
	0x49, 0x8d, 0x7e, 0xe0, //0x00004c01 leaq         $-32(%r14), %rdi
	0x48, 0x8d, 0xb4, 0x24, 0x80, 0x00, 0x00, 0x00, //0x00004c05 leaq         $128(%rsp), %rsi
	0xe9, 0x08, 0x00, 0x00, 0x00, //0x00004c0d jmp          LBB0_919
	//0x00004c12 LBB0_918
	0x48, 0x8d, 0x74, 0x24, 0x60, //0x00004c12 leaq         $96(%rsp), %rsi
	0x4c, 0x89, 0xf7, //0x00004c17 movq         %r14, %rdi
	//0x00004c1a LBB0_919
	0x48, 0x83, 0xff, 0x10, //0x00004c1a cmpq         $16, %rdi
	0x0f, 0x82, 0x5a, 0x00, 0x00, 0x00, //0x00004c1e jb           LBB0_920
	0xc4, 0xc1, 0x7a, 0x6f, 0x03, //0x00004c24 vmovdqu      (%r11), %xmm0
	0xc5, 0xfa, 0x7f, 0x06, //0x00004c29 vmovdqu      %xmm0, (%rsi)
	0x49, 0x83, 0xc3, 0x10, //0x00004c2d addq         $16, %r11
	0x48, 0x83, 0xc6, 0x10, //0x00004c31 addq         $16, %rsi
	0x48, 0x83, 0xc7, 0xf0, //0x00004c35 addq         $-16, %rdi
	0x48, 0x83, 0xff, 0x08, //0x00004c39 cmpq         $8, %rdi
	0x0f, 0x83, 0x45, 0x00, 0x00, 0x00, //0x00004c3d jae          LBB0_925
	//0x00004c43 LBB0_921
	0x48, 0x83, 0xff, 0x04, //0x00004c43 cmpq         $4, %rdi
	0x0f, 0x8c, 0x57, 0x00, 0x00, 0x00, //0x00004c47 jl           LBB0_922
	//0x00004c4d LBB0_926
	0x41, 0x8b, 0x03, //0x00004c4d movl         (%r11), %eax
	0x89, 0x06, //0x00004c50 movl         %eax, (%rsi)
	0x49, 0x83, 0xc3, 0x04, //0x00004c52 addq         $4, %r11
	0x48, 0x83, 0xc6, 0x04, //0x00004c56 addq         $4, %rsi
	0x48, 0x83, 0xc7, 0xfc, //0x00004c5a addq         $-4, %rdi
	0x48, 0x83, 0xff, 0x02, //0x00004c5e cmpq         $2, %rdi
	0x0f, 0x83, 0x46, 0x00, 0x00, 0x00, //0x00004c62 jae          LBB0_927
	//0x00004c68 LBB0_923
	0x4c, 0x89, 0xda, //0x00004c68 movq         %r11, %rdx
	0x4c, 0x8d, 0x5c, 0x24, 0x60, //0x00004c6b leaq         $96(%rsp), %r11
	0x48, 0x85, 0xff, //0x00004c70 testq        %rdi, %rdi
	0x0f, 0x85, 0x59, 0x00, 0x00, 0x00, //0x00004c73 jne          LBB0_928
	0xe9, 0x96, 0xfd, 0xff, 0xff, //0x00004c79 jmp          LBB0_908
	//0x00004c7e LBB0_920
	0x48, 0x83, 0xff, 0x08, //0x00004c7e cmpq         $8, %rdi
	0x0f, 0x82, 0xbb, 0xff, 0xff, 0xff, //0x00004c82 jb           LBB0_921
	//0x00004c88 LBB0_925
	0x49, 0x8b, 0x03, //0x00004c88 movq         (%r11), %rax
	0x48, 0x89, 0x06, //0x00004c8b movq         %rax, (%rsi)
	0x49, 0x83, 0xc3, 0x08, //0x00004c8e addq         $8, %r11
	0x48, 0x83, 0xc6, 0x08, //0x00004c92 addq         $8, %rsi
	0x48, 0x83, 0xc7, 0xf8, //0x00004c96 addq         $-8, %rdi
	0x48, 0x83, 0xff, 0x04, //0x00004c9a cmpq         $4, %rdi
	0x0f, 0x8d, 0xa9, 0xff, 0xff, 0xff, //0x00004c9e jge          LBB0_926
	//0x00004ca4 LBB0_922
	0x48, 0x83, 0xff, 0x02, //0x00004ca4 cmpq         $2, %rdi
	0x0f, 0x82, 0xba, 0xff, 0xff, 0xff, //0x00004ca8 jb           LBB0_923
	//0x00004cae LBB0_927
	0x41, 0x0f, 0xb7, 0x03, //0x00004cae movzwl       (%r11), %eax
	0x66, 0x89, 0x06, //0x00004cb2 movw         %ax, (%rsi)
	0x49, 0x83, 0xc3, 0x02, //0x00004cb5 addq         $2, %r11
	0x48, 0x83, 0xc6, 0x02, //0x00004cb9 addq         $2, %rsi
	0x48, 0x83, 0xc7, 0xfe, //0x00004cbd addq         $-2, %rdi
	0x4c, 0x89, 0xda, //0x00004cc1 movq         %r11, %rdx
	0x4c, 0x8d, 0x5c, 0x24, 0x60, //0x00004cc4 leaq         $96(%rsp), %r11
	0x48, 0x85, 0xff, //0x00004cc9 testq        %rdi, %rdi
	0x0f, 0x84, 0x42, 0xfd, 0xff, 0xff, //0x00004ccc je           LBB0_908
	//0x00004cd2 LBB0_928
	0x8a, 0x02, //0x00004cd2 movb         (%rdx), %al
	0x88, 0x06, //0x00004cd4 movb         %al, (%rsi)
	0x4c, 0x8d, 0x5c, 0x24, 0x60, //0x00004cd6 leaq         $96(%rsp), %r11
	0xe9, 0x34, 0xfd, 0xff, 0xff, //0x00004cdb jmp          LBB0_908
	//0x00004ce0 LBB0_929
	0x48, 0x8b, 0x74, 0x24, 0x28, //0x00004ce0 movq         $40(%rsp), %rsi
	0x48, 0x8b, 0x46, 0x08, //0x00004ce5 movq         $8(%rsi), %rax
	0x48, 0x0f, 0xbc, 0xca, //0x00004ce9 bsfq         %rdx, %rcx
	0x4c, 0x29, 0xf1, //0x00004ced subq         %r14, %rcx
	0x48, 0x8d, 0x44, 0x01, 0x01, //0x00004cf0 leaq         $1(%rcx,%rax), %rax
	0x48, 0x8b, 0x54, 0x24, 0x18, //0x00004cf5 movq         $24(%rsp), %rdx
	0x48, 0x89, 0x02, //0x00004cfa movq         %rax, (%rdx)
	0x48, 0x8b, 0x4e, 0x08, //0x00004cfd movq         $8(%rsi), %rcx
	0x48, 0x39, 0xc8, //0x00004d01 cmpq         %rcx, %rax
	0x48, 0x0f, 0x47, 0xc1, //0x00004d04 cmovaq       %rcx, %rax
	0x48, 0x89, 0x02, //0x00004d08 movq         %rax, (%rdx)
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00004d0b movq         $-1, %rax
	0x4c, 0x0f, 0x47, 0xe0, //0x00004d12 cmovaq       %rax, %r12
	0x4d, 0x89, 0xe5, //0x00004d16 movq         %r12, %r13
	0xe9, 0x70, 0xf6, 0xff, 0xff, //0x00004d19 jmp          LBB0_839
	//0x00004d1e LBB0_930
	0x4c, 0x89, 0xf8, //0x00004d1e movq         %r15, %rax
	//0x00004d21 LBB0_931
	0x48, 0xf7, 0xd0, //0x00004d21 notq         %rax
	0x49, 0x01, 0xc1, //0x00004d24 addq         %rax, %r9
	0x4d, 0x89, 0x08, //0x00004d27 movq         %r9, (%r8)
	0xe9, 0x58, 0xf6, 0xff, 0xff, //0x00004d2a jmp          LBB0_838
	//0x00004d2f LBB0_941
	0x49, 0x89, 0x00, //0x00004d2f movq         %rax, (%r8)
	0xe9, 0x57, 0xf6, 0xff, 0xff, //0x00004d32 jmp          LBB0_839
	//0x00004d37 LBB0_936
	0x4c, 0x89, 0x94, 0x24, 0xb8, 0x00, 0x00, 0x00, //0x00004d37 movq         %r10, $184(%rsp)
	//0x00004d3f LBB0_937
	0x48, 0x8b, 0x44, 0x24, 0x18, //0x00004d3f movq         $24(%rsp), %rax
	0x48, 0x8b, 0x8c, 0x24, 0xb8, 0x00, 0x00, 0x00, //0x00004d44 movq         $184(%rsp), %rcx
	0x48, 0x89, 0x08, //0x00004d4c movq         %rcx, (%rax)
	0x49, 0xc7, 0xc5, 0xff, 0xff, 0xff, 0xff, //0x00004d4f movq         $-1, %r13
	0xe9, 0x33, 0xf6, 0xff, 0xff, //0x00004d56 jmp          LBB0_839
	//0x00004d5b LBB0_938
	0x4c, 0x29, 0xdf, //0x00004d5b subq         %r11, %rdi
	0xe9, 0x90, 0xf7, 0xff, 0xff, //0x00004d5e jmp          LBB0_859
	//0x00004d63 LBB0_939
	0x49, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x00004d63 movq         $-1, %r10
	//0x00004d6a LBB0_940
	0x4d, 0x29, 0xd1, //0x00004d6a subq         %r10, %r9
	0x48, 0x8b, 0x44, 0x24, 0x18, //0x00004d6d movq         $24(%rsp), %rax
	0x4c, 0x89, 0x08, //0x00004d72 movq         %r9, (%rax)
	0xe9, 0x0d, 0xf6, 0xff, 0xff, //0x00004d75 jmp          LBB0_838
	//0x00004d7a LBB0_943
	0x49, 0xc7, 0xc5, 0xfe, 0xff, 0xff, 0xff, //0x00004d7a movq         $-2, %r13
	0x3c, 0x61, //0x00004d81 cmpb         $97, %al
	0x0f, 0x85, 0x05, 0xf6, 0xff, 0xff, //0x00004d83 jne          LBB0_839
	0x49, 0x8d, 0x41, 0x02, //0x00004d89 leaq         $2(%r9), %rax
	0x49, 0x89, 0x00, //0x00004d8d movq         %rax, (%r8)
	0x43, 0x80, 0x7c, 0x0c, 0x02, 0x6c, //0x00004d90 cmpb         $108, $2(%r12,%r9)
	0x0f, 0x85, 0xf2, 0xf5, 0xff, 0xff, //0x00004d96 jne          LBB0_839
	0x49, 0x8d, 0x41, 0x03, //0x00004d9c leaq         $3(%r9), %rax
	0x49, 0x89, 0x00, //0x00004da0 movq         %rax, (%r8)
	0x43, 0x80, 0x7c, 0x0c, 0x03, 0x73, //0x00004da3 cmpb         $115, $3(%r12,%r9)
	0x0f, 0x85, 0xdf, 0xf5, 0xff, 0xff, //0x00004da9 jne          LBB0_839
	0x49, 0x8d, 0x41, 0x04, //0x00004daf leaq         $4(%r9), %rax
	0x49, 0x89, 0x00, //0x00004db3 movq         %rax, (%r8)
	0x43, 0x80, 0x7c, 0x0c, 0x04, 0x65, //0x00004db6 cmpb         $101, $4(%r12,%r9)
	0x0f, 0x85, 0xcc, 0xf5, 0xff, 0xff, //0x00004dbc jne          LBB0_839
	0x49, 0x83, 0xc1, 0x05, //0x00004dc2 addq         $5, %r9
	0xe9, 0xa3, 0x00, 0x00, 0x00, //0x00004dc6 jmp          LBB0_953
	//0x00004dcb LBB0_752
	0x4d, 0x89, 0x08, //0x00004dcb movq         %r9, (%r8)
	0x49, 0xc7, 0xc5, 0xfe, 0xff, 0xff, 0xff, //0x00004dce movq         $-2, %r13
	0x41, 0x80, 0x3a, 0x6e, //0x00004dd5 cmpb         $110, (%r10)
	0x0f, 0x85, 0xaf, 0xf5, 0xff, 0xff, //0x00004dd9 jne          LBB0_839
	0x49, 0x8d, 0x41, 0x01, //0x00004ddf leaq         $1(%r9), %rax
	0x49, 0x89, 0x00, //0x00004de3 movq         %rax, (%r8)
	0x43, 0x80, 0x7c, 0x0c, 0x01, 0x75, //0x00004de6 cmpb         $117, $1(%r12,%r9)
	0x0f, 0x85, 0x9c, 0xf5, 0xff, 0xff, //0x00004dec jne          LBB0_839
	0x49, 0x8d, 0x41, 0x02, //0x00004df2 leaq         $2(%r9), %rax
	0x49, 0x89, 0x00, //0x00004df6 movq         %rax, (%r8)
	0x43, 0x80, 0x7c, 0x0c, 0x02, 0x6c, //0x00004df9 cmpb         $108, $2(%r12,%r9)
	0x0f, 0x85, 0x89, 0xf5, 0xff, 0xff, //0x00004dff jne          LBB0_839
	0x49, 0x8d, 0x41, 0x03, //0x00004e05 leaq         $3(%r9), %rax
	0x49, 0x89, 0x00, //0x00004e09 movq         %rax, (%r8)
	0x43, 0x80, 0x7c, 0x0c, 0x03, 0x6c, //0x00004e0c cmpb         $108, $3(%r12,%r9)
	0x0f, 0x85, 0x76, 0xf5, 0xff, 0xff, //0x00004e12 jne          LBB0_839
	0xe9, 0x4d, 0x00, 0x00, 0x00, //0x00004e18 jmp          LBB0_952
	//0x00004e1d LBB0_948
	0x4d, 0x89, 0x08, //0x00004e1d movq         %r9, (%r8)
	0x49, 0xc7, 0xc5, 0xfe, 0xff, 0xff, 0xff, //0x00004e20 movq         $-2, %r13
	0x41, 0x80, 0x3a, 0x74, //0x00004e27 cmpb         $116, (%r10)
	0x0f, 0x85, 0x5d, 0xf5, 0xff, 0xff, //0x00004e2b jne          LBB0_839
	0x49, 0x8d, 0x41, 0x01, //0x00004e31 leaq         $1(%r9), %rax
	0x49, 0x89, 0x00, //0x00004e35 movq         %rax, (%r8)
	0x43, 0x80, 0x7c, 0x0c, 0x01, 0x72, //0x00004e38 cmpb         $114, $1(%r12,%r9)
	0x0f, 0x85, 0x4a, 0xf5, 0xff, 0xff, //0x00004e3e jne          LBB0_839
	0x49, 0x8d, 0x41, 0x02, //0x00004e44 leaq         $2(%r9), %rax
	0x49, 0x89, 0x00, //0x00004e48 movq         %rax, (%r8)
	0x43, 0x80, 0x7c, 0x0c, 0x02, 0x75, //0x00004e4b cmpb         $117, $2(%r12,%r9)
	0x0f, 0x85, 0x37, 0xf5, 0xff, 0xff, //0x00004e51 jne          LBB0_839
	0x49, 0x8d, 0x41, 0x03, //0x00004e57 leaq         $3(%r9), %rax
	0x49, 0x89, 0x00, //0x00004e5b movq         %rax, (%r8)
	0x43, 0x80, 0x7c, 0x0c, 0x03, 0x65, //0x00004e5e cmpb         $101, $3(%r12,%r9)
	0x0f, 0x85, 0x24, 0xf5, 0xff, 0xff, //0x00004e64 jne          LBB0_839
	//0x00004e6a LBB0_952
	0x49, 0x83, 0xc1, 0x04, //0x00004e6a addq         $4, %r9
	//0x00004e6e LBB0_953
	0x48, 0x8b, 0x44, 0x24, 0x18, //0x00004e6e movq         $24(%rsp), %rax
	0x4c, 0x89, 0x08, //0x00004e73 movq         %r9, (%rax)
	0xe9, 0x13, 0xf5, 0xff, 0xff, //0x00004e76 jmp          LBB0_839
	//0x00004e7b LBB0_960
	0x4c, 0x89, 0xf8, //0x00004e7b movq         %r15, %rax
	0xe9, 0x91, 0xf6, 0xff, 0xff, //0x00004e7e jmp          LBB0_862
	//0x00004e83 LBB0_813
	0x48, 0x8b, 0x44, 0x24, 0x20, //0x00004e83 movq         $32(%rsp), %rax
	0xe9, 0x87, 0xf6, 0xff, 0xff, //0x00004e88 jmp          LBB0_862
	//0x00004e8d LBB0_954
	0x4c, 0x89, 0xf3, //0x00004e8d movq         %r14, %rbx
	0x48, 0x83, 0xc3, 0x02, //0x00004e90 addq         $2, %rbx
	0x49, 0xc7, 0xc5, 0xfe, 0xff, 0xff, 0xff, //0x00004e94 movq         $-2, %r13
	0xe9, 0xa6, 0x00, 0x00, 0x00, //0x00004e9b jmp          LBB0_959
	//0x00004ea0 LBB0_874
	0x4d, 0x85, 0xff, //0x00004ea0 testq        %r15, %r15
	0x0f, 0x85, 0xeb, 0x00, 0x00, 0x00, //0x00004ea3 jne          LBB0_966
	0x4b, 0x8d, 0x7c, 0x16, 0x01, //0x00004ea9 leaq         $1(%r14,%r10), %rdi
	0x49, 0xf7, 0xd6, //0x00004eae notq         %r14
	0x4d, 0x01, 0xc6, //0x00004eb1 addq         %r8, %r14
	//0x00004eb4 LBB0_876
	0x4d, 0x85, 0xf6, //0x00004eb4 testq        %r14, %r14
	0x48, 0x8b, 0x54, 0x24, 0x18, //0x00004eb7 movq         $24(%rsp), %rdx
	0x0f, 0x8e, 0xcc, 0xf4, 0xff, 0xff, //0x00004ebc jle          LBB0_839
	0x49, 0xc7, 0xc5, 0xff, 0xff, 0xff, 0xff, //0x00004ec2 movq         $-1, %r13
	0xe9, 0x18, 0x00, 0x00, 0x00, //0x00004ec9 jmp          LBB0_933
	//0x00004ece LBB0_932
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x00004ece movq         $-2, %rax
	0xb9, 0x02, 0x00, 0x00, 0x00, //0x00004ed5 movl         $2, %ecx
	0x48, 0x01, 0xcf, //0x00004eda addq         %rcx, %rdi
	0x49, 0x01, 0xc6, //0x00004edd addq         %rax, %r14
	0x0f, 0x8e, 0xa8, 0xf4, 0xff, 0xff, //0x00004ee0 jle          LBB0_839
	//0x00004ee6 LBB0_933
	0x0f, 0xb6, 0x07, //0x00004ee6 movzbl       (%rdi), %eax
	0x3c, 0x5c, //0x00004ee9 cmpb         $92, %al
	0x0f, 0x84, 0xdd, 0xff, 0xff, 0xff, //0x00004eeb je           LBB0_932
	0x3c, 0x22, //0x00004ef1 cmpb         $34, %al
	0x0f, 0x84, 0x1d, 0x00, 0x00, 0x00, //0x00004ef3 je           LBB0_955
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00004ef9 movq         $-1, %rax
	0xb9, 0x01, 0x00, 0x00, 0x00, //0x00004f00 movl         $1, %ecx
	0x48, 0x01, 0xcf, //0x00004f05 addq         %rcx, %rdi
	0x49, 0x01, 0xc6, //0x00004f08 addq         %rax, %r14
	0x0f, 0x8f, 0xd5, 0xff, 0xff, 0xff, //0x00004f0b jg           LBB0_933
	0xe9, 0x78, 0xf4, 0xff, 0xff, //0x00004f11 jmp          LBB0_839
	//0x00004f16 LBB0_955
	0x4c, 0x29, 0xdf, //0x00004f16 subq         %r11, %rdi
	0x48, 0xff, 0xc7, //0x00004f19 incq         %rdi
	0x48, 0x89, 0x3a, //0x00004f1c movq         %rdi, (%rdx)
	0x4d, 0x89, 0xe5, //0x00004f1f movq         %r12, %r13
	0xe9, 0x67, 0xf4, 0xff, 0xff, //0x00004f22 jmp          LBB0_839
	//0x00004f27 LBB0_956
	0x4c, 0x89, 0xf3, //0x00004f27 movq         %r14, %rbx
	0x48, 0xff, 0xc3, //0x00004f2a incq         %rbx
	0x49, 0xc7, 0xc5, 0xfd, 0xff, 0xff, 0xff, //0x00004f2d movq         $-3, %r13
	0xe9, 0x0d, 0x00, 0x00, 0x00, //0x00004f34 jmp          LBB0_959
	//0x00004f39 LBB0_957
	0x4c, 0x89, 0xf3, //0x00004f39 movq         %r14, %rbx
	0x48, 0xff, 0xc3, //0x00004f3c incq         %rbx
	//0x00004f3f LBB0_958
	0x49, 0xc7, 0xc5, 0xff, 0xff, 0xff, 0xff, //0x00004f3f movq         $-1, %r13
	//0x00004f46 LBB0_959
	0x48, 0x8b, 0x44, 0x24, 0x20, //0x00004f46 movq         $32(%rsp), %rax
	0x48, 0x29, 0xc3, //0x00004f4b subq         %rax, %rbx
	0x49, 0x89, 0x18, //0x00004f4e movq         %rbx, (%r8)
	0xe9, 0x38, 0xf4, 0xff, 0xff, //0x00004f51 jmp          LBB0_839
	//0x00004f56 LBB0_961
	0x4c, 0x01, 0xdf, //0x00004f56 addq         %r11, %rdi
	0xe9, 0x56, 0xff, 0xff, 0xff, //0x00004f59 jmp          LBB0_876
	//0x00004f5e LBB0_962
	0x48, 0x8b, 0x44, 0x24, 0x28, //0x00004f5e movq         $40(%rsp), %rax
	0x48, 0x8b, 0x40, 0x08, //0x00004f63 movq         $8(%rax), %rax
	0x48, 0x8b, 0x54, 0x24, 0x18, //0x00004f67 movq         $24(%rsp), %rdx
	0x48, 0x89, 0x02, //0x00004f6c movq         %rax, (%rdx)
	0xe9, 0x1a, 0xf4, 0xff, 0xff, //0x00004f6f jmp          LBB0_839
	//0x00004f74 LBB0_963
	0x49, 0xc7, 0xc5, 0xfc, 0xff, 0xff, 0xff, //0x00004f74 movq         $-4, %r13
	0xe9, 0x07, 0x00, 0x00, 0x00, //0x00004f7b jmp          LBB0_965
	//0x00004f80 LBB0_964
	0x49, 0xc7, 0xc5, 0xfe, 0xff, 0xff, 0xff, //0x00004f80 movq         $-2, %r13
	//0x00004f87 LBB0_965
	0x4c, 0x89, 0xc3, //0x00004f87 movq         %r8, %rbx
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x00004f8a movq         $24(%rsp), %r8
	0xe9, 0xb2, 0xff, 0xff, 0xff, //0x00004f8f jmp          LBB0_959
	//0x00004f94 LBB0_966
	0x49, 0x8d, 0x40, 0xff, //0x00004f94 leaq         $-1(%r8), %rax
	0x4c, 0x39, 0xf0, //0x00004f98 cmpq         %r14, %rax
	0x0f, 0x84, 0xed, 0xf3, 0xff, 0xff, //0x00004f9b je           LBB0_839
	0x4b, 0x8d, 0x7c, 0x16, 0x02, //0x00004fa1 leaq         $2(%r14,%r10), %rdi
	0x4d, 0x29, 0xf0, //0x00004fa6 subq         %r14, %r8
	0x49, 0x83, 0xc0, 0xfe, //0x00004fa9 addq         $-2, %r8
	0x4d, 0x89, 0xc6, //0x00004fad movq         %r8, %r14
	0xe9, 0xff, 0xfe, 0xff, 0xff, //0x00004fb0 jmp          LBB0_876
	0x90, 0x90, 0x90, //0x00004fb5 .p2align 2, 0x90
	// // .set L0_0_set_418, LBB0_418-LJTI0_0
	// // .set L0_0_set_488, LBB0_488-LJTI0_0
	// // .set L0_0_set_450, LBB0_450-LJTI0_0
	// // .set L0_0_set_403, LBB0_403-LJTI0_0
	// // .set L0_0_set_461, LBB0_461-LJTI0_0
	// // .set L0_0_set_487, LBB0_487-LJTI0_0
	// // .set L0_0_set_449, LBB0_449-LJTI0_0
	// // .set L0_0_set_489, LBB0_489-LJTI0_0
	//0x00004fb8 LJTI0_0
	0x88, 0xd1, 0xff, 0xff, //0x00004fb8 .long L0_0_set_418
	0x8d, 0xd7, 0xff, 0xff, //0x00004fbc .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x00004fc0 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x00004fc4 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x00004fc8 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x00004fcc .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x00004fd0 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x00004fd4 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x00004fd8 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x00004fdc .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x00004fe0 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x00004fe4 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x00004fe8 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x00004fec .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x00004ff0 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x00004ff4 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x00004ff8 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x00004ffc .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x00005000 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x00005004 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x00005008 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x0000500c .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x00005010 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x00005014 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x00005018 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x0000501c .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x00005020 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x00005024 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x00005028 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x0000502c .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x00005030 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x00005034 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x00005038 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x0000503c .long L0_0_set_488
	0x34, 0xd3, 0xff, 0xff, //0x00005040 .long L0_0_set_450
	0x8d, 0xd7, 0xff, 0xff, //0x00005044 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x00005048 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x0000504c .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x00005050 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x00005054 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x00005058 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x0000505c .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x00005060 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x00005064 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x00005068 .long L0_0_set_488
	0xb1, 0xd0, 0xff, 0xff, //0x0000506c .long L0_0_set_403
	0x8d, 0xd7, 0xff, 0xff, //0x00005070 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x00005074 .long L0_0_set_488
	0xb1, 0xd0, 0xff, 0xff, //0x00005078 .long L0_0_set_403
	0xb1, 0xd0, 0xff, 0xff, //0x0000507c .long L0_0_set_403
	0xb1, 0xd0, 0xff, 0xff, //0x00005080 .long L0_0_set_403
	0xb1, 0xd0, 0xff, 0xff, //0x00005084 .long L0_0_set_403
	0xb1, 0xd0, 0xff, 0xff, //0x00005088 .long L0_0_set_403
	0xb1, 0xd0, 0xff, 0xff, //0x0000508c .long L0_0_set_403
	0xb1, 0xd0, 0xff, 0xff, //0x00005090 .long L0_0_set_403
	0xb1, 0xd0, 0xff, 0xff, //0x00005094 .long L0_0_set_403
	0xb1, 0xd0, 0xff, 0xff, //0x00005098 .long L0_0_set_403
	0xb1, 0xd0, 0xff, 0xff, //0x0000509c .long L0_0_set_403
	0x8d, 0xd7, 0xff, 0xff, //0x000050a0 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x000050a4 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x000050a8 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x000050ac .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x000050b0 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x000050b4 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x000050b8 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x000050bc .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x000050c0 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x000050c4 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x000050c8 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x000050cc .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x000050d0 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x000050d4 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x000050d8 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x000050dc .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x000050e0 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x000050e4 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x000050e8 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x000050ec .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x000050f0 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x000050f4 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x000050f8 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x000050fc .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x00005100 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x00005104 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x00005108 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x0000510c .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x00005110 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x00005114 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x00005118 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x0000511c .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x00005120 .long L0_0_set_488
	0x5e, 0xd4, 0xff, 0xff, //0x00005124 .long L0_0_set_461
	0x8d, 0xd7, 0xff, 0xff, //0x00005128 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x0000512c .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x00005130 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x00005134 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x00005138 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x0000513c .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x00005140 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x00005144 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x00005148 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x0000514c .long L0_0_set_488
	0x80, 0xd7, 0xff, 0xff, //0x00005150 .long L0_0_set_487
	0x8d, 0xd7, 0xff, 0xff, //0x00005154 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x00005158 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x0000515c .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x00005160 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x00005164 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x00005168 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x0000516c .long L0_0_set_488
	0x22, 0xd3, 0xff, 0xff, //0x00005170 .long L0_0_set_449
	0x8d, 0xd7, 0xff, 0xff, //0x00005174 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x00005178 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x0000517c .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x00005180 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x00005184 .long L0_0_set_488
	0x22, 0xd3, 0xff, 0xff, //0x00005188 .long L0_0_set_449
	0x8d, 0xd7, 0xff, 0xff, //0x0000518c .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x00005190 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x00005194 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x00005198 .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x0000519c .long L0_0_set_488
	0x8d, 0xd7, 0xff, 0xff, //0x000051a0 .long L0_0_set_488
	0x98, 0xd7, 0xff, 0xff, //0x000051a4 .long L0_0_set_489
	// // .set L0_1_set_222, LBB0_222-LJTI0_1
	// // .set L0_1_set_289, LBB0_289-LJTI0_1
	// // .set L0_1_set_251, LBB0_251-LJTI0_1
	// // .set L0_1_set_207, LBB0_207-LJTI0_1
	// // .set L0_1_set_262, LBB0_262-LJTI0_1
	// // .set L0_1_set_288, LBB0_288-LJTI0_1
	// // .set L0_1_set_250, LBB0_250-LJTI0_1
	// // .set L0_1_set_290, LBB0_290-LJTI0_1
	//0x000051a8 LJTI0_1
	0x88, 0xbb, 0xff, 0xff, //0x000051a8 .long L0_1_set_222
	0x21, 0xc1, 0xff, 0xff, //0x000051ac .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x000051b0 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x000051b4 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x000051b8 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x000051bc .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x000051c0 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x000051c4 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x000051c8 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x000051cc .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x000051d0 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x000051d4 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x000051d8 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x000051dc .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x000051e0 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x000051e4 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x000051e8 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x000051ec .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x000051f0 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x000051f4 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x000051f8 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x000051fc .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x00005200 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x00005204 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x00005208 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x0000520c .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x00005210 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x00005214 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x00005218 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x0000521c .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x00005220 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x00005224 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x00005228 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x0000522c .long L0_1_set_289
	0xee, 0xbc, 0xff, 0xff, //0x00005230 .long L0_1_set_251
	0x21, 0xc1, 0xff, 0xff, //0x00005234 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x00005238 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x0000523c .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x00005240 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x00005244 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x00005248 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x0000524c .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x00005250 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x00005254 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x00005258 .long L0_1_set_289
	0xbc, 0xba, 0xff, 0xff, //0x0000525c .long L0_1_set_207
	0x21, 0xc1, 0xff, 0xff, //0x00005260 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x00005264 .long L0_1_set_289
	0xbc, 0xba, 0xff, 0xff, //0x00005268 .long L0_1_set_207
	0xbc, 0xba, 0xff, 0xff, //0x0000526c .long L0_1_set_207
	0xbc, 0xba, 0xff, 0xff, //0x00005270 .long L0_1_set_207
	0xbc, 0xba, 0xff, 0xff, //0x00005274 .long L0_1_set_207
	0xbc, 0xba, 0xff, 0xff, //0x00005278 .long L0_1_set_207
	0xbc, 0xba, 0xff, 0xff, //0x0000527c .long L0_1_set_207
	0xbc, 0xba, 0xff, 0xff, //0x00005280 .long L0_1_set_207
	0xbc, 0xba, 0xff, 0xff, //0x00005284 .long L0_1_set_207
	0xbc, 0xba, 0xff, 0xff, //0x00005288 .long L0_1_set_207
	0xbc, 0xba, 0xff, 0xff, //0x0000528c .long L0_1_set_207
	0x21, 0xc1, 0xff, 0xff, //0x00005290 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x00005294 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x00005298 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x0000529c .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x000052a0 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x000052a4 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x000052a8 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x000052ac .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x000052b0 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x000052b4 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x000052b8 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x000052bc .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x000052c0 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x000052c4 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x000052c8 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x000052cc .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x000052d0 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x000052d4 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x000052d8 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x000052dc .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x000052e0 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x000052e4 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x000052e8 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x000052ec .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x000052f0 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x000052f4 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x000052f8 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x000052fc .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x00005300 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x00005304 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x00005308 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x0000530c .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x00005310 .long L0_1_set_289
	0xfc, 0xbd, 0xff, 0xff, //0x00005314 .long L0_1_set_262
	0x21, 0xc1, 0xff, 0xff, //0x00005318 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x0000531c .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x00005320 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x00005324 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x00005328 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x0000532c .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x00005330 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x00005334 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x00005338 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x0000533c .long L0_1_set_289
	0x14, 0xc1, 0xff, 0xff, //0x00005340 .long L0_1_set_288
	0x21, 0xc1, 0xff, 0xff, //0x00005344 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x00005348 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x0000534c .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x00005350 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x00005354 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x00005358 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x0000535c .long L0_1_set_289
	0xdc, 0xbc, 0xff, 0xff, //0x00005360 .long L0_1_set_250
	0x21, 0xc1, 0xff, 0xff, //0x00005364 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x00005368 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x0000536c .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x00005370 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x00005374 .long L0_1_set_289
	0xdc, 0xbc, 0xff, 0xff, //0x00005378 .long L0_1_set_250
	0x21, 0xc1, 0xff, 0xff, //0x0000537c .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x00005380 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x00005384 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x00005388 .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x0000538c .long L0_1_set_289
	0x21, 0xc1, 0xff, 0xff, //0x00005390 .long L0_1_set_289
	0x2c, 0xc1, 0xff, 0xff, //0x00005394 .long L0_1_set_290
	// // .set L0_2_set_586, LBB0_586-LJTI0_2
	// // .set L0_2_set_590, LBB0_590-LJTI0_2
	// // .set L0_2_set_592, LBB0_592-LJTI0_2
	// // .set L0_2_set_611, LBB0_611-LJTI0_2
	// // .set L0_2_set_613, LBB0_613-LJTI0_2
	// // .set L0_2_set_616, LBB0_616-LJTI0_2
	//0x00005398 LJTI0_2
	0xf6, 0xdb, 0xff, 0xff, //0x00005398 .long L0_2_set_586
	0x1e, 0xdc, 0xff, 0xff, //0x0000539c .long L0_2_set_590
	0x49, 0xdc, 0xff, 0xff, //0x000053a0 .long L0_2_set_592
	0xe8, 0xdd, 0xff, 0xff, //0x000053a4 .long L0_2_set_611
	0xff, 0xdd, 0xff, 0xff, //0x000053a8 .long L0_2_set_613
	0x7d, 0xe0, 0xff, 0xff, //0x000053ac .long L0_2_set_616
	// // .set L0_3_set_839, LBB0_839-LJTI0_3
	// // .set L0_3_set_838, LBB0_838-LJTI0_3
	// // .set L0_3_set_690, LBB0_690-LJTI0_3
	// // .set L0_3_set_711, LBB0_711-LJTI0_3
	// // .set L0_3_set_618, LBB0_618-LJTI0_3
	// // .set L0_3_set_745, LBB0_745-LJTI0_3
	// // .set L0_3_set_747, LBB0_747-LJTI0_3
	// // .set L0_3_set_750, LBB0_750-LJTI0_3
	// // .set L0_3_set_756, LBB0_756-LJTI0_3
	// // .set L0_3_set_759, LBB0_759-LJTI0_3
	//0x000053b0 LJTI0_3
	0xde, 0xef, 0xff, 0xff, //0x000053b0 .long L0_3_set_839
	0xd7, 0xef, 0xff, 0xff, //0x000053b4 .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x000053b8 .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x000053bc .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x000053c0 .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x000053c4 .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x000053c8 .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x000053cc .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x000053d0 .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x000053d4 .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x000053d8 .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x000053dc .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x000053e0 .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x000053e4 .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x000053e8 .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x000053ec .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x000053f0 .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x000053f4 .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x000053f8 .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x000053fc .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x00005400 .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x00005404 .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x00005408 .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x0000540c .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x00005410 .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x00005414 .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x00005418 .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x0000541c .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x00005420 .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x00005424 .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x00005428 .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x0000542c .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x00005430 .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x00005434 .long L0_3_set_838
	0xe3, 0xe2, 0xff, 0xff, //0x00005438 .long L0_3_set_690
	0xd7, 0xef, 0xff, 0xff, //0x0000543c .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x00005440 .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x00005444 .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x00005448 .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x0000544c .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x00005450 .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x00005454 .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x00005458 .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x0000545c .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x00005460 .long L0_3_set_838
	0x70, 0xe4, 0xff, 0xff, //0x00005464 .long L0_3_set_711
	0xd7, 0xef, 0xff, 0xff, //0x00005468 .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x0000546c .long L0_3_set_838
	0x18, 0xde, 0xff, 0xff, //0x00005470 .long L0_3_set_618
	0x18, 0xde, 0xff, 0xff, //0x00005474 .long L0_3_set_618
	0x18, 0xde, 0xff, 0xff, //0x00005478 .long L0_3_set_618
	0x18, 0xde, 0xff, 0xff, //0x0000547c .long L0_3_set_618
	0x18, 0xde, 0xff, 0xff, //0x00005480 .long L0_3_set_618
	0x18, 0xde, 0xff, 0xff, //0x00005484 .long L0_3_set_618
	0x18, 0xde, 0xff, 0xff, //0x00005488 .long L0_3_set_618
	0x18, 0xde, 0xff, 0xff, //0x0000548c .long L0_3_set_618
	0x18, 0xde, 0xff, 0xff, //0x00005490 .long L0_3_set_618
	0x18, 0xde, 0xff, 0xff, //0x00005494 .long L0_3_set_618
	0xd7, 0xef, 0xff, 0xff, //0x00005498 .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x0000549c .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x000054a0 .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x000054a4 .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x000054a8 .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x000054ac .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x000054b0 .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x000054b4 .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x000054b8 .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x000054bc .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x000054c0 .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x000054c4 .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x000054c8 .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x000054cc .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x000054d0 .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x000054d4 .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x000054d8 .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x000054dc .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x000054e0 .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x000054e4 .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x000054e8 .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x000054ec .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x000054f0 .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x000054f4 .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x000054f8 .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x000054fc .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x00005500 .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x00005504 .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x00005508 .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x0000550c .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x00005510 .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x00005514 .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x00005518 .long L0_3_set_838
	0xb6, 0xe6, 0xff, 0xff, //0x0000551c .long L0_3_set_745
	0xd7, 0xef, 0xff, 0xff, //0x00005520 .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x00005524 .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x00005528 .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x0000552c .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x00005530 .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x00005534 .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x00005538 .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x0000553c .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x00005540 .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x00005544 .long L0_3_set_838
	0xda, 0xe6, 0xff, 0xff, //0x00005548 .long L0_3_set_747
	0xd7, 0xef, 0xff, 0xff, //0x0000554c .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x00005550 .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x00005554 .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x00005558 .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x0000555c .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x00005560 .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x00005564 .long L0_3_set_838
	0x08, 0xe7, 0xff, 0xff, //0x00005568 .long L0_3_set_750
	0xd7, 0xef, 0xff, 0xff, //0x0000556c .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x00005570 .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x00005574 .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x00005578 .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x0000557c .long L0_3_set_838
	0x30, 0xe7, 0xff, 0xff, //0x00005580 .long L0_3_set_756
	0xd7, 0xef, 0xff, 0xff, //0x00005584 .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x00005588 .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x0000558c .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x00005590 .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x00005594 .long L0_3_set_838
	0xd7, 0xef, 0xff, 0xff, //0x00005598 .long L0_3_set_838
	0x5c, 0xe7, 0xff, 0xff, //0x0000559c .long L0_3_set_759
	// // .set L0_4_set_736, LBB0_736-LJTI0_4
	// // .set L0_4_set_763, LBB0_763-LJTI0_4
	// // .set L0_4_set_743, LBB0_743-LJTI0_4
	// // .set L0_4_set_738, LBB0_738-LJTI0_4
	// // .set L0_4_set_741, LBB0_741-LJTI0_4
	//0x000055a0 LJTI0_4
	0x49, 0xe4, 0xff, 0xff, //0x000055a0 .long L0_4_set_736
	0xba, 0xe5, 0xff, 0xff, //0x000055a4 .long L0_4_set_763
	0x49, 0xe4, 0xff, 0xff, //0x000055a8 .long L0_4_set_736
	0xa7, 0xe4, 0xff, 0xff, //0x000055ac .long L0_4_set_743
	0xba, 0xe5, 0xff, 0xff, //0x000055b0 .long L0_4_set_763
	0x63, 0xe4, 0xff, 0xff, //0x000055b4 .long L0_4_set_738
	0x63, 0xe4, 0xff, 0xff, //0x000055b8 .long L0_4_set_738
	0x63, 0xe4, 0xff, 0xff, //0x000055bc .long L0_4_set_738
	0x63, 0xe4, 0xff, 0xff, //0x000055c0 .long L0_4_set_738
	0x63, 0xe4, 0xff, 0xff, //0x000055c4 .long L0_4_set_738
	0x63, 0xe4, 0xff, 0xff, //0x000055c8 .long L0_4_set_738
	0x63, 0xe4, 0xff, 0xff, //0x000055cc .long L0_4_set_738
	0x63, 0xe4, 0xff, 0xff, //0x000055d0 .long L0_4_set_738
	0x63, 0xe4, 0xff, 0xff, //0x000055d4 .long L0_4_set_738
	0x63, 0xe4, 0xff, 0xff, //0x000055d8 .long L0_4_set_738
	0xba, 0xe5, 0xff, 0xff, //0x000055dc .long L0_4_set_763
	0xba, 0xe5, 0xff, 0xff, //0x000055e0 .long L0_4_set_763
	0xba, 0xe5, 0xff, 0xff, //0x000055e4 .long L0_4_set_763
	0xba, 0xe5, 0xff, 0xff, //0x000055e8 .long L0_4_set_763
	0xba, 0xe5, 0xff, 0xff, //0x000055ec .long L0_4_set_763
	0xba, 0xe5, 0xff, 0xff, //0x000055f0 .long L0_4_set_763
	0xba, 0xe5, 0xff, 0xff, //0x000055f4 .long L0_4_set_763
	0xba, 0xe5, 0xff, 0xff, //0x000055f8 .long L0_4_set_763
	0xba, 0xe5, 0xff, 0xff, //0x000055fc .long L0_4_set_763
	0xba, 0xe5, 0xff, 0xff, //0x00005600 .long L0_4_set_763
	0xba, 0xe5, 0xff, 0xff, //0x00005604 .long L0_4_set_763
	0x8c, 0xe4, 0xff, 0xff, //0x00005608 .long L0_4_set_741
	0xba, 0xe5, 0xff, 0xff, //0x0000560c .long L0_4_set_763
	0xba, 0xe5, 0xff, 0xff, //0x00005610 .long L0_4_set_763
	0xba, 0xe5, 0xff, 0xff, //0x00005614 .long L0_4_set_763
	0xba, 0xe5, 0xff, 0xff, //0x00005618 .long L0_4_set_763
	0xba, 0xe5, 0xff, 0xff, //0x0000561c .long L0_4_set_763
	0xba, 0xe5, 0xff, 0xff, //0x00005620 .long L0_4_set_763
	0xba, 0xe5, 0xff, 0xff, //0x00005624 .long L0_4_set_763
	0xba, 0xe5, 0xff, 0xff, //0x00005628 .long L0_4_set_763
	0xba, 0xe5, 0xff, 0xff, //0x0000562c .long L0_4_set_763
	0xba, 0xe5, 0xff, 0xff, //0x00005630 .long L0_4_set_763
	0xba, 0xe5, 0xff, 0xff, //0x00005634 .long L0_4_set_763
	0xba, 0xe5, 0xff, 0xff, //0x00005638 .long L0_4_set_763
	0xba, 0xe5, 0xff, 0xff, //0x0000563c .long L0_4_set_763
	0xba, 0xe5, 0xff, 0xff, //0x00005640 .long L0_4_set_763
	0xba, 0xe5, 0xff, 0xff, //0x00005644 .long L0_4_set_763
	0xba, 0xe5, 0xff, 0xff, //0x00005648 .long L0_4_set_763
	0xba, 0xe5, 0xff, 0xff, //0x0000564c .long L0_4_set_763
	0xba, 0xe5, 0xff, 0xff, //0x00005650 .long L0_4_set_763
	0xba, 0xe5, 0xff, 0xff, //0x00005654 .long L0_4_set_763
	0xba, 0xe5, 0xff, 0xff, //0x00005658 .long L0_4_set_763
	0xba, 0xe5, 0xff, 0xff, //0x0000565c .long L0_4_set_763
	0xba, 0xe5, 0xff, 0xff, //0x00005660 .long L0_4_set_763
	0xba, 0xe5, 0xff, 0xff, //0x00005664 .long L0_4_set_763
	0xba, 0xe5, 0xff, 0xff, //0x00005668 .long L0_4_set_763
	0xba, 0xe5, 0xff, 0xff, //0x0000566c .long L0_4_set_763
	0xba, 0xe5, 0xff, 0xff, //0x00005670 .long L0_4_set_763
	0xba, 0xe5, 0xff, 0xff, //0x00005674 .long L0_4_set_763
	0xba, 0xe5, 0xff, 0xff, //0x00005678 .long L0_4_set_763
	0xba, 0xe5, 0xff, 0xff, //0x0000567c .long L0_4_set_763
	0xba, 0xe5, 0xff, 0xff, //0x00005680 .long L0_4_set_763
	0xba, 0xe5, 0xff, 0xff, //0x00005684 .long L0_4_set_763
	0x8c, 0xe4, 0xff, 0xff, //0x00005688 .long L0_4_set_741
	// // .set L0_5_set_646, LBB0_646-LJTI0_5
	// // .set L0_5_set_674, LBB0_674-LJTI0_5
	// // .set L0_5_set_650, LBB0_650-LJTI0_5
	// // .set L0_5_set_643, LBB0_643-LJTI0_5
	// // .set L0_5_set_648, LBB0_648-LJTI0_5
	//0x0000568c LJTI0_5
	0x34, 0xdd, 0xff, 0xff, //0x0000568c .long L0_5_set_646
	0x54, 0xdf, 0xff, 0xff, //0x00005690 .long L0_5_set_674
	0x34, 0xdd, 0xff, 0xff, //0x00005694 .long L0_5_set_646
	0x6a, 0xdd, 0xff, 0xff, //0x00005698 .long L0_5_set_650
	0x54, 0xdf, 0xff, 0xff, //0x0000569c .long L0_5_set_674
	0x04, 0xdd, 0xff, 0xff, //0x000056a0 .long L0_5_set_643
	0x04, 0xdd, 0xff, 0xff, //0x000056a4 .long L0_5_set_643
	0x04, 0xdd, 0xff, 0xff, //0x000056a8 .long L0_5_set_643
	0x04, 0xdd, 0xff, 0xff, //0x000056ac .long L0_5_set_643
	0x04, 0xdd, 0xff, 0xff, //0x000056b0 .long L0_5_set_643
	0x04, 0xdd, 0xff, 0xff, //0x000056b4 .long L0_5_set_643
	0x04, 0xdd, 0xff, 0xff, //0x000056b8 .long L0_5_set_643
	0x04, 0xdd, 0xff, 0xff, //0x000056bc .long L0_5_set_643
	0x04, 0xdd, 0xff, 0xff, //0x000056c0 .long L0_5_set_643
	0x04, 0xdd, 0xff, 0xff, //0x000056c4 .long L0_5_set_643
	0x54, 0xdf, 0xff, 0xff, //0x000056c8 .long L0_5_set_674
	0x54, 0xdf, 0xff, 0xff, //0x000056cc .long L0_5_set_674
	0x54, 0xdf, 0xff, 0xff, //0x000056d0 .long L0_5_set_674
	0x54, 0xdf, 0xff, 0xff, //0x000056d4 .long L0_5_set_674
	0x54, 0xdf, 0xff, 0xff, //0x000056d8 .long L0_5_set_674
	0x54, 0xdf, 0xff, 0xff, //0x000056dc .long L0_5_set_674
	0x54, 0xdf, 0xff, 0xff, //0x000056e0 .long L0_5_set_674
	0x54, 0xdf, 0xff, 0xff, //0x000056e4 .long L0_5_set_674
	0x54, 0xdf, 0xff, 0xff, //0x000056e8 .long L0_5_set_674
	0x54, 0xdf, 0xff, 0xff, //0x000056ec .long L0_5_set_674
	0x54, 0xdf, 0xff, 0xff, //0x000056f0 .long L0_5_set_674
	0x4f, 0xdd, 0xff, 0xff, //0x000056f4 .long L0_5_set_648
	0x54, 0xdf, 0xff, 0xff, //0x000056f8 .long L0_5_set_674
	0x54, 0xdf, 0xff, 0xff, //0x000056fc .long L0_5_set_674
	0x54, 0xdf, 0xff, 0xff, //0x00005700 .long L0_5_set_674
	0x54, 0xdf, 0xff, 0xff, //0x00005704 .long L0_5_set_674
	0x54, 0xdf, 0xff, 0xff, //0x00005708 .long L0_5_set_674
	0x54, 0xdf, 0xff, 0xff, //0x0000570c .long L0_5_set_674
	0x54, 0xdf, 0xff, 0xff, //0x00005710 .long L0_5_set_674
	0x54, 0xdf, 0xff, 0xff, //0x00005714 .long L0_5_set_674
	0x54, 0xdf, 0xff, 0xff, //0x00005718 .long L0_5_set_674
	0x54, 0xdf, 0xff, 0xff, //0x0000571c .long L0_5_set_674
	0x54, 0xdf, 0xff, 0xff, //0x00005720 .long L0_5_set_674
	0x54, 0xdf, 0xff, 0xff, //0x00005724 .long L0_5_set_674
	0x54, 0xdf, 0xff, 0xff, //0x00005728 .long L0_5_set_674
	0x54, 0xdf, 0xff, 0xff, //0x0000572c .long L0_5_set_674
	0x54, 0xdf, 0xff, 0xff, //0x00005730 .long L0_5_set_674
	0x54, 0xdf, 0xff, 0xff, //0x00005734 .long L0_5_set_674
	0x54, 0xdf, 0xff, 0xff, //0x00005738 .long L0_5_set_674
	0x54, 0xdf, 0xff, 0xff, //0x0000573c .long L0_5_set_674
	0x54, 0xdf, 0xff, 0xff, //0x00005740 .long L0_5_set_674
	0x54, 0xdf, 0xff, 0xff, //0x00005744 .long L0_5_set_674
	0x54, 0xdf, 0xff, 0xff, //0x00005748 .long L0_5_set_674
	0x54, 0xdf, 0xff, 0xff, //0x0000574c .long L0_5_set_674
	0x54, 0xdf, 0xff, 0xff, //0x00005750 .long L0_5_set_674
	0x54, 0xdf, 0xff, 0xff, //0x00005754 .long L0_5_set_674
	0x54, 0xdf, 0xff, 0xff, //0x00005758 .long L0_5_set_674
	0x54, 0xdf, 0xff, 0xff, //0x0000575c .long L0_5_set_674
	0x54, 0xdf, 0xff, 0xff, //0x00005760 .long L0_5_set_674
	0x54, 0xdf, 0xff, 0xff, //0x00005764 .long L0_5_set_674
	0x54, 0xdf, 0xff, 0xff, //0x00005768 .long L0_5_set_674
	0x54, 0xdf, 0xff, 0xff, //0x0000576c .long L0_5_set_674
	0x54, 0xdf, 0xff, 0xff, //0x00005770 .long L0_5_set_674
	0x4f, 0xdd, 0xff, 0xff, //0x00005774 .long L0_5_set_648
	// // .set L0_6_set_839, LBB0_839-LJTI0_6
	// // .set L0_6_set_866, LBB0_866-LJTI0_6
	// // .set L0_6_set_867, LBB0_867-LJTI0_6
	// // .set L0_6_set_845, LBB0_845-LJTI0_6
	// // .set L0_6_set_878, LBB0_878-LJTI0_6
	// // .set L0_6_set_902, LBB0_902-LJTI0_6
	// // .set L0_6_set_864, LBB0_864-LJTI0_6
	// // .set L0_6_set_905, LBB0_905-LJTI0_6
	//0x00005778 LJTI0_6
	0x16, 0xec, 0xff, 0xff, //0x00005778 .long L0_6_set_839
	0xc7, 0xed, 0xff, 0xff, //0x0000577c .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x00005780 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x00005784 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x00005788 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x0000578c .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x00005790 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x00005794 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x00005798 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x0000579c .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x000057a0 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x000057a4 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x000057a8 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x000057ac .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x000057b0 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x000057b4 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x000057b8 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x000057bc .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x000057c0 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x000057c4 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x000057c8 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x000057cc .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x000057d0 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x000057d4 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x000057d8 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x000057dc .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x000057e0 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x000057e4 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x000057e8 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x000057ec .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x000057f0 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x000057f4 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x000057f8 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x000057fc .long L0_6_set_866
	0xcf, 0xed, 0xff, 0xff, //0x00005800 .long L0_6_set_867
	0xc7, 0xed, 0xff, 0xff, //0x00005804 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x00005808 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x0000580c .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x00005810 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x00005814 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x00005818 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x0000581c .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x00005820 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x00005824 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x00005828 .long L0_6_set_866
	0x9b, 0xec, 0xff, 0xff, //0x0000582c .long L0_6_set_845
	0xc7, 0xed, 0xff, 0xff, //0x00005830 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x00005834 .long L0_6_set_866
	0x9b, 0xec, 0xff, 0xff, //0x00005838 .long L0_6_set_845
	0x9b, 0xec, 0xff, 0xff, //0x0000583c .long L0_6_set_845
	0x9b, 0xec, 0xff, 0xff, //0x00005840 .long L0_6_set_845
	0x9b, 0xec, 0xff, 0xff, //0x00005844 .long L0_6_set_845
	0x9b, 0xec, 0xff, 0xff, //0x00005848 .long L0_6_set_845
	0x9b, 0xec, 0xff, 0xff, //0x0000584c .long L0_6_set_845
	0x9b, 0xec, 0xff, 0xff, //0x00005850 .long L0_6_set_845
	0x9b, 0xec, 0xff, 0xff, //0x00005854 .long L0_6_set_845
	0x9b, 0xec, 0xff, 0xff, //0x00005858 .long L0_6_set_845
	0x9b, 0xec, 0xff, 0xff, //0x0000585c .long L0_6_set_845
	0xc7, 0xed, 0xff, 0xff, //0x00005860 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x00005864 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x00005868 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x0000586c .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x00005870 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x00005874 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x00005878 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x0000587c .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x00005880 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x00005884 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x00005888 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x0000588c .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x00005890 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x00005894 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x00005898 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x0000589c .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x000058a0 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x000058a4 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x000058a8 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x000058ac .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x000058b0 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x000058b4 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x000058b8 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x000058bc .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x000058c0 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x000058c4 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x000058c8 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x000058cc .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x000058d0 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x000058d4 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x000058d8 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x000058dc .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x000058e0 .long L0_6_set_866
	0xdf, 0xee, 0xff, 0xff, //0x000058e4 .long L0_6_set_878
	0xc7, 0xed, 0xff, 0xff, //0x000058e8 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x000058ec .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x000058f0 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x000058f4 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x000058f8 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x000058fc .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x00005900 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x00005904 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x00005908 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x0000590c .long L0_6_set_866
	0x14, 0xf2, 0xff, 0xff, //0x00005910 .long L0_6_set_902
	0xc7, 0xed, 0xff, 0xff, //0x00005914 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x00005918 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x0000591c .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x00005920 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x00005924 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x00005928 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x0000592c .long L0_6_set_866
	0xb1, 0xed, 0xff, 0xff, //0x00005930 .long L0_6_set_864
	0xc7, 0xed, 0xff, 0xff, //0x00005934 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x00005938 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x0000593c .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x00005940 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x00005944 .long L0_6_set_866
	0xb1, 0xed, 0xff, 0xff, //0x00005948 .long L0_6_set_864
	0xc7, 0xed, 0xff, 0xff, //0x0000594c .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x00005950 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x00005954 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x00005958 .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x0000595c .long L0_6_set_866
	0xc7, 0xed, 0xff, 0xff, //0x00005960 .long L0_6_set_866
	0x33, 0xf2, 0xff, 0xff, //0x00005964 .long L0_6_set_905
	//0x00005968 .p2align 2, 0x00
	//0x00005968 _MASK_USE_NUMBER
	0x02, 0x00, 0x00, 0x00, //0x00005968 .long 2
	0x00, 0x00, 0x00, 0x00, //0x0000596c .p2align 4, 0x00
	//0x00005970 __UnquoteTab
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005970 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .ascii 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005980 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .ascii 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x22, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2f, //0x00005990 QUAD $0x0000000000220000; QUAD $0x2f00000000000000  // .ascii 16, '\x00\x00"\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00/'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000059a0 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .ascii 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000059b0 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .ascii 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5c, 0x00, 0x00, 0x00, //0x000059c0 QUAD $0x0000000000000000; QUAD $0x0000005c00000000  // .ascii 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\\\x00\x00\x00'
	0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0a, 0x00, //0x000059d0 QUAD $0x000c000000080000; QUAD $0x000a000000000000  // .ascii 16, '\x00\x00\x08\x00\x00\x00\x0c\x00\x00\x00\x00\x00\x00\x00\n\x00'
	0x00, 0x00, 0x0d, 0x00, 0x09, 0xff, //0x000059e0 LONG $0x000d0000; WORD $0xff09  // .ascii 6, '\x00\x00\r\x00\t\xff'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000059e6 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000059f6 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005a06 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005a16 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005a26 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005a36 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005a46 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005a56 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005a66 QUAD $0x0000000000000000; WORD $0x0000  // .space 10, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
}
 
