// +build !noasm !appengine
// Code generated by asm2asm, DO NOT EDIT.

package avx

import (
	`github.com/bytedance/sonic/loader`
)

const (
    _entry__value = 192
)

const (
    _stack__value = 112
)

const (
    _size__value = 12816
)

var (
    _pcsp__value = [][2]uint32{
        {1, 0},
        {4, 8},
        {6, 16},
        {8, 24},
        {10, 32},
        {12, 40},
        {13, 48},
        {556, 112},
        {560, 48},
        {561, 40},
        {563, 32},
        {565, 24},
        {567, 16},
        {569, 8},
        {573, 0},
        {12816, 112},
    }
)

var _cfunc_value = []loader.CFunc{
    {"_value_entry", 0,  _entry__value, 0, nil},
    {"_value", _entry__value, _size__value, _stack__value, _pcsp__value},
}
