// +build amd64
// Code generated by asm2asm, DO NOT EDIT.

package avx

var _text_validate_utf8_fast = []byte{
	// .p2align 4, 0x90
	// _validate_utf8_fast
	0x55, // pushq        %rbp
	0x48, 0x89, 0xe5, //0x00000001 movq         %rsp, %rbp
	0x53, //0x00000004 pushq        %rbx
	0x50, //0x00000005 pushq        %rax
	0x4c, 0x8b, 0x17, //0x00000006 movq         (%rdi), %r10
	0x4c, 0x8b, 0x5f, 0x08, //0x00000009 movq         $8(%rdi), %r11
	0x4b, 0x8d, 0x74, 0x1a, 0xfd, //0x0000000d leaq         $-3(%r10,%r11), %rsi
	0x4c, 0x89, 0xd0, //0x00000012 movq         %r10, %rax
	0x49, 0x39, 0xf2, //0x00000015 cmpq         %rsi, %r10
	0x0f, 0x83, 0xe0, 0x00, 0x00, 0x00, //0x00000018 jae          LBB0_14
	0x4c, 0x89, 0xd0, //0x0000001e movq         %r10, %rax
	0xe9, 0x16, 0x00, 0x00, 0x00, //0x00000021 jmp          LBB0_3
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000026 .p2align 4, 0x90
	//0x00000030 LBB0_2
	0x48, 0x01, 0xd0, //0x00000030 addq         %rdx, %rax
	0x48, 0x39, 0xf0, //0x00000033 cmpq         %rsi, %rax
	0x0f, 0x83, 0xc2, 0x00, 0x00, 0x00, //0x00000036 jae          LBB0_14
	//0x0000003c LBB0_3
	0xba, 0x01, 0x00, 0x00, 0x00, //0x0000003c movl         $1, %edx
	0x80, 0x38, 0x00, //0x00000041 cmpb         $0, (%rax)
	0x0f, 0x89, 0xe6, 0xff, 0xff, 0xff, //0x00000044 jns          LBB0_2
	0x8b, 0x38, //0x0000004a movl         (%rax), %edi
	0x89, 0xf9, //0x0000004c movl         %edi, %ecx
	0x81, 0xe1, 0xf0, 0xc0, 0xc0, 0x00, //0x0000004e andl         $12632304, %ecx
	0x81, 0xf9, 0xe0, 0x80, 0x80, 0x00, //0x00000054 cmpl         $8421600, %ecx
	0x0f, 0x85, 0x30, 0x00, 0x00, 0x00, //0x0000005a jne          LBB0_7
	0x89, 0xf9, //0x00000060 movl         %edi, %ecx
	0x81, 0xe1, 0x0f, 0x20, 0x00, 0x00, //0x00000062 andl         $8207, %ecx
	0x81, 0xf9, 0x0d, 0x20, 0x00, 0x00, //0x00000068 cmpl         $8205, %ecx
	0x0f, 0x84, 0x1c, 0x00, 0x00, 0x00, //0x0000006e je           LBB0_7
	0xba, 0x03, 0x00, 0x00, 0x00, //0x00000074 movl         $3, %edx
	0x85, 0xc9, //0x00000079 testl        %ecx, %ecx
	0x0f, 0x85, 0xaf, 0xff, 0xff, 0xff, //0x0000007b jne          LBB0_2
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000081 .p2align 4, 0x90
	//0x00000090 LBB0_7
	0x89, 0xf9, //0x00000090 movl         %edi, %ecx
	0x81, 0xe1, 0xe0, 0xc0, 0x00, 0x00, //0x00000092 andl         $49376, %ecx
	0x81, 0xf9, 0xc0, 0x80, 0x00, 0x00, //0x00000098 cmpl         $32960, %ecx
	0x0f, 0x85, 0x10, 0x00, 0x00, 0x00, //0x0000009e jne          LBB0_9
	0x89, 0xf9, //0x000000a4 movl         %edi, %ecx
	0xba, 0x02, 0x00, 0x00, 0x00, //0x000000a6 movl         $2, %edx
	0x83, 0xe1, 0x1e, //0x000000ab andl         $30, %ecx
	0x0f, 0x85, 0x7c, 0xff, 0xff, 0xff, //0x000000ae jne          LBB0_2
	//0x000000b4 LBB0_9
	0x89, 0xf9, //0x000000b4 movl         %edi, %ecx
	0x81, 0xe1, 0xf8, 0xc0, 0xc0, 0xc0, //0x000000b6 andl         $-1061109512, %ecx
	0x81, 0xf9, 0xf0, 0x80, 0x80, 0x80, //0x000000bc cmpl         $-2139062032, %ecx
	0x0f, 0x85, 0x29, 0x00, 0x00, 0x00, //0x000000c2 jne          LBB0_13
	0x89, 0xf9, //0x000000c8 movl         %edi, %ecx
	0x81, 0xe1, 0x07, 0x30, 0x00, 0x00, //0x000000ca andl         $12295, %ecx
	0x0f, 0x84, 0x1b, 0x00, 0x00, 0x00, //0x000000d0 je           LBB0_13
	0xba, 0x04, 0x00, 0x00, 0x00, //0x000000d6 movl         $4, %edx
	0x40, 0xf6, 0xc7, 0x04, //0x000000db testb        $4, %dil
	0x0f, 0x84, 0x4b, 0xff, 0xff, 0xff, //0x000000df je           LBB0_2
	0x81, 0xe7, 0x03, 0x30, 0x00, 0x00, //0x000000e5 andl         $12291, %edi
	0x0f, 0x84, 0x3f, 0xff, 0xff, 0xff, //0x000000eb je           LBB0_2
	//0x000000f1 LBB0_13
	0x48, 0xf7, 0xd0, //0x000000f1 notq         %rax
	0x4c, 0x01, 0xd0, //0x000000f4 addq         %r10, %rax
	0x48, 0x83, 0xc4, 0x08, //0x000000f7 addq         $8, %rsp
	0x5b, //0x000000fb popq         %rbx
	0x5d, //0x000000fc popq         %rbp
	0xc3, //0x000000fd retq         
	//0x000000fe LBB0_14
	0x4d, 0x01, 0xd3, //0x000000fe addq         %r10, %r11
	0x4c, 0x39, 0xd8, //0x00000101 cmpq         %r11, %rax
	0x0f, 0x83, 0x03, 0x01, 0x00, 0x00, //0x00000104 jae          LBB0_30
	0x4c, 0x8d, 0x45, 0xf4, //0x0000010a leaq         $-12(%rbp), %r8
	0x4c, 0x8d, 0x4d, 0xf2, //0x0000010e leaq         $-14(%rbp), %r9
	0xe9, 0x15, 0x00, 0x00, 0x00, //0x00000112 jmp          LBB0_17
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000117 .p2align 4, 0x90
	//0x00000120 LBB0_16
	0x48, 0xff, 0xc0, //0x00000120 incq         %rax
	0x4c, 0x39, 0xd8, //0x00000123 cmpq         %r11, %rax
	0x0f, 0x83, 0xe1, 0x00, 0x00, 0x00, //0x00000126 jae          LBB0_30
	//0x0000012c LBB0_17
	0x80, 0x38, 0x00, //0x0000012c cmpb         $0, (%rax)
	0x0f, 0x89, 0xeb, 0xff, 0xff, 0xff, //0x0000012f jns          LBB0_16
	0xc6, 0x45, 0xf4, 0x00, //0x00000135 movb         $0, $-12(%rbp)
	0xc6, 0x45, 0xf2, 0x00, //0x00000139 movb         $0, $-14(%rbp)
	0x4c, 0x89, 0xda, //0x0000013d movq         %r11, %rdx
	0x48, 0x29, 0xc2, //0x00000140 subq         %rax, %rdx
	0x48, 0x83, 0xfa, 0x02, //0x00000143 cmpq         $2, %rdx
	0x0f, 0x82, 0x31, 0x00, 0x00, 0x00, //0x00000147 jb           LBB0_21
	0x0f, 0xb6, 0x30, //0x0000014d movzbl       (%rax), %esi
	0x0f, 0xb6, 0x78, 0x01, //0x00000150 movzbl       $1(%rax), %edi
	0x40, 0x88, 0x75, 0xf4, //0x00000154 movb         %sil, $-12(%rbp)
	0x48, 0x8d, 0x48, 0x02, //0x00000158 leaq         $2(%rax), %rcx
	0x48, 0x83, 0xc2, 0xfe, //0x0000015c addq         $-2, %rdx
	0x4c, 0x89, 0xcb, //0x00000160 movq         %r9, %rbx
	0x48, 0x85, 0xd2, //0x00000163 testq        %rdx, %rdx
	0x0f, 0x84, 0x25, 0x00, 0x00, 0x00, //0x00000166 je           LBB0_22
	//0x0000016c LBB0_20
	0x0f, 0xb6, 0x09, //0x0000016c movzbl       (%rcx), %ecx
	0x88, 0x0b, //0x0000016f movb         %cl, (%rbx)
	0x0f, 0xb6, 0x75, 0xf4, //0x00000171 movzbl       $-12(%rbp), %esi
	0x0f, 0xb6, 0x4d, 0xf2, //0x00000175 movzbl       $-14(%rbp), %ecx
	0xe9, 0x15, 0x00, 0x00, 0x00, //0x00000179 jmp          LBB0_23
	//0x0000017e LBB0_21
	0x31, 0xf6, //0x0000017e xorl         %esi, %esi
	0x31, 0xff, //0x00000180 xorl         %edi, %edi
	0x4c, 0x89, 0xc3, //0x00000182 movq         %r8, %rbx
	0x48, 0x89, 0xc1, //0x00000185 movq         %rax, %rcx
	0x48, 0x85, 0xd2, //0x00000188 testq        %rdx, %rdx
	0x0f, 0x85, 0xdb, 0xff, 0xff, 0xff, //0x0000018b jne          LBB0_20
	//0x00000191 LBB0_22
	0x31, 0xc9, //0x00000191 xorl         %ecx, %ecx
	//0x00000193 LBB0_23
	0x0f, 0xb6, 0xc9, //0x00000193 movzbl       %cl, %ecx
	0xc1, 0xe1, 0x10, //0x00000196 shll         $16, %ecx
	0x40, 0x0f, 0xb6, 0xff, //0x00000199 movzbl       %dil, %edi
	0xc1, 0xe7, 0x08, //0x0000019d shll         $8, %edi
	0x40, 0x0f, 0xb6, 0xd6, //0x000001a0 movzbl       %sil, %edx
	0x09, 0xfa, //0x000001a4 orl          %edi, %edx
	0x09, 0xd1, //0x000001a6 orl          %edx, %ecx
	0x81, 0xe1, 0xf0, 0xc0, 0xc0, 0x00, //0x000001a8 andl         $12632304, %ecx
	0x81, 0xf9, 0xe0, 0x80, 0x80, 0x00, //0x000001ae cmpl         $8421600, %ecx
	0x0f, 0x85, 0x26, 0x00, 0x00, 0x00, //0x000001b4 jne          LBB0_26
	0x89, 0xd7, //0x000001ba movl         %edx, %edi
	0x81, 0xe7, 0x0f, 0x20, 0x00, 0x00, //0x000001bc andl         $8207, %edi
	0x81, 0xff, 0x0d, 0x20, 0x00, 0x00, //0x000001c2 cmpl         $8205, %edi
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x000001c8 je           LBB0_26
	0xb9, 0x03, 0x00, 0x00, 0x00, //0x000001ce movl         $3, %ecx
	0x85, 0xff, //0x000001d3 testl        %edi, %edi
	0x0f, 0x85, 0x26, 0x00, 0x00, 0x00, //0x000001d5 jne          LBB0_28
	0x90, 0x90, 0x90, 0x90, 0x90, //0x000001db .p2align 4, 0x90
	//0x000001e0 LBB0_26
	0x40, 0xf6, 0xc6, 0x1e, //0x000001e0 testb        $30, %sil
	0x0f, 0x84, 0x07, 0xff, 0xff, 0xff, //0x000001e4 je           LBB0_13
	0x81, 0xe2, 0xe0, 0xc0, 0x00, 0x00, //0x000001ea andl         $49376, %edx
	0xb9, 0x02, 0x00, 0x00, 0x00, //0x000001f0 movl         $2, %ecx
	0x81, 0xfa, 0xc0, 0x80, 0x00, 0x00, //0x000001f5 cmpl         $32960, %edx
	0x0f, 0x85, 0xf0, 0xfe, 0xff, 0xff, //0x000001fb jne          LBB0_13
	//0x00000201 LBB0_28
	0x48, 0x01, 0xc8, //0x00000201 addq         %rcx, %rax
	0x4c, 0x39, 0xd8, //0x00000204 cmpq         %r11, %rax
	0x0f, 0x82, 0x1f, 0xff, 0xff, 0xff, //0x00000207 jb           LBB0_17
	//0x0000020d LBB0_30
	0x31, 0xc0, //0x0000020d xorl         %eax, %eax
	0x48, 0x83, 0xc4, 0x08, //0x0000020f addq         $8, %rsp
	0x5b, //0x00000213 popq         %rbx
	0x5d, //0x00000214 popq         %rbp
	0xc3, //0x00000215 retq         
	0x00, 0x00, //0x00000216 .p2align 2, 0x00
	//0x00000218 _MASK_USE_NUMBER
	0x02, 0x00, 0x00, 0x00, //0x00000218 .long 2
}
 
