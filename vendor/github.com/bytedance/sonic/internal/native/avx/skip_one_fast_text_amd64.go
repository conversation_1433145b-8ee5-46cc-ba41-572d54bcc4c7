// +build amd64
// Code generated by asm2asm, DO NOT EDIT.

package avx

var _text_skip_one_fast = []byte{
	// .p2align 4, 0x00
	// LCPI0_0
	0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, // QUAD $0x2c2c2c2c2c2c2c2c; QUAD $0x2c2c2c2c2c2c2c2c  // .space 16, ',,,,,,,,,,,,,,,,'
	//0x00000010 LCPI0_1
	0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, //0x00000010 QUAD $0x2020202020202020; QUAD $0x2020202020202020  // .space 16, '                '
	//0x00000020 LCPI0_2
	0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, //0x00000020 QUAD $0x7d7d7d7d7d7d7d7d; QUAD $0x7d7d7d7d7d7d7d7d  // .space 16, '}}}}}}}}}}}}}}}}'
	//0x00000030 LCPI0_3
	0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, //0x00000030 QUAD $0x2222222222222222; QUAD $0x2222222222222222  // .space 16, '""""""""""""""""'
	//0x00000040 LCPI0_4
	0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, //0x00000040 QUAD $0x5c5c5c5c5c5c5c5c; QUAD $0x5c5c5c5c5c5c5c5c  // .space 16, '\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\'
	//0x00000050 LCPI0_5
	0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, //0x00000050 QUAD $0x7b7b7b7b7b7b7b7b; QUAD $0x7b7b7b7b7b7b7b7b  // .space 16, '{{{{{{{{{{{{{{{{'
	//0x00000060 LCPI0_6
	0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, //0x00000060 QUAD $0x5b5b5b5b5b5b5b5b; QUAD $0x5b5b5b5b5b5b5b5b  // .space 16, '[[[[[[[[[[[[[[[['
	//0x00000070 LCPI0_7
	0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, //0x00000070 QUAD $0x5d5d5d5d5d5d5d5d; QUAD $0x5d5d5d5d5d5d5d5d  // .space 16, ']]]]]]]]]]]]]]]]'
	//0x00000080 .p2align 4, 0x90
	//0x00000080 _skip_one_fast
	0x55, //0x00000080 pushq        %rbp
	0x48, 0x89, 0xe5, //0x00000081 movq         %rsp, %rbp
	0x41, 0x57, //0x00000084 pushq        %r15
	0x41, 0x56, //0x00000086 pushq        %r14
	0x41, 0x55, //0x00000088 pushq        %r13
	0x41, 0x54, //0x0000008a pushq        %r12
	0x53, //0x0000008c pushq        %rbx
	0x48, 0x81, 0xec, 0x80, 0x00, 0x00, 0x00, //0x0000008d subq         $128, %rsp
	0x4c, 0x8b, 0x37, //0x00000094 movq         (%rdi), %r14
	0x4c, 0x8b, 0x47, 0x08, //0x00000097 movq         $8(%rdi), %r8
	0x48, 0x8b, 0x16, //0x0000009b movq         (%rsi), %rdx
	0x48, 0x89, 0xd0, //0x0000009e movq         %rdx, %rax
	0x4c, 0x29, 0xc0, //0x000000a1 subq         %r8, %rax
	0x0f, 0x83, 0x2a, 0x00, 0x00, 0x00, //0x000000a4 jae          LBB0_5
	0x41, 0x8a, 0x0c, 0x16, //0x000000aa movb         (%r14,%rdx), %cl
	0x80, 0xf9, 0x0d, //0x000000ae cmpb         $13, %cl
	0x0f, 0x84, 0x1d, 0x00, 0x00, 0x00, //0x000000b1 je           LBB0_5
	0x80, 0xf9, 0x20, //0x000000b7 cmpb         $32, %cl
	0x0f, 0x84, 0x14, 0x00, 0x00, 0x00, //0x000000ba je           LBB0_5
	0x80, 0xc1, 0xf7, //0x000000c0 addb         $-9, %cl
	0x80, 0xf9, 0x01, //0x000000c3 cmpb         $1, %cl
	0x0f, 0x86, 0x08, 0x00, 0x00, 0x00, //0x000000c6 jbe          LBB0_5
	0x49, 0x89, 0xd3, //0x000000cc movq         %rdx, %r11
	0xe9, 0x2b, 0x01, 0x00, 0x00, //0x000000cf jmp          LBB0_27
	//0x000000d4 LBB0_5
	0x4c, 0x8d, 0x5a, 0x01, //0x000000d4 leaq         $1(%rdx), %r11
	0x4d, 0x39, 0xc3, //0x000000d8 cmpq         %r8, %r11
	0x0f, 0x83, 0x22, 0x00, 0x00, 0x00, //0x000000db jae          LBB0_9
	0x43, 0x8a, 0x0c, 0x1e, //0x000000e1 movb         (%r14,%r11), %cl
	0x80, 0xf9, 0x0d, //0x000000e5 cmpb         $13, %cl
	0x0f, 0x84, 0x15, 0x00, 0x00, 0x00, //0x000000e8 je           LBB0_9
	0x80, 0xf9, 0x20, //0x000000ee cmpb         $32, %cl
	0x0f, 0x84, 0x0c, 0x00, 0x00, 0x00, //0x000000f1 je           LBB0_9
	0x80, 0xc1, 0xf7, //0x000000f7 addb         $-9, %cl
	0x80, 0xf9, 0x01, //0x000000fa cmpb         $1, %cl
	0x0f, 0x87, 0xfc, 0x00, 0x00, 0x00, //0x000000fd ja           LBB0_27
	//0x00000103 LBB0_9
	0x4c, 0x8d, 0x5a, 0x02, //0x00000103 leaq         $2(%rdx), %r11
	0x4d, 0x39, 0xc3, //0x00000107 cmpq         %r8, %r11
	0x0f, 0x83, 0x22, 0x00, 0x00, 0x00, //0x0000010a jae          LBB0_13
	0x43, 0x8a, 0x0c, 0x1e, //0x00000110 movb         (%r14,%r11), %cl
	0x80, 0xf9, 0x0d, //0x00000114 cmpb         $13, %cl
	0x0f, 0x84, 0x15, 0x00, 0x00, 0x00, //0x00000117 je           LBB0_13
	0x80, 0xf9, 0x20, //0x0000011d cmpb         $32, %cl
	0x0f, 0x84, 0x0c, 0x00, 0x00, 0x00, //0x00000120 je           LBB0_13
	0x80, 0xc1, 0xf7, //0x00000126 addb         $-9, %cl
	0x80, 0xf9, 0x01, //0x00000129 cmpb         $1, %cl
	0x0f, 0x87, 0xcd, 0x00, 0x00, 0x00, //0x0000012c ja           LBB0_27
	//0x00000132 LBB0_13
	0x4c, 0x8d, 0x5a, 0x03, //0x00000132 leaq         $3(%rdx), %r11
	0x4d, 0x39, 0xc3, //0x00000136 cmpq         %r8, %r11
	0x0f, 0x83, 0x22, 0x00, 0x00, 0x00, //0x00000139 jae          LBB0_17
	0x43, 0x8a, 0x0c, 0x1e, //0x0000013f movb         (%r14,%r11), %cl
	0x80, 0xf9, 0x0d, //0x00000143 cmpb         $13, %cl
	0x0f, 0x84, 0x15, 0x00, 0x00, 0x00, //0x00000146 je           LBB0_17
	0x80, 0xf9, 0x20, //0x0000014c cmpb         $32, %cl
	0x0f, 0x84, 0x0c, 0x00, 0x00, 0x00, //0x0000014f je           LBB0_17
	0x80, 0xc1, 0xf7, //0x00000155 addb         $-9, %cl
	0x80, 0xf9, 0x01, //0x00000158 cmpb         $1, %cl
	0x0f, 0x87, 0x9e, 0x00, 0x00, 0x00, //0x0000015b ja           LBB0_27
	//0x00000161 LBB0_17
	0x48, 0x8d, 0x4a, 0x04, //0x00000161 leaq         $4(%rdx), %rcx
	0x49, 0x39, 0xc8, //0x00000165 cmpq         %rcx, %r8
	0x0f, 0x86, 0x4b, 0x00, 0x00, 0x00, //0x00000168 jbe          LBB0_23
	0x49, 0x39, 0xc8, //0x0000016e cmpq         %rcx, %r8
	0x0f, 0x84, 0x51, 0x00, 0x00, 0x00, //0x00000171 je           LBB0_24
	0x4b, 0x8d, 0x0c, 0x06, //0x00000177 leaq         (%r14,%r8), %rcx
	0x48, 0x83, 0xc0, 0x04, //0x0000017b addq         $4, %rax
	0x4e, 0x8d, 0x5c, 0x32, 0x05, //0x0000017f leaq         $5(%rdx,%r14), %r11
	0x48, 0xba, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00000184 movabsq      $4294977024, %rdx
	0x90, 0x90, //0x0000018e .p2align 4, 0x90
	//0x00000190 LBB0_20
	0x41, 0x0f, 0xbe, 0x5b, 0xff, //0x00000190 movsbl       $-1(%r11), %ebx
	0x83, 0xfb, 0x20, //0x00000195 cmpl         $32, %ebx
	0x0f, 0x87, 0x48, 0x00, 0x00, 0x00, //0x00000198 ja           LBB0_26
	0x48, 0x0f, 0xa3, 0xda, //0x0000019e btq          %rbx, %rdx
	0x0f, 0x83, 0x3e, 0x00, 0x00, 0x00, //0x000001a2 jae          LBB0_26
	0x49, 0xff, 0xc3, //0x000001a8 incq         %r11
	0x48, 0xff, 0xc0, //0x000001ab incq         %rax
	0x0f, 0x85, 0xdc, 0xff, 0xff, 0xff, //0x000001ae jne          LBB0_20
	0xe9, 0x12, 0x00, 0x00, 0x00, //0x000001b4 jmp          LBB0_25
	//0x000001b9 LBB0_23
	0x48, 0x89, 0x0e, //0x000001b9 movq         %rcx, (%rsi)
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x000001bc movq         $-1, %rax
	0xe9, 0x46, 0x01, 0x00, 0x00, //0x000001c3 jmp          LBB0_45
	//0x000001c8 LBB0_24
	0x4c, 0x01, 0xf1, //0x000001c8 addq         %r14, %rcx
	//0x000001cb LBB0_25
	0x4c, 0x29, 0xf1, //0x000001cb subq         %r14, %rcx
	0x49, 0x89, 0xcb, //0x000001ce movq         %rcx, %r11
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x000001d1 movq         $-1, %rax
	0x4d, 0x39, 0xc3, //0x000001d8 cmpq         %r8, %r11
	0x0f, 0x82, 0x1e, 0x00, 0x00, 0x00, //0x000001db jb           LBB0_27
	0xe9, 0x28, 0x01, 0x00, 0x00, //0x000001e1 jmp          LBB0_45
	//0x000001e6 LBB0_26
	0x4c, 0x89, 0xf0, //0x000001e6 movq         %r14, %rax
	0x48, 0xf7, 0xd0, //0x000001e9 notq         %rax
	0x49, 0x01, 0xc3, //0x000001ec addq         %rax, %r11
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x000001ef movq         $-1, %rax
	0x4d, 0x39, 0xc3, //0x000001f6 cmpq         %r8, %r11
	0x0f, 0x83, 0x0f, 0x01, 0x00, 0x00, //0x000001f9 jae          LBB0_45
	//0x000001ff LBB0_27
	0x49, 0x8d, 0x5b, 0x01, //0x000001ff leaq         $1(%r11), %rbx
	0x48, 0x89, 0x1e, //0x00000203 movq         %rbx, (%rsi)
	0x43, 0x0f, 0xbe, 0x0c, 0x1e, //0x00000206 movsbl       (%r14,%r11), %ecx
	0x83, 0xf9, 0x7b, //0x0000020b cmpl         $123, %ecx
	0x0f, 0x87, 0x1f, 0x01, 0x00, 0x00, //0x0000020e ja           LBB0_47
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000214 movq         $-1, %rax
	0x48, 0x8d, 0x15, 0x26, 0x0a, 0x00, 0x00, //0x0000021b leaq         $2598(%rip), %rdx  /* LJTI0_0+0(%rip) */
	0x48, 0x63, 0x0c, 0x8a, //0x00000222 movslq       (%rdx,%rcx,4), %rcx
	0x48, 0x01, 0xd1, //0x00000226 addq         %rdx, %rcx
	0xff, 0xe1, //0x00000229 jmpq         *%rcx
	//0x0000022b LBB0_29
	0x48, 0x8b, 0x4f, 0x08, //0x0000022b movq         $8(%rdi), %rcx
	0x48, 0x89, 0xc8, //0x0000022f movq         %rcx, %rax
	0x48, 0x29, 0xd8, //0x00000232 subq         %rbx, %rax
	0x4c, 0x01, 0xf3, //0x00000235 addq         %r14, %rbx
	0x48, 0x83, 0xf8, 0x10, //0x00000238 cmpq         $16, %rax
	0x0f, 0x82, 0x77, 0x00, 0x00, 0x00, //0x0000023c jb           LBB0_34
	0x4c, 0x29, 0xd9, //0x00000242 subq         %r11, %rcx
	0x48, 0x83, 0xc1, 0xef, //0x00000245 addq         $-17, %rcx
	0x48, 0x89, 0xca, //0x00000249 movq         %rcx, %rdx
	0x48, 0x83, 0xe2, 0xf0, //0x0000024c andq         $-16, %rdx
	0x4c, 0x01, 0xda, //0x00000250 addq         %r11, %rdx
	0x49, 0x8d, 0x54, 0x16, 0x11, //0x00000253 leaq         $17(%r14,%rdx), %rdx
	0x83, 0xe1, 0x0f, //0x00000258 andl         $15, %ecx
	0xc5, 0xfa, 0x6f, 0x05, 0x9d, 0xfd, 0xff, 0xff, //0x0000025b vmovdqu      $-611(%rip), %xmm0  /* LCPI0_0+0(%rip) */
	0xc5, 0xfa, 0x6f, 0x0d, 0xa5, 0xfd, 0xff, 0xff, //0x00000263 vmovdqu      $-603(%rip), %xmm1  /* LCPI0_1+0(%rip) */
	0xc5, 0xfa, 0x6f, 0x15, 0xad, 0xfd, 0xff, 0xff, //0x0000026b vmovdqu      $-595(%rip), %xmm2  /* LCPI0_2+0(%rip) */
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000273 .p2align 4, 0x90
	//0x00000280 LBB0_31
	0xc5, 0xfa, 0x6f, 0x1b, //0x00000280 vmovdqu      (%rbx), %xmm3
	0xc5, 0xe1, 0x74, 0xe0, //0x00000284 vpcmpeqb     %xmm0, %xmm3, %xmm4
	0xc5, 0xe1, 0xeb, 0xd9, //0x00000288 vpor         %xmm1, %xmm3, %xmm3
	0xc5, 0xe1, 0x74, 0xda, //0x0000028c vpcmpeqb     %xmm2, %xmm3, %xmm3
	0xc5, 0xe1, 0xeb, 0xdc, //0x00000290 vpor         %xmm4, %xmm3, %xmm3
	0xc5, 0xf9, 0xd7, 0xfb, //0x00000294 vpmovmskb    %xmm3, %edi
	0x66, 0x85, 0xff, //0x00000298 testw        %di, %di
	0x0f, 0x85, 0x5a, 0x00, 0x00, 0x00, //0x0000029b jne          LBB0_42
	0x48, 0x83, 0xc3, 0x10, //0x000002a1 addq         $16, %rbx
	0x48, 0x83, 0xc0, 0xf0, //0x000002a5 addq         $-16, %rax
	0x48, 0x83, 0xf8, 0x0f, //0x000002a9 cmpq         $15, %rax
	0x0f, 0x87, 0xcd, 0xff, 0xff, 0xff, //0x000002ad ja           LBB0_31
	0x48, 0x89, 0xc8, //0x000002b3 movq         %rcx, %rax
	0x48, 0x89, 0xd3, //0x000002b6 movq         %rdx, %rbx
	//0x000002b9 LBB0_34
	0x48, 0x85, 0xc0, //0x000002b9 testq        %rax, %rax
	0x0f, 0x84, 0x31, 0x00, 0x00, 0x00, //0x000002bc je           LBB0_41
	0x48, 0x8d, 0x0c, 0x03, //0x000002c2 leaq         (%rbx,%rax), %rcx
	//0x000002c6 LBB0_36
	0x0f, 0xb6, 0x13, //0x000002c6 movzbl       (%rbx), %edx
	0x80, 0xfa, 0x2c, //0x000002c9 cmpb         $44, %dl
	0x0f, 0x84, 0x21, 0x00, 0x00, 0x00, //0x000002cc je           LBB0_41
	0x80, 0xfa, 0x7d, //0x000002d2 cmpb         $125, %dl
	0x0f, 0x84, 0x18, 0x00, 0x00, 0x00, //0x000002d5 je           LBB0_41
	0x80, 0xfa, 0x5d, //0x000002db cmpb         $93, %dl
	0x0f, 0x84, 0x0f, 0x00, 0x00, 0x00, //0x000002de je           LBB0_41
	0x48, 0xff, 0xc3, //0x000002e4 incq         %rbx
	0x48, 0xff, 0xc8, //0x000002e7 decq         %rax
	0x0f, 0x85, 0xd6, 0xff, 0xff, 0xff, //0x000002ea jne          LBB0_36
	0x48, 0x89, 0xcb, //0x000002f0 movq         %rcx, %rbx
	//0x000002f3 LBB0_41
	0x4c, 0x29, 0xf3, //0x000002f3 subq         %r14, %rbx
	0xe9, 0x0d, 0x00, 0x00, 0x00, //0x000002f6 jmp          LBB0_43
	//0x000002fb LBB0_42
	0x0f, 0xb7, 0xc7, //0x000002fb movzwl       %di, %eax
	0x48, 0x0f, 0xbc, 0xc0, //0x000002fe bsfq         %rax, %rax
	0x4c, 0x29, 0xf3, //0x00000302 subq         %r14, %rbx
	0x48, 0x01, 0xc3, //0x00000305 addq         %rax, %rbx
	//0x00000308 LBB0_43
	0x48, 0x89, 0x1e, //0x00000308 movq         %rbx, (%rsi)
	//0x0000030b LBB0_44
	0x4c, 0x89, 0xd8, //0x0000030b movq         %r11, %rax
	//0x0000030e LBB0_45
	0x48, 0x8d, 0x65, 0xd8, //0x0000030e leaq         $-40(%rbp), %rsp
	0x5b, //0x00000312 popq         %rbx
	0x41, 0x5c, //0x00000313 popq         %r12
	0x41, 0x5d, //0x00000315 popq         %r13
	0x41, 0x5e, //0x00000317 popq         %r14
	0x41, 0x5f, //0x00000319 popq         %r15
	0x5d, //0x0000031b popq         %rbp
	0xc5, 0xf8, 0x77, //0x0000031c vzeroupper   
	0xc3, //0x0000031f retq         
	//0x00000320 LBB0_46
	0x49, 0x8d, 0x4b, 0x04, //0x00000320 leaq         $4(%r11), %rcx
	0x48, 0x3b, 0x4f, 0x08, //0x00000324 cmpq         $8(%rdi), %rcx
	0x0f, 0x87, 0xe0, 0xff, 0xff, 0xff, //0x00000328 ja           LBB0_45
	0xe9, 0xa6, 0x04, 0x00, 0x00, //0x0000032e jmp          LBB0_83
	//0x00000333 LBB0_47
	0x4c, 0x89, 0x1e, //0x00000333 movq         %r11, (%rsi)
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x00000336 movq         $-2, %rax
	0xe9, 0xcc, 0xff, 0xff, 0xff, //0x0000033d jmp          LBB0_45
	//0x00000342 LBB0_48
	0x4c, 0x8b, 0x47, 0x08, //0x00000342 movq         $8(%rdi), %r8
	0x4d, 0x89, 0xc7, //0x00000346 movq         %r8, %r15
	0x49, 0x29, 0xdf, //0x00000349 subq         %rbx, %r15
	0x49, 0x83, 0xff, 0x20, //0x0000034c cmpq         $32, %r15
	0x0f, 0x8c, 0xbb, 0x08, 0x00, 0x00, //0x00000350 jl           LBB0_117
	0x41, 0xb9, 0xff, 0xff, 0xff, 0xff, //0x00000356 movl         $4294967295, %r9d
	0x4f, 0x8d, 0x14, 0x1e, //0x0000035c leaq         (%r14,%r11), %r10
	0x4d, 0x29, 0xd8, //0x00000360 subq         %r11, %r8
	0x41, 0xbd, 0x1f, 0x00, 0x00, 0x00, //0x00000363 movl         $31, %r13d
	0x45, 0x31, 0xff, //0x00000369 xorl         %r15d, %r15d
	0xc5, 0xfa, 0x6f, 0x05, 0xbc, 0xfc, 0xff, 0xff, //0x0000036c vmovdqu      $-836(%rip), %xmm0  /* LCPI0_3+0(%rip) */
	0xc5, 0xfa, 0x6f, 0x0d, 0xc4, 0xfc, 0xff, 0xff, //0x00000374 vmovdqu      $-828(%rip), %xmm1  /* LCPI0_4+0(%rip) */
	0x45, 0x31, 0xe4, //0x0000037c xorl         %r12d, %r12d
	0xe9, 0x2e, 0x00, 0x00, 0x00, //0x0000037f jmp          LBB0_50
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000384 .p2align 4, 0x90
	//0x00000390 LBB0_52
	0x45, 0x31, 0xe4, //0x00000390 xorl         %r12d, %r12d
	0x85, 0xc9, //0x00000393 testl        %ecx, %ecx
	0x0f, 0x85, 0x9e, 0x00, 0x00, 0x00, //0x00000395 jne          LBB0_110
	//0x0000039b LBB0_53
	0x49, 0x83, 0xc7, 0x20, //0x0000039b addq         $32, %r15
	0x4b, 0x8d, 0x4c, 0x28, 0xe0, //0x0000039f leaq         $-32(%r8,%r13), %rcx
	0x49, 0x83, 0xc5, 0xe0, //0x000003a4 addq         $-32, %r13
	0x48, 0x83, 0xf9, 0x3f, //0x000003a8 cmpq         $63, %rcx
	0x0f, 0x8e, 0xdc, 0x07, 0x00, 0x00, //0x000003ac jle          LBB0_54
	//0x000003b2 LBB0_50
	0xc4, 0x81, 0x7a, 0x6f, 0x54, 0x3a, 0x01, //0x000003b2 vmovdqu      $1(%r10,%r15), %xmm2
	0xc4, 0x81, 0x7a, 0x6f, 0x5c, 0x3a, 0x11, //0x000003b9 vmovdqu      $17(%r10,%r15), %xmm3
	0xc5, 0xe9, 0x74, 0xe0, //0x000003c0 vpcmpeqb     %xmm0, %xmm2, %xmm4
	0xc5, 0xf9, 0xd7, 0xfc, //0x000003c4 vpmovmskb    %xmm4, %edi
	0xc5, 0xe1, 0x74, 0xe0, //0x000003c8 vpcmpeqb     %xmm0, %xmm3, %xmm4
	0xc5, 0xf9, 0xd7, 0xcc, //0x000003cc vpmovmskb    %xmm4, %ecx
	0x48, 0xc1, 0xe1, 0x10, //0x000003d0 shlq         $16, %rcx
	0x48, 0x09, 0xf9, //0x000003d4 orq          %rdi, %rcx
	0xc5, 0xe9, 0x74, 0xd1, //0x000003d7 vpcmpeqb     %xmm1, %xmm2, %xmm2
	0xc5, 0xf9, 0xd7, 0xda, //0x000003db vpmovmskb    %xmm2, %ebx
	0xc5, 0xe1, 0x74, 0xd1, //0x000003df vpcmpeqb     %xmm1, %xmm3, %xmm2
	0xc5, 0xf9, 0xd7, 0xfa, //0x000003e3 vpmovmskb    %xmm2, %edi
	0x48, 0xc1, 0xe7, 0x10, //0x000003e7 shlq         $16, %rdi
	0x48, 0x09, 0xdf, //0x000003eb orq          %rbx, %rdi
	0x48, 0x89, 0xfb, //0x000003ee movq         %rdi, %rbx
	0x4c, 0x09, 0xe3, //0x000003f1 orq          %r12, %rbx
	0x0f, 0x84, 0x96, 0xff, 0xff, 0xff, //0x000003f4 je           LBB0_52
	0x44, 0x89, 0xe3, //0x000003fa movl         %r12d, %ebx
	0x44, 0x31, 0xcb, //0x000003fd xorl         %r9d, %ebx
	0x21, 0xdf, //0x00000400 andl         %ebx, %edi
	0x8d, 0x1c, 0x3f, //0x00000402 leal         (%rdi,%rdi), %ebx
	0x44, 0x09, 0xe3, //0x00000405 orl          %r12d, %ebx
	0x41, 0x8d, 0x91, 0xab, 0xaa, 0xaa, 0xaa, //0x00000408 leal         $-1431655765(%r9), %edx
	0x31, 0xda, //0x0000040f xorl         %ebx, %edx
	0x21, 0xfa, //0x00000411 andl         %edi, %edx
	0x81, 0xe2, 0xaa, 0xaa, 0xaa, 0xaa, //0x00000413 andl         $-1431655766, %edx
	0x45, 0x31, 0xe4, //0x00000419 xorl         %r12d, %r12d
	0x01, 0xfa, //0x0000041c addl         %edi, %edx
	0x41, 0x0f, 0x92, 0xc4, //0x0000041e setb         %r12b
	0x01, 0xd2, //0x00000422 addl         %edx, %edx
	0x81, 0xf2, 0x55, 0x55, 0x55, 0x55, //0x00000424 xorl         $1431655765, %edx
	0x21, 0xda, //0x0000042a andl         %ebx, %edx
	0x44, 0x31, 0xca, //0x0000042c xorl         %r9d, %edx
	0x21, 0xd1, //0x0000042f andl         %edx, %ecx
	0x85, 0xc9, //0x00000431 testl        %ecx, %ecx
	0x0f, 0x84, 0x62, 0xff, 0xff, 0xff, //0x00000433 je           LBB0_53
	//0x00000439 LBB0_110
	0x48, 0x0f, 0xbc, 0xc1, //0x00000439 bsfq         %rcx, %rax
	0x49, 0x01, 0xc2, //0x0000043d addq         %rax, %r10
	0x4d, 0x01, 0xfa, //0x00000440 addq         %r15, %r10
	0x4d, 0x29, 0xf2, //0x00000443 subq         %r14, %r10
	0x49, 0x83, 0xc2, 0x02, //0x00000446 addq         $2, %r10
	0x4c, 0x89, 0x16, //0x0000044a movq         %r10, (%rsi)
	0xe9, 0xb9, 0xfe, 0xff, 0xff, //0x0000044d jmp          LBB0_44
	//0x00000452 LBB0_57
	0x48, 0x8b, 0x4f, 0x08, //0x00000452 movq         $8(%rdi), %rcx
	0x48, 0x29, 0xd9, //0x00000456 subq         %rbx, %rcx
	0x49, 0x01, 0xde, //0x00000459 addq         %rbx, %r14
	0x45, 0x31, 0xe4, //0x0000045c xorl         %r12d, %r12d
	0xc5, 0x7a, 0x6f, 0x15, 0xc9, 0xfb, 0xff, 0xff, //0x0000045f vmovdqu      $-1079(%rip), %xmm10  /* LCPI0_3+0(%rip) */
	0xc5, 0xfa, 0x6f, 0x0d, 0xd1, 0xfb, 0xff, 0xff, //0x00000467 vmovdqu      $-1071(%rip), %xmm1  /* LCPI0_4+0(%rip) */
	0xc4, 0x41, 0x31, 0x76, 0xc9, //0x0000046f vpcmpeqd     %xmm9, %xmm9, %xmm9
	0xc5, 0xfa, 0x6f, 0x1d, 0xe4, 0xfb, 0xff, 0xff, //0x00000474 vmovdqu      $-1052(%rip), %xmm3  /* LCPI0_6+0(%rip) */
	0xc5, 0xfa, 0x6f, 0x25, 0xec, 0xfb, 0xff, 0xff, //0x0000047c vmovdqu      $-1044(%rip), %xmm4  /* LCPI0_7+0(%rip) */
	0xc4, 0x41, 0x38, 0x57, 0xc0, //0x00000484 vxorps       %xmm8, %xmm8, %xmm8
	0x31, 0xdb, //0x00000489 xorl         %ebx, %ebx
	0x45, 0x31, 0xc0, //0x0000048b xorl         %r8d, %r8d
	0x45, 0x31, 0xff, //0x0000048e xorl         %r15d, %r15d
	0x48, 0x83, 0xf9, 0x40, //0x00000491 cmpq         $64, %rcx
	0x48, 0x89, 0x4c, 0x24, 0x08, //0x00000495 movq         %rcx, $8(%rsp)
	0x4c, 0x89, 0x44, 0x24, 0x10, //0x0000049a movq         %r8, $16(%rsp)
	0x0f, 0x8d, 0x33, 0x01, 0x00, 0x00, //0x0000049f jge          LBB0_58
	//0x000004a5 LBB0_67
	0x48, 0x85, 0xc9, //0x000004a5 testq        %rcx, %rcx
	0x0f, 0x8e, 0x6b, 0x07, 0x00, 0x00, //0x000004a8 jle          LBB0_118
	0xc5, 0x7c, 0x11, 0x44, 0x24, 0x40, //0x000004ae vmovups      %ymm8, $64(%rsp)
	0xc5, 0x7c, 0x11, 0x44, 0x24, 0x20, //0x000004b4 vmovups      %ymm8, $32(%rsp)
	0x44, 0x89, 0xf1, //0x000004ba movl         %r14d, %ecx
	0x81, 0xe1, 0xff, 0x0f, 0x00, 0x00, //0x000004bd andl         $4095, %ecx
	0x81, 0xf9, 0xc1, 0x0f, 0x00, 0x00, //0x000004c3 cmpl         $4033, %ecx
	0x0f, 0x82, 0x09, 0x01, 0x00, 0x00, //0x000004c9 jb           LBB0_58
	0x48, 0x83, 0x7c, 0x24, 0x08, 0x20, //0x000004cf cmpq         $32, $8(%rsp)
	0x0f, 0x82, 0x2e, 0x00, 0x00, 0x00, //0x000004d5 jb           LBB0_71
	0xc4, 0xc1, 0x78, 0x10, 0x06, //0x000004db vmovups      (%r14), %xmm0
	0xc5, 0xf8, 0x11, 0x44, 0x24, 0x20, //0x000004e0 vmovups      %xmm0, $32(%rsp)
	0xc4, 0xc1, 0x7a, 0x6f, 0x46, 0x10, //0x000004e6 vmovdqu      $16(%r14), %xmm0
	0xc5, 0xfa, 0x7f, 0x44, 0x24, 0x30, //0x000004ec vmovdqu      %xmm0, $48(%rsp)
	0x49, 0x83, 0xc6, 0x20, //0x000004f2 addq         $32, %r14
	0x48, 0x8b, 0x4c, 0x24, 0x08, //0x000004f6 movq         $8(%rsp), %rcx
	0x48, 0x8d, 0x51, 0xe0, //0x000004fb leaq         $-32(%rcx), %rdx
	0x4c, 0x8d, 0x44, 0x24, 0x40, //0x000004ff leaq         $64(%rsp), %r8
	0xe9, 0x0a, 0x00, 0x00, 0x00, //0x00000504 jmp          LBB0_72
	//0x00000509 LBB0_71
	0x4c, 0x8d, 0x44, 0x24, 0x20, //0x00000509 leaq         $32(%rsp), %r8
	0x48, 0x8b, 0x54, 0x24, 0x08, //0x0000050e movq         $8(%rsp), %rdx
	//0x00000513 LBB0_72
	0x48, 0x83, 0xfa, 0x10, //0x00000513 cmpq         $16, %rdx
	0x0f, 0x82, 0x5c, 0x00, 0x00, 0x00, //0x00000517 jb           LBB0_73
	0xc4, 0xc1, 0x7a, 0x6f, 0x06, //0x0000051d vmovdqu      (%r14), %xmm0
	0xc4, 0xc1, 0x7a, 0x7f, 0x00, //0x00000522 vmovdqu      %xmm0, (%r8)
	0x49, 0x83, 0xc6, 0x10, //0x00000527 addq         $16, %r14
	0x49, 0x83, 0xc0, 0x10, //0x0000052b addq         $16, %r8
	0x48, 0x83, 0xc2, 0xf0, //0x0000052f addq         $-16, %rdx
	0x48, 0x83, 0xfa, 0x08, //0x00000533 cmpq         $8, %rdx
	0x0f, 0x83, 0x46, 0x00, 0x00, 0x00, //0x00000537 jae          LBB0_78
	//0x0000053d LBB0_74
	0x48, 0x83, 0xfa, 0x04, //0x0000053d cmpq         $4, %rdx
	0x0f, 0x8c, 0x58, 0x00, 0x00, 0x00, //0x00000541 jl           LBB0_75
	//0x00000547 LBB0_79
	0x41, 0x8b, 0x0e, //0x00000547 movl         (%r14), %ecx
	0x41, 0x89, 0x08, //0x0000054a movl         %ecx, (%r8)
	0x49, 0x83, 0xc6, 0x04, //0x0000054d addq         $4, %r14
	0x49, 0x83, 0xc0, 0x04, //0x00000551 addq         $4, %r8
	0x48, 0x83, 0xc2, 0xfc, //0x00000555 addq         $-4, %rdx
	0x48, 0x83, 0xfa, 0x02, //0x00000559 cmpq         $2, %rdx
	0x0f, 0x83, 0x46, 0x00, 0x00, 0x00, //0x0000055d jae          LBB0_80
	//0x00000563 LBB0_76
	0x4c, 0x89, 0xf1, //0x00000563 movq         %r14, %rcx
	0x4c, 0x8d, 0x74, 0x24, 0x20, //0x00000566 leaq         $32(%rsp), %r14
	0x48, 0x85, 0xd2, //0x0000056b testq        %rdx, %rdx
	0x0f, 0x85, 0x5a, 0x00, 0x00, 0x00, //0x0000056e jne          LBB0_81
	0xe9, 0x5f, 0x00, 0x00, 0x00, //0x00000574 jmp          LBB0_58
	//0x00000579 LBB0_73
	0x48, 0x83, 0xfa, 0x08, //0x00000579 cmpq         $8, %rdx
	0x0f, 0x82, 0xba, 0xff, 0xff, 0xff, //0x0000057d jb           LBB0_74
	//0x00000583 LBB0_78
	0x49, 0x8b, 0x0e, //0x00000583 movq         (%r14), %rcx
	0x49, 0x89, 0x08, //0x00000586 movq         %rcx, (%r8)
	0x49, 0x83, 0xc6, 0x08, //0x00000589 addq         $8, %r14
	0x49, 0x83, 0xc0, 0x08, //0x0000058d addq         $8, %r8
	0x48, 0x83, 0xc2, 0xf8, //0x00000591 addq         $-8, %rdx
	0x48, 0x83, 0xfa, 0x04, //0x00000595 cmpq         $4, %rdx
	0x0f, 0x8d, 0xa8, 0xff, 0xff, 0xff, //0x00000599 jge          LBB0_79
	//0x0000059f LBB0_75
	0x48, 0x83, 0xfa, 0x02, //0x0000059f cmpq         $2, %rdx
	0x0f, 0x82, 0xba, 0xff, 0xff, 0xff, //0x000005a3 jb           LBB0_76
	//0x000005a9 LBB0_80
	0x41, 0x0f, 0xb7, 0x0e, //0x000005a9 movzwl       (%r14), %ecx
	0x66, 0x41, 0x89, 0x08, //0x000005ad movw         %cx, (%r8)
	0x49, 0x83, 0xc6, 0x02, //0x000005b1 addq         $2, %r14
	0x49, 0x83, 0xc0, 0x02, //0x000005b5 addq         $2, %r8
	0x48, 0x83, 0xc2, 0xfe, //0x000005b9 addq         $-2, %rdx
	0x4c, 0x89, 0xf1, //0x000005bd movq         %r14, %rcx
	0x4c, 0x8d, 0x74, 0x24, 0x20, //0x000005c0 leaq         $32(%rsp), %r14
	0x48, 0x85, 0xd2, //0x000005c5 testq        %rdx, %rdx
	0x0f, 0x84, 0x0a, 0x00, 0x00, 0x00, //0x000005c8 je           LBB0_58
	//0x000005ce LBB0_81
	0x8a, 0x09, //0x000005ce movb         (%rcx), %cl
	0x41, 0x88, 0x08, //0x000005d0 movb         %cl, (%r8)
	0x4c, 0x8d, 0x74, 0x24, 0x20, //0x000005d3 leaq         $32(%rsp), %r14
	//0x000005d8 LBB0_58
	0xc4, 0xc1, 0x7a, 0x6f, 0x16, //0x000005d8 vmovdqu      (%r14), %xmm2
	0xc4, 0xc1, 0x7a, 0x6f, 0x6e, 0x10, //0x000005dd vmovdqu      $16(%r14), %xmm5
	0xc4, 0xc1, 0x7a, 0x6f, 0x7e, 0x20, //0x000005e3 vmovdqu      $32(%r14), %xmm7
	0xc4, 0xc1, 0x7a, 0x6f, 0x76, 0x30, //0x000005e9 vmovdqu      $48(%r14), %xmm6
	0xc5, 0xa9, 0x74, 0xc2, //0x000005ef vpcmpeqb     %xmm2, %xmm10, %xmm0
	0xc5, 0x79, 0xd7, 0xe8, //0x000005f3 vpmovmskb    %xmm0, %r13d
	0xc5, 0xa9, 0x74, 0xc5, //0x000005f7 vpcmpeqb     %xmm5, %xmm10, %xmm0
	0xc5, 0xf9, 0xd7, 0xc8, //0x000005fb vpmovmskb    %xmm0, %ecx
	0xc5, 0xa9, 0x74, 0xc7, //0x000005ff vpcmpeqb     %xmm7, %xmm10, %xmm0
	0xc5, 0xf9, 0xd7, 0xd0, //0x00000603 vpmovmskb    %xmm0, %edx
	0xc5, 0xa9, 0x74, 0xc6, //0x00000607 vpcmpeqb     %xmm6, %xmm10, %xmm0
	0xc5, 0x79, 0xd7, 0xc8, //0x0000060b vpmovmskb    %xmm0, %r9d
	0x49, 0xc1, 0xe1, 0x30, //0x0000060f shlq         $48, %r9
	0x48, 0xc1, 0xe2, 0x20, //0x00000613 shlq         $32, %rdx
	0x48, 0xc1, 0xe1, 0x10, //0x00000617 shlq         $16, %rcx
	0x49, 0x09, 0xcd, //0x0000061b orq          %rcx, %r13
	0x49, 0x09, 0xd5, //0x0000061e orq          %rdx, %r13
	0x4d, 0x09, 0xcd, //0x00000621 orq          %r9, %r13
	0xc5, 0xe9, 0x74, 0xc1, //0x00000624 vpcmpeqb     %xmm1, %xmm2, %xmm0
	0xc5, 0xf9, 0xd7, 0xc8, //0x00000628 vpmovmskb    %xmm0, %ecx
	0xc5, 0xd1, 0x74, 0xc1, //0x0000062c vpcmpeqb     %xmm1, %xmm5, %xmm0
	0xc5, 0xf9, 0xd7, 0xd0, //0x00000630 vpmovmskb    %xmm0, %edx
	0xc5, 0xc1, 0x74, 0xc1, //0x00000634 vpcmpeqb     %xmm1, %xmm7, %xmm0
	0xc5, 0x79, 0xd7, 0xc8, //0x00000638 vpmovmskb    %xmm0, %r9d
	0xc5, 0xc9, 0x74, 0xc1, //0x0000063c vpcmpeqb     %xmm1, %xmm6, %xmm0
	0xc5, 0x79, 0xd7, 0xd0, //0x00000640 vpmovmskb    %xmm0, %r10d
	0x49, 0xc1, 0xe2, 0x30, //0x00000644 shlq         $48, %r10
	0x49, 0xc1, 0xe1, 0x20, //0x00000648 shlq         $32, %r9
	0x48, 0xc1, 0xe2, 0x10, //0x0000064c shlq         $16, %rdx
	0x48, 0x09, 0xd1, //0x00000650 orq          %rdx, %rcx
	0x4c, 0x09, 0xc9, //0x00000653 orq          %r9, %rcx
	0x4c, 0x09, 0xd1, //0x00000656 orq          %r10, %rcx
	0x48, 0x89, 0xca, //0x00000659 movq         %rcx, %rdx
	0x48, 0x09, 0xda, //0x0000065c orq          %rbx, %rdx
	0x0f, 0x84, 0x49, 0x00, 0x00, 0x00, //0x0000065f je           LBB0_60
	0x48, 0x89, 0xda, //0x00000665 movq         %rbx, %rdx
	0x48, 0xf7, 0xd2, //0x00000668 notq         %rdx
	0x48, 0x21, 0xca, //0x0000066b andq         %rcx, %rdx
	0x4c, 0x8d, 0x0c, 0x12, //0x0000066e leaq         (%rdx,%rdx), %r9
	0x49, 0x09, 0xd9, //0x00000672 orq          %rbx, %r9
	0x4d, 0x89, 0xca, //0x00000675 movq         %r9, %r10
	0x48, 0xbb, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00000678 movabsq      $-6148914691236517206, %rbx
	0x49, 0x31, 0xda, //0x00000682 xorq         %rbx, %r10
	0x48, 0x21, 0xd9, //0x00000685 andq         %rbx, %rcx
	0x4c, 0x21, 0xd1, //0x00000688 andq         %r10, %rcx
	0x31, 0xdb, //0x0000068b xorl         %ebx, %ebx
	0x48, 0x01, 0xd1, //0x0000068d addq         %rdx, %rcx
	0x0f, 0x92, 0xc3, //0x00000690 setb         %bl
	0x48, 0x01, 0xc9, //0x00000693 addq         %rcx, %rcx
	0x48, 0xba, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00000696 movabsq      $6148914691236517205, %rdx
	0x48, 0x31, 0xd1, //0x000006a0 xorq         %rdx, %rcx
	0x4c, 0x21, 0xc9, //0x000006a3 andq         %r9, %rcx
	0x48, 0xf7, 0xd1, //0x000006a6 notq         %rcx
	0xe9, 0x09, 0x00, 0x00, 0x00, //0x000006a9 jmp          LBB0_61
	//0x000006ae LBB0_60
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x000006ae movq         $-1, %rcx
	0x31, 0xdb, //0x000006b5 xorl         %ebx, %ebx
	//0x000006b7 LBB0_61
	0x48, 0x89, 0x5c, 0x24, 0x18, //0x000006b7 movq         %rbx, $24(%rsp)
	0x4c, 0x21, 0xe9, //0x000006bc andq         %r13, %rcx
	0xc4, 0xe1, 0xf9, 0x6e, 0xc1, //0x000006bf vmovq        %rcx, %xmm0
	0xc4, 0xc3, 0x79, 0x44, 0xc1, 0x00, //0x000006c4 vpclmulqdq   $0, %xmm9, %xmm0, %xmm0
	0xc4, 0xc1, 0xf9, 0x7e, 0xc5, //0x000006ca vmovq        %xmm0, %r13
	0x4d, 0x31, 0xe5, //0x000006cf xorq         %r12, %r13
	0xc5, 0xe9, 0x74, 0xc3, //0x000006d2 vpcmpeqb     %xmm3, %xmm2, %xmm0
	0xc5, 0x79, 0xd7, 0xd0, //0x000006d6 vpmovmskb    %xmm0, %r10d
	0xc5, 0xd1, 0x74, 0xc3, //0x000006da vpcmpeqb     %xmm3, %xmm5, %xmm0
	0xc5, 0xf9, 0xd7, 0xc8, //0x000006de vpmovmskb    %xmm0, %ecx
	0xc5, 0xc1, 0x74, 0xc3, //0x000006e2 vpcmpeqb     %xmm3, %xmm7, %xmm0
	0xc5, 0xf9, 0xd7, 0xd0, //0x000006e6 vpmovmskb    %xmm0, %edx
	0xc5, 0xc9, 0x74, 0xc3, //0x000006ea vpcmpeqb     %xmm3, %xmm6, %xmm0
	0xc5, 0x79, 0xd7, 0xc8, //0x000006ee vpmovmskb    %xmm0, %r9d
	0x49, 0xc1, 0xe1, 0x30, //0x000006f2 shlq         $48, %r9
	0x48, 0xc1, 0xe2, 0x20, //0x000006f6 shlq         $32, %rdx
	0x48, 0xc1, 0xe1, 0x10, //0x000006fa shlq         $16, %rcx
	0x49, 0x09, 0xca, //0x000006fe orq          %rcx, %r10
	0x49, 0x09, 0xd2, //0x00000701 orq          %rdx, %r10
	0x4d, 0x09, 0xca, //0x00000704 orq          %r9, %r10
	0x4d, 0x89, 0xe9, //0x00000707 movq         %r13, %r9
	0x49, 0xf7, 0xd1, //0x0000070a notq         %r9
	0x4d, 0x21, 0xca, //0x0000070d andq         %r9, %r10
	0xc5, 0xe9, 0x74, 0xc4, //0x00000710 vpcmpeqb     %xmm4, %xmm2, %xmm0
	0xc5, 0xf9, 0xd7, 0xc8, //0x00000714 vpmovmskb    %xmm0, %ecx
	0xc5, 0xd1, 0x74, 0xc4, //0x00000718 vpcmpeqb     %xmm4, %xmm5, %xmm0
	0xc5, 0xf9, 0xd7, 0xd0, //0x0000071c vpmovmskb    %xmm0, %edx
	0xc5, 0xc1, 0x74, 0xc4, //0x00000720 vpcmpeqb     %xmm4, %xmm7, %xmm0
	0xc5, 0x79, 0xd7, 0xc0, //0x00000724 vpmovmskb    %xmm0, %r8d
	0xc5, 0xc9, 0x74, 0xc4, //0x00000728 vpcmpeqb     %xmm4, %xmm6, %xmm0
	0xc5, 0x79, 0xd7, 0xe0, //0x0000072c vpmovmskb    %xmm0, %r12d
	0x49, 0xc1, 0xe4, 0x30, //0x00000730 shlq         $48, %r12
	0x49, 0xc1, 0xe0, 0x20, //0x00000734 shlq         $32, %r8
	0x48, 0xc1, 0xe2, 0x10, //0x00000738 shlq         $16, %rdx
	0x48, 0x09, 0xd1, //0x0000073c orq          %rdx, %rcx
	0x4c, 0x09, 0xc1, //0x0000073f orq          %r8, %rcx
	0x4c, 0x09, 0xe1, //0x00000742 orq          %r12, %rcx
	0x4c, 0x21, 0xc9, //0x00000745 andq         %r9, %rcx
	0x0f, 0x84, 0x3e, 0x00, 0x00, 0x00, //0x00000748 je           LBB0_65
	0x4c, 0x8b, 0x44, 0x24, 0x10, //0x0000074e movq         $16(%rsp), %r8
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000753 .p2align 4, 0x90
	//0x00000760 LBB0_63
	0x48, 0x8d, 0x59, 0xff, //0x00000760 leaq         $-1(%rcx), %rbx
	0x48, 0x89, 0xda, //0x00000764 movq         %rbx, %rdx
	0x4c, 0x21, 0xd2, //0x00000767 andq         %r10, %rdx
	0xf3, 0x48, 0x0f, 0xb8, 0xd2, //0x0000076a popcntq      %rdx, %rdx
	0x4c, 0x01, 0xc2, //0x0000076f addq         %r8, %rdx
	0x4c, 0x39, 0xfa, //0x00000772 cmpq         %r15, %rdx
	0x0f, 0x86, 0xe0, 0x03, 0x00, 0x00, //0x00000775 jbe          LBB0_109
	0x49, 0xff, 0xc7, //0x0000077b incq         %r15
	0x48, 0x21, 0xd9, //0x0000077e andq         %rbx, %rcx
	0x0f, 0x85, 0xd9, 0xff, 0xff, 0xff, //0x00000781 jne          LBB0_63
	0xe9, 0x05, 0x00, 0x00, 0x00, //0x00000787 jmp          LBB0_66
	//0x0000078c LBB0_65
	0x4c, 0x8b, 0x44, 0x24, 0x10, //0x0000078c movq         $16(%rsp), %r8
	//0x00000791 LBB0_66
	0x49, 0xc1, 0xfd, 0x3f, //0x00000791 sarq         $63, %r13
	0xf3, 0x49, 0x0f, 0xb8, 0xca, //0x00000795 popcntq      %r10, %rcx
	0x49, 0x01, 0xc8, //0x0000079a addq         %rcx, %r8
	0x49, 0x83, 0xc6, 0x40, //0x0000079d addq         $64, %r14
	0x48, 0x8b, 0x4c, 0x24, 0x08, //0x000007a1 movq         $8(%rsp), %rcx
	0x48, 0x83, 0xc1, 0xc0, //0x000007a6 addq         $-64, %rcx
	0x4d, 0x89, 0xec, //0x000007aa movq         %r13, %r12
	0x48, 0x8b, 0x5c, 0x24, 0x18, //0x000007ad movq         $24(%rsp), %rbx
	0x48, 0x83, 0xf9, 0x40, //0x000007b2 cmpq         $64, %rcx
	0x48, 0x89, 0x4c, 0x24, 0x08, //0x000007b6 movq         %rcx, $8(%rsp)
	0x4c, 0x89, 0x44, 0x24, 0x10, //0x000007bb movq         %r8, $16(%rsp)
	0x0f, 0x8d, 0x12, 0xfe, 0xff, 0xff, //0x000007c0 jge          LBB0_58
	0xe9, 0xda, 0xfc, 0xff, 0xff, //0x000007c6 jmp          LBB0_67
	//0x000007cb LBB0_82
	0x49, 0x8d, 0x4b, 0x05, //0x000007cb leaq         $5(%r11), %rcx
	0x48, 0x3b, 0x4f, 0x08, //0x000007cf cmpq         $8(%rdi), %rcx
	0x0f, 0x87, 0x35, 0xfb, 0xff, 0xff, //0x000007d3 ja           LBB0_45
	//0x000007d9 LBB0_83
	0x48, 0x89, 0x0e, //0x000007d9 movq         %rcx, (%rsi)
	0xe9, 0x2a, 0xfb, 0xff, 0xff, //0x000007dc jmp          LBB0_44
	//0x000007e1 LBB0_84
	0x48, 0x8b, 0x4f, 0x08, //0x000007e1 movq         $8(%rdi), %rcx
	0x48, 0x29, 0xd9, //0x000007e5 subq         %rbx, %rcx
	0x49, 0x01, 0xde, //0x000007e8 addq         %rbx, %r14
	0x45, 0x31, 0xe4, //0x000007eb xorl         %r12d, %r12d
	0xc5, 0x7a, 0x6f, 0x15, 0x3a, 0xf8, 0xff, 0xff, //0x000007ee vmovdqu      $-1990(%rip), %xmm10  /* LCPI0_3+0(%rip) */
	0xc5, 0xfa, 0x6f, 0x0d, 0x42, 0xf8, 0xff, 0xff, //0x000007f6 vmovdqu      $-1982(%rip), %xmm1  /* LCPI0_4+0(%rip) */
	0xc4, 0x41, 0x31, 0x76, 0xc9, //0x000007fe vpcmpeqd     %xmm9, %xmm9, %xmm9
	0xc5, 0xfa, 0x6f, 0x1d, 0x45, 0xf8, 0xff, 0xff, //0x00000803 vmovdqu      $-1979(%rip), %xmm3  /* LCPI0_5+0(%rip) */
	0xc5, 0xfa, 0x6f, 0x25, 0x0d, 0xf8, 0xff, 0xff, //0x0000080b vmovdqu      $-2035(%rip), %xmm4  /* LCPI0_2+0(%rip) */
	0xc4, 0x41, 0x38, 0x57, 0xc0, //0x00000813 vxorps       %xmm8, %xmm8, %xmm8
	0x31, 0xdb, //0x00000818 xorl         %ebx, %ebx
	0x45, 0x31, 0xc0, //0x0000081a xorl         %r8d, %r8d
	0x45, 0x31, 0xff, //0x0000081d xorl         %r15d, %r15d
	0x48, 0x83, 0xf9, 0x40, //0x00000820 cmpq         $64, %rcx
	0x48, 0x89, 0x4c, 0x24, 0x08, //0x00000824 movq         %rcx, $8(%rsp)
	0x4c, 0x89, 0x44, 0x24, 0x10, //0x00000829 movq         %r8, $16(%rsp)
	0x0f, 0x8d, 0x33, 0x01, 0x00, 0x00, //0x0000082e jge          LBB0_85
	//0x00000834 LBB0_94
	0x48, 0x85, 0xc9, //0x00000834 testq        %rcx, %rcx
	0x0f, 0x8e, 0xdc, 0x03, 0x00, 0x00, //0x00000837 jle          LBB0_118
	0xc5, 0x7c, 0x11, 0x44, 0x24, 0x40, //0x0000083d vmovups      %ymm8, $64(%rsp)
	0xc5, 0x7c, 0x11, 0x44, 0x24, 0x20, //0x00000843 vmovups      %ymm8, $32(%rsp)
	0x44, 0x89, 0xf1, //0x00000849 movl         %r14d, %ecx
	0x81, 0xe1, 0xff, 0x0f, 0x00, 0x00, //0x0000084c andl         $4095, %ecx
	0x81, 0xf9, 0xc1, 0x0f, 0x00, 0x00, //0x00000852 cmpl         $4033, %ecx
	0x0f, 0x82, 0x09, 0x01, 0x00, 0x00, //0x00000858 jb           LBB0_85
	0x48, 0x83, 0x7c, 0x24, 0x08, 0x20, //0x0000085e cmpq         $32, $8(%rsp)
	0x0f, 0x82, 0x2e, 0x00, 0x00, 0x00, //0x00000864 jb           LBB0_98
	0xc4, 0xc1, 0x78, 0x10, 0x06, //0x0000086a vmovups      (%r14), %xmm0
	0xc5, 0xf8, 0x11, 0x44, 0x24, 0x20, //0x0000086f vmovups      %xmm0, $32(%rsp)
	0xc4, 0xc1, 0x7a, 0x6f, 0x46, 0x10, //0x00000875 vmovdqu      $16(%r14), %xmm0
	0xc5, 0xfa, 0x7f, 0x44, 0x24, 0x30, //0x0000087b vmovdqu      %xmm0, $48(%rsp)
	0x49, 0x83, 0xc6, 0x20, //0x00000881 addq         $32, %r14
	0x48, 0x8b, 0x4c, 0x24, 0x08, //0x00000885 movq         $8(%rsp), %rcx
	0x48, 0x8d, 0x51, 0xe0, //0x0000088a leaq         $-32(%rcx), %rdx
	0x4c, 0x8d, 0x44, 0x24, 0x40, //0x0000088e leaq         $64(%rsp), %r8
	0xe9, 0x0a, 0x00, 0x00, 0x00, //0x00000893 jmp          LBB0_99
	//0x00000898 LBB0_98
	0x4c, 0x8d, 0x44, 0x24, 0x20, //0x00000898 leaq         $32(%rsp), %r8
	0x48, 0x8b, 0x54, 0x24, 0x08, //0x0000089d movq         $8(%rsp), %rdx
	//0x000008a2 LBB0_99
	0x48, 0x83, 0xfa, 0x10, //0x000008a2 cmpq         $16, %rdx
	0x0f, 0x82, 0x5c, 0x00, 0x00, 0x00, //0x000008a6 jb           LBB0_100
	0xc4, 0xc1, 0x7a, 0x6f, 0x06, //0x000008ac vmovdqu      (%r14), %xmm0
	0xc4, 0xc1, 0x7a, 0x7f, 0x00, //0x000008b1 vmovdqu      %xmm0, (%r8)
	0x49, 0x83, 0xc6, 0x10, //0x000008b6 addq         $16, %r14
	0x49, 0x83, 0xc0, 0x10, //0x000008ba addq         $16, %r8
	0x48, 0x83, 0xc2, 0xf0, //0x000008be addq         $-16, %rdx
	0x48, 0x83, 0xfa, 0x08, //0x000008c2 cmpq         $8, %rdx
	0x0f, 0x83, 0x46, 0x00, 0x00, 0x00, //0x000008c6 jae          LBB0_105
	//0x000008cc LBB0_101
	0x48, 0x83, 0xfa, 0x04, //0x000008cc cmpq         $4, %rdx
	0x0f, 0x8c, 0x58, 0x00, 0x00, 0x00, //0x000008d0 jl           LBB0_102
	//0x000008d6 LBB0_106
	0x41, 0x8b, 0x0e, //0x000008d6 movl         (%r14), %ecx
	0x41, 0x89, 0x08, //0x000008d9 movl         %ecx, (%r8)
	0x49, 0x83, 0xc6, 0x04, //0x000008dc addq         $4, %r14
	0x49, 0x83, 0xc0, 0x04, //0x000008e0 addq         $4, %r8
	0x48, 0x83, 0xc2, 0xfc, //0x000008e4 addq         $-4, %rdx
	0x48, 0x83, 0xfa, 0x02, //0x000008e8 cmpq         $2, %rdx
	0x0f, 0x83, 0x46, 0x00, 0x00, 0x00, //0x000008ec jae          LBB0_107
	//0x000008f2 LBB0_103
	0x4c, 0x89, 0xf1, //0x000008f2 movq         %r14, %rcx
	0x4c, 0x8d, 0x74, 0x24, 0x20, //0x000008f5 leaq         $32(%rsp), %r14
	0x48, 0x85, 0xd2, //0x000008fa testq        %rdx, %rdx
	0x0f, 0x85, 0x5a, 0x00, 0x00, 0x00, //0x000008fd jne          LBB0_108
	0xe9, 0x5f, 0x00, 0x00, 0x00, //0x00000903 jmp          LBB0_85
	//0x00000908 LBB0_100
	0x48, 0x83, 0xfa, 0x08, //0x00000908 cmpq         $8, %rdx
	0x0f, 0x82, 0xba, 0xff, 0xff, 0xff, //0x0000090c jb           LBB0_101
	//0x00000912 LBB0_105
	0x49, 0x8b, 0x0e, //0x00000912 movq         (%r14), %rcx
	0x49, 0x89, 0x08, //0x00000915 movq         %rcx, (%r8)
	0x49, 0x83, 0xc6, 0x08, //0x00000918 addq         $8, %r14
	0x49, 0x83, 0xc0, 0x08, //0x0000091c addq         $8, %r8
	0x48, 0x83, 0xc2, 0xf8, //0x00000920 addq         $-8, %rdx
	0x48, 0x83, 0xfa, 0x04, //0x00000924 cmpq         $4, %rdx
	0x0f, 0x8d, 0xa8, 0xff, 0xff, 0xff, //0x00000928 jge          LBB0_106
	//0x0000092e LBB0_102
	0x48, 0x83, 0xfa, 0x02, //0x0000092e cmpq         $2, %rdx
	0x0f, 0x82, 0xba, 0xff, 0xff, 0xff, //0x00000932 jb           LBB0_103
	//0x00000938 LBB0_107
	0x41, 0x0f, 0xb7, 0x0e, //0x00000938 movzwl       (%r14), %ecx
	0x66, 0x41, 0x89, 0x08, //0x0000093c movw         %cx, (%r8)
	0x49, 0x83, 0xc6, 0x02, //0x00000940 addq         $2, %r14
	0x49, 0x83, 0xc0, 0x02, //0x00000944 addq         $2, %r8
	0x48, 0x83, 0xc2, 0xfe, //0x00000948 addq         $-2, %rdx
	0x4c, 0x89, 0xf1, //0x0000094c movq         %r14, %rcx
	0x4c, 0x8d, 0x74, 0x24, 0x20, //0x0000094f leaq         $32(%rsp), %r14
	0x48, 0x85, 0xd2, //0x00000954 testq        %rdx, %rdx
	0x0f, 0x84, 0x0a, 0x00, 0x00, 0x00, //0x00000957 je           LBB0_85
	//0x0000095d LBB0_108
	0x8a, 0x09, //0x0000095d movb         (%rcx), %cl
	0x41, 0x88, 0x08, //0x0000095f movb         %cl, (%r8)
	0x4c, 0x8d, 0x74, 0x24, 0x20, //0x00000962 leaq         $32(%rsp), %r14
	//0x00000967 LBB0_85
	0xc4, 0xc1, 0x7a, 0x6f, 0x16, //0x00000967 vmovdqu      (%r14), %xmm2
	0xc4, 0xc1, 0x7a, 0x6f, 0x6e, 0x10, //0x0000096c vmovdqu      $16(%r14), %xmm5
	0xc4, 0xc1, 0x7a, 0x6f, 0x7e, 0x20, //0x00000972 vmovdqu      $32(%r14), %xmm7
	0xc4, 0xc1, 0x7a, 0x6f, 0x76, 0x30, //0x00000978 vmovdqu      $48(%r14), %xmm6
	0xc5, 0xa9, 0x74, 0xc2, //0x0000097e vpcmpeqb     %xmm2, %xmm10, %xmm0
	0xc5, 0x79, 0xd7, 0xe8, //0x00000982 vpmovmskb    %xmm0, %r13d
	0xc5, 0xa9, 0x74, 0xc5, //0x00000986 vpcmpeqb     %xmm5, %xmm10, %xmm0
	0xc5, 0xf9, 0xd7, 0xc8, //0x0000098a vpmovmskb    %xmm0, %ecx
	0xc5, 0xa9, 0x74, 0xc7, //0x0000098e vpcmpeqb     %xmm7, %xmm10, %xmm0
	0xc5, 0xf9, 0xd7, 0xd0, //0x00000992 vpmovmskb    %xmm0, %edx
	0xc5, 0xa9, 0x74, 0xc6, //0x00000996 vpcmpeqb     %xmm6, %xmm10, %xmm0
	0xc5, 0x79, 0xd7, 0xc8, //0x0000099a vpmovmskb    %xmm0, %r9d
	0x49, 0xc1, 0xe1, 0x30, //0x0000099e shlq         $48, %r9
	0x48, 0xc1, 0xe2, 0x20, //0x000009a2 shlq         $32, %rdx
	0x48, 0xc1, 0xe1, 0x10, //0x000009a6 shlq         $16, %rcx
	0x49, 0x09, 0xcd, //0x000009aa orq          %rcx, %r13
	0x49, 0x09, 0xd5, //0x000009ad orq          %rdx, %r13
	0x4d, 0x09, 0xcd, //0x000009b0 orq          %r9, %r13
	0xc5, 0xe9, 0x74, 0xc1, //0x000009b3 vpcmpeqb     %xmm1, %xmm2, %xmm0
	0xc5, 0xf9, 0xd7, 0xc8, //0x000009b7 vpmovmskb    %xmm0, %ecx
	0xc5, 0xd1, 0x74, 0xc1, //0x000009bb vpcmpeqb     %xmm1, %xmm5, %xmm0
	0xc5, 0xf9, 0xd7, 0xd0, //0x000009bf vpmovmskb    %xmm0, %edx
	0xc5, 0xc1, 0x74, 0xc1, //0x000009c3 vpcmpeqb     %xmm1, %xmm7, %xmm0
	0xc5, 0x79, 0xd7, 0xc8, //0x000009c7 vpmovmskb    %xmm0, %r9d
	0xc5, 0xc9, 0x74, 0xc1, //0x000009cb vpcmpeqb     %xmm1, %xmm6, %xmm0
	0xc5, 0x79, 0xd7, 0xd0, //0x000009cf vpmovmskb    %xmm0, %r10d
	0x49, 0xc1, 0xe2, 0x30, //0x000009d3 shlq         $48, %r10
	0x49, 0xc1, 0xe1, 0x20, //0x000009d7 shlq         $32, %r9
	0x48, 0xc1, 0xe2, 0x10, //0x000009db shlq         $16, %rdx
	0x48, 0x09, 0xd1, //0x000009df orq          %rdx, %rcx
	0x4c, 0x09, 0xc9, //0x000009e2 orq          %r9, %rcx
	0x4c, 0x09, 0xd1, //0x000009e5 orq          %r10, %rcx
	0x48, 0x89, 0xca, //0x000009e8 movq         %rcx, %rdx
	0x48, 0x09, 0xda, //0x000009eb orq          %rbx, %rdx
	0x0f, 0x84, 0x49, 0x00, 0x00, 0x00, //0x000009ee je           LBB0_87
	0x48, 0x89, 0xda, //0x000009f4 movq         %rbx, %rdx
	0x48, 0xf7, 0xd2, //0x000009f7 notq         %rdx
	0x48, 0x21, 0xca, //0x000009fa andq         %rcx, %rdx
	0x4c, 0x8d, 0x0c, 0x12, //0x000009fd leaq         (%rdx,%rdx), %r9
	0x49, 0x09, 0xd9, //0x00000a01 orq          %rbx, %r9
	0x4d, 0x89, 0xca, //0x00000a04 movq         %r9, %r10
	0x48, 0xbb, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00000a07 movabsq      $-6148914691236517206, %rbx
	0x49, 0x31, 0xda, //0x00000a11 xorq         %rbx, %r10
	0x48, 0x21, 0xd9, //0x00000a14 andq         %rbx, %rcx
	0x4c, 0x21, 0xd1, //0x00000a17 andq         %r10, %rcx
	0x31, 0xdb, //0x00000a1a xorl         %ebx, %ebx
	0x48, 0x01, 0xd1, //0x00000a1c addq         %rdx, %rcx
	0x0f, 0x92, 0xc3, //0x00000a1f setb         %bl
	0x48, 0x01, 0xc9, //0x00000a22 addq         %rcx, %rcx
	0x48, 0xba, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00000a25 movabsq      $6148914691236517205, %rdx
	0x48, 0x31, 0xd1, //0x00000a2f xorq         %rdx, %rcx
	0x4c, 0x21, 0xc9, //0x00000a32 andq         %r9, %rcx
	0x48, 0xf7, 0xd1, //0x00000a35 notq         %rcx
	0xe9, 0x09, 0x00, 0x00, 0x00, //0x00000a38 jmp          LBB0_88
	//0x00000a3d LBB0_87
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00000a3d movq         $-1, %rcx
	0x31, 0xdb, //0x00000a44 xorl         %ebx, %ebx
	//0x00000a46 LBB0_88
	0x48, 0x89, 0x5c, 0x24, 0x18, //0x00000a46 movq         %rbx, $24(%rsp)
	0x4c, 0x21, 0xe9, //0x00000a4b andq         %r13, %rcx
	0xc4, 0xe1, 0xf9, 0x6e, 0xc1, //0x00000a4e vmovq        %rcx, %xmm0
	0xc4, 0xc3, 0x79, 0x44, 0xc1, 0x00, //0x00000a53 vpclmulqdq   $0, %xmm9, %xmm0, %xmm0
	0xc4, 0xc1, 0xf9, 0x7e, 0xc5, //0x00000a59 vmovq        %xmm0, %r13
	0x4d, 0x31, 0xe5, //0x00000a5e xorq         %r12, %r13
	0xc5, 0xe9, 0x74, 0xc3, //0x00000a61 vpcmpeqb     %xmm3, %xmm2, %xmm0
	0xc5, 0x79, 0xd7, 0xd0, //0x00000a65 vpmovmskb    %xmm0, %r10d
	0xc5, 0xd1, 0x74, 0xc3, //0x00000a69 vpcmpeqb     %xmm3, %xmm5, %xmm0
	0xc5, 0xf9, 0xd7, 0xc8, //0x00000a6d vpmovmskb    %xmm0, %ecx
	0xc5, 0xc1, 0x74, 0xc3, //0x00000a71 vpcmpeqb     %xmm3, %xmm7, %xmm0
	0xc5, 0xf9, 0xd7, 0xd0, //0x00000a75 vpmovmskb    %xmm0, %edx
	0xc5, 0xc9, 0x74, 0xc3, //0x00000a79 vpcmpeqb     %xmm3, %xmm6, %xmm0
	0xc5, 0x79, 0xd7, 0xc8, //0x00000a7d vpmovmskb    %xmm0, %r9d
	0x49, 0xc1, 0xe1, 0x30, //0x00000a81 shlq         $48, %r9
	0x48, 0xc1, 0xe2, 0x20, //0x00000a85 shlq         $32, %rdx
	0x48, 0xc1, 0xe1, 0x10, //0x00000a89 shlq         $16, %rcx
	0x49, 0x09, 0xca, //0x00000a8d orq          %rcx, %r10
	0x49, 0x09, 0xd2, //0x00000a90 orq          %rdx, %r10
	0x4d, 0x09, 0xca, //0x00000a93 orq          %r9, %r10
	0x4d, 0x89, 0xe9, //0x00000a96 movq         %r13, %r9
	0x49, 0xf7, 0xd1, //0x00000a99 notq         %r9
	0x4d, 0x21, 0xca, //0x00000a9c andq         %r9, %r10
	0xc5, 0xe9, 0x74, 0xc4, //0x00000a9f vpcmpeqb     %xmm4, %xmm2, %xmm0
	0xc5, 0xf9, 0xd7, 0xc8, //0x00000aa3 vpmovmskb    %xmm0, %ecx
	0xc5, 0xd1, 0x74, 0xc4, //0x00000aa7 vpcmpeqb     %xmm4, %xmm5, %xmm0
	0xc5, 0xf9, 0xd7, 0xd0, //0x00000aab vpmovmskb    %xmm0, %edx
	0xc5, 0xc1, 0x74, 0xc4, //0x00000aaf vpcmpeqb     %xmm4, %xmm7, %xmm0
	0xc5, 0x79, 0xd7, 0xc0, //0x00000ab3 vpmovmskb    %xmm0, %r8d
	0xc5, 0xc9, 0x74, 0xc4, //0x00000ab7 vpcmpeqb     %xmm4, %xmm6, %xmm0
	0xc5, 0x79, 0xd7, 0xe0, //0x00000abb vpmovmskb    %xmm0, %r12d
	0x49, 0xc1, 0xe4, 0x30, //0x00000abf shlq         $48, %r12
	0x49, 0xc1, 0xe0, 0x20, //0x00000ac3 shlq         $32, %r8
	0x48, 0xc1, 0xe2, 0x10, //0x00000ac7 shlq         $16, %rdx
	0x48, 0x09, 0xd1, //0x00000acb orq          %rdx, %rcx
	0x4c, 0x09, 0xc1, //0x00000ace orq          %r8, %rcx
	0x4c, 0x09, 0xe1, //0x00000ad1 orq          %r12, %rcx
	0x4c, 0x21, 0xc9, //0x00000ad4 andq         %r9, %rcx
	0x0f, 0x84, 0x3f, 0x00, 0x00, 0x00, //0x00000ad7 je           LBB0_92
	0x4c, 0x8b, 0x44, 0x24, 0x10, //0x00000add movq         $16(%rsp), %r8
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000ae2 .p2align 4, 0x90
	//0x00000af0 LBB0_90
	0x48, 0x8d, 0x59, 0xff, //0x00000af0 leaq         $-1(%rcx), %rbx
	0x48, 0x89, 0xda, //0x00000af4 movq         %rbx, %rdx
	0x4c, 0x21, 0xd2, //0x00000af7 andq         %r10, %rdx
	0xf3, 0x48, 0x0f, 0xb8, 0xd2, //0x00000afa popcntq      %rdx, %rdx
	0x4c, 0x01, 0xc2, //0x00000aff addq         %r8, %rdx
	0x4c, 0x39, 0xfa, //0x00000b02 cmpq         %r15, %rdx
	0x0f, 0x86, 0x50, 0x00, 0x00, 0x00, //0x00000b05 jbe          LBB0_109
	0x49, 0xff, 0xc7, //0x00000b0b incq         %r15
	0x48, 0x21, 0xd9, //0x00000b0e andq         %rbx, %rcx
	0x0f, 0x85, 0xd9, 0xff, 0xff, 0xff, //0x00000b11 jne          LBB0_90
	0xe9, 0x05, 0x00, 0x00, 0x00, //0x00000b17 jmp          LBB0_93
	//0x00000b1c LBB0_92
	0x4c, 0x8b, 0x44, 0x24, 0x10, //0x00000b1c movq         $16(%rsp), %r8
	//0x00000b21 LBB0_93
	0x49, 0xc1, 0xfd, 0x3f, //0x00000b21 sarq         $63, %r13
	0xf3, 0x49, 0x0f, 0xb8, 0xca, //0x00000b25 popcntq      %r10, %rcx
	0x49, 0x01, 0xc8, //0x00000b2a addq         %rcx, %r8
	0x49, 0x83, 0xc6, 0x40, //0x00000b2d addq         $64, %r14
	0x48, 0x8b, 0x4c, 0x24, 0x08, //0x00000b31 movq         $8(%rsp), %rcx
	0x48, 0x83, 0xc1, 0xc0, //0x00000b36 addq         $-64, %rcx
	0x4d, 0x89, 0xec, //0x00000b3a movq         %r13, %r12
	0x48, 0x8b, 0x5c, 0x24, 0x18, //0x00000b3d movq         $24(%rsp), %rbx
	0x48, 0x83, 0xf9, 0x40, //0x00000b42 cmpq         $64, %rcx
	0x48, 0x89, 0x4c, 0x24, 0x08, //0x00000b46 movq         %rcx, $8(%rsp)
	0x4c, 0x89, 0x44, 0x24, 0x10, //0x00000b4b movq         %r8, $16(%rsp)
	0x0f, 0x8d, 0x11, 0xfe, 0xff, 0xff, //0x00000b50 jge          LBB0_85
	0xe9, 0xd9, 0xfc, 0xff, 0xff, //0x00000b56 jmp          LBB0_94
	//0x00000b5b LBB0_109
	0x48, 0x8b, 0x47, 0x08, //0x00000b5b movq         $8(%rdi), %rax
	0x48, 0x0f, 0xbc, 0xc9, //0x00000b5f bsfq         %rcx, %rcx
	0x48, 0x2b, 0x4c, 0x24, 0x08, //0x00000b63 subq         $8(%rsp), %rcx
	0x48, 0x8d, 0x44, 0x01, 0x01, //0x00000b68 leaq         $1(%rcx,%rax), %rax
	0x48, 0x89, 0x06, //0x00000b6d movq         %rax, (%rsi)
	0x48, 0x8b, 0x4f, 0x08, //0x00000b70 movq         $8(%rdi), %rcx
	0x48, 0x39, 0xc8, //0x00000b74 cmpq         %rcx, %rax
	0x48, 0x0f, 0x47, 0xc1, //0x00000b77 cmovaq       %rcx, %rax
	0x48, 0x89, 0x06, //0x00000b7b movq         %rax, (%rsi)
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000b7e movq         $-1, %rax
	0x4c, 0x0f, 0x47, 0xd8, //0x00000b85 cmovaq       %rax, %r11
	0xe9, 0x7d, 0xf7, 0xff, 0xff, //0x00000b89 jmp          LBB0_44
	//0x00000b8e LBB0_54
	0x4d, 0x85, 0xe4, //0x00000b8e testq        %r12, %r12
	0x0f, 0x85, 0x8e, 0x00, 0x00, 0x00, //0x00000b91 jne          LBB0_119
	0x4b, 0x8d, 0x5c, 0x17, 0x01, //0x00000b97 leaq         $1(%r15,%r10), %rbx
	0x49, 0xf7, 0xd7, //0x00000b9c notq         %r15
	0x4d, 0x01, 0xc7, //0x00000b9f addq         %r8, %r15
	//0x00000ba2 LBB0_56
	0x4d, 0x85, 0xff, //0x00000ba2 testq        %r15, %r15
	0x0f, 0x8f, 0x24, 0x00, 0x00, 0x00, //0x00000ba5 jg           LBB0_113
	0xe9, 0x5e, 0xf7, 0xff, 0xff, //0x00000bab jmp          LBB0_45
	//0x00000bb0 LBB0_111
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x00000bb0 movq         $-2, %rcx
	0xb8, 0x02, 0x00, 0x00, 0x00, //0x00000bb7 movl         $2, %eax
	0x48, 0x01, 0xc3, //0x00000bbc addq         %rax, %rbx
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000bbf movq         $-1, %rax
	0x49, 0x01, 0xcf, //0x00000bc6 addq         %rcx, %r15
	0x0f, 0x8e, 0x3f, 0xf7, 0xff, 0xff, //0x00000bc9 jle          LBB0_45
	//0x00000bcf LBB0_113
	0x0f, 0xb6, 0x03, //0x00000bcf movzbl       (%rbx), %eax
	0x3c, 0x5c, //0x00000bd2 cmpb         $92, %al
	0x0f, 0x84, 0xd6, 0xff, 0xff, 0xff, //0x00000bd4 je           LBB0_111
	0x3c, 0x22, //0x00000bda cmpb         $34, %al
	0x0f, 0x84, 0x24, 0x00, 0x00, 0x00, //0x00000bdc je           LBB0_116
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00000be2 movq         $-1, %rcx
	0xb8, 0x01, 0x00, 0x00, 0x00, //0x00000be9 movl         $1, %eax
	0x48, 0x01, 0xc3, //0x00000bee addq         %rax, %rbx
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000bf1 movq         $-1, %rax
	0x49, 0x01, 0xcf, //0x00000bf8 addq         %rcx, %r15
	0x0f, 0x8f, 0xce, 0xff, 0xff, 0xff, //0x00000bfb jg           LBB0_113
	0xe9, 0x08, 0xf7, 0xff, 0xff, //0x00000c01 jmp          LBB0_45
	//0x00000c06 LBB0_116
	0x4c, 0x29, 0xf3, //0x00000c06 subq         %r14, %rbx
	0x48, 0xff, 0xc3, //0x00000c09 incq         %rbx
	0xe9, 0xf7, 0xf6, 0xff, 0xff, //0x00000c0c jmp          LBB0_43
	//0x00000c11 LBB0_117
	0x4c, 0x01, 0xf3, //0x00000c11 addq         %r14, %rbx
	0xe9, 0x89, 0xff, 0xff, 0xff, //0x00000c14 jmp          LBB0_56
	//0x00000c19 LBB0_118
	0x48, 0x8b, 0x4f, 0x08, //0x00000c19 movq         $8(%rdi), %rcx
	0x48, 0x89, 0x0e, //0x00000c1d movq         %rcx, (%rsi)
	0xe9, 0xe9, 0xf6, 0xff, 0xff, //0x00000c20 jmp          LBB0_45
	//0x00000c25 LBB0_119
	0x49, 0x8d, 0x48, 0xff, //0x00000c25 leaq         $-1(%r8), %rcx
	0x4c, 0x39, 0xf9, //0x00000c29 cmpq         %r15, %rcx
	0x0f, 0x84, 0xdc, 0xf6, 0xff, 0xff, //0x00000c2c je           LBB0_45
	0x4b, 0x8d, 0x5c, 0x17, 0x02, //0x00000c32 leaq         $2(%r15,%r10), %rbx
	0x4d, 0x29, 0xf8, //0x00000c37 subq         %r15, %r8
	0x49, 0x83, 0xc0, 0xfe, //0x00000c3a addq         $-2, %r8
	0x4d, 0x89, 0xc7, //0x00000c3e movq         %r8, %r15
	0xe9, 0x5c, 0xff, 0xff, 0xff, //0x00000c41 jmp          LBB0_56
	0x90, 0x90, //0x00000c46 .p2align 2, 0x90
	// // .set L0_0_set_45, LBB0_45-LJTI0_0
	// // .set L0_0_set_47, LBB0_47-LJTI0_0
	// // .set L0_0_set_48, LBB0_48-LJTI0_0
	// // .set L0_0_set_29, LBB0_29-LJTI0_0
	// // .set L0_0_set_57, LBB0_57-LJTI0_0
	// // .set L0_0_set_82, LBB0_82-LJTI0_0
	// // .set L0_0_set_46, LBB0_46-LJTI0_0
	// // .set L0_0_set_84, LBB0_84-LJTI0_0
	//0x00000c48 LJTI0_0
	0xc6, 0xf6, 0xff, 0xff, //0x00000c48 .long L0_0_set_45
	0xeb, 0xf6, 0xff, 0xff, //0x00000c4c .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000c50 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000c54 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000c58 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000c5c .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000c60 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000c64 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000c68 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000c6c .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000c70 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000c74 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000c78 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000c7c .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000c80 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000c84 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000c88 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000c8c .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000c90 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000c94 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000c98 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000c9c .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000ca0 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000ca4 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000ca8 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000cac .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000cb0 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000cb4 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000cb8 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000cbc .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000cc0 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000cc4 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000cc8 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000ccc .long L0_0_set_47
	0xfa, 0xf6, 0xff, 0xff, //0x00000cd0 .long L0_0_set_48
	0xeb, 0xf6, 0xff, 0xff, //0x00000cd4 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000cd8 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000cdc .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000ce0 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000ce4 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000ce8 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000cec .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000cf0 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000cf4 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000cf8 .long L0_0_set_47
	0xe3, 0xf5, 0xff, 0xff, //0x00000cfc .long L0_0_set_29
	0xeb, 0xf6, 0xff, 0xff, //0x00000d00 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000d04 .long L0_0_set_47
	0xe3, 0xf5, 0xff, 0xff, //0x00000d08 .long L0_0_set_29
	0xe3, 0xf5, 0xff, 0xff, //0x00000d0c .long L0_0_set_29
	0xe3, 0xf5, 0xff, 0xff, //0x00000d10 .long L0_0_set_29
	0xe3, 0xf5, 0xff, 0xff, //0x00000d14 .long L0_0_set_29
	0xe3, 0xf5, 0xff, 0xff, //0x00000d18 .long L0_0_set_29
	0xe3, 0xf5, 0xff, 0xff, //0x00000d1c .long L0_0_set_29
	0xe3, 0xf5, 0xff, 0xff, //0x00000d20 .long L0_0_set_29
	0xe3, 0xf5, 0xff, 0xff, //0x00000d24 .long L0_0_set_29
	0xe3, 0xf5, 0xff, 0xff, //0x00000d28 .long L0_0_set_29
	0xe3, 0xf5, 0xff, 0xff, //0x00000d2c .long L0_0_set_29
	0xeb, 0xf6, 0xff, 0xff, //0x00000d30 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000d34 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000d38 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000d3c .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000d40 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000d44 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000d48 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000d4c .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000d50 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000d54 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000d58 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000d5c .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000d60 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000d64 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000d68 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000d6c .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000d70 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000d74 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000d78 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000d7c .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000d80 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000d84 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000d88 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000d8c .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000d90 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000d94 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000d98 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000d9c .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000da0 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000da4 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000da8 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000dac .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000db0 .long L0_0_set_47
	0x0a, 0xf8, 0xff, 0xff, //0x00000db4 .long L0_0_set_57
	0xeb, 0xf6, 0xff, 0xff, //0x00000db8 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000dbc .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000dc0 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000dc4 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000dc8 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000dcc .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000dd0 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000dd4 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000dd8 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000ddc .long L0_0_set_47
	0x83, 0xfb, 0xff, 0xff, //0x00000de0 .long L0_0_set_82
	0xeb, 0xf6, 0xff, 0xff, //0x00000de4 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000de8 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000dec .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000df0 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000df4 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000df8 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000dfc .long L0_0_set_47
	0xd8, 0xf6, 0xff, 0xff, //0x00000e00 .long L0_0_set_46
	0xeb, 0xf6, 0xff, 0xff, //0x00000e04 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000e08 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000e0c .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000e10 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000e14 .long L0_0_set_47
	0xd8, 0xf6, 0xff, 0xff, //0x00000e18 .long L0_0_set_46
	0xeb, 0xf6, 0xff, 0xff, //0x00000e1c .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000e20 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000e24 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000e28 .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000e2c .long L0_0_set_47
	0xeb, 0xf6, 0xff, 0xff, //0x00000e30 .long L0_0_set_47
	0x99, 0xfb, 0xff, 0xff, //0x00000e34 .long L0_0_set_84
	//0x00000e38 .p2align 2, 0x00
	//0x00000e38 _MASK_USE_NUMBER
	0x02, 0x00, 0x00, 0x00, //0x00000e38 .long 2
}
 
