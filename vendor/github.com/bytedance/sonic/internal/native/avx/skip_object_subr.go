// +build !noasm !appengine
// Code generated by asm2asm, DO NOT EDIT.

package avx

import (
	`github.com/bytedance/sonic/loader`
)

const (
    _entry__skip_object = 160
)

const (
    _stack__skip_object = 152
)

const (
    _size__skip_object = 9524
)

var (
    _pcsp__skip_object = [][2]uint32{
        {1, 0},
        {4, 8},
        {6, 16},
        {8, 24},
        {10, 32},
        {12, 40},
        {13, 48},
        {8962, 152},
        {8966, 48},
        {8967, 40},
        {8969, 32},
        {8971, 24},
        {8973, 16},
        {8975, 8},
        {8976, 0},
        {9524, 152},
    }
)

var _cfunc_skip_object = []loader.CFunc{
    {"_skip_object_entry", 0,  _entry__skip_object, 0, nil},
    {"_skip_object", _entry__skip_object, _size__skip_object, _stack__skip_object, _pcsp__skip_object},
}
