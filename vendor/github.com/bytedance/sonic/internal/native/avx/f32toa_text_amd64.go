// +build amd64
// Code generated by asm2asm, DO NOT EDIT.

package avx

var _text_f32toa = []byte{
	// .p2align 5, 0x00
	// LCPI0_0
	0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, // QUAD $0x3030303030303030; QUAD $0x3030303030303030  // .space 16, '0000000000000000'
	0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, //0x00000010 QUAD $0x3030303030303030; QUAD $0x3030303030303030  // .space 16, '0000000000000000'
	//0x00000020 .p2align 4, 0x90
	//0x00000020 _f32toa
	0x55, //0x00000020 pushq        %rbp
	0x48, 0x89, 0xe5, //0x00000021 movq         %rsp, %rbp
	0x41, 0x57, //0x00000024 pushq        %r15
	0x41, 0x56, //0x00000026 pushq        %r14
	0x41, 0x55, //0x00000028 pushq        %r13
	0x41, 0x54, //0x0000002a pushq        %r12
	0x53, //0x0000002c pushq        %rbx
	0xc5, 0xf9, 0x7e, 0xc0, //0x0000002d vmovd        %xmm0, %eax
	0x89, 0xc1, //0x00000031 movl         %eax, %ecx
	0xc1, 0xe9, 0x17, //0x00000033 shrl         $23, %ecx
	0x0f, 0xb6, 0xd9, //0x00000036 movzbl       %cl, %ebx
	0x81, 0xfb, 0xff, 0x00, 0x00, 0x00, //0x00000039 cmpl         $255, %ebx
	0x0f, 0x84, 0xff, 0x0c, 0x00, 0x00, //0x0000003f je           LBB0_139
	0xc6, 0x07, 0x2d, //0x00000045 movb         $45, (%rdi)
	0x41, 0x89, 0xc1, //0x00000048 movl         %eax, %r9d
	0x41, 0xc1, 0xe9, 0x1f, //0x0000004b shrl         $31, %r9d
	0x4e, 0x8d, 0x04, 0x0f, //0x0000004f leaq         (%rdi,%r9), %r8
	0xa9, 0xff, 0xff, 0xff, 0x7f, //0x00000053 testl        $2147483647, %eax
	0x0f, 0x84, 0xc6, 0x01, 0x00, 0x00, //0x00000058 je           LBB0_14
	0x25, 0xff, 0xff, 0x7f, 0x00, //0x0000005e andl         $8388607, %eax
	0x85, 0xdb, //0x00000063 testl        %ebx, %ebx
	0x0f, 0x84, 0xe1, 0x0c, 0x00, 0x00, //0x00000065 je           LBB0_140
	0x8d, 0xb0, 0x00, 0x00, 0x80, 0x00, //0x0000006b leal         $8388608(%rax), %esi
	0x44, 0x8d, 0xbb, 0x6a, 0xff, 0xff, 0xff, //0x00000071 leal         $-150(%rbx), %r15d
	0x8d, 0x4b, 0x81, //0x00000078 leal         $-127(%rbx), %ecx
	0x83, 0xf9, 0x17, //0x0000007b cmpl         $23, %ecx
	0x0f, 0x87, 0x1b, 0x00, 0x00, 0x00, //0x0000007e ja           LBB0_5
	0xb9, 0x96, 0x00, 0x00, 0x00, //0x00000084 movl         $150, %ecx
	0x29, 0xd9, //0x00000089 subl         %ebx, %ecx
	0x48, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x0000008b movq         $-1, %rdx
	0x48, 0xd3, 0xe2, //0x00000092 shlq         %cl, %rdx
	0xf7, 0xd2, //0x00000095 notl         %edx
	0x85, 0xf2, //0x00000097 testl        %esi, %edx
	0x0f, 0x84, 0x12, 0x04, 0x00, 0x00, //0x00000099 je           LBB0_32
	//0x0000009f LBB0_5
	0x41, 0x89, 0xf6, //0x0000009f movl         %esi, %r14d
	0x41, 0x83, 0xe6, 0x01, //0x000000a2 andl         $1, %r14d
	0x85, 0xc0, //0x000000a6 testl        %eax, %eax
	0x0f, 0x94, 0xc0, //0x000000a8 sete         %al
	0x83, 0xfb, 0x01, //0x000000ab cmpl         $1, %ebx
	0x0f, 0x97, 0xc1, //0x000000ae seta         %cl
	0x20, 0xc1, //0x000000b1 andb         %al, %cl
	0x0f, 0xb6, 0xc9, //0x000000b3 movzbl       %cl, %ecx
	0x41, 0x89, 0xf2, //0x000000b6 movl         %esi, %r10d
	0x41, 0xc1, 0xe2, 0x02, //0x000000b9 shll         $2, %r10d
	0x8d, 0x44, 0xb1, 0xfe, //0x000000bd leal         $-2(%rcx,%rsi,4), %eax
	0x45, 0x69, 0xdf, 0x13, 0x44, 0x13, 0x00, //0x000000c1 imull        $1262611, %r15d, %r11d
	0x31, 0xd2, //0x000000c8 xorl         %edx, %edx
	0x84, 0xc9, //0x000000ca testb        %cl, %cl
	0xb9, 0xff, 0xfe, 0x07, 0x00, //0x000000cc movl         $524031, %ecx
	0x0f, 0x44, 0xca, //0x000000d1 cmovel       %edx, %ecx
	0x41, 0x29, 0xcb, //0x000000d4 subl         %ecx, %r11d
	0x41, 0xc1, 0xfb, 0x16, //0x000000d7 sarl         $22, %r11d
	0x41, 0x69, 0xcb, 0xb1, 0x6c, 0xe5, 0xff, //0x000000db imull        $-1741647, %r11d, %ecx
	0xc1, 0xe9, 0x13, //0x000000e2 shrl         $19, %ecx
	0x44, 0x01, 0xf9, //0x000000e5 addl         %r15d, %ecx
	0xba, 0x1f, 0x00, 0x00, 0x00, //0x000000e8 movl         $31, %edx
	0x44, 0x29, 0xda, //0x000000ed subl         %r11d, %edx
	0x48, 0x63, 0xd2, //0x000000f0 movslq       %edx, %rdx
	0x48, 0x8d, 0x1d, 0x36, 0x0d, 0x00, 0x00, //0x000000f3 leaq         $3382(%rip), %rbx  /* _pow10_ceil_sig_f32.g+0(%rip) */
	0xfe, 0xc1, //0x000000fa incb         %cl
	0xd3, 0xe0, //0x000000fc shll         %cl, %eax
	0x4c, 0x8b, 0x24, 0xd3, //0x000000fe movq         (%rbx,%rdx,8), %r12
	0x49, 0xf7, 0xe4, //0x00000102 mulq         %r12
	0x48, 0xc1, 0xe8, 0x20, //0x00000105 shrq         $32, %rax
	0x31, 0xdb, //0x00000109 xorl         %ebx, %ebx
	0x83, 0xf8, 0x01, //0x0000010b cmpl         $1, %eax
	0x0f, 0x97, 0xc3, //0x0000010e seta         %bl
	0x41, 0xd3, 0xe2, //0x00000111 shll         %cl, %r10d
	0x09, 0xd3, //0x00000114 orl          %edx, %ebx
	0x4c, 0x89, 0xd0, //0x00000116 movq         %r10, %rax
	0x49, 0xf7, 0xe4, //0x00000119 mulq         %r12
	0x49, 0x89, 0xd2, //0x0000011c movq         %rdx, %r10
	0x48, 0xc1, 0xe8, 0x20, //0x0000011f shrq         $32, %rax
	0x45, 0x31, 0xff, //0x00000123 xorl         %r15d, %r15d
	0x83, 0xf8, 0x01, //0x00000126 cmpl         $1, %eax
	0x41, 0x0f, 0x97, 0xc7, //0x00000129 seta         %r15b
	0x8d, 0x04, 0xb5, 0x02, 0x00, 0x00, 0x00, //0x0000012d leal         $2(,%rsi,4), %eax
	0xd3, 0xe0, //0x00000134 shll         %cl, %eax
	0x45, 0x09, 0xd7, //0x00000136 orl          %r10d, %r15d
	0x49, 0xf7, 0xe4, //0x00000139 mulq         %r12
	0x48, 0xc1, 0xe8, 0x20, //0x0000013c shrq         $32, %rax
	0x31, 0xc9, //0x00000140 xorl         %ecx, %ecx
	0x83, 0xf8, 0x01, //0x00000142 cmpl         $1, %eax
	0x0f, 0x97, 0xc1, //0x00000145 seta         %cl
	0x09, 0xd1, //0x00000148 orl          %edx, %ecx
	0x44, 0x01, 0xf3, //0x0000014a addl         %r14d, %ebx
	0x44, 0x29, 0xf1, //0x0000014d subl         %r14d, %ecx
	0x41, 0x83, 0xff, 0x28, //0x00000150 cmpl         $40, %r15d
	0x0f, 0x82, 0x9a, 0x00, 0x00, 0x00, //0x00000154 jb           LBB0_12
	0x44, 0x89, 0xd2, //0x0000015a movl         %r10d, %edx
	0xb8, 0xcd, 0xcc, 0xcc, 0xcc, //0x0000015d movl         $3435973837, %eax
	0x48, 0x0f, 0xaf, 0xc2, //0x00000162 imulq        %rdx, %rax
	0x48, 0xc1, 0xe8, 0x25, //0x00000166 shrq         $37, %rax
	0x41, 0x89, 0xde, //0x0000016a movl         %ebx, %r14d
	0x48, 0x8d, 0x34, 0xc5, 0x00, 0x00, 0x00, 0x00, //0x0000016d leaq         (,%rax,8), %rsi
	0x48, 0x8d, 0x14, 0xb6, //0x00000175 leaq         (%rsi,%rsi,4), %rdx
	0x4c, 0x39, 0xf2, //0x00000179 cmpq         %r14, %rdx
	0x41, 0x0f, 0x93, 0xc4, //0x0000017c setae        %r12b
	0x4c, 0x8d, 0x74, 0xb6, 0x28, //0x00000180 leaq         $40(%rsi,%rsi,4), %r14
	0x89, 0xce, //0x00000185 movl         %ecx, %esi
	0x49, 0x39, 0xf6, //0x00000187 cmpq         %rsi, %r14
	0x0f, 0x96, 0xc2, //0x0000018a setbe        %dl
	0x41, 0x38, 0xd4, //0x0000018d cmpb         %dl, %r12b
	0x0f, 0x84, 0x5e, 0x00, 0x00, 0x00, //0x00000190 je           LBB0_12
	0x45, 0x31, 0xed, //0x00000196 xorl         %r13d, %r13d
	0x49, 0x39, 0xf6, //0x00000199 cmpq         %rsi, %r14
	0x41, 0x0f, 0x96, 0xc5, //0x0000019c setbe        %r13b
	0x41, 0x01, 0xc5, //0x000001a0 addl         %eax, %r13d
	0x41, 0xff, 0xc3, //0x000001a3 incl         %r11d
	0x41, 0x81, 0xfd, 0xa0, 0x86, 0x01, 0x00, //0x000001a6 cmpl         $100000, %r13d
	0x0f, 0x83, 0xb0, 0x00, 0x00, 0x00, //0x000001ad jae          LBB0_18
	//0x000001b3 LBB0_8
	0xb8, 0x01, 0x00, 0x00, 0x00, //0x000001b3 movl         $1, %eax
	0x41, 0x83, 0xfd, 0x0a, //0x000001b8 cmpl         $10, %r13d
	0x0f, 0x82, 0xd4, 0x00, 0x00, 0x00, //0x000001bc jb           LBB0_22
	0xb8, 0x02, 0x00, 0x00, 0x00, //0x000001c2 movl         $2, %eax
	0x41, 0x83, 0xfd, 0x64, //0x000001c7 cmpl         $100, %r13d
	0x0f, 0x82, 0xc5, 0x00, 0x00, 0x00, //0x000001cb jb           LBB0_22
	0xb8, 0x03, 0x00, 0x00, 0x00, //0x000001d1 movl         $3, %eax
	0x41, 0x81, 0xfd, 0xe8, 0x03, 0x00, 0x00, //0x000001d6 cmpl         $1000, %r13d
	0x0f, 0x82, 0xb3, 0x00, 0x00, 0x00, //0x000001dd jb           LBB0_22
	0x41, 0x81, 0xfd, 0x10, 0x27, 0x00, 0x00, //0x000001e3 cmpl         $10000, %r13d
	0xb8, 0x05, 0x00, 0x00, 0x00, //0x000001ea movl         $5, %eax
	0xe9, 0x9f, 0x00, 0x00, 0x00, //0x000001ef jmp          LBB0_21
	//0x000001f4 LBB0_12
	0x4d, 0x89, 0xd6, //0x000001f4 movq         %r10, %r14
	0x49, 0xc1, 0xee, 0x02, //0x000001f7 shrq         $2, %r14
	0x44, 0x89, 0xd6, //0x000001fb movl         %r10d, %esi
	0x83, 0xe6, 0xfc, //0x000001fe andl         $-4, %esi
	0x39, 0xf3, //0x00000201 cmpl         %esi, %ebx
	0x0f, 0x96, 0xc2, //0x00000203 setbe        %dl
	0x8d, 0x5e, 0x04, //0x00000206 leal         $4(%rsi), %ebx
	0x39, 0xcb, //0x00000209 cmpl         %ecx, %ebx
	0x0f, 0x96, 0xc0, //0x0000020b setbe        %al
	0x38, 0xc2, //0x0000020e cmpb         %al, %dl
	0x0f, 0x84, 0x1d, 0x00, 0x00, 0x00, //0x00000210 je           LBB0_15
	0x45, 0x31, 0xed, //0x00000216 xorl         %r13d, %r13d
	0x39, 0xcb, //0x00000219 cmpl         %ecx, %ebx
	0x41, 0x0f, 0x96, 0xc5, //0x0000021b setbe        %r13b
	0xe9, 0x2f, 0x00, 0x00, 0x00, //0x0000021f jmp          LBB0_17
	//0x00000224 LBB0_14
	0x41, 0xc6, 0x00, 0x30, //0x00000224 movb         $48, (%r8)
	0x41, 0x29, 0xf8, //0x00000228 subl         %edi, %r8d
	0x41, 0xff, 0xc0, //0x0000022b incl         %r8d
	0xe9, 0x00, 0x0b, 0x00, 0x00, //0x0000022e jmp          LBB0_138
	//0x00000233 LBB0_15
	0x83, 0xce, 0x02, //0x00000233 orl          $2, %esi
	0x41, 0xbd, 0x01, 0x00, 0x00, 0x00, //0x00000236 movl         $1, %r13d
	0x41, 0x39, 0xf7, //0x0000023c cmpl         %esi, %r15d
	0x0f, 0x87, 0x0e, 0x00, 0x00, 0x00, //0x0000023f ja           LBB0_17
	0x0f, 0x94, 0xc0, //0x00000245 sete         %al
	0x41, 0xc0, 0xea, 0x02, //0x00000248 shrb         $2, %r10b
	0x41, 0x20, 0xc2, //0x0000024c andb         %al, %r10b
	0x45, 0x0f, 0xb6, 0xea, //0x0000024f movzbl       %r10b, %r13d
	//0x00000253 LBB0_17
	0x45, 0x01, 0xf5, //0x00000253 addl         %r14d, %r13d
	0x41, 0x81, 0xfd, 0xa0, 0x86, 0x01, 0x00, //0x00000256 cmpl         $100000, %r13d
	0x0f, 0x82, 0x50, 0xff, 0xff, 0xff, //0x0000025d jb           LBB0_8
	//0x00000263 LBB0_18
	0xb8, 0x06, 0x00, 0x00, 0x00, //0x00000263 movl         $6, %eax
	0x41, 0x81, 0xfd, 0x40, 0x42, 0x0f, 0x00, //0x00000268 cmpl         $1000000, %r13d
	0x0f, 0x82, 0x21, 0x00, 0x00, 0x00, //0x0000026f jb           LBB0_22
	0xb8, 0x07, 0x00, 0x00, 0x00, //0x00000275 movl         $7, %eax
	0x41, 0x81, 0xfd, 0x80, 0x96, 0x98, 0x00, //0x0000027a cmpl         $10000000, %r13d
	0x0f, 0x82, 0x0f, 0x00, 0x00, 0x00, //0x00000281 jb           LBB0_22
	0x41, 0x81, 0xfd, 0x00, 0xe1, 0xf5, 0x05, //0x00000287 cmpl         $100000000, %r13d
	0xb8, 0x09, 0x00, 0x00, 0x00, //0x0000028e movl         $9, %eax
	//0x00000293 LBB0_21
	0x83, 0xd8, 0x00, //0x00000293 sbbl         $0, %eax
	//0x00000296 LBB0_22
	0x46, 0x8d, 0x3c, 0x18, //0x00000296 leal         (%rax,%r11), %r15d
	0x42, 0x8d, 0x4c, 0x18, 0x05, //0x0000029a leal         $5(%rax,%r11), %ecx
	0x83, 0xf9, 0x1b, //0x0000029f cmpl         $27, %ecx
	0x0f, 0x82, 0x77, 0x00, 0x00, 0x00, //0x000002a2 jb           LBB0_26
	0x89, 0xc0, //0x000002a8 movl         %eax, %eax
	0x49, 0x8d, 0x5c, 0x00, 0x01, //0x000002aa leaq         $1(%r8,%rax), %rbx
	0x41, 0x81, 0xfd, 0x10, 0x27, 0x00, 0x00, //0x000002af cmpl         $10000, %r13d
	0x0f, 0x82, 0xd9, 0x00, 0x00, 0x00, //0x000002b6 jb           LBB0_30
	0x44, 0x89, 0xe8, //0x000002bc movl         %r13d, %eax
	0x41, 0xbb, 0x59, 0x17, 0xb7, 0xd1, //0x000002bf movl         $3518437209, %r11d
	0x4c, 0x0f, 0xaf, 0xd8, //0x000002c5 imulq        %rax, %r11
	0x49, 0xc1, 0xeb, 0x2d, //0x000002c9 shrq         $45, %r11
	0x41, 0x69, 0xc3, 0xf0, 0xd8, 0xff, 0xff, //0x000002cd imull        $-10000, %r11d, %eax
	0x44, 0x01, 0xe8, //0x000002d4 addl         %r13d, %eax
	0x0f, 0x84, 0xb3, 0x04, 0x00, 0x00, //0x000002d7 je           LBB0_62
	0x89, 0xc1, //0x000002dd movl         %eax, %ecx
	0x48, 0x69, 0xc9, 0x1f, 0x85, 0xeb, 0x51, //0x000002df imulq        $1374389535, %rcx, %rcx
	0x48, 0xc1, 0xe9, 0x25, //0x000002e6 shrq         $37, %rcx
	0x6b, 0xd1, 0x64, //0x000002ea imull        $100, %ecx, %edx
	0x29, 0xd0, //0x000002ed subl         %edx, %eax
	0x48, 0x8d, 0x15, 0x6a, 0x0a, 0x00, 0x00, //0x000002ef leaq         $2666(%rip), %rdx  /* _Digits+0(%rip) */
	0x0f, 0xb7, 0x04, 0x42, //0x000002f6 movzwl       (%rdx,%rax,2), %eax
	0x66, 0x89, 0x43, 0xfe, //0x000002fa movw         %ax, $-2(%rbx)
	0x0f, 0xb7, 0x04, 0x4a, //0x000002fe movzwl       (%rdx,%rcx,2), %eax
	0x66, 0x89, 0x43, 0xfc, //0x00000302 movw         %ax, $-4(%rbx)
	0x45, 0x31, 0xc9, //0x00000306 xorl         %r9d, %r9d
	0x48, 0x8d, 0x4b, 0xfc, //0x00000309 leaq         $-4(%rbx), %rcx
	0x41, 0x83, 0xfb, 0x64, //0x0000030d cmpl         $100, %r11d
	0x0f, 0x83, 0x91, 0x00, 0x00, 0x00, //0x00000311 jae          LBB0_64
	//0x00000317 LBB0_31
	0x44, 0x89, 0xda, //0x00000317 movl         %r11d, %edx
	0xe9, 0xd4, 0x00, 0x00, 0x00, //0x0000031a jmp          LBB0_66
	//0x0000031f LBB0_26
	0x41, 0x89, 0xc4, //0x0000031f movl         %eax, %r12d
	0x45, 0x85, 0xdb, //0x00000322 testl        %r11d, %r11d
	0x0f, 0x88, 0x1d, 0x02, 0x00, 0x00, //0x00000325 js           LBB0_38
	0x4b, 0x8d, 0x34, 0x20, //0x0000032b leaq         (%r8,%r12), %rsi
	0x41, 0x81, 0xfd, 0x10, 0x27, 0x00, 0x00, //0x0000032f cmpl         $10000, %r13d
	0x0f, 0x82, 0xa7, 0x02, 0x00, 0x00, //0x00000336 jb           LBB0_43
	0x44, 0x89, 0xe8, //0x0000033c movl         %r13d, %eax
	0xb9, 0x59, 0x17, 0xb7, 0xd1, //0x0000033f movl         $3518437209, %ecx
	0x48, 0x0f, 0xaf, 0xc8, //0x00000344 imulq        %rax, %rcx
	0x48, 0xc1, 0xe9, 0x2d, //0x00000348 shrq         $45, %rcx
	0x69, 0xc1, 0xf0, 0xd8, 0xff, 0xff, //0x0000034c imull        $-10000, %ecx, %eax
	0x44, 0x01, 0xe8, //0x00000352 addl         %r13d, %eax
	0x48, 0x69, 0xd0, 0x1f, 0x85, 0xeb, 0x51, //0x00000355 imulq        $1374389535, %rax, %rdx
	0x48, 0xc1, 0xea, 0x25, //0x0000035c shrq         $37, %rdx
	0x6b, 0xda, 0x64, //0x00000360 imull        $100, %edx, %ebx
	0x29, 0xd8, //0x00000363 subl         %ebx, %eax
	0x48, 0x8d, 0x1d, 0xf4, 0x09, 0x00, 0x00, //0x00000365 leaq         $2548(%rip), %rbx  /* _Digits+0(%rip) */
	0x0f, 0xb7, 0x04, 0x43, //0x0000036c movzwl       (%rbx,%rax,2), %eax
	0x66, 0x89, 0x46, 0xfe, //0x00000370 movw         %ax, $-2(%rsi)
	0x48, 0x8d, 0x46, 0xfc, //0x00000374 leaq         $-4(%rsi), %rax
	0x0f, 0xb7, 0x14, 0x53, //0x00000378 movzwl       (%rbx,%rdx,2), %edx
	0x66, 0x89, 0x56, 0xfc, //0x0000037c movw         %dx, $-4(%rsi)
	0x41, 0x89, 0xcd, //0x00000380 movl         %ecx, %r13d
	0x41, 0x83, 0xfd, 0x64, //0x00000383 cmpl         $100, %r13d
	0x0f, 0x83, 0x63, 0x02, 0x00, 0x00, //0x00000387 jae          LBB0_44
	//0x0000038d LBB0_29
	0x44, 0x89, 0xe9, //0x0000038d movl         %r13d, %ecx
	0xe9, 0x9e, 0x02, 0x00, 0x00, //0x00000390 jmp          LBB0_46
	//0x00000395 LBB0_30
	0x45, 0x31, 0xc9, //0x00000395 xorl         %r9d, %r9d
	0x48, 0x89, 0xd9, //0x00000398 movq         %rbx, %rcx
	0x45, 0x89, 0xeb, //0x0000039b movl         %r13d, %r11d
	0x41, 0x83, 0xfb, 0x64, //0x0000039e cmpl         $100, %r11d
	0x0f, 0x82, 0x6f, 0xff, 0xff, 0xff, //0x000003a2 jb           LBB0_31
	//0x000003a8 LBB0_64
	0x48, 0xff, 0xc9, //0x000003a8 decq         %rcx
	0x4c, 0x8d, 0x15, 0xae, 0x09, 0x00, 0x00, //0x000003ab leaq         $2478(%rip), %r10  /* _Digits+0(%rip) */
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000003b2 .p2align 4, 0x90
	//0x000003c0 LBB0_65
	0x44, 0x89, 0xda, //0x000003c0 movl         %r11d, %edx
	0x48, 0x69, 0xd2, 0x1f, 0x85, 0xeb, 0x51, //0x000003c3 imulq        $1374389535, %rdx, %rdx
	0x48, 0xc1, 0xea, 0x25, //0x000003ca shrq         $37, %rdx
	0x6b, 0xc2, 0x64, //0x000003ce imull        $100, %edx, %eax
	0x44, 0x89, 0xde, //0x000003d1 movl         %r11d, %esi
	0x29, 0xc6, //0x000003d4 subl         %eax, %esi
	0x41, 0x0f, 0xb7, 0x04, 0x72, //0x000003d6 movzwl       (%r10,%rsi,2), %eax
	0x66, 0x89, 0x41, 0xff, //0x000003db movw         %ax, $-1(%rcx)
	0x48, 0x83, 0xc1, 0xfe, //0x000003df addq         $-2, %rcx
	0x41, 0x81, 0xfb, 0x0f, 0x27, 0x00, 0x00, //0x000003e3 cmpl         $9999, %r11d
	0x41, 0x89, 0xd3, //0x000003ea movl         %edx, %r11d
	0x0f, 0x87, 0xcd, 0xff, 0xff, 0xff, //0x000003ed ja           LBB0_65
	//0x000003f3 LBB0_66
	0x49, 0x8d, 0x70, 0x01, //0x000003f3 leaq         $1(%r8), %rsi
	0x83, 0xfa, 0x0a, //0x000003f7 cmpl         $10, %edx
	0x0f, 0x82, 0x1d, 0x00, 0x00, 0x00, //0x000003fa jb           LBB0_68
	0x89, 0xd0, //0x00000400 movl         %edx, %eax
	0x48, 0x8d, 0x0d, 0x57, 0x09, 0x00, 0x00, //0x00000402 leaq         $2391(%rip), %rcx  /* _Digits+0(%rip) */
	0x8a, 0x14, 0x41, //0x00000409 movb         (%rcx,%rax,2), %dl
	0x8a, 0x44, 0x41, 0x01, //0x0000040c movb         $1(%rcx,%rax,2), %al
	0x41, 0x88, 0x50, 0x01, //0x00000410 movb         %dl, $1(%r8)
	0x41, 0x88, 0x40, 0x02, //0x00000414 movb         %al, $2(%r8)
	0xe9, 0x05, 0x00, 0x00, 0x00, //0x00000418 jmp          LBB0_69
	//0x0000041d LBB0_68
	0x80, 0xc2, 0x30, //0x0000041d addb         $48, %dl
	0x88, 0x16, //0x00000420 movb         %dl, (%rsi)
	//0x00000422 LBB0_69
	0x4c, 0x29, 0xcb, //0x00000422 subq         %r9, %rbx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000425 .p2align 4, 0x90
	//0x00000430 LBB0_70
	0x80, 0x7b, 0xff, 0x30, //0x00000430 cmpb         $48, $-1(%rbx)
	0x48, 0x8d, 0x5b, 0xff, //0x00000434 leaq         $-1(%rbx), %rbx
	0x0f, 0x84, 0xf2, 0xff, 0xff, 0xff, //0x00000438 je           LBB0_70
	0x41, 0x88, 0x10, //0x0000043e movb         %dl, (%r8)
	0x48, 0x8d, 0x43, 0x01, //0x00000441 leaq         $1(%rbx), %rax
	0x48, 0x89, 0xc1, //0x00000445 movq         %rax, %rcx
	0x48, 0x29, 0xf1, //0x00000448 subq         %rsi, %rcx
	0x48, 0x83, 0xf9, 0x02, //0x0000044b cmpq         $2, %rcx
	0x0f, 0x8c, 0x06, 0x00, 0x00, 0x00, //0x0000044f jl           LBB0_73
	0xc6, 0x06, 0x2e, //0x00000455 movb         $46, (%rsi)
	0x48, 0x89, 0xc3, //0x00000458 movq         %rax, %rbx
	//0x0000045b LBB0_73
	0xc6, 0x03, 0x65, //0x0000045b movb         $101, (%rbx)
	0x45, 0x85, 0xff, //0x0000045e testl        %r15d, %r15d
	0x0f, 0x8e, 0x41, 0x01, 0x00, 0x00, //0x00000461 jle          LBB0_76
	0x41, 0xff, 0xcf, //0x00000467 decl         %r15d
	0xc6, 0x43, 0x01, 0x2b, //0x0000046a movb         $43, $1(%rbx)
	0x44, 0x89, 0xf8, //0x0000046e movl         %r15d, %eax
	0x83, 0xf8, 0x64, //0x00000471 cmpl         $100, %eax
	0x0f, 0x8c, 0x43, 0x01, 0x00, 0x00, //0x00000474 jl           LBB0_77
	//0x0000047a LBB0_75
	0x89, 0xc1, //0x0000047a movl         %eax, %ecx
	0xba, 0xcd, 0xcc, 0xcc, 0xcc, //0x0000047c movl         $3435973837, %edx
	0x48, 0x0f, 0xaf, 0xd1, //0x00000481 imulq        %rcx, %rdx
	0x48, 0xc1, 0xea, 0x23, //0x00000485 shrq         $35, %rdx
	0x8d, 0x0c, 0x12, //0x00000489 leal         (%rdx,%rdx), %ecx
	0x8d, 0x0c, 0x89, //0x0000048c leal         (%rcx,%rcx,4), %ecx
	0x29, 0xc8, //0x0000048f subl         %ecx, %eax
	0x48, 0x8d, 0x0d, 0xc8, 0x08, 0x00, 0x00, //0x00000491 leaq         $2248(%rip), %rcx  /* _Digits+0(%rip) */
	0x0f, 0xb7, 0x0c, 0x51, //0x00000498 movzwl       (%rcx,%rdx,2), %ecx
	0x66, 0x89, 0x4b, 0x02, //0x0000049c movw         %cx, $2(%rbx)
	0x0c, 0x30, //0x000004a0 orb          $48, %al
	0x88, 0x43, 0x04, //0x000004a2 movb         %al, $4(%rbx)
	0x48, 0x83, 0xc3, 0x05, //0x000004a5 addq         $5, %rbx
	0x49, 0x89, 0xd8, //0x000004a9 movq         %rbx, %r8
	0xe9, 0x7f, 0x08, 0x00, 0x00, //0x000004ac jmp          LBB0_137
	//0x000004b1 LBB0_32
	0xd3, 0xee, //0x000004b1 shrl         %cl, %esi
	0x81, 0xfe, 0xa0, 0x86, 0x01, 0x00, //0x000004b3 cmpl         $100000, %esi
	0x0f, 0x82, 0x1a, 0x02, 0x00, 0x00, //0x000004b9 jb           LBB0_52
	0xb8, 0x06, 0x00, 0x00, 0x00, //0x000004bf movl         $6, %eax
	0x81, 0xfe, 0x40, 0x42, 0x0f, 0x00, //0x000004c4 cmpl         $1000000, %esi
	0x0f, 0x82, 0x20, 0x00, 0x00, 0x00, //0x000004ca jb           LBB0_36
	0xb8, 0x07, 0x00, 0x00, 0x00, //0x000004d0 movl         $7, %eax
	0x81, 0xfe, 0x80, 0x96, 0x98, 0x00, //0x000004d5 cmpl         $10000000, %esi
	0x0f, 0x82, 0x0f, 0x00, 0x00, 0x00, //0x000004db jb           LBB0_36
	0x81, 0xfe, 0x00, 0xe1, 0xf5, 0x05, //0x000004e1 cmpl         $100000000, %esi
	0xb8, 0x09, 0x00, 0x00, 0x00, //0x000004e7 movl         $9, %eax
	0x48, 0x83, 0xd8, 0x00, //0x000004ec sbbq         $0, %rax
	//0x000004f0 LBB0_36
	0x4c, 0x01, 0xc0, //0x000004f0 addq         %r8, %rax
	//0x000004f3 LBB0_37
	0x89, 0xf1, //0x000004f3 movl         %esi, %ecx
	0xba, 0x59, 0x17, 0xb7, 0xd1, //0x000004f5 movl         $3518437209, %edx
	0x48, 0x0f, 0xaf, 0xd1, //0x000004fa imulq        %rcx, %rdx
	0x48, 0xc1, 0xea, 0x2d, //0x000004fe shrq         $45, %rdx
	0x69, 0xca, 0xf0, 0xd8, 0xff, 0xff, //0x00000502 imull        $-10000, %edx, %ecx
	0x01, 0xf1, //0x00000508 addl         %esi, %ecx
	0x48, 0x69, 0xf1, 0x1f, 0x85, 0xeb, 0x51, //0x0000050a imulq        $1374389535, %rcx, %rsi
	0x48, 0xc1, 0xee, 0x25, //0x00000511 shrq         $37, %rsi
	0x6b, 0xde, 0x64, //0x00000515 imull        $100, %esi, %ebx
	0x29, 0xd9, //0x00000518 subl         %ebx, %ecx
	0x48, 0x8d, 0x1d, 0x3f, 0x08, 0x00, 0x00, //0x0000051a leaq         $2111(%rip), %rbx  /* _Digits+0(%rip) */
	0x0f, 0xb7, 0x0c, 0x4b, //0x00000521 movzwl       (%rbx,%rcx,2), %ecx
	0x66, 0x89, 0x48, 0xfe, //0x00000525 movw         %cx, $-2(%rax)
	0x0f, 0xb7, 0x0c, 0x73, //0x00000529 movzwl       (%rbx,%rsi,2), %ecx
	0x66, 0x89, 0x48, 0xfc, //0x0000052d movw         %cx, $-4(%rax)
	0x49, 0x89, 0xc1, //0x00000531 movq         %rax, %r9
	0x48, 0x83, 0xc0, 0xfc, //0x00000534 addq         $-4, %rax
	0x89, 0xd6, //0x00000538 movl         %edx, %esi
	0x83, 0xfe, 0x64, //0x0000053a cmpl         $100, %esi
	0x0f, 0x83, 0xd5, 0x01, 0x00, 0x00, //0x0000053d jae          LBB0_56
	0xe9, 0x17, 0x02, 0x00, 0x00, //0x00000543 jmp          LBB0_58
	//0x00000548 LBB0_38
	0x45, 0x85, 0xff, //0x00000548 testl        %r15d, %r15d
	0x0f, 0x8f, 0x90, 0x04, 0x00, 0x00, //0x0000054b jg           LBB0_98
	0x66, 0x41, 0xc7, 0x00, 0x30, 0x2e, //0x00000551 movw         $11824, (%r8)
	0x49, 0x83, 0xc0, 0x02, //0x00000557 addq         $2, %r8
	0x45, 0x85, 0xff, //0x0000055b testl        %r15d, %r15d
	0x0f, 0x89, 0x7d, 0x04, 0x00, 0x00, //0x0000055e jns          LBB0_98
	0x31, 0xf6, //0x00000564 xorl         %esi, %esi
	0x41, 0x83, 0xff, 0x80, //0x00000566 cmpl         $-128, %r15d
	0x0f, 0x87, 0x5a, 0x04, 0x00, 0x00, //0x0000056a ja           LBB0_96
	0x45, 0x89, 0xfa, //0x00000570 movl         %r15d, %r10d
	0x41, 0xf7, 0xd2, //0x00000573 notl         %r10d
	0x49, 0xff, 0xc2, //0x00000576 incq         %r10
	0x4c, 0x89, 0xd6, //0x00000579 movq         %r10, %rsi
	0x48, 0x83, 0xe6, 0x80, //0x0000057c andq         $-128, %rsi
	0x48, 0x8d, 0x46, 0x80, //0x00000580 leaq         $-128(%rsi), %rax
	0x48, 0x89, 0xc1, //0x00000584 movq         %rax, %rcx
	0x48, 0xc1, 0xe9, 0x07, //0x00000587 shrq         $7, %rcx
	0x48, 0xff, 0xc1, //0x0000058b incq         %rcx
	0x41, 0x89, 0xcb, //0x0000058e movl         %ecx, %r11d
	0x41, 0x83, 0xe3, 0x03, //0x00000591 andl         $3, %r11d
	0x48, 0x3d, 0x80, 0x01, 0x00, 0x00, //0x00000595 cmpq         $384, %rax
	0x0f, 0x83, 0x38, 0x03, 0x00, 0x00, //0x0000059b jae          LBB0_90
	0x31, 0xdb, //0x000005a1 xorl         %ebx, %ebx
	0xe9, 0xda, 0x03, 0x00, 0x00, //0x000005a3 jmp          LBB0_92
	//0x000005a8 LBB0_76
	0xc6, 0x43, 0x01, 0x2d, //0x000005a8 movb         $45, $1(%rbx)
	0xb8, 0x01, 0x00, 0x00, 0x00, //0x000005ac movl         $1, %eax
	0x44, 0x29, 0xf8, //0x000005b1 subl         %r15d, %eax
	0x83, 0xf8, 0x64, //0x000005b4 cmpl         $100, %eax
	0x0f, 0x8d, 0xbd, 0xfe, 0xff, 0xff, //0x000005b7 jge          LBB0_75
	//0x000005bd LBB0_77
	0x83, 0xf8, 0x0a, //0x000005bd cmpl         $10, %eax
	0x0f, 0x8c, 0x02, 0x01, 0x00, 0x00, //0x000005c0 jl           LBB0_79
	0x48, 0x98, //0x000005c6 cltq         
	0x48, 0x8d, 0x0d, 0x91, 0x07, 0x00, 0x00, //0x000005c8 leaq         $1937(%rip), %rcx  /* _Digits+0(%rip) */
	0x0f, 0xb7, 0x04, 0x41, //0x000005cf movzwl       (%rcx,%rax,2), %eax
	0x66, 0x89, 0x43, 0x02, //0x000005d3 movw         %ax, $2(%rbx)
	0x48, 0x83, 0xc3, 0x04, //0x000005d7 addq         $4, %rbx
	0x49, 0x89, 0xd8, //0x000005db movq         %rbx, %r8
	0xe9, 0x4d, 0x07, 0x00, 0x00, //0x000005de jmp          LBB0_137
	//0x000005e3 LBB0_43
	0x48, 0x89, 0xf0, //0x000005e3 movq         %rsi, %rax
	0x41, 0x83, 0xfd, 0x64, //0x000005e6 cmpl         $100, %r13d
	0x0f, 0x82, 0x9d, 0xfd, 0xff, 0xff, //0x000005ea jb           LBB0_29
	//0x000005f0 LBB0_44
	0x48, 0xff, 0xc8, //0x000005f0 decq         %rax
	0x4c, 0x8d, 0x15, 0x66, 0x07, 0x00, 0x00, //0x000005f3 leaq         $1894(%rip), %r10  /* _Digits+0(%rip) */
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000005fa .p2align 4, 0x90
	//0x00000600 LBB0_45
	0x44, 0x89, 0xe9, //0x00000600 movl         %r13d, %ecx
	0x48, 0x69, 0xc9, 0x1f, 0x85, 0xeb, 0x51, //0x00000603 imulq        $1374389535, %rcx, %rcx
	0x48, 0xc1, 0xe9, 0x25, //0x0000060a shrq         $37, %rcx
	0x6b, 0xd9, 0x64, //0x0000060e imull        $100, %ecx, %ebx
	0x44, 0x89, 0xea, //0x00000611 movl         %r13d, %edx
	0x29, 0xda, //0x00000614 subl         %ebx, %edx
	0x41, 0x0f, 0xb7, 0x14, 0x52, //0x00000616 movzwl       (%r10,%rdx,2), %edx
	0x66, 0x89, 0x50, 0xff, //0x0000061b movw         %dx, $-1(%rax)
	0x48, 0x83, 0xc0, 0xfe, //0x0000061f addq         $-2, %rax
	0x41, 0x81, 0xfd, 0x0f, 0x27, 0x00, 0x00, //0x00000623 cmpl         $9999, %r13d
	0x41, 0x89, 0xcd, //0x0000062a movl         %ecx, %r13d
	0x0f, 0x87, 0xcd, 0xff, 0xff, 0xff, //0x0000062d ja           LBB0_45
	//0x00000633 LBB0_46
	0x49, 0x63, 0xc7, //0x00000633 movslq       %r15d, %rax
	0x83, 0xf9, 0x0a, //0x00000636 cmpl         $10, %ecx
	0x0f, 0x82, 0x22, 0x00, 0x00, 0x00, //0x00000639 jb           LBB0_48
	0x89, 0xc9, //0x0000063f movl         %ecx, %ecx
	0x48, 0x8d, 0x15, 0x18, 0x07, 0x00, 0x00, //0x00000641 leaq         $1816(%rip), %rdx  /* _Digits+0(%rip) */
	0x0f, 0xb7, 0x0c, 0x4a, //0x00000648 movzwl       (%rdx,%rcx,2), %ecx
	0x66, 0x41, 0x89, 0x08, //0x0000064c movw         %cx, (%r8)
	0x49, 0x01, 0xc0, //0x00000650 addq         %rax, %r8
	0x49, 0x39, 0xc4, //0x00000653 cmpq         %rax, %r12
	0x0f, 0x8c, 0x17, 0x00, 0x00, 0x00, //0x00000656 jl           LBB0_49
	0xe9, 0xcf, 0x06, 0x00, 0x00, //0x0000065c jmp          LBB0_137
	//0x00000661 LBB0_48
	0x80, 0xc1, 0x30, //0x00000661 addb         $48, %cl
	0x41, 0x88, 0x08, //0x00000664 movb         %cl, (%r8)
	0x49, 0x01, 0xc0, //0x00000667 addq         %rax, %r8
	0x49, 0x39, 0xc4, //0x0000066a cmpq         %rax, %r12
	0x0f, 0x8d, 0xbd, 0x06, 0x00, 0x00, //0x0000066d jge          LBB0_137
	//0x00000673 LBB0_49
	0x4b, 0x8d, 0x04, 0x21, //0x00000673 leaq         (%r9,%r12), %rax
	0x4c, 0x8d, 0x5c, 0x07, 0x01, //0x00000677 leaq         $1(%rdi,%rax), %r11
	0x4d, 0x39, 0xc3, //0x0000067c cmpq         %r8, %r11
	0x4d, 0x0f, 0x46, 0xd8, //0x0000067f cmovbeq      %r8, %r11
	0x4a, 0x8d, 0x0c, 0x0f, //0x00000683 leaq         (%rdi,%r9), %rcx
	0x4c, 0x01, 0xe1, //0x00000687 addq         %r12, %rcx
	0x49, 0x29, 0xcb, //0x0000068a subq         %rcx, %r11
	0x49, 0x81, 0xfb, 0x80, 0x00, 0x00, 0x00, //0x0000068d cmpq         $128, %r11
	0x0f, 0x82, 0x06, 0x02, 0x00, 0x00, //0x00000694 jb           LBB0_87
	0x4d, 0x89, 0xda, //0x0000069a movq         %r11, %r10
	0x49, 0x83, 0xe2, 0x80, //0x0000069d andq         $-128, %r10
	0x49, 0x8d, 0x4a, 0x80, //0x000006a1 leaq         $-128(%r10), %rcx
	0x48, 0x89, 0xcb, //0x000006a5 movq         %rcx, %rbx
	0x48, 0xc1, 0xeb, 0x07, //0x000006a8 shrq         $7, %rbx
	0x48, 0xff, 0xc3, //0x000006ac incq         %rbx
	0x89, 0xda, //0x000006af movl         %ebx, %edx
	0x83, 0xe2, 0x03, //0x000006b1 andl         $3, %edx
	0x48, 0x81, 0xf9, 0x80, 0x01, 0x00, 0x00, //0x000006b4 cmpq         $384, %rcx
	0x0f, 0x83, 0xe8, 0x00, 0x00, 0x00, //0x000006bb jae          LBB0_80
	0x31, 0xc0, //0x000006c1 xorl         %eax, %eax
	0xe9, 0x88, 0x01, 0x00, 0x00, //0x000006c3 jmp          LBB0_82
	//0x000006c8 LBB0_79
	0x04, 0x30, //0x000006c8 addb         $48, %al
	0x88, 0x43, 0x02, //0x000006ca movb         %al, $2(%rbx)
	0x48, 0x83, 0xc3, 0x03, //0x000006cd addq         $3, %rbx
	0x49, 0x89, 0xd8, //0x000006d1 movq         %rbx, %r8
	0xe9, 0x57, 0x06, 0x00, 0x00, //0x000006d4 jmp          LBB0_137
	//0x000006d9 LBB0_52
	0x41, 0xb9, 0x01, 0x00, 0x00, 0x00, //0x000006d9 movl         $1, %r9d
	0x83, 0xfe, 0x0a, //0x000006df cmpl         $10, %esi
	0x0f, 0x82, 0x21, 0x00, 0x00, 0x00, //0x000006e2 jb           LBB0_55
	0x41, 0xb9, 0x02, 0x00, 0x00, 0x00, //0x000006e8 movl         $2, %r9d
	0x83, 0xfe, 0x64, //0x000006ee cmpl         $100, %esi
	0x0f, 0x82, 0x12, 0x00, 0x00, 0x00, //0x000006f1 jb           LBB0_55
	0x41, 0xb9, 0x03, 0x00, 0x00, 0x00, //0x000006f7 movl         $3, %r9d
	0x81, 0xfe, 0xe8, 0x03, 0x00, 0x00, //0x000006fd cmpl         $1000, %esi
	0x0f, 0x83, 0xab, 0x01, 0x00, 0x00, //0x00000703 jae          LBB0_88
	//0x00000709 LBB0_55
	0x4d, 0x01, 0xc1, //0x00000709 addq         %r8, %r9
	0x4c, 0x89, 0xc8, //0x0000070c movq         %r9, %rax
	0x83, 0xfe, 0x64, //0x0000070f cmpl         $100, %esi
	0x0f, 0x82, 0x47, 0x00, 0x00, 0x00, //0x00000712 jb           LBB0_58
	//0x00000718 LBB0_56
	0x48, 0xff, 0xc8, //0x00000718 decq         %rax
	0x4c, 0x8d, 0x15, 0x3e, 0x06, 0x00, 0x00, //0x0000071b leaq         $1598(%rip), %r10  /* _Digits+0(%rip) */
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000722 .p2align 4, 0x90
	//0x00000730 LBB0_57
	0x89, 0xf3, //0x00000730 movl         %esi, %ebx
	0x89, 0xf6, //0x00000732 movl         %esi, %esi
	0x48, 0x69, 0xf6, 0x1f, 0x85, 0xeb, 0x51, //0x00000734 imulq        $1374389535, %rsi, %rsi
	0x48, 0xc1, 0xee, 0x25, //0x0000073b shrq         $37, %rsi
	0x6b, 0xce, 0x64, //0x0000073f imull        $100, %esi, %ecx
	0x89, 0xda, //0x00000742 movl         %ebx, %edx
	0x29, 0xca, //0x00000744 subl         %ecx, %edx
	0x41, 0x0f, 0xb7, 0x0c, 0x52, //0x00000746 movzwl       (%r10,%rdx,2), %ecx
	0x66, 0x89, 0x48, 0xff, //0x0000074b movw         %cx, $-1(%rax)
	0x48, 0x83, 0xc0, 0xfe, //0x0000074f addq         $-2, %rax
	0x81, 0xfb, 0x0f, 0x27, 0x00, 0x00, //0x00000753 cmpl         $9999, %ebx
	0x0f, 0x87, 0xd1, 0xff, 0xff, 0xff, //0x00000759 ja           LBB0_57
	//0x0000075f LBB0_58
	0x83, 0xfe, 0x0a, //0x0000075f cmpl         $10, %esi
	0x0f, 0x82, 0x16, 0x00, 0x00, 0x00, //0x00000762 jb           LBB0_60
	0x89, 0xf0, //0x00000768 movl         %esi, %eax
	0x48, 0x8d, 0x0d, 0xef, 0x05, 0x00, 0x00, //0x0000076a leaq         $1519(%rip), %rcx  /* _Digits+0(%rip) */
	0x0f, 0xb7, 0x04, 0x41, //0x00000771 movzwl       (%rcx,%rax,2), %eax
	0x66, 0x41, 0x89, 0x00, //0x00000775 movw         %ax, (%r8)
	0xe9, 0x07, 0x00, 0x00, 0x00, //0x00000779 jmp          LBB0_61
	//0x0000077e LBB0_60
	0x40, 0x80, 0xc6, 0x30, //0x0000077e addb         $48, %sil
	0x41, 0x88, 0x30, //0x00000782 movb         %sil, (%r8)
	//0x00000785 LBB0_61
	0x41, 0x29, 0xf9, //0x00000785 subl         %edi, %r9d
	0x45, 0x89, 0xc8, //0x00000788 movl         %r9d, %r8d
	0xe9, 0xa3, 0x05, 0x00, 0x00, //0x0000078b jmp          LBB0_138
	//0x00000790 LBB0_62
	0x41, 0xb9, 0x04, 0x00, 0x00, 0x00, //0x00000790 movl         $4, %r9d
	0x48, 0x8d, 0x4b, 0xfc, //0x00000796 leaq         $-4(%rbx), %rcx
	0x41, 0x83, 0xfb, 0x64, //0x0000079a cmpl         $100, %r11d
	0x0f, 0x82, 0x73, 0xfb, 0xff, 0xff, //0x0000079e jb           LBB0_31
	0xe9, 0xff, 0xfb, 0xff, 0xff, //0x000007a4 jmp          LBB0_64
	//0x000007a9 LBB0_80
	0x48, 0x29, 0xd3, //0x000007a9 subq         %rdx, %rbx
	0x48, 0x8d, 0x8c, 0x07, 0xe0, 0x01, 0x00, 0x00, //0x000007ac leaq         $480(%rdi,%rax), %rcx
	0x31, 0xc0, //0x000007b4 xorl         %eax, %eax
	0xc5, 0xfe, 0x6f, 0x05, 0x42, 0xf8, 0xff, 0xff, //0x000007b6 vmovdqu      $-1982(%rip), %ymm0  /* LCPI0_0+0(%rip) */
	0x90, 0x90, //0x000007be .p2align 4, 0x90
	//0x000007c0 LBB0_81
	0xc5, 0xfe, 0x7f, 0x84, 0x01, 0x20, 0xfe, 0xff, 0xff, //0x000007c0 vmovdqu      %ymm0, $-480(%rcx,%rax)
	0xc5, 0xfe, 0x7f, 0x84, 0x01, 0x40, 0xfe, 0xff, 0xff, //0x000007c9 vmovdqu      %ymm0, $-448(%rcx,%rax)
	0xc5, 0xfe, 0x7f, 0x84, 0x01, 0x60, 0xfe, 0xff, 0xff, //0x000007d2 vmovdqu      %ymm0, $-416(%rcx,%rax)
	0xc5, 0xfe, 0x7f, 0x84, 0x01, 0x80, 0xfe, 0xff, 0xff, //0x000007db vmovdqu      %ymm0, $-384(%rcx,%rax)
	0xc5, 0xfe, 0x7f, 0x84, 0x01, 0xa0, 0xfe, 0xff, 0xff, //0x000007e4 vmovdqu      %ymm0, $-352(%rcx,%rax)
	0xc5, 0xfe, 0x7f, 0x84, 0x01, 0xc0, 0xfe, 0xff, 0xff, //0x000007ed vmovdqu      %ymm0, $-320(%rcx,%rax)
	0xc5, 0xfe, 0x7f, 0x84, 0x01, 0xe0, 0xfe, 0xff, 0xff, //0x000007f6 vmovdqu      %ymm0, $-288(%rcx,%rax)
	0xc5, 0xfe, 0x7f, 0x84, 0x01, 0x00, 0xff, 0xff, 0xff, //0x000007ff vmovdqu      %ymm0, $-256(%rcx,%rax)
	0xc5, 0xfe, 0x7f, 0x84, 0x01, 0x20, 0xff, 0xff, 0xff, //0x00000808 vmovdqu      %ymm0, $-224(%rcx,%rax)
	0xc5, 0xfe, 0x7f, 0x84, 0x01, 0x40, 0xff, 0xff, 0xff, //0x00000811 vmovdqu      %ymm0, $-192(%rcx,%rax)
	0xc5, 0xfe, 0x7f, 0x84, 0x01, 0x60, 0xff, 0xff, 0xff, //0x0000081a vmovdqu      %ymm0, $-160(%rcx,%rax)
	0xc5, 0xfe, 0x7f, 0x44, 0x01, 0x80, //0x00000823 vmovdqu      %ymm0, $-128(%rcx,%rax)
	0xc5, 0xfe, 0x7f, 0x44, 0x01, 0xa0, //0x00000829 vmovdqu      %ymm0, $-96(%rcx,%rax)
	0xc5, 0xfe, 0x7f, 0x44, 0x01, 0xc0, //0x0000082f vmovdqu      %ymm0, $-64(%rcx,%rax)
	0xc5, 0xfe, 0x7f, 0x44, 0x01, 0xe0, //0x00000835 vmovdqu      %ymm0, $-32(%rcx,%rax)
	0xc5, 0xfe, 0x7f, 0x04, 0x01, //0x0000083b vmovdqu      %ymm0, (%rcx,%rax)
	0x48, 0x05, 0x00, 0x02, 0x00, 0x00, //0x00000840 addq         $512, %rax
	0x48, 0x83, 0xc3, 0xfc, //0x00000846 addq         $-4, %rbx
	0x0f, 0x85, 0x70, 0xff, 0xff, 0xff, //0x0000084a jne          LBB0_81
	//0x00000850 LBB0_82
	0x48, 0x85, 0xd2, //0x00000850 testq        %rdx, %rdx
	0x0f, 0x84, 0x37, 0x00, 0x00, 0x00, //0x00000853 je           LBB0_85
	0x4c, 0x01, 0xc8, //0x00000859 addq         %r9, %rax
	0x4c, 0x01, 0xe0, //0x0000085c addq         %r12, %rax
	0x48, 0x8d, 0x44, 0x07, 0x60, //0x0000085f leaq         $96(%rdi,%rax), %rax
	0x48, 0xf7, 0xda, //0x00000864 negq         %rdx
	0xc5, 0xfe, 0x6f, 0x05, 0x91, 0xf7, 0xff, 0xff, //0x00000867 vmovdqu      $-2159(%rip), %ymm0  /* LCPI0_0+0(%rip) */
	0x90, //0x0000086f .p2align 4, 0x90
	//0x00000870 LBB0_84
	0xc5, 0xfe, 0x7f, 0x40, 0xa0, //0x00000870 vmovdqu      %ymm0, $-96(%rax)
	0xc5, 0xfe, 0x7f, 0x40, 0xc0, //0x00000875 vmovdqu      %ymm0, $-64(%rax)
	0xc5, 0xfe, 0x7f, 0x40, 0xe0, //0x0000087a vmovdqu      %ymm0, $-32(%rax)
	0xc5, 0xfe, 0x7f, 0x00, //0x0000087f vmovdqu      %ymm0, (%rax)
	0x48, 0x83, 0xe8, 0x80, //0x00000883 subq         $-128, %rax
	0x48, 0xff, 0xc2, //0x00000887 incq         %rdx
	0x0f, 0x85, 0xe0, 0xff, 0xff, 0xff, //0x0000088a jne          LBB0_84
	//0x00000890 LBB0_85
	0x4d, 0x39, 0xda, //0x00000890 cmpq         %r11, %r10
	0x0f, 0x84, 0x97, 0x04, 0x00, 0x00, //0x00000893 je           LBB0_137
	0x4c, 0x01, 0xd6, //0x00000899 addq         %r10, %rsi
	0x90, 0x90, 0x90, 0x90, //0x0000089c .p2align 4, 0x90
	//0x000008a0 LBB0_87
	0xc6, 0x06, 0x30, //0x000008a0 movb         $48, (%rsi)
	0x48, 0xff, 0xc6, //0x000008a3 incq         %rsi
	0x4c, 0x39, 0xc6, //0x000008a6 cmpq         %r8, %rsi
	0x0f, 0x82, 0xf1, 0xff, 0xff, 0xff, //0x000008a9 jb           LBB0_87
	0xe9, 0x7c, 0x04, 0x00, 0x00, //0x000008af jmp          LBB0_137
	//0x000008b4 LBB0_88
	0x81, 0xfe, 0x10, 0x27, 0x00, 0x00, //0x000008b4 cmpl         $10000, %esi
	0x4c, 0x89, 0xc0, //0x000008ba movq         %r8, %rax
	0x48, 0x83, 0xd8, 0x00, //0x000008bd sbbq         $0, %rax
	0x48, 0x83, 0xc0, 0x05, //0x000008c1 addq         $5, %rax
	0x81, 0xfe, 0x10, 0x27, 0x00, 0x00, //0x000008c5 cmpl         $10000, %esi
	0x0f, 0x83, 0x22, 0xfc, 0xff, 0xff, //0x000008cb jae          LBB0_37
	0x49, 0x89, 0xc1, //0x000008d1 movq         %rax, %r9
	0xe9, 0x3f, 0xfe, 0xff, 0xff, //0x000008d4 jmp          LBB0_56
	//0x000008d9 LBB0_90
	0x49, 0x8d, 0x84, 0x39, 0xe2, 0x01, 0x00, 0x00, //0x000008d9 leaq         $482(%r9,%rdi), %rax
	0x4d, 0x89, 0xde, //0x000008e1 movq         %r11, %r14
	0x49, 0x29, 0xce, //0x000008e4 subq         %rcx, %r14
	0x31, 0xdb, //0x000008e7 xorl         %ebx, %ebx
	0xc5, 0xfe, 0x6f, 0x05, 0x0f, 0xf7, 0xff, 0xff, //0x000008e9 vmovdqu      $-2289(%rip), %ymm0  /* LCPI0_0+0(%rip) */
	//0x000008f1 LBB0_91
	0xc5, 0xfe, 0x7f, 0x84, 0x18, 0x20, 0xfe, 0xff, 0xff, //0x000008f1 vmovdqu      %ymm0, $-480(%rax,%rbx)
	0xc5, 0xfe, 0x7f, 0x84, 0x18, 0x40, 0xfe, 0xff, 0xff, //0x000008fa vmovdqu      %ymm0, $-448(%rax,%rbx)
	0xc5, 0xfe, 0x7f, 0x84, 0x18, 0x60, 0xfe, 0xff, 0xff, //0x00000903 vmovdqu      %ymm0, $-416(%rax,%rbx)
	0xc5, 0xfe, 0x7f, 0x84, 0x18, 0x80, 0xfe, 0xff, 0xff, //0x0000090c vmovdqu      %ymm0, $-384(%rax,%rbx)
	0xc5, 0xfe, 0x7f, 0x84, 0x18, 0xa0, 0xfe, 0xff, 0xff, //0x00000915 vmovdqu      %ymm0, $-352(%rax,%rbx)
	0xc5, 0xfe, 0x7f, 0x84, 0x18, 0xc0, 0xfe, 0xff, 0xff, //0x0000091e vmovdqu      %ymm0, $-320(%rax,%rbx)
	0xc5, 0xfe, 0x7f, 0x84, 0x18, 0xe0, 0xfe, 0xff, 0xff, //0x00000927 vmovdqu      %ymm0, $-288(%rax,%rbx)
	0xc5, 0xfe, 0x7f, 0x84, 0x18, 0x00, 0xff, 0xff, 0xff, //0x00000930 vmovdqu      %ymm0, $-256(%rax,%rbx)
	0xc5, 0xfe, 0x7f, 0x84, 0x18, 0x20, 0xff, 0xff, 0xff, //0x00000939 vmovdqu      %ymm0, $-224(%rax,%rbx)
	0xc5, 0xfe, 0x7f, 0x84, 0x18, 0x40, 0xff, 0xff, 0xff, //0x00000942 vmovdqu      %ymm0, $-192(%rax,%rbx)
	0xc5, 0xfe, 0x7f, 0x84, 0x18, 0x60, 0xff, 0xff, 0xff, //0x0000094b vmovdqu      %ymm0, $-160(%rax,%rbx)
	0xc5, 0xfe, 0x7f, 0x44, 0x18, 0x80, //0x00000954 vmovdqu      %ymm0, $-128(%rax,%rbx)
	0xc5, 0xfe, 0x7f, 0x44, 0x18, 0xa0, //0x0000095a vmovdqu      %ymm0, $-96(%rax,%rbx)
	0xc5, 0xfe, 0x7f, 0x44, 0x18, 0xc0, //0x00000960 vmovdqu      %ymm0, $-64(%rax,%rbx)
	0xc5, 0xfe, 0x7f, 0x44, 0x18, 0xe0, //0x00000966 vmovdqu      %ymm0, $-32(%rax,%rbx)
	0xc5, 0xfe, 0x7f, 0x04, 0x18, //0x0000096c vmovdqu      %ymm0, (%rax,%rbx)
	0x48, 0x81, 0xc3, 0x00, 0x02, 0x00, 0x00, //0x00000971 addq         $512, %rbx
	0x49, 0x83, 0xc6, 0x04, //0x00000978 addq         $4, %r14
	0x0f, 0x85, 0x6f, 0xff, 0xff, 0xff, //0x0000097c jne          LBB0_91
	//0x00000982 LBB0_92
	0x4d, 0x85, 0xdb, //0x00000982 testq        %r11, %r11
	0x0f, 0x84, 0x33, 0x00, 0x00, 0x00, //0x00000985 je           LBB0_95
	0x4c, 0x01, 0xcb, //0x0000098b addq         %r9, %rbx
	0x48, 0x8d, 0x44, 0x1f, 0x62, //0x0000098e leaq         $98(%rdi,%rbx), %rax
	0x49, 0xf7, 0xdb, //0x00000993 negq         %r11
	0xc5, 0xfe, 0x6f, 0x05, 0x62, 0xf6, 0xff, 0xff, //0x00000996 vmovdqu      $-2462(%rip), %ymm0  /* LCPI0_0+0(%rip) */
	//0x0000099e LBB0_94
	0xc5, 0xfe, 0x7f, 0x40, 0xa0, //0x0000099e vmovdqu      %ymm0, $-96(%rax)
	0xc5, 0xfe, 0x7f, 0x40, 0xc0, //0x000009a3 vmovdqu      %ymm0, $-64(%rax)
	0xc5, 0xfe, 0x7f, 0x40, 0xe0, //0x000009a8 vmovdqu      %ymm0, $-32(%rax)
	0xc5, 0xfe, 0x7f, 0x00, //0x000009ad vmovdqu      %ymm0, (%rax)
	0x48, 0x83, 0xe8, 0x80, //0x000009b1 subq         $-128, %rax
	0x49, 0xff, 0xc3, //0x000009b5 incq         %r11
	0x0f, 0x85, 0xe0, 0xff, 0xff, 0xff, //0x000009b8 jne          LBB0_94
	//0x000009be LBB0_95
	0x49, 0x01, 0xf0, //0x000009be addq         %rsi, %r8
	0x49, 0x39, 0xf2, //0x000009c1 cmpq         %rsi, %r10
	0x0f, 0x84, 0x17, 0x00, 0x00, 0x00, //0x000009c4 je           LBB0_98
	//0x000009ca LBB0_96
	0x44, 0x89, 0xf8, //0x000009ca movl         %r15d, %eax
	0xf7, 0xd8, //0x000009cd negl         %eax
	0x90, //0x000009cf .p2align 4, 0x90
	//0x000009d0 LBB0_97
	0x41, 0xc6, 0x00, 0x30, //0x000009d0 movb         $48, (%r8)
	0x49, 0xff, 0xc0, //0x000009d4 incq         %r8
	0xff, 0xc6, //0x000009d7 incl         %esi
	0x39, 0xc6, //0x000009d9 cmpl         %eax, %esi
	0x0f, 0x8c, 0xef, 0xff, 0xff, 0xff, //0x000009db jl           LBB0_97
	//0x000009e1 LBB0_98
	0x4b, 0x8d, 0x04, 0x20, //0x000009e1 leaq         (%r8,%r12), %rax
	0x41, 0x81, 0xfd, 0x10, 0x27, 0x00, 0x00, //0x000009e5 cmpl         $10000, %r13d
	0x0f, 0x82, 0x63, 0x00, 0x00, 0x00, //0x000009ec jb           LBB0_101
	0x44, 0x89, 0xe9, //0x000009f2 movl         %r13d, %ecx
	0x41, 0xba, 0x59, 0x17, 0xb7, 0xd1, //0x000009f5 movl         $3518437209, %r10d
	0x4c, 0x0f, 0xaf, 0xd1, //0x000009fb imulq        %rcx, %r10
	0x49, 0xc1, 0xea, 0x2d, //0x000009ff shrq         $45, %r10
	0x41, 0x69, 0xca, 0xf0, 0xd8, 0xff, 0xff, //0x00000a03 imull        $-10000, %r10d, %ecx
	0x44, 0x01, 0xe9, //0x00000a0a addl         %r13d, %ecx
	0x0f, 0x84, 0x87, 0x01, 0x00, 0x00, //0x00000a0d je           LBB0_103
	0x89, 0xca, //0x00000a13 movl         %ecx, %edx
	0x48, 0x69, 0xd2, 0x1f, 0x85, 0xeb, 0x51, //0x00000a15 imulq        $1374389535, %rdx, %rdx
	0x48, 0xc1, 0xea, 0x25, //0x00000a1c shrq         $37, %rdx
	0x6b, 0xda, 0x64, //0x00000a20 imull        $100, %edx, %ebx
	0x29, 0xd9, //0x00000a23 subl         %ebx, %ecx
	0x48, 0x8d, 0x1d, 0x34, 0x03, 0x00, 0x00, //0x00000a25 leaq         $820(%rip), %rbx  /* _Digits+0(%rip) */
	0x0f, 0xb7, 0x0c, 0x4b, //0x00000a2c movzwl       (%rbx,%rcx,2), %ecx
	0x66, 0x89, 0x48, 0xfe, //0x00000a30 movw         %cx, $-2(%rax)
	0x0f, 0xb7, 0x0c, 0x53, //0x00000a34 movzwl       (%rbx,%rdx,2), %ecx
	0x66, 0x89, 0x48, 0xfc, //0x00000a38 movw         %cx, $-4(%rax)
	0x45, 0x31, 0xc9, //0x00000a3c xorl         %r9d, %r9d
	0x48, 0x83, 0xc0, 0xfc, //0x00000a3f addq         $-4, %rax
	0x41, 0x83, 0xfa, 0x64, //0x00000a43 cmpl         $100, %r10d
	0x0f, 0x83, 0x18, 0x00, 0x00, 0x00, //0x00000a47 jae          LBB0_105
	//0x00000a4d LBB0_102
	0x44, 0x89, 0xd1, //0x00000a4d movl         %r10d, %ecx
	0xe9, 0x4d, 0x00, 0x00, 0x00, //0x00000a50 jmp          LBB0_107
	//0x00000a55 LBB0_101
	0x45, 0x31, 0xc9, //0x00000a55 xorl         %r9d, %r9d
	0x45, 0x89, 0xea, //0x00000a58 movl         %r13d, %r10d
	0x41, 0x83, 0xfa, 0x64, //0x00000a5b cmpl         $100, %r10d
	0x0f, 0x82, 0xe8, 0xff, 0xff, 0xff, //0x00000a5f jb           LBB0_102
	//0x00000a65 LBB0_105
	0x48, 0xff, 0xc8, //0x00000a65 decq         %rax
	0x48, 0x8d, 0x15, 0xf1, 0x02, 0x00, 0x00, //0x00000a68 leaq         $753(%rip), %rdx  /* _Digits+0(%rip) */
	0x90, //0x00000a6f .p2align 4, 0x90
	//0x00000a70 LBB0_106
	0x44, 0x89, 0xd1, //0x00000a70 movl         %r10d, %ecx
	0x48, 0x69, 0xc9, 0x1f, 0x85, 0xeb, 0x51, //0x00000a73 imulq        $1374389535, %rcx, %rcx
	0x48, 0xc1, 0xe9, 0x25, //0x00000a7a shrq         $37, %rcx
	0x6b, 0xd9, 0x64, //0x00000a7e imull        $100, %ecx, %ebx
	0x44, 0x89, 0xd6, //0x00000a81 movl         %r10d, %esi
	0x29, 0xde, //0x00000a84 subl         %ebx, %esi
	0x0f, 0xb7, 0x34, 0x72, //0x00000a86 movzwl       (%rdx,%rsi,2), %esi
	0x66, 0x89, 0x70, 0xff, //0x00000a8a movw         %si, $-1(%rax)
	0x48, 0x83, 0xc0, 0xfe, //0x00000a8e addq         $-2, %rax
	0x41, 0x81, 0xfa, 0x0f, 0x27, 0x00, 0x00, //0x00000a92 cmpl         $9999, %r10d
	0x41, 0x89, 0xca, //0x00000a99 movl         %ecx, %r10d
	0x0f, 0x87, 0xce, 0xff, 0xff, 0xff, //0x00000a9c ja           LBB0_106
	//0x00000aa2 LBB0_107
	0x83, 0xf9, 0x0a, //0x00000aa2 cmpl         $10, %ecx
	0x0f, 0x82, 0x16, 0x00, 0x00, 0x00, //0x00000aa5 jb           LBB0_109
	0x89, 0xc8, //0x00000aab movl         %ecx, %eax
	0x48, 0x8d, 0x0d, 0xac, 0x02, 0x00, 0x00, //0x00000aad leaq         $684(%rip), %rcx  /* _Digits+0(%rip) */
	0x0f, 0xb7, 0x04, 0x41, //0x00000ab4 movzwl       (%rcx,%rax,2), %eax
	0x66, 0x41, 0x89, 0x00, //0x00000ab8 movw         %ax, (%r8)
	0xe9, 0x06, 0x00, 0x00, 0x00, //0x00000abc jmp          LBB0_110
	//0x00000ac1 LBB0_109
	0x80, 0xc1, 0x30, //0x00000ac1 addb         $48, %cl
	0x41, 0x88, 0x08, //0x00000ac4 movb         %cl, (%r8)
	//0x00000ac7 LBB0_110
	0x4d, 0x29, 0xcc, //0x00000ac7 subq         %r9, %r12
	0x49, 0x8d, 0x74, 0x24, 0x01, //0x00000aca leaq         $1(%r12), %rsi
	0x49, 0x8d, 0x54, 0x24, 0x61, //0x00000acf leaq         $97(%r12), %rdx
	0x49, 0x8d, 0x44, 0x24, 0x02, //0x00000ad4 leaq         $2(%r12), %rax
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000ad9 .p2align 4, 0x90
	//0x00000ae0 LBB0_111
	0x48, 0xff, 0xca, //0x00000ae0 decq         %rdx
	0x48, 0xff, 0xce, //0x00000ae3 decq         %rsi
	0x48, 0xff, 0xc8, //0x00000ae6 decq         %rax
	0x43, 0x80, 0x7c, 0x20, 0xff, 0x30, //0x00000ae9 cmpb         $48, $-1(%r8,%r12)
	0x4d, 0x8d, 0x64, 0x24, 0xff, //0x00000aef leaq         $-1(%r12), %r12
	0x0f, 0x84, 0xe6, 0xff, 0xff, 0xff, //0x00000af4 je           LBB0_111
	0x4d, 0x8d, 0x0c, 0x30, //0x00000afa leaq         (%r8,%rsi), %r9
	0x45, 0x85, 0xff, //0x00000afe testl        %r15d, %r15d
	0x0f, 0x8e, 0x8b, 0x00, 0x00, 0x00, //0x00000b01 jle          LBB0_116
	0x44, 0x89, 0xc9, //0x00000b07 movl         %r9d, %ecx
	0x44, 0x29, 0xc1, //0x00000b0a subl         %r8d, %ecx
	0x41, 0x39, 0xcf, //0x00000b0d cmpl         %ecx, %r15d
	0x0f, 0x8d, 0x23, 0x00, 0x00, 0x00, //0x00000b10 jge          LBB0_117
	0x43, 0x8d, 0x0c, 0x07, //0x00000b16 leal         (%r15,%r8), %ecx
	0x41, 0x29, 0xc9, //0x00000b1a subl         %ecx, %r9d
	0x49, 0x8d, 0x49, 0xff, //0x00000b1d leaq         $-1(%r9), %rcx
	0x45, 0x89, 0xca, //0x00000b21 movl         %r9d, %r10d
	0x41, 0x83, 0xe2, 0x03, //0x00000b24 andl         $3, %r10d
	0x48, 0x83, 0xf9, 0x03, //0x00000b28 cmpq         $3, %rcx
	0x0f, 0x83, 0x81, 0x00, 0x00, 0x00, //0x00000b2c jae          LBB0_121
	0x31, 0xc9, //0x00000b32 xorl         %ecx, %ecx
	0xe9, 0xa3, 0x00, 0x00, 0x00, //0x00000b34 jmp          LBB0_124
	//0x00000b39 LBB0_117
	0x0f, 0x8e, 0x53, 0x00, 0x00, 0x00, //0x00000b39 jle          LBB0_116
	0x45, 0x01, 0xc7, //0x00000b3f addl         %r8d, %r15d
	0x45, 0x89, 0xce, //0x00000b42 movl         %r9d, %r14d
	0x41, 0xf7, 0xd6, //0x00000b45 notl         %r14d
	0x45, 0x01, 0xfe, //0x00000b48 addl         %r15d, %r14d
	0x45, 0x31, 0xd2, //0x00000b4b xorl         %r10d, %r10d
	0x4d, 0x89, 0xcb, //0x00000b4e movq         %r9, %r11
	0x41, 0x83, 0xfe, 0x7e, //0x00000b51 cmpl         $126, %r14d
	0x0f, 0x86, 0xb4, 0x01, 0x00, 0x00, //0x00000b55 jbe          LBB0_135
	0x49, 0xff, 0xc6, //0x00000b5b incq         %r14
	0x4d, 0x89, 0xf2, //0x00000b5e movq         %r14, %r10
	0x49, 0x83, 0xe2, 0x80, //0x00000b61 andq         $-128, %r10
	0x4f, 0x8d, 0x1c, 0x10, //0x00000b65 leaq         (%r8,%r10), %r11
	0x49, 0x8d, 0x5a, 0x80, //0x00000b69 leaq         $-128(%r10), %rbx
	0x48, 0x89, 0xd9, //0x00000b6d movq         %rbx, %rcx
	0x48, 0xc1, 0xe9, 0x07, //0x00000b70 shrq         $7, %rcx
	0x48, 0xff, 0xc1, //0x00000b74 incq         %rcx
	0x41, 0x89, 0xcc, //0x00000b77 movl         %ecx, %r12d
	0x41, 0x83, 0xe4, 0x03, //0x00000b7a andl         $3, %r12d
	0x48, 0x81, 0xfb, 0x80, 0x01, 0x00, 0x00, //0x00000b7e cmpq         $384, %rbx
	0x0f, 0x83, 0x8f, 0x00, 0x00, 0x00, //0x00000b85 jae          LBB0_129
	0x31, 0xc9, //0x00000b8b xorl         %ecx, %ecx
	0xe9, 0x30, 0x01, 0x00, 0x00, //0x00000b8d jmp          LBB0_131
	//0x00000b92 LBB0_116
	0x4d, 0x89, 0xc8, //0x00000b92 movq         %r9, %r8
	0xe9, 0x96, 0x01, 0x00, 0x00, //0x00000b95 jmp          LBB0_137
	//0x00000b9a LBB0_103
	0x41, 0xb9, 0x04, 0x00, 0x00, 0x00, //0x00000b9a movl         $4, %r9d
	0x48, 0x83, 0xc0, 0xfc, //0x00000ba0 addq         $-4, %rax
	0x41, 0x83, 0xfa, 0x64, //0x00000ba4 cmpl         $100, %r10d
	0x0f, 0x82, 0x9f, 0xfe, 0xff, 0xff, //0x00000ba8 jb           LBB0_102
	0xe9, 0xb2, 0xfe, 0xff, 0xff, //0x00000bae jmp          LBB0_105
	//0x00000bb3 LBB0_121
	0x4d, 0x89, 0xd3, //0x00000bb3 movq         %r10, %r11
	0x4d, 0x29, 0xcb, //0x00000bb6 subq         %r9, %r11
	0x31, 0xc9, //0x00000bb9 xorl         %ecx, %ecx
	0x90, 0x90, 0x90, 0x90, 0x90, //0x00000bbb .p2align 4, 0x90
	//0x00000bc0 LBB0_122
	0x49, 0x8d, 0x1c, 0x08, //0x00000bc0 leaq         (%r8,%rcx), %rbx
	0x8b, 0x54, 0x1e, 0xfc, //0x00000bc4 movl         $-4(%rsi,%rbx), %edx
	0x89, 0x54, 0x1e, 0xfd, //0x00000bc8 movl         %edx, $-3(%rsi,%rbx)
	0x48, 0x83, 0xc1, 0xfc, //0x00000bcc addq         $-4, %rcx
	0x49, 0x39, 0xcb, //0x00000bd0 cmpq         %rcx, %r11
	0x0f, 0x85, 0xe7, 0xff, 0xff, 0xff, //0x00000bd3 jne          LBB0_122
	0x48, 0xf7, 0xd9, //0x00000bd9 negq         %rcx
	//0x00000bdc LBB0_124
	0x4d, 0x85, 0xd2, //0x00000bdc testq        %r10, %r10
	0x0f, 0x84, 0x25, 0x00, 0x00, 0x00, //0x00000bdf je           LBB0_127
	0x49, 0xf7, 0xda, //0x00000be5 negq         %r10
	0x4c, 0x89, 0xc2, //0x00000be8 movq         %r8, %rdx
	0x48, 0x29, 0xca, //0x00000beb subq         %rcx, %rdx
	0x31, 0xc9, //0x00000bee xorl         %ecx, %ecx
	//0x00000bf0 .p2align 4, 0x90
	//0x00000bf0 LBB0_126
	0x48, 0x8d, 0x34, 0x0a, //0x00000bf0 leaq         (%rdx,%rcx), %rsi
	0x41, 0x0f, 0xb6, 0x1c, 0x34, //0x00000bf4 movzbl       (%r12,%rsi), %ebx
	0x41, 0x88, 0x5c, 0x34, 0x01, //0x00000bf9 movb         %bl, $1(%r12,%rsi)
	0x48, 0xff, 0xc9, //0x00000bfe decq         %rcx
	0x49, 0x39, 0xca, //0x00000c01 cmpq         %rcx, %r10
	0x0f, 0x85, 0xe6, 0xff, 0xff, 0xff, //0x00000c04 jne          LBB0_126
	//0x00000c0a LBB0_127
	0x49, 0x63, 0xcf, //0x00000c0a movslq       %r15d, %rcx
	0x41, 0xc6, 0x04, 0x08, 0x2e, //0x00000c0d movb         $46, (%r8,%rcx)
	0x49, 0x01, 0xc0, //0x00000c12 addq         %rax, %r8
	0xe9, 0x16, 0x01, 0x00, 0x00, //0x00000c15 jmp          LBB0_137
	//0x00000c1a LBB0_129
	0x4c, 0x89, 0xe3, //0x00000c1a movq         %r12, %rbx
	0x48, 0x29, 0xcb, //0x00000c1d subq         %rcx, %rbx
	0x31, 0xc9, //0x00000c20 xorl         %ecx, %ecx
	0xc5, 0xfe, 0x6f, 0x05, 0xd6, 0xf3, 0xff, 0xff, //0x00000c22 vmovdqu      $-3114(%rip), %ymm0  /* LCPI0_0+0(%rip) */
	//0x00000c2a LBB0_130
	0x49, 0x8d, 0x04, 0x08, //0x00000c2a leaq         (%r8,%rcx), %rax
	0xc5, 0xfe, 0x7f, 0x04, 0x06, //0x00000c2e vmovdqu      %ymm0, (%rsi,%rax)
	0xc5, 0xfe, 0x7f, 0x44, 0x06, 0x20, //0x00000c33 vmovdqu      %ymm0, $32(%rsi,%rax)
	0xc5, 0xfe, 0x7f, 0x44, 0x06, 0x40, //0x00000c39 vmovdqu      %ymm0, $64(%rsi,%rax)
	0xc5, 0xfe, 0x7f, 0x44, 0x06, 0x60, //0x00000c3f vmovdqu      %ymm0, $96(%rsi,%rax)
	0xc5, 0xfe, 0x7f, 0x84, 0x06, 0x80, 0x00, 0x00, 0x00, //0x00000c45 vmovdqu      %ymm0, $128(%rsi,%rax)
	0xc5, 0xfe, 0x7f, 0x84, 0x06, 0xa0, 0x00, 0x00, 0x00, //0x00000c4e vmovdqu      %ymm0, $160(%rsi,%rax)
	0xc5, 0xfe, 0x7f, 0x84, 0x06, 0xc0, 0x00, 0x00, 0x00, //0x00000c57 vmovdqu      %ymm0, $192(%rsi,%rax)
	0xc5, 0xfe, 0x7f, 0x84, 0x06, 0xe0, 0x00, 0x00, 0x00, //0x00000c60 vmovdqu      %ymm0, $224(%rsi,%rax)
	0xc5, 0xfe, 0x7f, 0x84, 0x06, 0x00, 0x01, 0x00, 0x00, //0x00000c69 vmovdqu      %ymm0, $256(%rsi,%rax)
	0xc5, 0xfe, 0x7f, 0x84, 0x06, 0x20, 0x01, 0x00, 0x00, //0x00000c72 vmovdqu      %ymm0, $288(%rsi,%rax)
	0xc5, 0xfe, 0x7f, 0x84, 0x06, 0x40, 0x01, 0x00, 0x00, //0x00000c7b vmovdqu      %ymm0, $320(%rsi,%rax)
	0xc5, 0xfe, 0x7f, 0x84, 0x06, 0x60, 0x01, 0x00, 0x00, //0x00000c84 vmovdqu      %ymm0, $352(%rsi,%rax)
	0xc5, 0xfe, 0x7f, 0x84, 0x06, 0x80, 0x01, 0x00, 0x00, //0x00000c8d vmovdqu      %ymm0, $384(%rsi,%rax)
	0xc5, 0xfe, 0x7f, 0x84, 0x06, 0xa0, 0x01, 0x00, 0x00, //0x00000c96 vmovdqu      %ymm0, $416(%rsi,%rax)
	0xc5, 0xfe, 0x7f, 0x84, 0x06, 0xc0, 0x01, 0x00, 0x00, //0x00000c9f vmovdqu      %ymm0, $448(%rsi,%rax)
	0xc5, 0xfe, 0x7f, 0x84, 0x06, 0xe0, 0x01, 0x00, 0x00, //0x00000ca8 vmovdqu      %ymm0, $480(%rsi,%rax)
	0x48, 0x81, 0xc1, 0x00, 0x02, 0x00, 0x00, //0x00000cb1 addq         $512, %rcx
	0x48, 0x83, 0xc3, 0x04, //0x00000cb8 addq         $4, %rbx
	0x0f, 0x85, 0x68, 0xff, 0xff, 0xff, //0x00000cbc jne          LBB0_130
	//0x00000cc2 LBB0_131
	0x49, 0x01, 0xf3, //0x00000cc2 addq         %rsi, %r11
	0x4d, 0x85, 0xe4, //0x00000cc5 testq        %r12, %r12
	0x0f, 0x84, 0x35, 0x00, 0x00, 0x00, //0x00000cc8 je           LBB0_134
	0x49, 0x01, 0xc8, //0x00000cce addq         %rcx, %r8
	0x49, 0x01, 0xd0, //0x00000cd1 addq         %rdx, %r8
	0x49, 0xf7, 0xdc, //0x00000cd4 negq         %r12
	0xc5, 0xfe, 0x6f, 0x05, 0x21, 0xf3, 0xff, 0xff, //0x00000cd7 vmovdqu      $-3295(%rip), %ymm0  /* LCPI0_0+0(%rip) */
	//0x00000cdf LBB0_133
	0xc4, 0xc1, 0x7e, 0x7f, 0x40, 0xa0, //0x00000cdf vmovdqu      %ymm0, $-96(%r8)
	0xc4, 0xc1, 0x7e, 0x7f, 0x40, 0xc0, //0x00000ce5 vmovdqu      %ymm0, $-64(%r8)
	0xc4, 0xc1, 0x7e, 0x7f, 0x40, 0xe0, //0x00000ceb vmovdqu      %ymm0, $-32(%r8)
	0xc4, 0xc1, 0x7e, 0x7f, 0x00, //0x00000cf1 vmovdqu      %ymm0, (%r8)
	0x49, 0x83, 0xe8, 0x80, //0x00000cf6 subq         $-128, %r8
	0x49, 0xff, 0xc4, //0x00000cfa incq         %r12
	0x0f, 0x85, 0xdc, 0xff, 0xff, 0xff, //0x00000cfd jne          LBB0_133
	//0x00000d03 LBB0_134
	0x4d, 0x89, 0xd8, //0x00000d03 movq         %r11, %r8
	0x4d, 0x39, 0xd6, //0x00000d06 cmpq         %r10, %r14
	0x0f, 0x84, 0x21, 0x00, 0x00, 0x00, //0x00000d09 je           LBB0_137
	//0x00000d0f LBB0_135
	0x45, 0x29, 0xd7, //0x00000d0f subl         %r10d, %r15d
	0x45, 0x29, 0xcf, //0x00000d12 subl         %r9d, %r15d
	0x4d, 0x89, 0xd8, //0x00000d15 movq         %r11, %r8
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000d18 .p2align 4, 0x90
	//0x00000d20 LBB0_136
	0x41, 0xc6, 0x00, 0x30, //0x00000d20 movb         $48, (%r8)
	0x49, 0xff, 0xc0, //0x00000d24 incq         %r8
	0x41, 0xff, 0xcf, //0x00000d27 decl         %r15d
	0x0f, 0x85, 0xf0, 0xff, 0xff, 0xff, //0x00000d2a jne          LBB0_136
	//0x00000d30 LBB0_137
	0x41, 0x29, 0xf8, //0x00000d30 subl         %edi, %r8d
	//0x00000d33 LBB0_138
	0x44, 0x89, 0xc0, //0x00000d33 movl         %r8d, %eax
	0x5b, //0x00000d36 popq         %rbx
	0x41, 0x5c, //0x00000d37 popq         %r12
	0x41, 0x5d, //0x00000d39 popq         %r13
	0x41, 0x5e, //0x00000d3b popq         %r14
	0x41, 0x5f, //0x00000d3d popq         %r15
	0x5d, //0x00000d3f popq         %rbp
	0xc5, 0xf8, 0x77, //0x00000d40 vzeroupper   
	0xc3, //0x00000d43 retq         
	//0x00000d44 LBB0_139
	0x45, 0x31, 0xc0, //0x00000d44 xorl         %r8d, %r8d
	0xe9, 0xe7, 0xff, 0xff, 0xff, //0x00000d47 jmp          LBB0_138
	//0x00000d4c LBB0_140
	0x41, 0xbf, 0x6b, 0xff, 0xff, 0xff, //0x00000d4c movl         $-149, %r15d
	0x89, 0xc6, //0x00000d52 movl         %eax, %esi
	0xe9, 0x46, 0xf3, 0xff, 0xff, //0x00000d54 jmp          LBB0_5
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000d59 .p2align 4, 0x00
	//0x00000d60 _Digits
	0x30, 0x30, 0x30, 0x31, 0x30, 0x32, 0x30, 0x33, 0x30, 0x34, 0x30, 0x35, 0x30, 0x36, 0x30, 0x37, //0x00000d60 QUAD $0x3330323031303030; QUAD $0x3730363035303430  // .ascii 16, '0001020304050607'
	0x30, 0x38, 0x30, 0x39, 0x31, 0x30, 0x31, 0x31, 0x31, 0x32, 0x31, 0x33, 0x31, 0x34, 0x31, 0x35, //0x00000d70 QUAD $0x3131303139303830; QUAD $0x3531343133313231  // .ascii 16, '0809101112131415'
	0x31, 0x36, 0x31, 0x37, 0x31, 0x38, 0x31, 0x39, 0x32, 0x30, 0x32, 0x31, 0x32, 0x32, 0x32, 0x33, //0x00000d80 QUAD $0x3931383137313631; QUAD $0x3332323231323032  // .ascii 16, '1617181920212223'
	0x32, 0x34, 0x32, 0x35, 0x32, 0x36, 0x32, 0x37, 0x32, 0x38, 0x32, 0x39, 0x33, 0x30, 0x33, 0x31, //0x00000d90 QUAD $0x3732363235323432; QUAD $0x3133303339323832  // .ascii 16, '2425262728293031'
	0x33, 0x32, 0x33, 0x33, 0x33, 0x34, 0x33, 0x35, 0x33, 0x36, 0x33, 0x37, 0x33, 0x38, 0x33, 0x39, //0x00000da0 QUAD $0x3533343333333233; QUAD $0x3933383337333633  // .ascii 16, '3233343536373839'
	0x34, 0x30, 0x34, 0x31, 0x34, 0x32, 0x34, 0x33, 0x34, 0x34, 0x34, 0x35, 0x34, 0x36, 0x34, 0x37, //0x00000db0 QUAD $0x3334323431343034; QUAD $0x3734363435343434  // .ascii 16, '4041424344454647'
	0x34, 0x38, 0x34, 0x39, 0x35, 0x30, 0x35, 0x31, 0x35, 0x32, 0x35, 0x33, 0x35, 0x34, 0x35, 0x35, //0x00000dc0 QUAD $0x3135303539343834; QUAD $0x3535343533353235  // .ascii 16, '4849505152535455'
	0x35, 0x36, 0x35, 0x37, 0x35, 0x38, 0x35, 0x39, 0x36, 0x30, 0x36, 0x31, 0x36, 0x32, 0x36, 0x33, //0x00000dd0 QUAD $0x3935383537353635; QUAD $0x3336323631363036  // .ascii 16, '5657585960616263'
	0x36, 0x34, 0x36, 0x35, 0x36, 0x36, 0x36, 0x37, 0x36, 0x38, 0x36, 0x39, 0x37, 0x30, 0x37, 0x31, //0x00000de0 QUAD $0x3736363635363436; QUAD $0x3137303739363836  // .ascii 16, '6465666768697071'
	0x37, 0x32, 0x37, 0x33, 0x37, 0x34, 0x37, 0x35, 0x37, 0x36, 0x37, 0x37, 0x37, 0x38, 0x37, 0x39, //0x00000df0 QUAD $0x3537343733373237; QUAD $0x3937383737373637  // .ascii 16, '7273747576777879'
	0x38, 0x30, 0x38, 0x31, 0x38, 0x32, 0x38, 0x33, 0x38, 0x34, 0x38, 0x35, 0x38, 0x36, 0x38, 0x37, //0x00000e00 QUAD $0x3338323831383038; QUAD $0x3738363835383438  // .ascii 16, '8081828384858687'
	0x38, 0x38, 0x38, 0x39, 0x39, 0x30, 0x39, 0x31, 0x39, 0x32, 0x39, 0x33, 0x39, 0x34, 0x39, 0x35, //0x00000e10 QUAD $0x3139303939383838; QUAD $0x3539343933393239  // .ascii 16, '8889909192939495'
	0x39, 0x36, 0x39, 0x37, 0x39, 0x38, 0x39, 0x39, //0x00000e20 QUAD $0x3939383937393639  // .ascii 8, '96979899'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000e28 .p2align 4, 0x00
	//0x00000e30 _pow10_ceil_sig_f32.g
	0xf5, 0xfc, 0x43, 0x4b, 0x2c, 0xb3, 0xce, 0x81, //0x00000e30 .quad -9093133594791772939
	0x32, 0xfc, 0x14, 0x5e, 0xf7, 0x5f, 0x42, 0xa2, //0x00000e38 .quad -6754730975062328270
	0x3f, 0x3b, 0x9a, 0x35, 0xf5, 0xf7, 0xd2, 0xca, //0x00000e40 .quad -3831727700400522433
	0x0e, 0xca, 0x00, 0x83, 0xf2, 0xb5, 0x87, 0xfd, //0x00000e48 .quad -177973607073265138
	0x49, 0x7e, 0xe0, 0x91, 0xb7, 0xd1, 0x74, 0x9e, //0x00000e50 .quad -7028762532061872567
	0xdb, 0x9d, 0x58, 0x76, 0x25, 0x06, 0x12, 0xc6, //0x00000e58 .quad -4174267146649952805
	0x52, 0xc5, 0xee, 0xd3, 0xae, 0x87, 0x96, 0xf7, //0x00000e60 .quad -606147914885053102
	0x53, 0x3b, 0x75, 0x44, 0xcd, 0x14, 0xbe, 0x9a, //0x00000e68 .quad -7296371474444240045
	0x28, 0x8a, 0x92, 0x95, 0x00, 0x9a, 0x6d, 0xc1, //0x00000e70 .quad -4508778324627912152
	0xb2, 0x2c, 0xf7, 0xba, 0x80, 0x00, 0xc9, 0xf1, //0x00000e78 .quad -1024286887357502286
	0xef, 0x7b, 0xda, 0x74, 0x50, 0xa0, 0x1d, 0x97, //0x00000e80 .quad -7557708332239520785
	0xeb, 0x1a, 0x11, 0x92, 0x64, 0x08, 0xe5, 0xbc, //0x00000e88 .quad -4835449396872013077
	0xa6, 0x61, 0x95, 0xb6, 0x7d, 0x4a, 0x1e, 0xec, //0x00000e90 .quad -1432625727662628442
	0x08, 0x5d, 0x1d, 0x92, 0x8e, 0xee, 0x92, 0x93, //0x00000e98 .quad -7812920107430224632
	0x4a, 0xb4, 0xa4, 0x36, 0x32, 0xaa, 0x77, 0xb8, //0x00000ea0 .quad -5154464115860392886
	0x5c, 0xe1, 0x4d, 0xc4, 0xbe, 0x94, 0x95, 0xe6, //0x00000ea8 .quad -1831394126398103204
	0xda, 0xac, 0xb0, 0x3a, 0xf7, 0x7c, 0x1d, 0x90, //0x00000eb0 .quad -8062150356639896358
	0x10, 0xd8, 0x5c, 0x09, 0x35, 0xdc, 0x24, 0xb4, //0x00000eb8 .quad -5466001927372482544
	0x14, 0x0e, 0xb4, 0x4b, 0x42, 0x13, 0x2e, 0xe1, //0x00000ec0 .quad -2220816390788215276
	0xcc, 0x88, 0x50, 0x6f, 0x09, 0xcc, 0xbc, 0x8c, //0x00000ec8 .quad -8305539271883716404
	0xff, 0xaa, 0x24, 0xcb, 0x0b, 0xff, 0xeb, 0xaf, //0x00000ed0 .quad -5770238071427257601
	0xbf, 0xd5, 0xed, 0xbd, 0xce, 0xfe, 0xe6, 0xdb, //0x00000ed8 .quad -2601111570856684097
	0x98, 0xa5, 0xb4, 0x36, 0x41, 0x5f, 0x70, 0x89, //0x00000ee0 .quad -8543223759426509416
	0xfd, 0xce, 0x61, 0x84, 0x11, 0x77, 0xcc, 0xab, //0x00000ee8 .quad -6067343680855748867
	0xbd, 0x42, 0x7a, 0xe5, 0xd5, 0x94, 0xbf, 0xd6, //0x00000ef0 .quad -2972493582642298179
	0xb6, 0x69, 0x6c, 0xaf, 0x05, 0xbd, 0x37, 0x86, //0x00000ef8 .quad -8775337516792518218
	0x24, 0x84, 0x47, 0x1b, 0x47, 0xac, 0xc5, 0xa7, //0x00000f00 .quad -6357485877563259868
	0x2c, 0x65, 0x19, 0xe2, 0x58, 0x17, 0xb7, 0xd1, //0x00000f08 .quad -3335171328526686932
	0x3c, 0xdf, 0x4f, 0x8d, 0x97, 0x6e, 0x12, 0x83, //0x00000f10 .quad -9002011107970261188
	0x0b, 0xd7, 0xa3, 0x70, 0x3d, 0x0a, 0xd7, 0xa3, //0x00000f18 .quad -6640827866535438581
	0xcd, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, //0x00000f20 .quad -3689348814741910323
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, //0x00000f28 .quad -9223372036854775808
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xa0, //0x00000f30 .quad -6917529027641081856
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc8, //0x00000f38 .quad -4035225266123964416
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfa, //0x00000f40 .quad -432345564227567616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x9c, //0x00000f48 .quad -7187745005283311616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x50, 0xc3, //0x00000f50 .quad -4372995238176751616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x24, 0xf4, //0x00000f58 .quad -854558029293551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x96, 0x98, //0x00000f60 .quad -7451627795949551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0xbc, 0xbe, //0x00000f68 .quad -4702848726509551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x28, 0x6b, 0xee, //0x00000f70 .quad -1266874889709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0xf9, 0x02, 0x95, //0x00000f78 .quad -7709325833709551616
	0x00, 0x00, 0x00, 0x00, 0x40, 0xb7, 0x43, 0xba, //0x00000f80 .quad -5024971273709551616
	0x00, 0x00, 0x00, 0x00, 0x10, 0xa5, 0xd4, 0xe8, //0x00000f88 .quad -1669528073709551616
	0x00, 0x00, 0x00, 0x00, 0x2a, 0xe7, 0x84, 0x91, //0x00000f90 .quad -7960984073709551616
	0x00, 0x00, 0x00, 0x80, 0xf4, 0x20, 0xe6, 0xb5, //0x00000f98 .quad -5339544073709551616
	0x00, 0x00, 0x00, 0xa0, 0x31, 0xa9, 0x5f, 0xe3, //0x00000fa0 .quad -2062744073709551616
	0x00, 0x00, 0x00, 0x04, 0xbf, 0xc9, 0x1b, 0x8e, //0x00000fa8 .quad -8206744073709551616
	0x00, 0x00, 0x00, 0xc5, 0x2e, 0xbc, 0xa2, 0xb1, //0x00000fb0 .quad -5646744073709551616
	0x00, 0x00, 0x40, 0x76, 0x3a, 0x6b, 0x0b, 0xde, //0x00000fb8 .quad -2446744073709551616
	0x00, 0x00, 0xe8, 0x89, 0x04, 0x23, 0xc7, 0x8a, //0x00000fc0 .quad -8446744073709551616
	0x00, 0x00, 0x62, 0xac, 0xc5, 0xeb, 0x78, 0xad, //0x00000fc8 .quad -5946744073709551616
	0x00, 0x80, 0x7a, 0x17, 0xb7, 0x26, 0xd7, 0xd8, //0x00000fd0 .quad -2821744073709551616
	0x00, 0x90, 0xac, 0x6e, 0x32, 0x78, 0x86, 0x87, //0x00000fd8 .quad -8681119073709551616
	0x00, 0xb4, 0x57, 0x0a, 0x3f, 0x16, 0x68, 0xa9, //0x00000fe0 .quad -6239712823709551616
	0x00, 0xa1, 0xed, 0xcc, 0xce, 0x1b, 0xc2, 0xd3, //0x00000fe8 .quad -3187955011209551616
	0xa0, 0x84, 0x14, 0x40, 0x61, 0x51, 0x59, 0x84, //0x00000ff0 .quad -8910000909647051616
	0xc8, 0xa5, 0x19, 0x90, 0xb9, 0xa5, 0x6f, 0xa5, //0x00000ff8 .quad -6525815118631426616
	0x3a, 0x0f, 0x20, 0xf4, 0x27, 0x8f, 0xcb, 0xce, //0x00001000 .quad -3545582879861895366
	0x85, 0x09, 0x94, 0xf8, 0x78, 0x39, 0x3f, 0x81, //0x00001008 .quad -9133518327554766459
	0xe6, 0x0b, 0xb9, 0x36, 0xd7, 0x07, 0x8f, 0xa1, //0x00001010 .quad -6805211891016070170
	0xdf, 0x4e, 0x67, 0x04, 0xcd, 0xc9, 0xf2, 0xc9, //0x00001018 .quad -3894828845342699809
	0x97, 0x22, 0x81, 0x45, 0x40, 0x7c, 0x6f, 0xfc, //0x00001020 .quad -256850038250986857
	0x9e, 0xb5, 0x70, 0x2b, 0xa8, 0xad, 0xc5, 0x9d, //0x00001028 .quad -7078060301547948642
	0x06, 0xe3, 0x4c, 0x36, 0x12, 0x19, 0x37, 0xc5, //0x00001030 .quad -4235889358507547898
	0xc7, 0x1b, 0xe0, 0xc3, 0x56, 0xdf, 0x84, 0xf6, //0x00001038 .quad -683175679707046969
	0x5d, 0x11, 0x6c, 0x3a, 0x96, 0x0b, 0x13, 0x9a, //0x00001040 .quad -7344513827457986211
	0xb4, 0x15, 0x07, 0xc9, 0x7b, 0xce, 0x97, 0xc0, //0x00001048 .quad -4568956265895094860
	0x21, 0xdb, 0x48, 0xbb, 0x1a, 0xc2, 0xbd, 0xf0, //0x00001050 .quad -1099509313941480671
	0xf5, 0x88, 0x0d, 0xb5, 0x50, 0x99, 0x76, 0x96, //0x00001058 .quad -7604722348854507275
	0x32, 0xeb, 0x50, 0xe2, 0xa4, 0x3f, 0x14, 0xbc, //0x00001060 .quad -4894216917640746190
	0xfe, 0x25, 0xe5, 0x1a, 0x8e, 0x4f, 0x19, 0xeb, //0x00001068 .quad -1506085128623544834
	0xbf, 0x37, 0xcf, 0xd0, 0xb8, 0xd1, 0xef, 0x92, //0x00001070 .quad -7858832233030797377
	0xae, 0x05, 0x03, 0x05, 0x27, 0xc6, 0xab, 0xb7, //0x00001078 .quad -5211854272861108818
	0x1a, 0xc7, 0x43, 0xc6, 0xb0, 0xb7, 0x96, 0xe5, //0x00001080 .quad -1903131822648998118
	0x70, 0x5c, 0xea, 0x7b, 0xce, 0x32, 0x7e, 0x8f, //0x00001088 .quad -8106986416796705680
	0x8c, 0xf3, 0xe4, 0x1a, 0x82, 0xbf, 0x5d, 0xb3, //0x00001090 .quad -5522047002568494196
}
 
