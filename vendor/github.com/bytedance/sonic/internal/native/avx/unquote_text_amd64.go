// +build amd64
// Code generated by asm2asm, DO NOT EDIT.

package avx

var _text_unquote = []byte{
	// .p2align 4, 0x00
	// LCPI0_0
	0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, // QUAD $0x5c5c5c5c5c5c5c5c; QUAD $0x5c5c5c5c5c5c5c5c  // .space 16, '\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\'
	//0x00000010 .p2align 4, 0x90
	//0x00000010 _unquote
	0x55, //0x00000010 pushq        %rbp
	0x48, 0x89, 0xe5, //0x00000011 movq         %rsp, %rbp
	0x41, 0x57, //0x00000014 pushq        %r15
	0x41, 0x56, //0x00000016 pushq        %r14
	0x41, 0x55, //0x00000018 pushq        %r13
	0x41, 0x54, //0x0000001a pushq        %r12
	0x53, //0x0000001c pushq        %rbx
	0x48, 0x83, 0xec, 0x28, //0x0000001d subq         $40, %rsp
	0x48, 0x85, 0xf6, //0x00000021 testq        %rsi, %rsi
	0x0f, 0x84, 0x6e, 0x06, 0x00, 0x00, //0x00000024 je           LBB0_82
	0x49, 0x89, 0xf3, //0x0000002a movq         %rsi, %r11
	0x48, 0x89, 0x4d, 0xc8, //0x0000002d movq         %rcx, $-56(%rbp)
	0x4c, 0x89, 0xc0, //0x00000031 movq         %r8, %rax
	0x4c, 0x89, 0x45, 0xb8, //0x00000034 movq         %r8, $-72(%rbp)
	0x45, 0x89, 0xc2, //0x00000038 movl         %r8d, %r10d
	0x41, 0x83, 0xe2, 0x01, //0x0000003b andl         $1, %r10d
	0x4c, 0x8d, 0x05, 0xaa, 0x08, 0x00, 0x00, //0x0000003f leaq         $2218(%rip), %r8  /* __UnquoteTab+0(%rip) */
	0xc5, 0xfa, 0x6f, 0x05, 0xb2, 0xff, 0xff, 0xff, //0x00000046 vmovdqu      $-78(%rip), %xmm0  /* LCPI0_0+0(%rip) */
	0x49, 0x89, 0xf9, //0x0000004e movq         %rdi, %r9
	0x49, 0x89, 0xf5, //0x00000051 movq         %rsi, %r13
	0x48, 0x89, 0xd0, //0x00000054 movq         %rdx, %rax
	//0x00000057 LBB0_2
	0x41, 0x80, 0x39, 0x5c, //0x00000057 cmpb         $92, (%r9)
	0x0f, 0x85, 0x0f, 0x00, 0x00, 0x00, //0x0000005b jne          LBB0_4
	0x31, 0xf6, //0x00000061 xorl         %esi, %esi
	0xe9, 0xc8, 0x00, 0x00, 0x00, //0x00000063 jmp          LBB0_13
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000068 .p2align 4, 0x90
	//0x00000070 LBB0_4
	0x4d, 0x89, 0xef, //0x00000070 movq         %r13, %r15
	0x48, 0x89, 0xc6, //0x00000073 movq         %rax, %rsi
	0x4d, 0x89, 0xce, //0x00000076 movq         %r9, %r14
	0x49, 0x83, 0xfd, 0x10, //0x00000079 cmpq         $16, %r13
	0x0f, 0x8c, 0x3d, 0x00, 0x00, 0x00, //0x0000007d jl           LBB0_7
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000083 .p2align 4, 0x90
	//0x00000090 LBB0_5
	0xc4, 0xc1, 0x7a, 0x6f, 0x0e, //0x00000090 vmovdqu      (%r14), %xmm1
	0xc5, 0xfa, 0x7f, 0x0e, //0x00000095 vmovdqu      %xmm1, (%rsi)
	0xc5, 0xf1, 0x74, 0xc8, //0x00000099 vpcmpeqb     %xmm0, %xmm1, %xmm1
	0xc5, 0xf9, 0xd7, 0xd9, //0x0000009d vpmovmskb    %xmm1, %ebx
	0x66, 0x85, 0xdb, //0x000000a1 testw        %bx, %bx
	0x0f, 0x85, 0x60, 0x00, 0x00, 0x00, //0x000000a4 jne          LBB0_12
	0x49, 0x83, 0xc6, 0x10, //0x000000aa addq         $16, %r14
	0x48, 0x83, 0xc6, 0x10, //0x000000ae addq         $16, %rsi
	0x49, 0x83, 0xff, 0x1f, //0x000000b2 cmpq         $31, %r15
	0x4d, 0x8d, 0x7f, 0xf0, //0x000000b6 leaq         $-16(%r15), %r15
	0x0f, 0x8f, 0xd0, 0xff, 0xff, 0xff, //0x000000ba jg           LBB0_5
	//0x000000c0 LBB0_7
	0x4d, 0x85, 0xff, //0x000000c0 testq        %r15, %r15
	0x0f, 0x84, 0xd5, 0x05, 0x00, 0x00, //0x000000c3 je           LBB0_83
	0x31, 0xdb, //0x000000c9 xorl         %ebx, %ebx
	0x90, 0x90, 0x90, 0x90, 0x90, //0x000000cb .p2align 4, 0x90
	//0x000000d0 LBB0_9
	0x41, 0x0f, 0xb6, 0x0c, 0x1e, //0x000000d0 movzbl       (%r14,%rbx), %ecx
	0x80, 0xf9, 0x5c, //0x000000d5 cmpb         $92, %cl
	0x0f, 0x84, 0x14, 0x00, 0x00, 0x00, //0x000000d8 je           LBB0_11
	0x88, 0x0c, 0x1e, //0x000000de movb         %cl, (%rsi,%rbx)
	0x48, 0xff, 0xc3, //0x000000e1 incq         %rbx
	0x49, 0x39, 0xdf, //0x000000e4 cmpq         %rbx, %r15
	0x0f, 0x85, 0xe3, 0xff, 0xff, 0xff, //0x000000e7 jne          LBB0_9
	0xe9, 0xac, 0x05, 0x00, 0x00, //0x000000ed jmp          LBB0_83
	//0x000000f2 LBB0_11
	0x49, 0x01, 0xde, //0x000000f2 addq         %rbx, %r14
	0x4d, 0x29, 0xce, //0x000000f5 subq         %r9, %r14
	0x4c, 0x89, 0xf6, //0x000000f8 movq         %r14, %rsi
	0x48, 0x83, 0xfe, 0xff, //0x000000fb cmpq         $-1, %rsi
	0x0f, 0x85, 0x2b, 0x00, 0x00, 0x00, //0x000000ff jne          LBB0_13
	0xe9, 0x94, 0x05, 0x00, 0x00, //0x00000105 jmp          LBB0_83
	//0x0000010a LBB0_12
	0x0f, 0xb7, 0xcb, //0x0000010a movzwl       %bx, %ecx
	0x4d, 0x29, 0xce, //0x0000010d subq         %r9, %r14
	0x48, 0x0f, 0xbc, 0xf1, //0x00000110 bsfq         %rcx, %rsi
	0x4c, 0x01, 0xf6, //0x00000114 addq         %r14, %rsi
	0x48, 0x83, 0xfe, 0xff, //0x00000117 cmpq         $-1, %rsi
	0x0f, 0x84, 0x7d, 0x05, 0x00, 0x00, //0x0000011b je           LBB0_83
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000121 .p2align 4, 0x90
	//0x00000130 LBB0_13
	0x48, 0x8d, 0x4e, 0x02, //0x00000130 leaq         $2(%rsi), %rcx
	0x49, 0x29, 0xcd, //0x00000134 subq         %rcx, %r13
	0x0f, 0x88, 0x2a, 0x06, 0x00, 0x00, //0x00000137 js           LBB0_94
	0x4d, 0x8d, 0x4c, 0x31, 0x02, //0x0000013d leaq         $2(%r9,%rsi), %r9
	0x4d, 0x85, 0xd2, //0x00000142 testq        %r10, %r10
	0x0f, 0x85, 0xe5, 0x03, 0x00, 0x00, //0x00000145 jne          LBB0_58
	//0x0000014b LBB0_15
	0x48, 0x01, 0xf0, //0x0000014b addq         %rsi, %rax
	0x41, 0x0f, 0xb6, 0x49, 0xff, //0x0000014e movzbl       $-1(%r9), %ecx
	0x42, 0x8a, 0x0c, 0x01, //0x00000153 movb         (%rcx,%r8), %cl
	0x80, 0xf9, 0xff, //0x00000157 cmpb         $-1, %cl
	0x0f, 0x84, 0x20, 0x00, 0x00, 0x00, //0x0000015a je           LBB0_18
	0x84, 0xc9, //0x00000160 testb        %cl, %cl
	0x0f, 0x84, 0x12, 0x06, 0x00, 0x00, //0x00000162 je           LBB0_95
	0x88, 0x08, //0x00000168 movb         %cl, (%rax)
	0x48, 0xff, 0xc0, //0x0000016a incq         %rax
	0xe9, 0xb0, 0x03, 0x00, 0x00, //0x0000016d jmp          LBB0_57
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000172 .p2align 4, 0x90
	//0x00000180 LBB0_18
	0x49, 0x83, 0xfd, 0x03, //0x00000180 cmpq         $3, %r13
	0x0f, 0x8e, 0xdd, 0x05, 0x00, 0x00, //0x00000184 jle          LBB0_94
	0x41, 0x8b, 0x31, //0x0000018a movl         (%r9), %esi
	0x89, 0xf1, //0x0000018d movl         %esi, %ecx
	0xf7, 0xd1, //0x0000018f notl         %ecx
	0x8d, 0x9e, 0xd0, 0xcf, 0xcf, 0xcf, //0x00000191 leal         $-808464432(%rsi), %ebx
	0x81, 0xe1, 0x80, 0x80, 0x80, 0x80, //0x00000197 andl         $-2139062144, %ecx
	0x85, 0xd9, //0x0000019d testl        %ebx, %ecx
	0x0f, 0x85, 0x0e, 0x05, 0x00, 0x00, //0x0000019f jne          LBB0_85
	0x8d, 0x9e, 0x19, 0x19, 0x19, 0x19, //0x000001a5 leal         $421075225(%rsi), %ebx
	0x09, 0xf3, //0x000001ab orl          %esi, %ebx
	0xf7, 0xc3, 0x80, 0x80, 0x80, 0x80, //0x000001ad testl        $-2139062144, %ebx
	0x0f, 0x85, 0xfa, 0x04, 0x00, 0x00, //0x000001b3 jne          LBB0_85
	0x89, 0xf3, //0x000001b9 movl         %esi, %ebx
	0x81, 0xe3, 0x7f, 0x7f, 0x7f, 0x7f, //0x000001bb andl         $2139062143, %ebx
	0x41, 0xbe, 0xc0, 0xc0, 0xc0, 0xc0, //0x000001c1 movl         $-1061109568, %r14d
	0x41, 0x29, 0xde, //0x000001c7 subl         %ebx, %r14d
	0x44, 0x8d, 0xbb, 0x46, 0x46, 0x46, 0x46, //0x000001ca leal         $1179010630(%rbx), %r15d
	0x41, 0x21, 0xce, //0x000001d1 andl         %ecx, %r14d
	0x45, 0x85, 0xfe, //0x000001d4 testl        %r15d, %r14d
	0x0f, 0x85, 0xd6, 0x04, 0x00, 0x00, //0x000001d7 jne          LBB0_85
	0x41, 0xbe, 0xe0, 0xe0, 0xe0, 0xe0, //0x000001dd movl         $-522133280, %r14d
	0x41, 0x29, 0xde, //0x000001e3 subl         %ebx, %r14d
	0x81, 0xc3, 0x39, 0x39, 0x39, 0x39, //0x000001e6 addl         $960051513, %ebx
	0x44, 0x21, 0xf1, //0x000001ec andl         %r14d, %ecx
	0x85, 0xd9, //0x000001ef testl        %ebx, %ecx
	0x0f, 0x85, 0xbc, 0x04, 0x00, 0x00, //0x000001f1 jne          LBB0_85
	0x0f, 0xce, //0x000001f7 bswapl       %esi
	0x89, 0xf1, //0x000001f9 movl         %esi, %ecx
	0xc1, 0xe9, 0x04, //0x000001fb shrl         $4, %ecx
	0xf7, 0xd1, //0x000001fe notl         %ecx
	0x81, 0xe1, 0x01, 0x01, 0x01, 0x01, //0x00000200 andl         $16843009, %ecx
	0x8d, 0x0c, 0xc9, //0x00000206 leal         (%rcx,%rcx,8), %ecx
	0x81, 0xe6, 0x0f, 0x0f, 0x0f, 0x0f, //0x00000209 andl         $252645135, %esi
	0x01, 0xce, //0x0000020f addl         %ecx, %esi
	0x89, 0xf1, //0x00000211 movl         %esi, %ecx
	0xc1, 0xe9, 0x04, //0x00000213 shrl         $4, %ecx
	0x09, 0xf1, //0x00000216 orl          %esi, %ecx
	0x44, 0x0f, 0xb6, 0xf9, //0x00000218 movzbl       %cl, %r15d
	0xc1, 0xe9, 0x08, //0x0000021c shrl         $8, %ecx
	0x81, 0xe1, 0x00, 0xff, 0x00, 0x00, //0x0000021f andl         $65280, %ecx
	0x41, 0x09, 0xcf, //0x00000225 orl          %ecx, %r15d
	0x4d, 0x8d, 0x75, 0xfc, //0x00000228 leaq         $-4(%r13), %r14
	0x41, 0x81, 0xff, 0x80, 0x00, 0x00, 0x00, //0x0000022c cmpl         $128, %r15d
	0x0f, 0x82, 0x46, 0x03, 0x00, 0x00, //0x00000233 jb           LBB0_66
	0x45, 0x31, 0xe4, //0x00000239 xorl         %r12d, %r12d
	0x4d, 0x85, 0xd2, //0x0000023c testq        %r10, %r10
	0x0f, 0x84, 0x5b, 0x01, 0x00, 0x00, //0x0000023f je           LBB0_40
	//0x00000245 LBB0_25
	0x41, 0x81, 0xff, 0x00, 0x08, 0x00, 0x00, //0x00000245 cmpl         $2048, %r15d
	0x0f, 0x82, 0x3c, 0x03, 0x00, 0x00, //0x0000024c jb           LBB0_68
	0x44, 0x89, 0xf9, //0x00000252 movl         %r15d, %ecx
	0x81, 0xe1, 0x00, 0xf8, 0xff, 0xff, //0x00000255 andl         $-2048, %ecx
	0x81, 0xf9, 0x00, 0xd8, 0x00, 0x00, //0x0000025b cmpl         $55296, %ecx
	0x0f, 0x85, 0x89, 0x02, 0x00, 0x00, //0x00000261 jne          LBB0_54
	0x4d, 0x85, 0xf6, //0x00000267 testq        %r14, %r14
	0x0f, 0x8e, 0x6b, 0x03, 0x00, 0x00, //0x0000026a jle          LBB0_72
	0x43, 0x80, 0x7c, 0x21, 0x04, 0x5c, //0x00000270 cmpb         $92, $4(%r9,%r12)
	0x0f, 0x85, 0x6e, 0x03, 0x00, 0x00, //0x00000276 jne          LBB0_73
	0x41, 0x81, 0xff, 0xff, 0xdb, 0x00, 0x00, //0x0000027c cmpl         $56319, %r15d
	0x0f, 0x87, 0x34, 0x03, 0x00, 0x00, //0x00000283 ja           LBB0_70
	0x49, 0x83, 0xfe, 0x07, //0x00000289 cmpq         $7, %r14
	0x0f, 0x8c, 0x2a, 0x03, 0x00, 0x00, //0x0000028d jl           LBB0_70
	0x43, 0x80, 0x7c, 0x21, 0x05, 0x5c, //0x00000293 cmpb         $92, $5(%r9,%r12)
	0x0f, 0x85, 0x1e, 0x03, 0x00, 0x00, //0x00000299 jne          LBB0_70
	0x43, 0x80, 0x7c, 0x21, 0x06, 0x75, //0x0000029f cmpb         $117, $6(%r9,%r12)
	0x0f, 0x85, 0x12, 0x03, 0x00, 0x00, //0x000002a5 jne          LBB0_70
	0x43, 0x8b, 0x74, 0x21, 0x07, //0x000002ab movl         $7(%r9,%r12), %esi
	0x89, 0xf1, //0x000002b0 movl         %esi, %ecx
	0xf7, 0xd1, //0x000002b2 notl         %ecx
	0x8d, 0x9e, 0xd0, 0xcf, 0xcf, 0xcf, //0x000002b4 leal         $-808464432(%rsi), %ebx
	0x81, 0xe1, 0x80, 0x80, 0x80, 0x80, //0x000002ba andl         $-2139062144, %ecx
	0x85, 0xd9, //0x000002c0 testl        %ebx, %ecx
	0x0f, 0x85, 0xe8, 0x04, 0x00, 0x00, //0x000002c2 jne          LBB0_99
	0x8d, 0x9e, 0x19, 0x19, 0x19, 0x19, //0x000002c8 leal         $421075225(%rsi), %ebx
	0x09, 0xf3, //0x000002ce orl          %esi, %ebx
	0xf7, 0xc3, 0x80, 0x80, 0x80, 0x80, //0x000002d0 testl        $-2139062144, %ebx
	0x0f, 0x85, 0xd4, 0x04, 0x00, 0x00, //0x000002d6 jne          LBB0_99
	0x89, 0xf3, //0x000002dc movl         %esi, %ebx
	0x81, 0xe3, 0x7f, 0x7f, 0x7f, 0x7f, //0x000002de andl         $2139062143, %ebx
	0xc7, 0x45, 0xd4, 0xc0, 0xc0, 0xc0, 0xc0, //0x000002e4 movl         $-1061109568, $-44(%rbp)
	0x29, 0x5d, 0xd4, //0x000002eb subl         %ebx, $-44(%rbp)
	0x48, 0x89, 0x75, 0xb0, //0x000002ee movq         %rsi, $-80(%rbp)
	0x8d, 0xb3, 0x46, 0x46, 0x46, 0x46, //0x000002f2 leal         $1179010630(%rbx), %esi
	0x89, 0x75, 0xc4, //0x000002f8 movl         %esi, $-60(%rbp)
	0x21, 0x4d, 0xd4, //0x000002fb andl         %ecx, $-44(%rbp)
	0x8b, 0x75, 0xc4, //0x000002fe movl         $-60(%rbp), %esi
	0x85, 0x75, 0xd4, //0x00000301 testl        %esi, $-44(%rbp)
	0x48, 0x8b, 0x75, 0xb0, //0x00000304 movq         $-80(%rbp), %rsi
	0x0f, 0x85, 0xa2, 0x04, 0x00, 0x00, //0x00000308 jne          LBB0_99
	0xc7, 0x45, 0xd4, 0xe0, 0xe0, 0xe0, 0xe0, //0x0000030e movl         $-522133280, $-44(%rbp)
	0x29, 0x5d, 0xd4, //0x00000315 subl         %ebx, $-44(%rbp)
	0x81, 0xc3, 0x39, 0x39, 0x39, 0x39, //0x00000318 addl         $960051513, %ebx
	0x23, 0x4d, 0xd4, //0x0000031e andl         $-44(%rbp), %ecx
	0x85, 0xd9, //0x00000321 testl        %ebx, %ecx
	0x0f, 0x85, 0x87, 0x04, 0x00, 0x00, //0x00000323 jne          LBB0_99
	0x0f, 0xce, //0x00000329 bswapl       %esi
	0x89, 0xf1, //0x0000032b movl         %esi, %ecx
	0xc1, 0xe9, 0x04, //0x0000032d shrl         $4, %ecx
	0xf7, 0xd1, //0x00000330 notl         %ecx
	0x81, 0xe1, 0x01, 0x01, 0x01, 0x01, //0x00000332 andl         $16843009, %ecx
	0x8d, 0x0c, 0xc9, //0x00000338 leal         (%rcx,%rcx,8), %ecx
	0x81, 0xe6, 0x0f, 0x0f, 0x0f, 0x0f, //0x0000033b andl         $252645135, %esi
	0x01, 0xce, //0x00000341 addl         %ecx, %esi
	0x89, 0xf1, //0x00000343 movl         %esi, %ecx
	0xc1, 0xe9, 0x04, //0x00000345 shrl         $4, %ecx
	0x09, 0xf1, //0x00000348 orl          %esi, %ecx
	0x89, 0xce, //0x0000034a movl         %ecx, %esi
	0xc1, 0xee, 0x08, //0x0000034c shrl         $8, %esi
	0x81, 0xe6, 0x00, 0xff, 0x00, 0x00, //0x0000034f andl         $65280, %esi
	0x0f, 0xb6, 0xd9, //0x00000355 movzbl       %cl, %ebx
	0x09, 0xf3, //0x00000358 orl          %esi, %ebx
	0x81, 0xe1, 0x00, 0x00, 0xfc, 0x00, //0x0000035a andl         $16515072, %ecx
	0x81, 0xf9, 0x00, 0x00, 0xdc, 0x00, //0x00000360 cmpl         $14417920, %ecx
	0x0f, 0x84, 0xac, 0x02, 0x00, 0x00, //0x00000366 je           LBB0_77
	0xf6, 0x45, 0xb8, 0x02, //0x0000036c testb        $2, $-72(%rbp)
	0x0f, 0x84, 0x41, 0x05, 0x00, 0x00, //0x00000370 je           LBB0_114
	0x49, 0x83, 0xc6, 0xf9, //0x00000376 addq         $-7, %r14
	0x66, 0xc7, 0x00, 0xef, 0xbf, //0x0000037a movw         $-16401, (%rax)
	0xc6, 0x40, 0x02, 0xbd, //0x0000037f movb         $-67, $2(%rax)
	0x48, 0x83, 0xc0, 0x03, //0x00000383 addq         $3, %rax
	0x49, 0x83, 0xc4, 0x07, //0x00000387 addq         $7, %r12
	0x41, 0x89, 0xdf, //0x0000038b movl         %ebx, %r15d
	0x83, 0xfb, 0x7f, //0x0000038e cmpl         $127, %ebx
	0x0f, 0x87, 0xae, 0xfe, 0xff, 0xff, //0x00000391 ja           LBB0_25
	0xe9, 0x44, 0x01, 0x00, 0x00, //0x00000397 jmp          LBB0_53
	0x90, 0x90, 0x90, 0x90, //0x0000039c .p2align 4, 0x90
	//0x000003a0 LBB0_40
	0x41, 0x81, 0xff, 0x00, 0x08, 0x00, 0x00, //0x000003a0 cmpl         $2048, %r15d
	0x0f, 0x82, 0xe1, 0x01, 0x00, 0x00, //0x000003a7 jb           LBB0_68
	0x44, 0x89, 0xf9, //0x000003ad movl         %r15d, %ecx
	0x81, 0xe1, 0x00, 0xf8, 0xff, 0xff, //0x000003b0 andl         $-2048, %ecx
	0x81, 0xf9, 0x00, 0xd8, 0x00, 0x00, //0x000003b6 cmpl         $55296, %ecx
	0x0f, 0x85, 0x2e, 0x01, 0x00, 0x00, //0x000003bc jne          LBB0_54
	0x41, 0x81, 0xff, 0xff, 0xdb, 0x00, 0x00, //0x000003c2 cmpl         $56319, %r15d
	0x0f, 0x87, 0xe4, 0x01, 0x00, 0x00, //0x000003c9 ja           LBB0_69
	0x49, 0x83, 0xfe, 0x06, //0x000003cf cmpq         $6, %r14
	0x0f, 0x8c, 0xda, 0x01, 0x00, 0x00, //0x000003d3 jl           LBB0_69
	0x43, 0x80, 0x7c, 0x21, 0x04, 0x5c, //0x000003d9 cmpb         $92, $4(%r9,%r12)
	0x0f, 0x85, 0xce, 0x01, 0x00, 0x00, //0x000003df jne          LBB0_69
	0x43, 0x80, 0x7c, 0x21, 0x05, 0x75, //0x000003e5 cmpb         $117, $5(%r9,%r12)
	0x0f, 0x85, 0xc2, 0x01, 0x00, 0x00, //0x000003eb jne          LBB0_69
	0x43, 0x8b, 0x74, 0x21, 0x06, //0x000003f1 movl         $6(%r9,%r12), %esi
	0x89, 0xf1, //0x000003f6 movl         %esi, %ecx
	0xf7, 0xd1, //0x000003f8 notl         %ecx
	0x8d, 0x9e, 0xd0, 0xcf, 0xcf, 0xcf, //0x000003fa leal         $-808464432(%rsi), %ebx
	0x81, 0xe1, 0x80, 0x80, 0x80, 0x80, //0x00000400 andl         $-2139062144, %ecx
	0x85, 0xd9, //0x00000406 testl        %ebx, %ecx
	0x0f, 0x85, 0x98, 0x03, 0x00, 0x00, //0x00000408 jne          LBB0_98
	0x8d, 0x9e, 0x19, 0x19, 0x19, 0x19, //0x0000040e leal         $421075225(%rsi), %ebx
	0x09, 0xf3, //0x00000414 orl          %esi, %ebx
	0xf7, 0xc3, 0x80, 0x80, 0x80, 0x80, //0x00000416 testl        $-2139062144, %ebx
	0x0f, 0x85, 0x84, 0x03, 0x00, 0x00, //0x0000041c jne          LBB0_98
	0x89, 0xf3, //0x00000422 movl         %esi, %ebx
	0x81, 0xe3, 0x7f, 0x7f, 0x7f, 0x7f, //0x00000424 andl         $2139062143, %ebx
	0xc7, 0x45, 0xd4, 0xc0, 0xc0, 0xc0, 0xc0, //0x0000042a movl         $-1061109568, $-44(%rbp)
	0x29, 0x5d, 0xd4, //0x00000431 subl         %ebx, $-44(%rbp)
	0x48, 0x89, 0x75, 0xb0, //0x00000434 movq         %rsi, $-80(%rbp)
	0x8d, 0xb3, 0x46, 0x46, 0x46, 0x46, //0x00000438 leal         $1179010630(%rbx), %esi
	0x89, 0x75, 0xc4, //0x0000043e movl         %esi, $-60(%rbp)
	0x21, 0x4d, 0xd4, //0x00000441 andl         %ecx, $-44(%rbp)
	0x8b, 0x75, 0xc4, //0x00000444 movl         $-60(%rbp), %esi
	0x85, 0x75, 0xd4, //0x00000447 testl        %esi, $-44(%rbp)
	0x48, 0x8b, 0x75, 0xb0, //0x0000044a movq         $-80(%rbp), %rsi
	0x0f, 0x85, 0x52, 0x03, 0x00, 0x00, //0x0000044e jne          LBB0_98
	0xc7, 0x45, 0xd4, 0xe0, 0xe0, 0xe0, 0xe0, //0x00000454 movl         $-522133280, $-44(%rbp)
	0x29, 0x5d, 0xd4, //0x0000045b subl         %ebx, $-44(%rbp)
	0x81, 0xc3, 0x39, 0x39, 0x39, 0x39, //0x0000045e addl         $960051513, %ebx
	0x23, 0x4d, 0xd4, //0x00000464 andl         $-44(%rbp), %ecx
	0x85, 0xd9, //0x00000467 testl        %ebx, %ecx
	0x0f, 0x85, 0x37, 0x03, 0x00, 0x00, //0x00000469 jne          LBB0_98
	0x0f, 0xce, //0x0000046f bswapl       %esi
	0x89, 0xf1, //0x00000471 movl         %esi, %ecx
	0xc1, 0xe9, 0x04, //0x00000473 shrl         $4, %ecx
	0xf7, 0xd1, //0x00000476 notl         %ecx
	0x81, 0xe1, 0x01, 0x01, 0x01, 0x01, //0x00000478 andl         $16843009, %ecx
	0x8d, 0x0c, 0xc9, //0x0000047e leal         (%rcx,%rcx,8), %ecx
	0x81, 0xe6, 0x0f, 0x0f, 0x0f, 0x0f, //0x00000481 andl         $252645135, %esi
	0x01, 0xce, //0x00000487 addl         %ecx, %esi
	0x89, 0xf1, //0x00000489 movl         %esi, %ecx
	0xc1, 0xe9, 0x04, //0x0000048b shrl         $4, %ecx
	0x09, 0xf1, //0x0000048e orl          %esi, %ecx
	0x89, 0xce, //0x00000490 movl         %ecx, %esi
	0xc1, 0xee, 0x08, //0x00000492 shrl         $8, %esi
	0x81, 0xe6, 0x00, 0xff, 0x00, 0x00, //0x00000495 andl         $65280, %esi
	0x0f, 0xb6, 0xd9, //0x0000049b movzbl       %cl, %ebx
	0x09, 0xf3, //0x0000049e orl          %esi, %ebx
	0x81, 0xe1, 0x00, 0x00, 0xfc, 0x00, //0x000004a0 andl         $16515072, %ecx
	0x81, 0xf9, 0x00, 0x00, 0xdc, 0x00, //0x000004a6 cmpl         $14417920, %ecx
	0x0f, 0x84, 0x55, 0x01, 0x00, 0x00, //0x000004ac je           LBB0_76
	0xf6, 0x45, 0xb8, 0x02, //0x000004b2 testb        $2, $-72(%rbp)
	0x0f, 0x84, 0xf1, 0x03, 0x00, 0x00, //0x000004b6 je           LBB0_113
	0x49, 0x83, 0xc6, 0xfa, //0x000004bc addq         $-6, %r14
	0x66, 0xc7, 0x00, 0xef, 0xbf, //0x000004c0 movw         $-16401, (%rax)
	0xc6, 0x40, 0x02, 0xbd, //0x000004c5 movb         $-67, $2(%rax)
	0x48, 0x83, 0xc0, 0x03, //0x000004c9 addq         $3, %rax
	0x49, 0x83, 0xc4, 0x06, //0x000004cd addq         $6, %r12
	0x41, 0x89, 0xdf, //0x000004d1 movl         %ebx, %r15d
	0x81, 0xfb, 0x80, 0x00, 0x00, 0x00, //0x000004d4 cmpl         $128, %ebx
	0x0f, 0x83, 0xc0, 0xfe, 0xff, 0xff, //0x000004da jae          LBB0_40
	//0x000004e0 LBB0_53
	0x4f, 0x8d, 0x4c, 0x21, 0x04, //0x000004e0 leaq         $4(%r9,%r12), %r9
	0x41, 0x89, 0xdf, //0x000004e5 movl         %ebx, %r15d
	0xe9, 0x96, 0x00, 0x00, 0x00, //0x000004e8 jmp          LBB0_67
	0x90, 0x90, 0x90, //0x000004ed .p2align 4, 0x90
	//0x000004f0 LBB0_54
	0x4f, 0x8d, 0x4c, 0x21, 0x04, //0x000004f0 leaq         $4(%r9,%r12), %r9
	0x44, 0x89, 0xf9, //0x000004f5 movl         %r15d, %ecx
	0xc1, 0xe9, 0x0c, //0x000004f8 shrl         $12, %ecx
	0x80, 0xc9, 0xe0, //0x000004fb orb          $-32, %cl
	0x88, 0x08, //0x000004fe movb         %cl, (%rax)
	0x44, 0x89, 0xf9, //0x00000500 movl         %r15d, %ecx
	0xc1, 0xe9, 0x06, //0x00000503 shrl         $6, %ecx
	0x80, 0xe1, 0x3f, //0x00000506 andb         $63, %cl
	0x80, 0xc9, 0x80, //0x00000509 orb          $-128, %cl
	0x88, 0x48, 0x01, //0x0000050c movb         %cl, $1(%rax)
	0x41, 0x80, 0xe7, 0x3f, //0x0000050f andb         $63, %r15b
	0x41, 0x80, 0xcf, 0x80, //0x00000513 orb          $-128, %r15b
	0x44, 0x88, 0x78, 0x02, //0x00000517 movb         %r15b, $2(%rax)
	//0x0000051b LBB0_55
	0x48, 0x83, 0xc0, 0x03, //0x0000051b addq         $3, %rax
	//0x0000051f LBB0_56
	0x4d, 0x89, 0xf5, //0x0000051f movq         %r14, %r13
	//0x00000522 LBB0_57
	0x4d, 0x85, 0xed, //0x00000522 testq        %r13, %r13
	0x0f, 0x85, 0x2c, 0xfb, 0xff, 0xff, //0x00000525 jne          LBB0_2
	0xe9, 0x63, 0x02, 0x00, 0x00, //0x0000052b jmp          LBB0_96
	//0x00000530 LBB0_58
	0x45, 0x85, 0xed, //0x00000530 testl        %r13d, %r13d
	0x0f, 0x84, 0x2e, 0x02, 0x00, 0x00, //0x00000533 je           LBB0_94
	0x41, 0x80, 0x79, 0xff, 0x5c, //0x00000539 cmpb         $92, $-1(%r9)
	0x0f, 0x85, 0x57, 0x02, 0x00, 0x00, //0x0000053e jne          LBB0_97
	0x41, 0x80, 0x39, 0x5c, //0x00000544 cmpb         $92, (%r9)
	0x0f, 0x85, 0x26, 0x00, 0x00, 0x00, //0x00000548 jne          LBB0_65
	0x41, 0x83, 0xfd, 0x01, //0x0000054e cmpl         $1, %r13d
	0x0f, 0x8e, 0x0f, 0x02, 0x00, 0x00, //0x00000552 jle          LBB0_94
	0x41, 0x8a, 0x49, 0x01, //0x00000558 movb         $1(%r9), %cl
	0x80, 0xf9, 0x22, //0x0000055c cmpb         $34, %cl
	0x0f, 0x84, 0x09, 0x00, 0x00, 0x00, //0x0000055f je           LBB0_64
	0x80, 0xf9, 0x5c, //0x00000565 cmpb         $92, %cl
	0x0f, 0x85, 0x0c, 0x03, 0x00, 0x00, //0x00000568 jne          LBB0_109
	//0x0000056e LBB0_64
	0x49, 0xff, 0xc1, //0x0000056e incq         %r9
	0x49, 0xff, 0xcd, //0x00000571 decq         %r13
	//0x00000574 LBB0_65
	0x49, 0xff, 0xc1, //0x00000574 incq         %r9
	0x49, 0xff, 0xcd, //0x00000577 decq         %r13
	0xe9, 0xcc, 0xfb, 0xff, 0xff, //0x0000057a jmp          LBB0_15
	//0x0000057f LBB0_66
	0x49, 0x83, 0xc1, 0x04, //0x0000057f addq         $4, %r9
	//0x00000583 LBB0_67
	0x44, 0x88, 0x38, //0x00000583 movb         %r15b, (%rax)
	0x48, 0xff, 0xc0, //0x00000586 incq         %rax
	0xe9, 0x91, 0xff, 0xff, 0xff, //0x00000589 jmp          LBB0_56
	//0x0000058e LBB0_68
	0x4f, 0x8d, 0x4c, 0x21, 0x04, //0x0000058e leaq         $4(%r9,%r12), %r9
	0x44, 0x89, 0xf9, //0x00000593 movl         %r15d, %ecx
	0xc1, 0xe9, 0x06, //0x00000596 shrl         $6, %ecx
	0x80, 0xc9, 0xc0, //0x00000599 orb          $-64, %cl
	0x88, 0x08, //0x0000059c movb         %cl, (%rax)
	0x41, 0x80, 0xe7, 0x3f, //0x0000059e andb         $63, %r15b
	0x41, 0x80, 0xcf, 0x80, //0x000005a2 orb          $-128, %r15b
	0x44, 0x88, 0x78, 0x01, //0x000005a6 movb         %r15b, $1(%rax)
	0x48, 0x83, 0xc0, 0x02, //0x000005aa addq         $2, %rax
	0xe9, 0x6c, 0xff, 0xff, 0xff, //0x000005ae jmp          LBB0_56
	//0x000005b3 LBB0_69
	0x4f, 0x8d, 0x4c, 0x21, 0x04, //0x000005b3 leaq         $4(%r9,%r12), %r9
	0xe9, 0x0f, 0x00, 0x00, 0x00, //0x000005b8 jmp          LBB0_71
	//0x000005bd LBB0_70
	0x4f, 0x8d, 0x4c, 0x21, 0x05, //0x000005bd leaq         $5(%r9,%r12), %r9
	0x4d, 0x29, 0xe5, //0x000005c2 subq         %r12, %r13
	0x49, 0x83, 0xc5, 0xfb, //0x000005c5 addq         $-5, %r13
	0x4d, 0x89, 0xee, //0x000005c9 movq         %r13, %r14
	//0x000005cc LBB0_71
	0xf6, 0x45, 0xb8, 0x02, //0x000005cc testb        $2, $-72(%rbp)
	0x0f, 0x85, 0x23, 0x00, 0x00, 0x00, //0x000005d0 jne          LBB0_75
	0xe9, 0xfb, 0x02, 0x00, 0x00, //0x000005d6 jmp          LBB0_116
	//0x000005db LBB0_72
	0xf6, 0x45, 0xb8, 0x02, //0x000005db testb        $2, $-72(%rbp)
	0x0f, 0x85, 0x0f, 0x00, 0x00, 0x00, //0x000005df jne          LBB0_74
	0xe9, 0x7d, 0x01, 0x00, 0x00, //0x000005e5 jmp          LBB0_94
	//0x000005ea LBB0_73
	0xf6, 0x45, 0xb8, 0x02, //0x000005ea testb        $2, $-72(%rbp)
	0x0f, 0x84, 0xef, 0x02, 0x00, 0x00, //0x000005ee je           LBB0_117
	//0x000005f4 LBB0_74
	0x4f, 0x8d, 0x4c, 0x21, 0x04, //0x000005f4 leaq         $4(%r9,%r12), %r9
	//0x000005f9 LBB0_75
	0x66, 0xc7, 0x00, 0xef, 0xbf, //0x000005f9 movw         $-16401, (%rax)
	0xc6, 0x40, 0x02, 0xbd, //0x000005fe movb         $-67, $2(%rax)
	0xe9, 0x14, 0xff, 0xff, 0xff, //0x00000602 jmp          LBB0_55
	//0x00000607 LBB0_76
	0x4f, 0x8d, 0x4c, 0x21, 0x0a, //0x00000607 leaq         $10(%r9,%r12), %r9
	0x4d, 0x29, 0xe5, //0x0000060c subq         %r12, %r13
	0x49, 0x83, 0xc5, 0xf6, //0x0000060f addq         $-10, %r13
	0xe9, 0x0c, 0x00, 0x00, 0x00, //0x00000613 jmp          LBB0_78
	//0x00000618 LBB0_77
	0x4f, 0x8d, 0x4c, 0x21, 0x0b, //0x00000618 leaq         $11(%r9,%r12), %r9
	0x4d, 0x29, 0xe5, //0x0000061d subq         %r12, %r13
	0x49, 0x83, 0xc5, 0xf5, //0x00000620 addq         $-11, %r13
	//0x00000624 LBB0_78
	0x41, 0xc1, 0xe7, 0x0a, //0x00000624 shll         $10, %r15d
	0x41, 0x8d, 0x8c, 0x1f, 0x00, 0x24, 0xa0, 0xfc, //0x00000628 leal         $-56613888(%r15,%rbx), %ecx
	0x81, 0xf9, 0x00, 0x00, 0x11, 0x00, //0x00000630 cmpl         $1114112, %ecx
	0x0f, 0x82, 0x1c, 0x00, 0x00, 0x00, //0x00000636 jb           LBB0_81
	0xf6, 0x45, 0xb8, 0x02, //0x0000063c testb        $2, $-72(%rbp)
	0x0f, 0x84, 0x4d, 0x02, 0x00, 0x00, //0x00000640 je           LBB0_111
	0x66, 0xc7, 0x00, 0xef, 0xbf, //0x00000646 movw         $-16401, (%rax)
	0xc6, 0x40, 0x02, 0xbd, //0x0000064b movb         $-67, $2(%rax)
	0x48, 0x83, 0xc0, 0x03, //0x0000064f addq         $3, %rax
	0xe9, 0xca, 0xfe, 0xff, 0xff, //0x00000653 jmp          LBB0_57
	//0x00000658 LBB0_81
	0x89, 0xce, //0x00000658 movl         %ecx, %esi
	0xc1, 0xee, 0x12, //0x0000065a shrl         $18, %esi
	0x40, 0x80, 0xce, 0xf0, //0x0000065d orb          $-16, %sil
	0x40, 0x88, 0x30, //0x00000661 movb         %sil, (%rax)
	0x89, 0xce, //0x00000664 movl         %ecx, %esi
	0xc1, 0xee, 0x0c, //0x00000666 shrl         $12, %esi
	0x40, 0x80, 0xe6, 0x3f, //0x00000669 andb         $63, %sil
	0x40, 0x80, 0xce, 0x80, //0x0000066d orb          $-128, %sil
	0x40, 0x88, 0x70, 0x01, //0x00000671 movb         %sil, $1(%rax)
	0x89, 0xce, //0x00000675 movl         %ecx, %esi
	0xc1, 0xee, 0x06, //0x00000677 shrl         $6, %esi
	0x40, 0x80, 0xe6, 0x3f, //0x0000067a andb         $63, %sil
	0x40, 0x80, 0xce, 0x80, //0x0000067e orb          $-128, %sil
	0x40, 0x88, 0x70, 0x02, //0x00000682 movb         %sil, $2(%rax)
	0x80, 0xe1, 0x3f, //0x00000686 andb         $63, %cl
	0x80, 0xc9, 0x80, //0x00000689 orb          $-128, %cl
	0x88, 0x48, 0x03, //0x0000068c movb         %cl, $3(%rax)
	0x48, 0x83, 0xc0, 0x04, //0x0000068f addq         $4, %rax
	0xe9, 0x8a, 0xfe, 0xff, 0xff, //0x00000693 jmp          LBB0_57
	//0x00000698 LBB0_82
	0x45, 0x31, 0xed, //0x00000698 xorl         %r13d, %r13d
	0x48, 0x89, 0xd0, //0x0000069b movq         %rdx, %rax
	//0x0000069e LBB0_83
	0x4c, 0x01, 0xe8, //0x0000069e addq         %r13, %rax
	0x48, 0x29, 0xd0, //0x000006a1 subq         %rdx, %rax
	//0x000006a4 LBB0_84
	0x48, 0x83, 0xc4, 0x28, //0x000006a4 addq         $40, %rsp
	0x5b, //0x000006a8 popq         %rbx
	0x41, 0x5c, //0x000006a9 popq         %r12
	0x41, 0x5d, //0x000006ab popq         %r13
	0x41, 0x5e, //0x000006ad popq         %r14
	0x41, 0x5f, //0x000006af popq         %r15
	0x5d, //0x000006b1 popq         %rbp
	0xc3, //0x000006b2 retq         
	//0x000006b3 LBB0_85
	0x4c, 0x89, 0xca, //0x000006b3 movq         %r9, %rdx
	0x48, 0x29, 0xfa, //0x000006b6 subq         %rdi, %rdx
	0x48, 0x8b, 0x7d, 0xc8, //0x000006b9 movq         $-56(%rbp), %rdi
	0x48, 0x89, 0x17, //0x000006bd movq         %rdx, (%rdi)
	0x41, 0x8a, 0x09, //0x000006c0 movb         (%r9), %cl
	0x8d, 0x71, 0xd0, //0x000006c3 leal         $-48(%rcx), %esi
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x000006c6 movq         $-2, %rax
	0x40, 0x80, 0xfe, 0x0a, //0x000006cd cmpb         $10, %sil
	0x0f, 0x82, 0x0f, 0x00, 0x00, 0x00, //0x000006d1 jb           LBB0_87
	0x80, 0xe1, 0xdf, //0x000006d7 andb         $-33, %cl
	0x80, 0xc1, 0xbf, //0x000006da addb         $-65, %cl
	0x80, 0xf9, 0x05, //0x000006dd cmpb         $5, %cl
	0x0f, 0x87, 0xbe, 0xff, 0xff, 0xff, //0x000006e0 ja           LBB0_84
	//0x000006e6 LBB0_87
	0x48, 0x8d, 0x4a, 0x01, //0x000006e6 leaq         $1(%rdx), %rcx
	0x48, 0x89, 0x0f, //0x000006ea movq         %rcx, (%rdi)
	0x41, 0x8a, 0x49, 0x01, //0x000006ed movb         $1(%r9), %cl
	0x8d, 0x71, 0xd0, //0x000006f1 leal         $-48(%rcx), %esi
	0x40, 0x80, 0xfe, 0x0a, //0x000006f4 cmpb         $10, %sil
	0x0f, 0x82, 0x0f, 0x00, 0x00, 0x00, //0x000006f8 jb           LBB0_89
	0x80, 0xe1, 0xdf, //0x000006fe andb         $-33, %cl
	0x80, 0xc1, 0xbf, //0x00000701 addb         $-65, %cl
	0x80, 0xf9, 0x05, //0x00000704 cmpb         $5, %cl
	0x0f, 0x87, 0x97, 0xff, 0xff, 0xff, //0x00000707 ja           LBB0_84
	//0x0000070d LBB0_89
	0x48, 0x8d, 0x4a, 0x02, //0x0000070d leaq         $2(%rdx), %rcx
	0x48, 0x89, 0x0f, //0x00000711 movq         %rcx, (%rdi)
	0x41, 0x8a, 0x49, 0x02, //0x00000714 movb         $2(%r9), %cl
	0x8d, 0x71, 0xd0, //0x00000718 leal         $-48(%rcx), %esi
	0x40, 0x80, 0xfe, 0x0a, //0x0000071b cmpb         $10, %sil
	0x0f, 0x82, 0x0f, 0x00, 0x00, 0x00, //0x0000071f jb           LBB0_91
	0x80, 0xe1, 0xdf, //0x00000725 andb         $-33, %cl
	0x80, 0xc1, 0xbf, //0x00000728 addb         $-65, %cl
	0x80, 0xf9, 0x05, //0x0000072b cmpb         $5, %cl
	0x0f, 0x87, 0x70, 0xff, 0xff, 0xff, //0x0000072e ja           LBB0_84
	//0x00000734 LBB0_91
	0x48, 0x8d, 0x4a, 0x03, //0x00000734 leaq         $3(%rdx), %rcx
	0x48, 0x89, 0x0f, //0x00000738 movq         %rcx, (%rdi)
	0x41, 0x8a, 0x49, 0x03, //0x0000073b movb         $3(%r9), %cl
	0x8d, 0x71, 0xd0, //0x0000073f leal         $-48(%rcx), %esi
	0x40, 0x80, 0xfe, 0x0a, //0x00000742 cmpb         $10, %sil
	0x0f, 0x82, 0x0f, 0x00, 0x00, 0x00, //0x00000746 jb           LBB0_93
	0x80, 0xe1, 0xdf, //0x0000074c andb         $-33, %cl
	0x80, 0xc1, 0xbf, //0x0000074f addb         $-65, %cl
	0x80, 0xf9, 0x05, //0x00000752 cmpb         $5, %cl
	0x0f, 0x87, 0x49, 0xff, 0xff, 0xff, //0x00000755 ja           LBB0_84
	//0x0000075b LBB0_93
	0x48, 0x83, 0xc2, 0x04, //0x0000075b addq         $4, %rdx
	0x48, 0x89, 0x17, //0x0000075f movq         %rdx, (%rdi)
	0xe9, 0x3d, 0xff, 0xff, 0xff, //0x00000762 jmp          LBB0_84
	//0x00000767 LBB0_94
	0x48, 0x8b, 0x45, 0xc8, //0x00000767 movq         $-56(%rbp), %rax
	0x4c, 0x89, 0x18, //0x0000076b movq         %r11, (%rax)
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x0000076e movq         $-1, %rax
	0xe9, 0x2a, 0xff, 0xff, 0xff, //0x00000775 jmp          LBB0_84
	//0x0000077a LBB0_95
	0x48, 0xf7, 0xd7, //0x0000077a notq         %rdi
	0x49, 0x01, 0xf9, //0x0000077d addq         %rdi, %r9
	0x48, 0x8b, 0x45, 0xc8, //0x00000780 movq         $-56(%rbp), %rax
	0x4c, 0x89, 0x08, //0x00000784 movq         %r9, (%rax)
	0x48, 0xc7, 0xc0, 0xfd, 0xff, 0xff, 0xff, //0x00000787 movq         $-3, %rax
	0xe9, 0x11, 0xff, 0xff, 0xff, //0x0000078e jmp          LBB0_84
	//0x00000793 LBB0_96
	0x45, 0x31, 0xed, //0x00000793 xorl         %r13d, %r13d
	0xe9, 0x03, 0xff, 0xff, 0xff, //0x00000796 jmp          LBB0_83
	//0x0000079b LBB0_97
	0x48, 0xf7, 0xd7, //0x0000079b notq         %rdi
	0x49, 0x01, 0xf9, //0x0000079e addq         %rdi, %r9
	0xe9, 0xda, 0x00, 0x00, 0x00, //0x000007a1 jmp          LBB0_110
	//0x000007a6 LBB0_98
	0x4b, 0x8d, 0x74, 0x21, 0x04, //0x000007a6 leaq         $4(%r9,%r12), %rsi
	0xe9, 0x05, 0x00, 0x00, 0x00, //0x000007ab jmp          LBB0_100
	//0x000007b0 LBB0_99
	0x4b, 0x8d, 0x74, 0x21, 0x05, //0x000007b0 leaq         $5(%r9,%r12), %rsi
	//0x000007b5 LBB0_100
	0x48, 0x89, 0xf2, //0x000007b5 movq         %rsi, %rdx
	0x48, 0x29, 0xfa, //0x000007b8 subq         %rdi, %rdx
	0x48, 0x83, 0xc2, 0x02, //0x000007bb addq         $2, %rdx
	0x48, 0x8b, 0x45, 0xc8, //0x000007bf movq         $-56(%rbp), %rax
	0x48, 0x89, 0x10, //0x000007c3 movq         %rdx, (%rax)
	0x8a, 0x4e, 0x02, //0x000007c6 movb         $2(%rsi), %cl
	0x8d, 0x79, 0xd0, //0x000007c9 leal         $-48(%rcx), %edi
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x000007cc movq         $-2, %rax
	0x40, 0x80, 0xff, 0x0a, //0x000007d3 cmpb         $10, %dil
	0x0f, 0x82, 0x0f, 0x00, 0x00, 0x00, //0x000007d7 jb           LBB0_102
	0x80, 0xe1, 0xdf, //0x000007dd andb         $-33, %cl
	0x80, 0xc1, 0xbf, //0x000007e0 addb         $-65, %cl
	0x80, 0xf9, 0x05, //0x000007e3 cmpb         $5, %cl
	0x0f, 0x87, 0xb8, 0xfe, 0xff, 0xff, //0x000007e6 ja           LBB0_84
	//0x000007ec LBB0_102
	0x48, 0x8d, 0x4a, 0x01, //0x000007ec leaq         $1(%rdx), %rcx
	0x48, 0x8b, 0x7d, 0xc8, //0x000007f0 movq         $-56(%rbp), %rdi
	0x48, 0x89, 0x0f, //0x000007f4 movq         %rcx, (%rdi)
	0x8a, 0x4e, 0x03, //0x000007f7 movb         $3(%rsi), %cl
	0x8d, 0x79, 0xd0, //0x000007fa leal         $-48(%rcx), %edi
	0x40, 0x80, 0xff, 0x0a, //0x000007fd cmpb         $10, %dil
	0x0f, 0x82, 0x0f, 0x00, 0x00, 0x00, //0x00000801 jb           LBB0_104
	0x80, 0xe1, 0xdf, //0x00000807 andb         $-33, %cl
	0x80, 0xc1, 0xbf, //0x0000080a addb         $-65, %cl
	0x80, 0xf9, 0x05, //0x0000080d cmpb         $5, %cl
	0x0f, 0x87, 0x8e, 0xfe, 0xff, 0xff, //0x00000810 ja           LBB0_84
	//0x00000816 LBB0_104
	0x48, 0x8d, 0x4a, 0x02, //0x00000816 leaq         $2(%rdx), %rcx
	0x48, 0x8b, 0x7d, 0xc8, //0x0000081a movq         $-56(%rbp), %rdi
	0x48, 0x89, 0x0f, //0x0000081e movq         %rcx, (%rdi)
	0x8a, 0x4e, 0x04, //0x00000821 movb         $4(%rsi), %cl
	0x8d, 0x79, 0xd0, //0x00000824 leal         $-48(%rcx), %edi
	0x40, 0x80, 0xff, 0x0a, //0x00000827 cmpb         $10, %dil
	0x0f, 0x82, 0x0f, 0x00, 0x00, 0x00, //0x0000082b jb           LBB0_106
	0x80, 0xe1, 0xdf, //0x00000831 andb         $-33, %cl
	0x80, 0xc1, 0xbf, //0x00000834 addb         $-65, %cl
	0x80, 0xf9, 0x05, //0x00000837 cmpb         $5, %cl
	0x0f, 0x87, 0x64, 0xfe, 0xff, 0xff, //0x0000083a ja           LBB0_84
	//0x00000840 LBB0_106
	0x48, 0x8d, 0x4a, 0x03, //0x00000840 leaq         $3(%rdx), %rcx
	0x48, 0x8b, 0x7d, 0xc8, //0x00000844 movq         $-56(%rbp), %rdi
	0x48, 0x89, 0x0f, //0x00000848 movq         %rcx, (%rdi)
	0x8a, 0x4e, 0x05, //0x0000084b movb         $5(%rsi), %cl
	0x8d, 0x71, 0xd0, //0x0000084e leal         $-48(%rcx), %esi
	0x40, 0x80, 0xfe, 0x0a, //0x00000851 cmpb         $10, %sil
	0x0f, 0x82, 0x0f, 0x00, 0x00, 0x00, //0x00000855 jb           LBB0_108
	0x80, 0xe1, 0xdf, //0x0000085b andb         $-33, %cl
	0x80, 0xc1, 0xbf, //0x0000085e addb         $-65, %cl
	0x80, 0xf9, 0x05, //0x00000861 cmpb         $5, %cl
	0x0f, 0x87, 0x3a, 0xfe, 0xff, 0xff, //0x00000864 ja           LBB0_84
	//0x0000086a LBB0_108
	0x48, 0x83, 0xc2, 0x04, //0x0000086a addq         $4, %rdx
	0x48, 0x8b, 0x4d, 0xc8, //0x0000086e movq         $-56(%rbp), %rcx
	0x48, 0x89, 0x11, //0x00000872 movq         %rdx, (%rcx)
	0xe9, 0x2a, 0xfe, 0xff, 0xff, //0x00000875 jmp          LBB0_84
	//0x0000087a LBB0_109
	0x49, 0x29, 0xf9, //0x0000087a subq         %rdi, %r9
	0x49, 0xff, 0xc1, //0x0000087d incq         %r9
	//0x00000880 LBB0_110
	0x48, 0x8b, 0x45, 0xc8, //0x00000880 movq         $-56(%rbp), %rax
	0x4c, 0x89, 0x08, //0x00000884 movq         %r9, (%rax)
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x00000887 movq         $-2, %rax
	0xe9, 0x11, 0xfe, 0xff, 0xff, //0x0000088e jmp          LBB0_84
	//0x00000893 LBB0_111
	0x49, 0x29, 0xf9, //0x00000893 subq         %rdi, %r9
	0x49, 0x83, 0xc1, 0xfc, //0x00000896 addq         $-4, %r9
	//0x0000089a LBB0_112
	0x48, 0x8b, 0x45, 0xc8, //0x0000089a movq         $-56(%rbp), %rax
	0x4c, 0x89, 0x08, //0x0000089e movq         %r9, (%rax)
	0x48, 0xc7, 0xc0, 0xfc, 0xff, 0xff, 0xff, //0x000008a1 movq         $-4, %rax
	0xe9, 0xf7, 0xfd, 0xff, 0xff, //0x000008a8 jmp          LBB0_84
	//0x000008ad LBB0_113
	0x4b, 0x8d, 0x44, 0x21, 0x0a, //0x000008ad leaq         $10(%r9,%r12), %rax
	0xe9, 0x05, 0x00, 0x00, 0x00, //0x000008b2 jmp          LBB0_115
	//0x000008b7 LBB0_114
	0x4b, 0x8d, 0x44, 0x21, 0x0b, //0x000008b7 leaq         $11(%r9,%r12), %rax
	//0x000008bc LBB0_115
	0x48, 0x29, 0xf8, //0x000008bc subq         %rdi, %rax
	0x48, 0x83, 0xc0, 0xfc, //0x000008bf addq         $-4, %rax
	0x48, 0x8b, 0x4d, 0xc8, //0x000008c3 movq         $-56(%rbp), %rcx
	0x48, 0x89, 0x01, //0x000008c7 movq         %rax, (%rcx)
	0x48, 0xc7, 0xc0, 0xfc, 0xff, 0xff, 0xff, //0x000008ca movq         $-4, %rax
	0xe9, 0xce, 0xfd, 0xff, 0xff, //0x000008d1 jmp          LBB0_84
	//0x000008d6 LBB0_116
	0x49, 0x8d, 0x44, 0x3a, 0x04, //0x000008d6 leaq         $4(%r10,%rdi), %rax
	0x49, 0x29, 0xc1, //0x000008db subq         %rax, %r9
	0xe9, 0xb7, 0xff, 0xff, 0xff, //0x000008de jmp          LBB0_112
	//0x000008e3 LBB0_117
	0x4d, 0x01, 0xe1, //0x000008e3 addq         %r12, %r9
	0x49, 0x29, 0xf9, //0x000008e6 subq         %rdi, %r9
	0xe9, 0xac, 0xff, 0xff, 0xff, //0x000008e9 jmp          LBB0_112
	0x00, 0x00, //0x000008ee .p2align 4, 0x00
	//0x000008f0 __UnquoteTab
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000008f0 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .ascii 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000900 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .ascii 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x22, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2f, //0x00000910 QUAD $0x0000000000220000; QUAD $0x2f00000000000000  // .ascii 16, '\x00\x00"\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00/'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000920 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .ascii 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000930 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .ascii 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5c, 0x00, 0x00, 0x00, //0x00000940 QUAD $0x0000000000000000; QUAD $0x0000005c00000000  // .ascii 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\\\x00\x00\x00'
	0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0a, 0x00, //0x00000950 QUAD $0x000c000000080000; QUAD $0x000a000000000000  // .ascii 16, '\x00\x00\x08\x00\x00\x00\x0c\x00\x00\x00\x00\x00\x00\x00\n\x00'
	0x00, 0x00, 0x0d, 0x00, 0x09, 0xff, //0x00000960 LONG $0x000d0000; WORD $0xff09  // .ascii 6, '\x00\x00\r\x00\t\xff'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000966 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000976 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000986 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000996 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000009a6 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000009b6 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000009c6 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000009d6 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000009e6 QUAD $0x0000000000000000; WORD $0x0000  // .space 10, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
}
 
