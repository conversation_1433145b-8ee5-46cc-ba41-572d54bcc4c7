// +build !noasm !appengine
// Code generated by asm2asm, DO NOT EDIT.

package avx

import (
	`github.com/bytedance/sonic/loader`
)

const (
    _entry__u64toa = 64
)

const (
    _stack__u64toa = 8
)

const (
    _size__u64toa = 1232
)

var (
    _pcsp__u64toa = [][2]uint32{
        {1, 0},
        {161, 8},
        {162, 0},
        {457, 8},
        {458, 0},
        {756, 8},
        {757, 0},
        {1221, 8},
        {1223, 0},
    }
)

var _cfunc_u64toa = []loader.CFunc{
    {"_u64toa_entry", 0,  _entry__u64toa, 0, nil},
    {"_u64toa", _entry__u64toa, _size__u64toa, _stack__u64toa, _pcsp__u64toa},
}
