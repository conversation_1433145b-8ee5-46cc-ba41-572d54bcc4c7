// +build !noasm !appengine
// Code generated by asm2asm, DO NOT EDIT.

package avx

import (
	`github.com/bytedance/sonic/loader`
)

const (
    _entry__vnumber = 48
)

const (
    _stack__vnumber = 112
)

const (
    _size__vnumber = 9048
)

var (
    _pcsp__vnumber = [][2]uint32{
        {1, 0},
        {4, 8},
        {6, 16},
        {8, 24},
        {10, 32},
        {12, 40},
        {13, 48},
        {751, 112},
        {755, 48},
        {756, 40},
        {758, 32},
        {760, 24},
        {762, 16},
        {764, 8},
        {768, 0},
        {9045, 112},
    }
)

var _cfunc_vnumber = []loader.CFunc{
    {"_vnumber_entry", 0,  _entry__vnumber, 0, nil},
    {"_vnumber", _entry__vnumber, _size__vnumber, _stack__vnumber, _pcsp__vnumber},
}
