// +build !noasm !appengine
// Code generated by asm2asm, DO NOT EDIT.

package avx

import (
	`github.com/bytedance/sonic/loader`
)

const (
    _entry__validate_one = 144
)

const (
    _stack__validate_one = 152
)

const (
    _size__validate_one = 9524
)

var (
    _pcsp__validate_one = [][2]uint32{
        {1, 0},
        {4, 8},
        {6, 16},
        {8, 24},
        {10, 32},
        {12, 40},
        {13, 48},
        {8962, 152},
        {8966, 48},
        {8967, 40},
        {8969, 32},
        {8971, 24},
        {8973, 16},
        {8975, 8},
        {8976, 0},
        {9524, 152},
    }
)

var _cfunc_validate_one = []loader.CFunc{
    {"_validate_one_entry", 0,  _entry__validate_one, 0, nil},
    {"_validate_one", _entry__validate_one, _size__validate_one, _stack__validate_one, _pcsp__validate_one},
}
