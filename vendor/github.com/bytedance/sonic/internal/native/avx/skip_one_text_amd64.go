// +build amd64
// Code generated by asm2asm, DO NOT EDIT.

package avx

var _text_skip_one = []byte{
	// .p2align 4, 0x00
	// LCPI0_0
	0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, // QUAD $0x2222222222222222; QUAD $0x2222222222222222  // .space 16, '""""""""""""""""'
	//0x00000010 LCPI0_1
	0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, //0x00000010 QUAD $0x5c5c5c5c5c5c5c5c; QUAD $0x5c5c5c5c5c5c5c5c  // .space 16, '\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\'
	//0x00000020 LCPI0_2
	0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, //0x00000020 QUAD $0x2020202020202020; QUAD $0x2020202020202020  // .space 16, '                '
	//0x00000030 LCPI0_3
	0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, //0x00000030 QUAD $0x2f2f2f2f2f2f2f2f; QUAD $0x2f2f2f2f2f2f2f2f  // .space 16, '////////////////'
	//0x00000040 LCPI0_4
	0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, //0x00000040 QUAD $0x3a3a3a3a3a3a3a3a; QUAD $0x3a3a3a3a3a3a3a3a  // .space 16, '::::::::::::::::'
	//0x00000050 LCPI0_5
	0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, //0x00000050 QUAD $0x2b2b2b2b2b2b2b2b; QUAD $0x2b2b2b2b2b2b2b2b  // .space 16, '++++++++++++++++'
	//0x00000060 LCPI0_6
	0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, //0x00000060 QUAD $0x2d2d2d2d2d2d2d2d; QUAD $0x2d2d2d2d2d2d2d2d  // .space 16, '----------------'
	//0x00000070 LCPI0_7
	0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, //0x00000070 QUAD $0x2e2e2e2e2e2e2e2e; QUAD $0x2e2e2e2e2e2e2e2e  // .space 16, '................'
	//0x00000080 LCPI0_8
	0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, //0x00000080 QUAD $0x6565656565656565; QUAD $0x6565656565656565  // .space 16, 'eeeeeeeeeeeeeeee'
	//0x00000090 .p2align 4, 0x90
	//0x00000090 _skip_one
	0x55, //0x00000090 pushq        %rbp
	0x48, 0x89, 0xe5, //0x00000091 movq         %rsp, %rbp
	0x41, 0x57, //0x00000094 pushq        %r15
	0x41, 0x56, //0x00000096 pushq        %r14
	0x41, 0x55, //0x00000098 pushq        %r13
	0x41, 0x54, //0x0000009a pushq        %r12
	0x53, //0x0000009c pushq        %rbx
	0x48, 0x83, 0xec, 0x70, //0x0000009d subq         $112, %rsp
	0x48, 0x89, 0x8d, 0x78, 0xff, 0xff, 0xff, //0x000000a1 movq         %rcx, $-136(%rbp)
	0x41, 0xb8, 0x01, 0x00, 0x00, 0x00, //0x000000a8 movl         $1, %r8d
	0xc4, 0xc1, 0xf9, 0x6e, 0xc0, //0x000000ae vmovq        %r8, %xmm0
	0xc5, 0xfa, 0x7f, 0x02, //0x000000b3 vmovdqu      %xmm0, (%rdx)
	0x4c, 0x8b, 0x27, //0x000000b7 movq         (%rdi), %r12
	0x4c, 0x89, 0xe0, //0x000000ba movq         %r12, %rax
	0x48, 0xf7, 0xd0, //0x000000bd notq         %rax
	0x48, 0x89, 0x45, 0x98, //0x000000c0 movq         %rax, $-104(%rbp)
	0xb8, 0x01, 0x00, 0x00, 0x00, //0x000000c4 movl         $1, %eax
	0x4c, 0x29, 0xe0, //0x000000c9 subq         %r12, %rax
	0x48, 0x89, 0x45, 0x90, //0x000000cc movq         %rax, $-112(%rbp)
	0x49, 0x8d, 0x44, 0x24, 0x40, //0x000000d0 leaq         $64(%r12), %rax
	0x48, 0x89, 0x45, 0x88, //0x000000d5 movq         %rax, $-120(%rbp)
	0x4c, 0x8b, 0x36, //0x000000d9 movq         (%rsi), %r14
	0x49, 0x8d, 0x44, 0x24, 0x05, //0x000000dc leaq         $5(%r12), %rax
	0x48, 0x89, 0x85, 0x68, 0xff, 0xff, 0xff, //0x000000e1 movq         %rax, $-152(%rbp)
	0x48, 0xc7, 0x85, 0x70, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x000000e8 movq         $-1, $-144(%rbp)
	0xc5, 0xfa, 0x6f, 0x05, 0x05, 0xff, 0xff, 0xff, //0x000000f3 vmovdqu      $-251(%rip), %xmm0  /* LCPI0_0+0(%rip) */
	0xc5, 0xfa, 0x6f, 0x0d, 0x0d, 0xff, 0xff, 0xff, //0x000000fb vmovdqu      $-243(%rip), %xmm1  /* LCPI0_1+0(%rip) */
	0xc5, 0x7a, 0x6f, 0x3d, 0x15, 0xff, 0xff, 0xff, //0x00000103 vmovdqu      $-235(%rip), %xmm15  /* LCPI0_2+0(%rip) */
	0xc5, 0xe1, 0x76, 0xdb, //0x0000010b vpcmpeqd     %xmm3, %xmm3, %xmm3
	0xc5, 0x7a, 0x6f, 0x05, 0x19, 0xff, 0xff, 0xff, //0x0000010f vmovdqu      $-231(%rip), %xmm8  /* LCPI0_3+0(%rip) */
	0xc5, 0x7a, 0x6f, 0x0d, 0x21, 0xff, 0xff, 0xff, //0x00000117 vmovdqu      $-223(%rip), %xmm9  /* LCPI0_4+0(%rip) */
	0xc5, 0x7a, 0x6f, 0x15, 0x29, 0xff, 0xff, 0xff, //0x0000011f vmovdqu      $-215(%rip), %xmm10  /* LCPI0_5+0(%rip) */
	0xc5, 0x7a, 0x6f, 0x1d, 0x31, 0xff, 0xff, 0xff, //0x00000127 vmovdqu      $-207(%rip), %xmm11  /* LCPI0_6+0(%rip) */
	0xc5, 0x7a, 0x6f, 0x25, 0xe9, 0xfe, 0xff, 0xff, //0x0000012f vmovdqu      $-279(%rip), %xmm12  /* LCPI0_2+0(%rip) */
	0xc5, 0x7a, 0x6f, 0x2d, 0x31, 0xff, 0xff, 0xff, //0x00000137 vmovdqu      $-207(%rip), %xmm13  /* LCPI0_7+0(%rip) */
	0xc5, 0x7a, 0x6f, 0x35, 0x39, 0xff, 0xff, 0xff, //0x0000013f vmovdqu      $-199(%rip), %xmm14  /* LCPI0_8+0(%rip) */
	0x48, 0x89, 0x75, 0xc8, //0x00000147 movq         %rsi, $-56(%rbp)
	0x4c, 0x89, 0x65, 0xd0, //0x0000014b movq         %r12, $-48(%rbp)
	0x48, 0x89, 0x55, 0xb8, //0x0000014f movq         %rdx, $-72(%rbp)
	0x48, 0x89, 0x7d, 0xc0, //0x00000153 movq         %rdi, $-64(%rbp)
	0xe9, 0x20, 0x00, 0x00, 0x00, //0x00000157 jmp          LBB0_4
	0x90, 0x90, 0x90, 0x90, //0x0000015c .p2align 4, 0x90
	//0x00000160 LBB0_1
	0x4d, 0x89, 0xd3, //0x00000160 movq         %r10, %r11
	//0x00000163 LBB0_2
	0x4c, 0x8b, 0x0a, //0x00000163 movq         (%rdx), %r9
	0x4d, 0x89, 0xde, //0x00000166 movq         %r11, %r14
	//0x00000169 LBB0_3
	0x4d, 0x89, 0xc8, //0x00000169 movq         %r9, %r8
	0x48, 0x8b, 0x85, 0x70, 0xff, 0xff, 0xff, //0x0000016c movq         $-144(%rbp), %rax
	0x4d, 0x85, 0xc9, //0x00000173 testq        %r9, %r9
	0x0f, 0x84, 0x68, 0x22, 0x00, 0x00, //0x00000176 je           LBB0_424
	//0x0000017c LBB0_4
	0x4c, 0x8b, 0x4f, 0x08, //0x0000017c movq         $8(%rdi), %r9
	0x4c, 0x89, 0xf0, //0x00000180 movq         %r14, %rax
	0x4c, 0x89, 0xf3, //0x00000183 movq         %r14, %rbx
	0x4c, 0x29, 0xcb, //0x00000186 subq         %r9, %rbx
	0x0f, 0x83, 0x31, 0x00, 0x00, 0x00, //0x00000189 jae          LBB0_9
	0x41, 0x8a, 0x0c, 0x04, //0x0000018f movb         (%r12,%rax), %cl
	0x80, 0xf9, 0x0d, //0x00000193 cmpb         $13, %cl
	0x0f, 0x84, 0x24, 0x00, 0x00, 0x00, //0x00000196 je           LBB0_9
	0x80, 0xf9, 0x20, //0x0000019c cmpb         $32, %cl
	0x0f, 0x84, 0x1b, 0x00, 0x00, 0x00, //0x0000019f je           LBB0_9
	0x80, 0xc1, 0xf7, //0x000001a5 addb         $-9, %cl
	0x80, 0xf9, 0x01, //0x000001a8 cmpb         $1, %cl
	0x0f, 0x86, 0x0f, 0x00, 0x00, 0x00, //0x000001ab jbe          LBB0_9
	0x49, 0x89, 0xc7, //0x000001b1 movq         %rax, %r15
	0xe9, 0x34, 0x01, 0x00, 0x00, //0x000001b4 jmp          LBB0_30
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000001b9 .p2align 4, 0x90
	//0x000001c0 LBB0_9
	0x4c, 0x8d, 0x78, 0x01, //0x000001c0 leaq         $1(%rax), %r15
	0x4d, 0x39, 0xcf, //0x000001c4 cmpq         %r9, %r15
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x000001c7 jae          LBB0_13
	0x43, 0x8a, 0x0c, 0x3c, //0x000001cd movb         (%r12,%r15), %cl
	0x80, 0xf9, 0x0d, //0x000001d1 cmpb         $13, %cl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x000001d4 je           LBB0_13
	0x80, 0xf9, 0x20, //0x000001da cmpb         $32, %cl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x000001dd je           LBB0_13
	0x80, 0xc1, 0xf7, //0x000001e3 addb         $-9, %cl
	0x80, 0xf9, 0x01, //0x000001e6 cmpb         $1, %cl
	0x0f, 0x87, 0xfe, 0x00, 0x00, 0x00, //0x000001e9 ja           LBB0_30
	0x90, //0x000001ef .p2align 4, 0x90
	//0x000001f0 LBB0_13
	0x4c, 0x8d, 0x78, 0x02, //0x000001f0 leaq         $2(%rax), %r15
	0x4d, 0x39, 0xcf, //0x000001f4 cmpq         %r9, %r15
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x000001f7 jae          LBB0_17
	0x43, 0x8a, 0x0c, 0x3c, //0x000001fd movb         (%r12,%r15), %cl
	0x80, 0xf9, 0x0d, //0x00000201 cmpb         $13, %cl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00000204 je           LBB0_17
	0x80, 0xf9, 0x20, //0x0000020a cmpb         $32, %cl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x0000020d je           LBB0_17
	0x80, 0xc1, 0xf7, //0x00000213 addb         $-9, %cl
	0x80, 0xf9, 0x01, //0x00000216 cmpb         $1, %cl
	0x0f, 0x87, 0xce, 0x00, 0x00, 0x00, //0x00000219 ja           LBB0_30
	0x90, //0x0000021f .p2align 4, 0x90
	//0x00000220 LBB0_17
	0x4c, 0x8d, 0x78, 0x03, //0x00000220 leaq         $3(%rax), %r15
	0x4d, 0x39, 0xcf, //0x00000224 cmpq         %r9, %r15
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00000227 jae          LBB0_21
	0x43, 0x8a, 0x0c, 0x3c, //0x0000022d movb         (%r12,%r15), %cl
	0x80, 0xf9, 0x0d, //0x00000231 cmpb         $13, %cl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00000234 je           LBB0_21
	0x80, 0xf9, 0x20, //0x0000023a cmpb         $32, %cl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x0000023d je           LBB0_21
	0x80, 0xc1, 0xf7, //0x00000243 addb         $-9, %cl
	0x80, 0xf9, 0x01, //0x00000246 cmpb         $1, %cl
	0x0f, 0x87, 0x9e, 0x00, 0x00, 0x00, //0x00000249 ja           LBB0_30
	0x90, //0x0000024f .p2align 4, 0x90
	//0x00000250 LBB0_21
	0x48, 0x8d, 0x48, 0x04, //0x00000250 leaq         $4(%rax), %rcx
	0x49, 0x39, 0xc9, //0x00000254 cmpq         %rcx, %r9
	0x0f, 0x86, 0x33, 0x21, 0x00, 0x00, //0x00000257 jbe          LBB0_413
	0x49, 0x39, 0xc9, //0x0000025d cmpq         %rcx, %r9
	0x0f, 0x84, 0x5a, 0x00, 0x00, 0x00, //0x00000260 je           LBB0_27
	0x4b, 0x8d, 0x0c, 0x0c, //0x00000266 leaq         (%r12,%r9), %rcx
	0x48, 0x83, 0xc3, 0x04, //0x0000026a addq         $4, %rbx
	0x48, 0x03, 0x85, 0x68, 0xff, 0xff, 0xff, //0x0000026e addq         $-152(%rbp), %rax
	0x49, 0x89, 0xc7, //0x00000275 movq         %rax, %r15
	0x49, 0xba, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00000278 movabsq      $4294977024, %r10
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000282 .p2align 4, 0x90
	//0x00000290 LBB0_24
	0x41, 0x0f, 0xbe, 0x47, 0xff, //0x00000290 movsbl       $-1(%r15), %eax
	0x83, 0xf8, 0x20, //0x00000295 cmpl         $32, %eax
	0x0f, 0x87, 0x42, 0x00, 0x00, 0x00, //0x00000298 ja           LBB0_29
	0x49, 0x0f, 0xa3, 0xc2, //0x0000029e btq          %rax, %r10
	0x0f, 0x83, 0x38, 0x00, 0x00, 0x00, //0x000002a2 jae          LBB0_29
	0x49, 0xff, 0xc7, //0x000002a8 incq         %r15
	0x48, 0xff, 0xc3, //0x000002ab incq         %rbx
	0x0f, 0x85, 0xdc, 0xff, 0xff, 0xff, //0x000002ae jne          LBB0_24
	0xe9, 0x0a, 0x00, 0x00, 0x00, //0x000002b4 jmp          LBB0_28
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000002b9 .p2align 4, 0x90
	//0x000002c0 LBB0_27
	0x4c, 0x01, 0xe1, //0x000002c0 addq         %r12, %rcx
	//0x000002c3 LBB0_28
	0x4c, 0x29, 0xe1, //0x000002c3 subq         %r12, %rcx
	0x49, 0x89, 0xcf, //0x000002c6 movq         %rcx, %r15
	0x4d, 0x39, 0xcf, //0x000002c9 cmpq         %r9, %r15
	0x0f, 0x82, 0x1b, 0x00, 0x00, 0x00, //0x000002cc jb           LBB0_30
	0xe9, 0xbc, 0x20, 0x00, 0x00, //0x000002d2 jmp          LBB0_414
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000002d7 .p2align 4, 0x90
	//0x000002e0 LBB0_29
	0x4c, 0x03, 0x7d, 0x98, //0x000002e0 addq         $-104(%rbp), %r15
	0x4d, 0x39, 0xcf, //0x000002e4 cmpq         %r9, %r15
	0x0f, 0x83, 0xa6, 0x20, 0x00, 0x00, //0x000002e7 jae          LBB0_414
	//0x000002ed LBB0_30
	0x4d, 0x8d, 0x57, 0x01, //0x000002ed leaq         $1(%r15), %r10
	0x4c, 0x89, 0x16, //0x000002f1 movq         %r10, (%rsi)
	0x47, 0x0f, 0xbe, 0x1c, 0x3c, //0x000002f4 movsbl       (%r12,%r15), %r11d
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x000002f9 movq         $-1, %rax
	0x45, 0x85, 0xdb, //0x00000300 testl        %r11d, %r11d
	0x0f, 0x84, 0xdb, 0x20, 0x00, 0x00, //0x00000303 je           LBB0_424
	0x4d, 0x8d, 0x48, 0xff, //0x00000309 leaq         $-1(%r8), %r9
	0x42, 0x8b, 0x0c, 0xc2, //0x0000030d movl         (%rdx,%r8,8), %ecx
	0x48, 0x8b, 0x9d, 0x70, 0xff, 0xff, 0xff, //0x00000311 movq         $-144(%rbp), %rbx
	0x48, 0x83, 0xfb, 0xff, //0x00000318 cmpq         $-1, %rbx
	0x49, 0x0f, 0x44, 0xdf, //0x0000031c cmoveq       %r15, %rbx
	0x48, 0x89, 0x9d, 0x70, 0xff, 0xff, 0xff, //0x00000320 movq         %rbx, $-144(%rbp)
	0xff, 0xc9, //0x00000327 decl         %ecx
	0x83, 0xf9, 0x05, //0x00000329 cmpl         $5, %ecx
	0x0f, 0x87, 0x3d, 0x02, 0x00, 0x00, //0x0000032c ja           LBB0_62
	0x48, 0x8d, 0x1d, 0x57, 0x22, 0x00, 0x00, //0x00000332 leaq         $8791(%rip), %rbx  /* LJTI0_0+0(%rip) */
	0x48, 0x63, 0x0c, 0x8b, //0x00000339 movslq       (%rbx,%rcx,4), %rcx
	0x48, 0x01, 0xd9, //0x0000033d addq         %rbx, %rcx
	0xff, 0xe1, //0x00000340 jmpq         *%rcx
	//0x00000342 LBB0_33
	0x41, 0x83, 0xfb, 0x2c, //0x00000342 cmpl         $44, %r11d
	0x0f, 0x84, 0xb2, 0x04, 0x00, 0x00, //0x00000346 je           LBB0_102
	0x41, 0x83, 0xfb, 0x5d, //0x0000034c cmpl         $93, %r11d
	0x0f, 0x84, 0x42, 0x02, 0x00, 0x00, //0x00000350 je           LBB0_65
	0xe9, 0x82, 0x20, 0x00, 0x00, //0x00000356 jmp          LBB0_423
	//0x0000035b LBB0_35
	0x41, 0x83, 0xfb, 0x2c, //0x0000035b cmpl         $44, %r11d
	0x0f, 0x85, 0x29, 0x02, 0x00, 0x00, //0x0000035f jne          LBB0_64
	0x49, 0x81, 0xf8, 0xff, 0x0f, 0x00, 0x00, //0x00000365 cmpq         $4095, %r8
	0x0f, 0x8f, 0x2d, 0x20, 0x00, 0x00, //0x0000036c jg           LBB0_432
	0x49, 0x8d, 0x40, 0x01, //0x00000372 leaq         $1(%r8), %rax
	0x48, 0x89, 0x02, //0x00000376 movq         %rax, (%rdx)
	0x4a, 0xc7, 0x44, 0xc2, 0x08, 0x03, 0x00, 0x00, 0x00, //0x00000379 movq         $3, $8(%rdx,%r8,8)
	0xe9, 0xd9, 0xfd, 0xff, 0xff, //0x00000382 jmp          LBB0_1
	//0x00000387 LBB0_38
	0x41, 0x80, 0xfb, 0x22, //0x00000387 cmpb         $34, %r11b
	0x0f, 0x85, 0x4c, 0x20, 0x00, 0x00, //0x0000038b jne          LBB0_423
	0x4a, 0xc7, 0x04, 0xc2, 0x04, 0x00, 0x00, 0x00, //0x00000391 movq         $4, (%rdx,%r8,8)
	0x48, 0x8b, 0x4f, 0x08, //0x00000399 movq         $8(%rdi), %rcx
	0xf6, 0x85, 0x78, 0xff, 0xff, 0xff, 0x20, //0x0000039d testb        $32, $-136(%rbp)
	0x48, 0x89, 0x4d, 0xa0, //0x000003a4 movq         %rcx, $-96(%rbp)
	0x0f, 0x85, 0x72, 0x04, 0x00, 0x00, //0x000003a8 jne          LBB0_104
	0x49, 0x89, 0xcd, //0x000003ae movq         %rcx, %r13
	0x4d, 0x29, 0xd5, //0x000003b1 subq         %r10, %r13
	0x0f, 0x84, 0x9d, 0x21, 0x00, 0x00, //0x000003b4 je           LBB0_449
	0x4f, 0x8d, 0x1c, 0x14, //0x000003ba leaq         (%r12,%r10), %r11
	0x49, 0x83, 0xfd, 0x40, //0x000003be cmpq         $64, %r13
	0x0f, 0x82, 0x40, 0x1a, 0x00, 0x00, //0x000003c2 jb           LBB0_353
	0x44, 0x89, 0xe8, //0x000003c8 movl         %r13d, %eax
	0x83, 0xe0, 0x3f, //0x000003cb andl         $63, %eax
	0x48, 0x89, 0x45, 0xa8, //0x000003ce movq         %rax, $-88(%rbp)
	0x4c, 0x29, 0xf9, //0x000003d2 subq         %r15, %rcx
	0x48, 0x83, 0xc1, 0xbf, //0x000003d5 addq         $-65, %rcx
	0x48, 0x83, 0xe1, 0xc0, //0x000003d9 andq         $-64, %rcx
	0x4c, 0x01, 0xd1, //0x000003dd addq         %r10, %rcx
	0x48, 0x03, 0x4d, 0x88, //0x000003e0 addq         $-120(%rbp), %rcx
	0x48, 0x89, 0x4d, 0x80, //0x000003e4 movq         %rcx, $-128(%rbp)
	0x49, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x000003e8 movq         $-1, %r9
	0x45, 0x31, 0xe4, //0x000003ef xorl         %r12d, %r12d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000003f2 .p2align 4, 0x90
	//0x00000400 LBB0_43
	0xc4, 0xc1, 0x7a, 0x6f, 0x13, //0x00000400 vmovdqu      (%r11), %xmm2
	0xc4, 0xc1, 0x7a, 0x6f, 0x63, 0x10, //0x00000405 vmovdqu      $16(%r11), %xmm4
	0xc4, 0xc1, 0x7a, 0x6f, 0x6b, 0x20, //0x0000040b vmovdqu      $32(%r11), %xmm5
	0xc4, 0xc1, 0x7a, 0x6f, 0x73, 0x30, //0x00000411 vmovdqu      $48(%r11), %xmm6
	0xc5, 0xe9, 0x74, 0xf8, //0x00000417 vpcmpeqb     %xmm0, %xmm2, %xmm7
	0xc5, 0xf9, 0xd7, 0xff, //0x0000041b vpmovmskb    %xmm7, %edi
	0xc5, 0xd9, 0x74, 0xf8, //0x0000041f vpcmpeqb     %xmm0, %xmm4, %xmm7
	0xc5, 0xf9, 0xd7, 0xcf, //0x00000423 vpmovmskb    %xmm7, %ecx
	0xc5, 0xd1, 0x74, 0xf8, //0x00000427 vpcmpeqb     %xmm0, %xmm5, %xmm7
	0xc5, 0xf9, 0xd7, 0xf7, //0x0000042b vpmovmskb    %xmm7, %esi
	0xc5, 0xc9, 0x74, 0xf8, //0x0000042f vpcmpeqb     %xmm0, %xmm6, %xmm7
	0xc5, 0xf9, 0xd7, 0xdf, //0x00000433 vpmovmskb    %xmm7, %ebx
	0xc5, 0xe9, 0x74, 0xd1, //0x00000437 vpcmpeqb     %xmm1, %xmm2, %xmm2
	0xc5, 0xf9, 0xd7, 0xd2, //0x0000043b vpmovmskb    %xmm2, %edx
	0xc5, 0xd9, 0x74, 0xd1, //0x0000043f vpcmpeqb     %xmm1, %xmm4, %xmm2
	0xc5, 0xf9, 0xd7, 0xc2, //0x00000443 vpmovmskb    %xmm2, %eax
	0xc5, 0xd1, 0x74, 0xd1, //0x00000447 vpcmpeqb     %xmm1, %xmm5, %xmm2
	0xc5, 0x79, 0xd7, 0xf2, //0x0000044b vpmovmskb    %xmm2, %r14d
	0xc5, 0xc9, 0x74, 0xd1, //0x0000044f vpcmpeqb     %xmm1, %xmm6, %xmm2
	0xc5, 0x79, 0xd7, 0xc2, //0x00000453 vpmovmskb    %xmm2, %r8d
	0x48, 0xc1, 0xe3, 0x30, //0x00000457 shlq         $48, %rbx
	0x48, 0xc1, 0xe6, 0x20, //0x0000045b shlq         $32, %rsi
	0x48, 0xc1, 0xe1, 0x10, //0x0000045f shlq         $16, %rcx
	0x48, 0x09, 0xcf, //0x00000463 orq          %rcx, %rdi
	0x48, 0x09, 0xf7, //0x00000466 orq          %rsi, %rdi
	0x49, 0xc1, 0xe0, 0x30, //0x00000469 shlq         $48, %r8
	0x49, 0xc1, 0xe6, 0x20, //0x0000046d shlq         $32, %r14
	0x48, 0xc1, 0xe0, 0x10, //0x00000471 shlq         $16, %rax
	0x48, 0x09, 0xc2, //0x00000475 orq          %rax, %rdx
	0x4c, 0x09, 0xf2, //0x00000478 orq          %r14, %rdx
	0x4c, 0x09, 0xc2, //0x0000047b orq          %r8, %rdx
	0x49, 0x83, 0xf9, 0xff, //0x0000047e cmpq         $-1, %r9
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x00000482 jne          LBB0_45
	0x48, 0x85, 0xd2, //0x00000488 testq        %rdx, %rdx
	0x0f, 0x85, 0x2f, 0x00, 0x00, 0x00, //0x0000048b jne          LBB0_54
	//0x00000491 LBB0_45
	0x48, 0x09, 0xdf, //0x00000491 orq          %rbx, %rdi
	0x48, 0x89, 0xd0, //0x00000494 movq         %rdx, %rax
	0x4c, 0x09, 0xe0, //0x00000497 orq          %r12, %rax
	0x0f, 0x85, 0x3d, 0x00, 0x00, 0x00, //0x0000049a jne          LBB0_55
	//0x000004a0 LBB0_46
	0x48, 0x85, 0xff, //0x000004a0 testq        %rdi, %rdi
	0x0f, 0x85, 0x86, 0x00, 0x00, 0x00, //0x000004a3 jne          LBB0_56
	//0x000004a9 LBB0_47
	0x49, 0x83, 0xc5, 0xc0, //0x000004a9 addq         $-64, %r13
	0x49, 0x83, 0xc3, 0x40, //0x000004ad addq         $64, %r11
	0x49, 0x83, 0xfd, 0x3f, //0x000004b1 cmpq         $63, %r13
	0x0f, 0x87, 0x45, 0xff, 0xff, 0xff, //0x000004b5 ja           LBB0_43
	0xe9, 0xb4, 0x12, 0x00, 0x00, //0x000004bb jmp          LBB0_48
	//0x000004c0 LBB0_54
	0x4c, 0x89, 0xd8, //0x000004c0 movq         %r11, %rax
	0x48, 0x2b, 0x45, 0xd0, //0x000004c3 subq         $-48(%rbp), %rax
	0x4c, 0x0f, 0xbc, 0xca, //0x000004c7 bsfq         %rdx, %r9
	0x49, 0x01, 0xc1, //0x000004cb addq         %rax, %r9
	0x48, 0x09, 0xdf, //0x000004ce orq          %rbx, %rdi
	0x48, 0x89, 0xd0, //0x000004d1 movq         %rdx, %rax
	0x4c, 0x09, 0xe0, //0x000004d4 orq          %r12, %rax
	0x0f, 0x84, 0xc3, 0xff, 0xff, 0xff, //0x000004d7 je           LBB0_46
	//0x000004dd LBB0_55
	0x4c, 0x89, 0xe0, //0x000004dd movq         %r12, %rax
	0x48, 0xf7, 0xd0, //0x000004e0 notq         %rax
	0x48, 0x21, 0xd0, //0x000004e3 andq         %rdx, %rax
	0x48, 0x8d, 0x0c, 0x00, //0x000004e6 leaq         (%rax,%rax), %rcx
	0x4c, 0x09, 0xe1, //0x000004ea orq          %r12, %rcx
	0x48, 0x89, 0xce, //0x000004ed movq         %rcx, %rsi
	0x48, 0xf7, 0xd6, //0x000004f0 notq         %rsi
	0x48, 0x21, 0xd6, //0x000004f3 andq         %rdx, %rsi
	0x48, 0xba, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x000004f6 movabsq      $-6148914691236517206, %rdx
	0x48, 0x21, 0xd6, //0x00000500 andq         %rdx, %rsi
	0x45, 0x31, 0xe4, //0x00000503 xorl         %r12d, %r12d
	0x48, 0x01, 0xc6, //0x00000506 addq         %rax, %rsi
	0x41, 0x0f, 0x92, 0xc4, //0x00000509 setb         %r12b
	0x48, 0x01, 0xf6, //0x0000050d addq         %rsi, %rsi
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00000510 movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc6, //0x0000051a xorq         %rax, %rsi
	0x48, 0x21, 0xce, //0x0000051d andq         %rcx, %rsi
	0x48, 0xf7, 0xd6, //0x00000520 notq         %rsi
	0x48, 0x21, 0xf7, //0x00000523 andq         %rsi, %rdi
	0x48, 0x85, 0xff, //0x00000526 testq        %rdi, %rdi
	0x0f, 0x84, 0x7a, 0xff, 0xff, 0xff, //0x00000529 je           LBB0_47
	//0x0000052f LBB0_56
	0x48, 0x0f, 0xbc, 0xc7, //0x0000052f bsfq         %rdi, %rax
	//0x00000533 LBB0_57
	0x4c, 0x03, 0x5d, 0x90, //0x00000533 addq         $-112(%rbp), %r11
	0x49, 0x01, 0xc3, //0x00000537 addq         %rax, %r11
	0x4c, 0x8b, 0x65, 0xd0, //0x0000053a movq         $-48(%rbp), %r12
	0xe9, 0x80, 0x09, 0x00, 0x00, //0x0000053e jmp          LBB0_186
	//0x00000543 LBB0_58
	0x41, 0x80, 0xfb, 0x3a, //0x00000543 cmpb         $58, %r11b
	0x0f, 0x85, 0x90, 0x1e, 0x00, 0x00, //0x00000547 jne          LBB0_423
	0x4a, 0xc7, 0x04, 0xc2, 0x00, 0x00, 0x00, 0x00, //0x0000054d movq         $0, (%rdx,%r8,8)
	0xe9, 0x06, 0xfc, 0xff, 0xff, //0x00000555 jmp          LBB0_1
	//0x0000055a LBB0_60
	0x4d, 0x89, 0xd6, //0x0000055a movq         %r10, %r14
	0x41, 0x80, 0xfb, 0x5d, //0x0000055d cmpb         $93, %r11b
	0x0f, 0x85, 0x3c, 0x00, 0x00, 0x00, //0x00000561 jne          LBB0_66
	0x4c, 0x89, 0x0a, //0x00000567 movq         %r9, (%rdx)
	0xe9, 0xfa, 0xfb, 0xff, 0xff, //0x0000056a jmp          LBB0_3
	//0x0000056f LBB0_62
	0x4d, 0x89, 0xd6, //0x0000056f movq         %r10, %r14
	0x4c, 0x89, 0x0a, //0x00000572 movq         %r9, (%rdx)
	0x41, 0x83, 0xfb, 0x7b, //0x00000575 cmpl         $123, %r11d
	0x0f, 0x86, 0x36, 0x00, 0x00, 0x00, //0x00000579 jbe          LBB0_67
	0xe9, 0x59, 0x1e, 0x00, 0x00, //0x0000057f jmp          LBB0_423
	//0x00000584 LBB0_63
	0x41, 0x83, 0xfb, 0x22, //0x00000584 cmpl         $34, %r11d
	0x0f, 0x84, 0x6d, 0x04, 0x00, 0x00, //0x00000588 je           LBB0_125
	//0x0000058e LBB0_64
	0x41, 0x83, 0xfb, 0x7d, //0x0000058e cmpl         $125, %r11d
	0x0f, 0x85, 0x45, 0x1e, 0x00, 0x00, //0x00000592 jne          LBB0_423
	//0x00000598 LBB0_65
	0x4d, 0x89, 0xd6, //0x00000598 movq         %r10, %r14
	0x4c, 0x89, 0x0a, //0x0000059b movq         %r9, (%rdx)
	0xe9, 0xc6, 0xfb, 0xff, 0xff, //0x0000059e jmp          LBB0_3
	//0x000005a3 LBB0_66
	0x4a, 0xc7, 0x04, 0xc2, 0x01, 0x00, 0x00, 0x00, //0x000005a3 movq         $1, (%rdx,%r8,8)
	0x41, 0x83, 0xfb, 0x7b, //0x000005ab cmpl         $123, %r11d
	0x0f, 0x87, 0x28, 0x1e, 0x00, 0x00, //0x000005af ja           LBB0_423
	//0x000005b5 LBB0_67
	0x4f, 0x8d, 0x14, 0x3c, //0x000005b5 leaq         (%r12,%r15), %r10
	0x44, 0x89, 0xd9, //0x000005b9 movl         %r11d, %ecx
	0x48, 0x8d, 0x1d, 0xe5, 0x1f, 0x00, 0x00, //0x000005bc leaq         $8165(%rip), %rbx  /* LJTI0_1+0(%rip) */
	0x48, 0x63, 0x0c, 0x8b, //0x000005c3 movslq       (%rbx,%rcx,4), %rcx
	0x48, 0x01, 0xd9, //0x000005c7 addq         %rbx, %rcx
	0xff, 0xe1, //0x000005ca jmpq         *%rcx
	//0x000005cc LBB0_68
	0x4c, 0x8b, 0x4f, 0x08, //0x000005cc movq         $8(%rdi), %r9
	0x4d, 0x29, 0xf9, //0x000005d0 subq         %r15, %r9
	0x0f, 0x84, 0xec, 0x1d, 0x00, 0x00, //0x000005d3 je           LBB0_418
	0x41, 0x80, 0x3a, 0x30, //0x000005d9 cmpb         $48, (%r10)
	0x0f, 0x85, 0x2f, 0x00, 0x00, 0x00, //0x000005dd jne          LBB0_73
	0x49, 0x83, 0xf9, 0x01, //0x000005e3 cmpq         $1, %r9
	0x0f, 0x84, 0xa1, 0x06, 0x00, 0x00, //0x000005e7 je           LBB0_269
	0x43, 0x8a, 0x04, 0x34, //0x000005ed movb         (%r12,%r14), %al
	0x04, 0xd2, //0x000005f1 addb         $-46, %al
	0x3c, 0x37, //0x000005f3 cmpb         $55, %al
	0x0f, 0x87, 0x93, 0x06, 0x00, 0x00, //0x000005f5 ja           LBB0_269
	0x0f, 0xb6, 0xc0, //0x000005fb movzbl       %al, %eax
	0x48, 0xb9, 0x01, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, //0x000005fe movabsq      $36028797027352577, %rcx
	0x48, 0x0f, 0xa3, 0xc1, //0x00000608 btq          %rax, %rcx
	0x0f, 0x83, 0x7c, 0x06, 0x00, 0x00, //0x0000060c jae          LBB0_269
	//0x00000612 LBB0_73
	0x49, 0x83, 0xf9, 0x10, //0x00000612 cmpq         $16, %r9
	0x0f, 0x82, 0xcb, 0x17, 0x00, 0x00, //0x00000616 jb           LBB0_342
	0x49, 0x8d, 0x79, 0xf0, //0x0000061c leaq         $-16(%r9), %rdi
	0x48, 0x89, 0xf8, //0x00000620 movq         %rdi, %rax
	0x48, 0x83, 0xe0, 0xf0, //0x00000623 andq         $-16, %rax
	0x4a, 0x8d, 0x44, 0x10, 0x10, //0x00000627 leaq         $16(%rax,%r10), %rax
	0x48, 0x89, 0x45, 0xb0, //0x0000062c movq         %rax, $-80(%rbp)
	0x83, 0xe7, 0x0f, //0x00000630 andl         $15, %edi
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x00000633 movq         $-1, %r12
	0x49, 0xc7, 0xc5, 0xff, 0xff, 0xff, 0xff, //0x0000063a movq         $-1, %r13
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00000641 movq         $-1, %r11
	0x4d, 0x89, 0xd6, //0x00000648 movq         %r10, %r14
	0x90, 0x90, 0x90, 0x90, 0x90, //0x0000064b .p2align 4, 0x90
	//0x00000650 LBB0_75
	0xc4, 0xc1, 0x7a, 0x6f, 0x16, //0x00000650 vmovdqu      (%r14), %xmm2
	0xc4, 0xc1, 0x69, 0x64, 0xe0, //0x00000655 vpcmpgtb     %xmm8, %xmm2, %xmm4
	0xc5, 0xb1, 0x64, 0xea, //0x0000065a vpcmpgtb     %xmm2, %xmm9, %xmm5
	0xc5, 0xd9, 0xdb, 0xe5, //0x0000065e vpand        %xmm5, %xmm4, %xmm4
	0xc5, 0xa9, 0x74, 0xea, //0x00000662 vpcmpeqb     %xmm2, %xmm10, %xmm5
	0xc5, 0xa1, 0x74, 0xf2, //0x00000666 vpcmpeqb     %xmm2, %xmm11, %xmm6
	0xc5, 0xc9, 0xeb, 0xed, //0x0000066a vpor         %xmm5, %xmm6, %xmm5
	0xc5, 0x99, 0xeb, 0xf2, //0x0000066e vpor         %xmm2, %xmm12, %xmm6
	0xc5, 0x91, 0x74, 0xd2, //0x00000672 vpcmpeqb     %xmm2, %xmm13, %xmm2
	0xc5, 0x89, 0x74, 0xf6, //0x00000676 vpcmpeqb     %xmm6, %xmm14, %xmm6
	0xc5, 0xc9, 0xeb, 0xfa, //0x0000067a vpor         %xmm2, %xmm6, %xmm7
	0xc5, 0xd1, 0xeb, 0xe4, //0x0000067e vpor         %xmm4, %xmm5, %xmm4
	0xc5, 0xc1, 0xeb, 0xe4, //0x00000682 vpor         %xmm4, %xmm7, %xmm4
	0xc5, 0x79, 0xd7, 0xc2, //0x00000686 vpmovmskb    %xmm2, %r8d
	0xc5, 0xf9, 0xd7, 0xc6, //0x0000068a vpmovmskb    %xmm6, %eax
	0xc5, 0xf9, 0xd7, 0xd5, //0x0000068e vpmovmskb    %xmm5, %edx
	0xc5, 0xf9, 0xd7, 0xcc, //0x00000692 vpmovmskb    %xmm4, %ecx
	0xbe, 0xaa, 0xaa, 0xaa, 0xaa, //0x00000696 movl         $2863311530, %esi
	0x48, 0x81, 0xc6, 0x55, 0x55, 0x55, 0x55, //0x0000069b addq         $1431655765, %rsi
	0x48, 0x31, 0xce, //0x000006a2 xorq         %rcx, %rsi
	0x48, 0x0f, 0xbc, 0xce, //0x000006a5 bsfq         %rsi, %rcx
	0x83, 0xf9, 0x10, //0x000006a9 cmpl         $16, %ecx
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x000006ac je           LBB0_77
	0xbe, 0xff, 0xff, 0xff, 0xff, //0x000006b2 movl         $-1, %esi
	0xd3, 0xe6, //0x000006b7 shll         %cl, %esi
	0xf7, 0xd6, //0x000006b9 notl         %esi
	0x41, 0x21, 0xf0, //0x000006bb andl         %esi, %r8d
	0x21, 0xf0, //0x000006be andl         %esi, %eax
	0x21, 0xd6, //0x000006c0 andl         %edx, %esi
	0x89, 0xf2, //0x000006c2 movl         %esi, %edx
	//0x000006c4 LBB0_77
	0x41, 0x8d, 0x70, 0xff, //0x000006c4 leal         $-1(%r8), %esi
	0x44, 0x21, 0xc6, //0x000006c8 andl         %r8d, %esi
	0x0f, 0x85, 0x6b, 0x10, 0x00, 0x00, //0x000006cb jne          LBB0_309
	0x8d, 0x70, 0xff, //0x000006d1 leal         $-1(%rax), %esi
	0x21, 0xc6, //0x000006d4 andl         %eax, %esi
	0x0f, 0x85, 0x60, 0x10, 0x00, 0x00, //0x000006d6 jne          LBB0_309
	0x8d, 0x72, 0xff, //0x000006dc leal         $-1(%rdx), %esi
	0x21, 0xd6, //0x000006df andl         %edx, %esi
	0x0f, 0x85, 0x55, 0x10, 0x00, 0x00, //0x000006e1 jne          LBB0_309
	0x45, 0x85, 0xc0, //0x000006e7 testl        %r8d, %r8d
	0x0f, 0x84, 0x1a, 0x00, 0x00, 0x00, //0x000006ea je           LBB0_83
	0x4c, 0x89, 0xf3, //0x000006f0 movq         %r14, %rbx
	0x4c, 0x29, 0xd3, //0x000006f3 subq         %r10, %rbx
	0x41, 0x0f, 0xbc, 0xf0, //0x000006f6 bsfl         %r8d, %esi
	0x48, 0x01, 0xde, //0x000006fa addq         %rbx, %rsi
	0x49, 0x83, 0xfb, 0xff, //0x000006fd cmpq         $-1, %r11
	0x0f, 0x85, 0xf5, 0x13, 0x00, 0x00, //0x00000701 jne          LBB0_326
	0x49, 0x89, 0xf3, //0x00000707 movq         %rsi, %r11
	//0x0000070a LBB0_83
	0x85, 0xc0, //0x0000070a testl        %eax, %eax
	0x0f, 0x84, 0x19, 0x00, 0x00, 0x00, //0x0000070c je           LBB0_86
	0x4c, 0x89, 0xf6, //0x00000712 movq         %r14, %rsi
	0x4c, 0x29, 0xd6, //0x00000715 subq         %r10, %rsi
	0x0f, 0xbc, 0xc0, //0x00000718 bsfl         %eax, %eax
	0x48, 0x01, 0xf0, //0x0000071b addq         %rsi, %rax
	0x49, 0x83, 0xfd, 0xff, //0x0000071e cmpq         $-1, %r13
	0x0f, 0x85, 0xbd, 0x11, 0x00, 0x00, //0x00000722 jne          LBB0_314
	0x49, 0x89, 0xc5, //0x00000728 movq         %rax, %r13
	//0x0000072b LBB0_86
	0x85, 0xd2, //0x0000072b testl        %edx, %edx
	0x0f, 0x84, 0x19, 0x00, 0x00, 0x00, //0x0000072d je           LBB0_89
	0x4c, 0x89, 0xf6, //0x00000733 movq         %r14, %rsi
	0x4c, 0x29, 0xd6, //0x00000736 subq         %r10, %rsi
	0x0f, 0xbc, 0xc2, //0x00000739 bsfl         %edx, %eax
	0x48, 0x01, 0xf0, //0x0000073c addq         %rsi, %rax
	0x49, 0x83, 0xfc, 0xff, //0x0000073f cmpq         $-1, %r12
	0x0f, 0x85, 0x9c, 0x11, 0x00, 0x00, //0x00000743 jne          LBB0_314
	0x49, 0x89, 0xc4, //0x00000749 movq         %rax, %r12
	//0x0000074c LBB0_89
	0x83, 0xf9, 0x10, //0x0000074c cmpl         $16, %ecx
	0x0f, 0x85, 0x74, 0x04, 0x00, 0x00, //0x0000074f jne          LBB0_147
	0x49, 0x83, 0xc6, 0x10, //0x00000755 addq         $16, %r14
	0x49, 0x83, 0xc1, 0xf0, //0x00000759 addq         $-16, %r9
	0x49, 0x83, 0xf9, 0x0f, //0x0000075d cmpq         $15, %r9
	0x0f, 0x87, 0xe9, 0xfe, 0xff, 0xff, //0x00000761 ja           LBB0_75
	0x48, 0x85, 0xff, //0x00000767 testq        %rdi, %rdi
	0x0f, 0x84, 0x7d, 0x04, 0x00, 0x00, //0x0000076a je           LBB0_149
	//0x00000770 LBB0_92
	0x48, 0x8b, 0x45, 0xb0, //0x00000770 movq         $-80(%rbp), %rax
	0x48, 0x8d, 0x0c, 0x38, //0x00000774 leaq         (%rax,%rdi), %rcx
	0x48, 0x8d, 0x35, 0x05, 0x21, 0x00, 0x00, //0x00000778 leaq         $8453(%rip), %rsi  /* LJTI0_3+0(%rip) */
	0xe9, 0x28, 0x00, 0x00, 0x00, //0x0000077f jmp          LBB0_96
	//0x00000784 LBB0_93
	0x48, 0x89, 0xd8, //0x00000784 movq         %rbx, %rax
	0x4c, 0x29, 0xd0, //0x00000787 subq         %r10, %rax
	0x49, 0x83, 0xfc, 0xff, //0x0000078a cmpq         $-1, %r12
	0x0f, 0x85, 0x0f, 0x14, 0x00, 0x00, //0x0000078e jne          LBB0_419
	0x48, 0xff, 0xc8, //0x00000794 decq         %rax
	0x49, 0x89, 0xc4, //0x00000797 movq         %rax, %r12
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x0000079a .p2align 4, 0x90
	//0x000007a0 LBB0_95
	0x48, 0x89, 0xd8, //0x000007a0 movq         %rbx, %rax
	0x48, 0xff, 0xcf, //0x000007a3 decq         %rdi
	0x0f, 0x84, 0x48, 0x11, 0x00, 0x00, //0x000007a6 je           LBB0_315
	//0x000007ac LBB0_96
	0x0f, 0xbe, 0x10, //0x000007ac movsbl       (%rax), %edx
	0x83, 0xc2, 0xd5, //0x000007af addl         $-43, %edx
	0x83, 0xfa, 0x3a, //0x000007b2 cmpl         $58, %edx
	0x0f, 0x87, 0x2e, 0x04, 0x00, 0x00, //0x000007b5 ja           LBB0_148
	0x48, 0x8d, 0x58, 0x01, //0x000007bb leaq         $1(%rax), %rbx
	0x48, 0x63, 0x14, 0x96, //0x000007bf movslq       (%rsi,%rdx,4), %rdx
	0x48, 0x01, 0xf2, //0x000007c3 addq         %rsi, %rdx
	0xff, 0xe2, //0x000007c6 jmpq         *%rdx
	//0x000007c8 LBB0_98
	0x48, 0x89, 0xd8, //0x000007c8 movq         %rbx, %rax
	0x4c, 0x29, 0xd0, //0x000007cb subq         %r10, %rax
	0x49, 0x83, 0xfd, 0xff, //0x000007ce cmpq         $-1, %r13
	0x0f, 0x85, 0xcb, 0x13, 0x00, 0x00, //0x000007d2 jne          LBB0_419
	0x48, 0xff, 0xc8, //0x000007d8 decq         %rax
	0x49, 0x89, 0xc5, //0x000007db movq         %rax, %r13
	0xe9, 0xbd, 0xff, 0xff, 0xff, //0x000007de jmp          LBB0_95
	//0x000007e3 LBB0_100
	0x48, 0x89, 0xd8, //0x000007e3 movq         %rbx, %rax
	0x4c, 0x29, 0xd0, //0x000007e6 subq         %r10, %rax
	0x49, 0x83, 0xfb, 0xff, //0x000007e9 cmpq         $-1, %r11
	0x0f, 0x85, 0xb0, 0x13, 0x00, 0x00, //0x000007ed jne          LBB0_419
	0x48, 0xff, 0xc8, //0x000007f3 decq         %rax
	0x49, 0x89, 0xc3, //0x000007f6 movq         %rax, %r11
	0xe9, 0xa2, 0xff, 0xff, 0xff, //0x000007f9 jmp          LBB0_95
	//0x000007fe LBB0_102
	0x49, 0x81, 0xf8, 0xff, 0x0f, 0x00, 0x00, //0x000007fe cmpq         $4095, %r8
	0x0f, 0x8f, 0x94, 0x1b, 0x00, 0x00, //0x00000805 jg           LBB0_432
	0x49, 0x8d, 0x40, 0x01, //0x0000080b leaq         $1(%r8), %rax
	0x48, 0x89, 0x02, //0x0000080f movq         %rax, (%rdx)
	0x4a, 0xc7, 0x44, 0xc2, 0x08, 0x00, 0x00, 0x00, 0x00, //0x00000812 movq         $0, $8(%rdx,%r8,8)
	0xe9, 0x40, 0xf9, 0xff, 0xff, //0x0000081b jmp          LBB0_1
	//0x00000820 LBB0_104
	0x48, 0x89, 0xc8, //0x00000820 movq         %rcx, %rax
	0x4c, 0x29, 0xd0, //0x00000823 subq         %r10, %rax
	0x0f, 0x84, 0x2b, 0x1d, 0x00, 0x00, //0x00000826 je           LBB0_449
	0x4f, 0x8d, 0x1c, 0x14, //0x0000082c leaq         (%r12,%r10), %r11
	0x48, 0x83, 0xf8, 0x40, //0x00000830 cmpq         $64, %rax
	0x0f, 0x82, 0xe7, 0x15, 0x00, 0x00, //0x00000834 jb           LBB0_354
	0x89, 0xc2, //0x0000083a movl         %eax, %edx
	0x83, 0xe2, 0x3f, //0x0000083c andl         $63, %edx
	0x48, 0x89, 0x55, 0xa8, //0x0000083f movq         %rdx, $-88(%rbp)
	0x4c, 0x29, 0xf9, //0x00000843 subq         %r15, %rcx
	0x48, 0x83, 0xc1, 0xbf, //0x00000846 addq         $-65, %rcx
	0x48, 0x83, 0xe1, 0xc0, //0x0000084a andq         $-64, %rcx
	0x4c, 0x01, 0xd1, //0x0000084e addq         %r10, %rcx
	0x48, 0x03, 0x4d, 0x88, //0x00000851 addq         $-120(%rbp), %rcx
	0x48, 0x89, 0x4d, 0x80, //0x00000855 movq         %rcx, $-128(%rbp)
	0x49, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00000859 movq         $-1, %r9
	0x45, 0x31, 0xc0, //0x00000860 xorl         %r8d, %r8d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000863 .p2align 4, 0x90
	//0x00000870 LBB0_107
	0xc4, 0xc1, 0x7a, 0x6f, 0x33, //0x00000870 vmovdqu      (%r11), %xmm6
	0xc4, 0xc1, 0x7a, 0x6f, 0x6b, 0x10, //0x00000875 vmovdqu      $16(%r11), %xmm5
	0xc4, 0xc1, 0x7a, 0x6f, 0x63, 0x20, //0x0000087b vmovdqu      $32(%r11), %xmm4
	0xc4, 0xc1, 0x7a, 0x6f, 0x7b, 0x30, //0x00000881 vmovdqu      $48(%r11), %xmm7
	0xc5, 0xc9, 0x74, 0xd0, //0x00000887 vpcmpeqb     %xmm0, %xmm6, %xmm2
	0xc5, 0xf9, 0xd7, 0xf2, //0x0000088b vpmovmskb    %xmm2, %esi
	0xc5, 0xd1, 0x74, 0xd0, //0x0000088f vpcmpeqb     %xmm0, %xmm5, %xmm2
	0xc5, 0xf9, 0xd7, 0xda, //0x00000893 vpmovmskb    %xmm2, %ebx
	0xc5, 0xd9, 0x74, 0xd0, //0x00000897 vpcmpeqb     %xmm0, %xmm4, %xmm2
	0xc5, 0xf9, 0xd7, 0xca, //0x0000089b vpmovmskb    %xmm2, %ecx
	0xc5, 0xc1, 0x74, 0xd0, //0x0000089f vpcmpeqb     %xmm0, %xmm7, %xmm2
	0xc5, 0xf9, 0xd7, 0xd2, //0x000008a3 vpmovmskb    %xmm2, %edx
	0xc5, 0xc9, 0x74, 0xd1, //0x000008a7 vpcmpeqb     %xmm1, %xmm6, %xmm2
	0xc5, 0x79, 0xd7, 0xea, //0x000008ab vpmovmskb    %xmm2, %r13d
	0xc5, 0xd1, 0x74, 0xd1, //0x000008af vpcmpeqb     %xmm1, %xmm5, %xmm2
	0xc5, 0xf9, 0xd7, 0xfa, //0x000008b3 vpmovmskb    %xmm2, %edi
	0xc5, 0xd9, 0x74, 0xd1, //0x000008b7 vpcmpeqb     %xmm1, %xmm4, %xmm2
	0x48, 0xc1, 0xe3, 0x10, //0x000008bb shlq         $16, %rbx
	0x48, 0x09, 0xde, //0x000008bf orq          %rbx, %rsi
	0xc5, 0xf9, 0xd7, 0xda, //0x000008c2 vpmovmskb    %xmm2, %ebx
	0xc5, 0xc1, 0x74, 0xd1, //0x000008c6 vpcmpeqb     %xmm1, %xmm7, %xmm2
	0x48, 0xc1, 0xe1, 0x20, //0x000008ca shlq         $32, %rcx
	0x48, 0x09, 0xce, //0x000008ce orq          %rcx, %rsi
	0xc5, 0xf9, 0xd7, 0xca, //0x000008d1 vpmovmskb    %xmm2, %ecx
	0xc5, 0x81, 0x64, 0xd6, //0x000008d5 vpcmpgtb     %xmm6, %xmm15, %xmm2
	0xc5, 0xc9, 0x64, 0xf3, //0x000008d9 vpcmpgtb     %xmm3, %xmm6, %xmm6
	0xc5, 0xc9, 0xdb, 0xd2, //0x000008dd vpand        %xmm2, %xmm6, %xmm2
	0x48, 0xc1, 0xe7, 0x10, //0x000008e1 shlq         $16, %rdi
	0x49, 0x09, 0xfd, //0x000008e5 orq          %rdi, %r13
	0xc5, 0x79, 0xd7, 0xe2, //0x000008e8 vpmovmskb    %xmm2, %r12d
	0xc5, 0x81, 0x64, 0xd5, //0x000008ec vpcmpgtb     %xmm5, %xmm15, %xmm2
	0xc5, 0xd1, 0x64, 0xeb, //0x000008f0 vpcmpgtb     %xmm3, %xmm5, %xmm5
	0xc5, 0xd1, 0xdb, 0xd2, //0x000008f4 vpand        %xmm2, %xmm5, %xmm2
	0x48, 0xc1, 0xe3, 0x20, //0x000008f8 shlq         $32, %rbx
	0x49, 0x09, 0xdd, //0x000008fc orq          %rbx, %r13
	0xc5, 0xf9, 0xd7, 0xfa, //0x000008ff vpmovmskb    %xmm2, %edi
	0xc5, 0x81, 0x64, 0xd4, //0x00000903 vpcmpgtb     %xmm4, %xmm15, %xmm2
	0xc5, 0xd9, 0x64, 0xe3, //0x00000907 vpcmpgtb     %xmm3, %xmm4, %xmm4
	0xc5, 0xd9, 0xdb, 0xd2, //0x0000090b vpand        %xmm2, %xmm4, %xmm2
	0x48, 0xc1, 0xe1, 0x30, //0x0000090f shlq         $48, %rcx
	0x49, 0x09, 0xcd, //0x00000913 orq          %rcx, %r13
	0xc5, 0xf9, 0xd7, 0xca, //0x00000916 vpmovmskb    %xmm2, %ecx
	0xc5, 0x81, 0x64, 0xd7, //0x0000091a vpcmpgtb     %xmm7, %xmm15, %xmm2
	0xc5, 0xc1, 0x64, 0xe3, //0x0000091e vpcmpgtb     %xmm3, %xmm7, %xmm4
	0xc5, 0xd9, 0xdb, 0xd2, //0x00000922 vpand        %xmm2, %xmm4, %xmm2
	0x48, 0xc1, 0xe7, 0x10, //0x00000926 shlq         $16, %rdi
	0x49, 0x09, 0xfc, //0x0000092a orq          %rdi, %r12
	0xc5, 0x79, 0xd7, 0xf2, //0x0000092d vpmovmskb    %xmm2, %r14d
	0x48, 0xc1, 0xe2, 0x30, //0x00000931 shlq         $48, %rdx
	0x48, 0xc1, 0xe1, 0x20, //0x00000935 shlq         $32, %rcx
	0x49, 0x83, 0xf9, 0xff, //0x00000939 cmpq         $-1, %r9
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x0000093d jne          LBB0_109
	0x4d, 0x85, 0xed, //0x00000943 testq        %r13, %r13
	0x0f, 0x85, 0x9c, 0x00, 0x00, 0x00, //0x00000946 jne          LBB0_124
	//0x0000094c LBB0_109
	0x49, 0xc1, 0xe6, 0x30, //0x0000094c shlq         $48, %r14
	0x49, 0x09, 0xcc, //0x00000950 orq          %rcx, %r12
	0x48, 0x09, 0xd6, //0x00000953 orq          %rdx, %rsi
	0x4c, 0x89, 0xe9, //0x00000956 movq         %r13, %rcx
	0x4c, 0x09, 0xc1, //0x00000959 orq          %r8, %rcx
	0x0f, 0x85, 0x2c, 0x00, 0x00, 0x00, //0x0000095c jne          LBB0_144
	0x4d, 0x09, 0xf4, //0x00000962 orq          %r14, %r12
	0x48, 0x85, 0xf6, //0x00000965 testq        %rsi, %rsi
	0x0f, 0x85, 0x35, 0x02, 0x00, 0x00, //0x00000968 jne          LBB0_145
	//0x0000096e LBB0_111
	0x4d, 0x85, 0xe4, //0x0000096e testq        %r12, %r12
	0x0f, 0x85, 0x84, 0x1a, 0x00, 0x00, //0x00000971 jne          LBB0_425
	0x48, 0x83, 0xc0, 0xc0, //0x00000977 addq         $-64, %rax
	0x49, 0x83, 0xc3, 0x40, //0x0000097b addq         $64, %r11
	0x48, 0x83, 0xf8, 0x3f, //0x0000097f cmpq         $63, %rax
	0x0f, 0x87, 0xe7, 0xfe, 0xff, 0xff, //0x00000983 ja           LBB0_107
	0xe9, 0x62, 0x0e, 0x00, 0x00, //0x00000989 jmp          LBB0_113
	//0x0000098e LBB0_144
	0x4c, 0x89, 0xc1, //0x0000098e movq         %r8, %rcx
	0x48, 0xf7, 0xd1, //0x00000991 notq         %rcx
	0x4c, 0x21, 0xe9, //0x00000994 andq         %r13, %rcx
	0x48, 0x8d, 0x14, 0x09, //0x00000997 leaq         (%rcx,%rcx), %rdx
	0x4c, 0x09, 0xc2, //0x0000099b orq          %r8, %rdx
	0x48, 0x89, 0xd7, //0x0000099e movq         %rdx, %rdi
	0x48, 0xf7, 0xd7, //0x000009a1 notq         %rdi
	0x4c, 0x21, 0xef, //0x000009a4 andq         %r13, %rdi
	0x48, 0xbb, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x000009a7 movabsq      $-6148914691236517206, %rbx
	0x48, 0x21, 0xdf, //0x000009b1 andq         %rbx, %rdi
	0x45, 0x31, 0xc0, //0x000009b4 xorl         %r8d, %r8d
	0x48, 0x01, 0xcf, //0x000009b7 addq         %rcx, %rdi
	0x41, 0x0f, 0x92, 0xc0, //0x000009ba setb         %r8b
	0x48, 0x01, 0xff, //0x000009be addq         %rdi, %rdi
	0x48, 0xb9, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x000009c1 movabsq      $6148914691236517205, %rcx
	0x48, 0x31, 0xcf, //0x000009cb xorq         %rcx, %rdi
	0x48, 0x21, 0xd7, //0x000009ce andq         %rdx, %rdi
	0x48, 0xf7, 0xd7, //0x000009d1 notq         %rdi
	0x48, 0x21, 0xfe, //0x000009d4 andq         %rdi, %rsi
	0x4d, 0x09, 0xf4, //0x000009d7 orq          %r14, %r12
	0x48, 0x85, 0xf6, //0x000009da testq        %rsi, %rsi
	0x0f, 0x84, 0x8b, 0xff, 0xff, 0xff, //0x000009dd je           LBB0_111
	0xe9, 0xbb, 0x01, 0x00, 0x00, //0x000009e3 jmp          LBB0_145
	//0x000009e8 LBB0_124
	0x4c, 0x89, 0xdf, //0x000009e8 movq         %r11, %rdi
	0x48, 0x2b, 0x7d, 0xd0, //0x000009eb subq         $-48(%rbp), %rdi
	0x4d, 0x0f, 0xbc, 0xcd, //0x000009ef bsfq         %r13, %r9
	0x49, 0x01, 0xf9, //0x000009f3 addq         %rdi, %r9
	0xe9, 0x51, 0xff, 0xff, 0xff, //0x000009f6 jmp          LBB0_109
	//0x000009fb LBB0_125
	0x4a, 0xc7, 0x04, 0xc2, 0x02, 0x00, 0x00, 0x00, //0x000009fb movq         $2, (%rdx,%r8,8)
	0x48, 0x8b, 0x4f, 0x08, //0x00000a03 movq         $8(%rdi), %rcx
	0xf6, 0x85, 0x78, 0xff, 0xff, 0xff, 0x20, //0x00000a07 testb        $32, $-136(%rbp)
	0x48, 0x89, 0x4d, 0xa0, //0x00000a0e movq         %rcx, $-96(%rbp)
	0x0f, 0x85, 0x8d, 0x02, 0x00, 0x00, //0x00000a12 jne          LBB0_160
	0x49, 0x89, 0xcd, //0x00000a18 movq         %rcx, %r13
	0x4d, 0x29, 0xd5, //0x00000a1b subq         %r10, %r13
	0x0f, 0x84, 0x33, 0x1b, 0x00, 0x00, //0x00000a1e je           LBB0_449
	0x4f, 0x8d, 0x1c, 0x14, //0x00000a24 leaq         (%r12,%r10), %r11
	0x49, 0x83, 0xfd, 0x40, //0x00000a28 cmpq         $64, %r13
	0x0f, 0x82, 0x10, 0x14, 0x00, 0x00, //0x00000a2c jb           LBB0_357
	0x44, 0x89, 0xe8, //0x00000a32 movl         %r13d, %eax
	0x83, 0xe0, 0x3f, //0x00000a35 andl         $63, %eax
	0x48, 0x89, 0x45, 0xa8, //0x00000a38 movq         %rax, $-88(%rbp)
	0x4c, 0x29, 0xf9, //0x00000a3c subq         %r15, %rcx
	0x48, 0x83, 0xc1, 0xbf, //0x00000a3f addq         $-65, %rcx
	0x48, 0x83, 0xe1, 0xc0, //0x00000a43 andq         $-64, %rcx
	0x4c, 0x01, 0xd1, //0x00000a47 addq         %r10, %rcx
	0x48, 0x03, 0x4d, 0x88, //0x00000a4a addq         $-120(%rbp), %rcx
	0x48, 0x89, 0x4d, 0x80, //0x00000a4e movq         %rcx, $-128(%rbp)
	0x49, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00000a52 movq         $-1, %r9
	0x45, 0x31, 0xe4, //0x00000a59 xorl         %r12d, %r12d
	0x90, 0x90, 0x90, 0x90, //0x00000a5c .p2align 4, 0x90
	//0x00000a60 LBB0_129
	0xc4, 0xc1, 0x7a, 0x6f, 0x13, //0x00000a60 vmovdqu      (%r11), %xmm2
	0xc4, 0xc1, 0x7a, 0x6f, 0x63, 0x10, //0x00000a65 vmovdqu      $16(%r11), %xmm4
	0xc4, 0xc1, 0x7a, 0x6f, 0x6b, 0x20, //0x00000a6b vmovdqu      $32(%r11), %xmm5
	0xc4, 0xc1, 0x7a, 0x6f, 0x73, 0x30, //0x00000a71 vmovdqu      $48(%r11), %xmm6
	0xc5, 0xe9, 0x74, 0xf8, //0x00000a77 vpcmpeqb     %xmm0, %xmm2, %xmm7
	0xc5, 0xf9, 0xd7, 0xff, //0x00000a7b vpmovmskb    %xmm7, %edi
	0xc5, 0xd9, 0x74, 0xf8, //0x00000a7f vpcmpeqb     %xmm0, %xmm4, %xmm7
	0xc5, 0xf9, 0xd7, 0xcf, //0x00000a83 vpmovmskb    %xmm7, %ecx
	0xc5, 0xd1, 0x74, 0xf8, //0x00000a87 vpcmpeqb     %xmm0, %xmm5, %xmm7
	0xc5, 0xf9, 0xd7, 0xf7, //0x00000a8b vpmovmskb    %xmm7, %esi
	0xc5, 0xc9, 0x74, 0xf8, //0x00000a8f vpcmpeqb     %xmm0, %xmm6, %xmm7
	0xc5, 0xf9, 0xd7, 0xdf, //0x00000a93 vpmovmskb    %xmm7, %ebx
	0xc5, 0xe9, 0x74, 0xd1, //0x00000a97 vpcmpeqb     %xmm1, %xmm2, %xmm2
	0xc5, 0xf9, 0xd7, 0xd2, //0x00000a9b vpmovmskb    %xmm2, %edx
	0xc5, 0xd9, 0x74, 0xd1, //0x00000a9f vpcmpeqb     %xmm1, %xmm4, %xmm2
	0xc5, 0xf9, 0xd7, 0xc2, //0x00000aa3 vpmovmskb    %xmm2, %eax
	0xc5, 0xd1, 0x74, 0xd1, //0x00000aa7 vpcmpeqb     %xmm1, %xmm5, %xmm2
	0xc5, 0x79, 0xd7, 0xf2, //0x00000aab vpmovmskb    %xmm2, %r14d
	0xc5, 0xc9, 0x74, 0xd1, //0x00000aaf vpcmpeqb     %xmm1, %xmm6, %xmm2
	0xc5, 0x79, 0xd7, 0xc2, //0x00000ab3 vpmovmskb    %xmm2, %r8d
	0x48, 0xc1, 0xe3, 0x30, //0x00000ab7 shlq         $48, %rbx
	0x48, 0xc1, 0xe6, 0x20, //0x00000abb shlq         $32, %rsi
	0x48, 0xc1, 0xe1, 0x10, //0x00000abf shlq         $16, %rcx
	0x48, 0x09, 0xcf, //0x00000ac3 orq          %rcx, %rdi
	0x48, 0x09, 0xf7, //0x00000ac6 orq          %rsi, %rdi
	0x49, 0xc1, 0xe0, 0x30, //0x00000ac9 shlq         $48, %r8
	0x49, 0xc1, 0xe6, 0x20, //0x00000acd shlq         $32, %r14
	0x48, 0xc1, 0xe0, 0x10, //0x00000ad1 shlq         $16, %rax
	0x48, 0x09, 0xc2, //0x00000ad5 orq          %rax, %rdx
	0x4c, 0x09, 0xf2, //0x00000ad8 orq          %r14, %rdx
	0x4c, 0x09, 0xc2, //0x00000adb orq          %r8, %rdx
	0x49, 0x83, 0xf9, 0xff, //0x00000ade cmpq         $-1, %r9
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x00000ae2 jne          LBB0_131
	0x48, 0x85, 0xd2, //0x00000ae8 testq        %rdx, %rdx
	0x0f, 0x85, 0x2f, 0x00, 0x00, 0x00, //0x00000aeb jne          LBB0_140
	//0x00000af1 LBB0_131
	0x48, 0x09, 0xdf, //0x00000af1 orq          %rbx, %rdi
	0x48, 0x89, 0xd0, //0x00000af4 movq         %rdx, %rax
	0x4c, 0x09, 0xe0, //0x00000af7 orq          %r12, %rax
	0x0f, 0x85, 0x3d, 0x00, 0x00, 0x00, //0x00000afa jne          LBB0_141
	//0x00000b00 LBB0_132
	0x48, 0x85, 0xff, //0x00000b00 testq        %rdi, %rdi
	0x0f, 0x85, 0x86, 0x00, 0x00, 0x00, //0x00000b03 jne          LBB0_142
	//0x00000b09 LBB0_133
	0x49, 0x83, 0xc5, 0xc0, //0x00000b09 addq         $-64, %r13
	0x49, 0x83, 0xc3, 0x40, //0x00000b0d addq         $64, %r11
	0x49, 0x83, 0xfd, 0x3f, //0x00000b11 cmpq         $63, %r13
	0x0f, 0x87, 0x45, 0xff, 0xff, 0xff, //0x00000b15 ja           LBB0_129
	0xe9, 0xf1, 0x0d, 0x00, 0x00, //0x00000b1b jmp          LBB0_134
	//0x00000b20 LBB0_140
	0x4c, 0x89, 0xd8, //0x00000b20 movq         %r11, %rax
	0x48, 0x2b, 0x45, 0xd0, //0x00000b23 subq         $-48(%rbp), %rax
	0x4c, 0x0f, 0xbc, 0xca, //0x00000b27 bsfq         %rdx, %r9
	0x49, 0x01, 0xc1, //0x00000b2b addq         %rax, %r9
	0x48, 0x09, 0xdf, //0x00000b2e orq          %rbx, %rdi
	0x48, 0x89, 0xd0, //0x00000b31 movq         %rdx, %rax
	0x4c, 0x09, 0xe0, //0x00000b34 orq          %r12, %rax
	0x0f, 0x84, 0xc3, 0xff, 0xff, 0xff, //0x00000b37 je           LBB0_132
	//0x00000b3d LBB0_141
	0x4c, 0x89, 0xe0, //0x00000b3d movq         %r12, %rax
	0x48, 0xf7, 0xd0, //0x00000b40 notq         %rax
	0x48, 0x21, 0xd0, //0x00000b43 andq         %rdx, %rax
	0x48, 0x8d, 0x0c, 0x00, //0x00000b46 leaq         (%rax,%rax), %rcx
	0x4c, 0x09, 0xe1, //0x00000b4a orq          %r12, %rcx
	0x48, 0x89, 0xce, //0x00000b4d movq         %rcx, %rsi
	0x48, 0xf7, 0xd6, //0x00000b50 notq         %rsi
	0x48, 0x21, 0xd6, //0x00000b53 andq         %rdx, %rsi
	0x48, 0xba, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00000b56 movabsq      $-6148914691236517206, %rdx
	0x48, 0x21, 0xd6, //0x00000b60 andq         %rdx, %rsi
	0x45, 0x31, 0xe4, //0x00000b63 xorl         %r12d, %r12d
	0x48, 0x01, 0xc6, //0x00000b66 addq         %rax, %rsi
	0x41, 0x0f, 0x92, 0xc4, //0x00000b69 setb         %r12b
	0x48, 0x01, 0xf6, //0x00000b6d addq         %rsi, %rsi
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00000b70 movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc6, //0x00000b7a xorq         %rax, %rsi
	0x48, 0x21, 0xce, //0x00000b7d andq         %rcx, %rsi
	0x48, 0xf7, 0xd6, //0x00000b80 notq         %rsi
	0x48, 0x21, 0xf7, //0x00000b83 andq         %rsi, %rdi
	0x48, 0x85, 0xff, //0x00000b86 testq        %rdi, %rdi
	0x0f, 0x84, 0x7a, 0xff, 0xff, 0xff, //0x00000b89 je           LBB0_133
	//0x00000b8f LBB0_142
	0x48, 0x0f, 0xbc, 0xc7, //0x00000b8f bsfq         %rdi, %rax
	//0x00000b93 LBB0_143
	0x4c, 0x03, 0x5d, 0x90, //0x00000b93 addq         $-112(%rbp), %r11
	0x49, 0x01, 0xc3, //0x00000b97 addq         %rax, %r11
	0x4c, 0x8b, 0x65, 0xd0, //0x00000b9a movq         $-48(%rbp), %r12
	0xe9, 0x8f, 0x03, 0x00, 0x00, //0x00000b9e jmp          LBB0_193
	//0x00000ba3 LBB0_145
	0x48, 0x0f, 0xbc, 0xc6, //0x00000ba3 bsfq         %rsi, %rax
	0x4d, 0x85, 0xe4, //0x00000ba7 testq        %r12, %r12
	0x0f, 0x84, 0xf9, 0x02, 0x00, 0x00, //0x00000baa je           LBB0_184
	0x49, 0x0f, 0xbc, 0xcc, //0x00000bb0 bsfq         %r12, %rcx
	0x4c, 0x8b, 0x65, 0xd0, //0x00000bb4 movq         $-48(%rbp), %r12
	0x4d, 0x29, 0xe3, //0x00000bb8 subq         %r12, %r11
	0x48, 0x39, 0xc1, //0x00000bbb cmpq         %rax, %rcx
	0x0f, 0x83, 0xfa, 0x02, 0x00, 0x00, //0x00000bbe jae          LBB0_185
	0xe9, 0x97, 0x19, 0x00, 0x00, //0x00000bc4 jmp          LBB0_308
	//0x00000bc9 LBB0_147
	0x49, 0x01, 0xce, //0x00000bc9 addq         %rcx, %r14
	0x4c, 0x89, 0x75, 0xb0, //0x00000bcc movq         %r14, $-80(%rbp)
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000bd0 movq         $-1, %rax
	0x4d, 0x85, 0xed, //0x00000bd7 testq        %r13, %r13
	0x48, 0x8b, 0x75, 0xc8, //0x00000bda movq         $-56(%rbp), %rsi
	0x0f, 0x85, 0x1d, 0x00, 0x00, 0x00, //0x00000bde jne          LBB0_150
	0xe9, 0xeb, 0x17, 0x00, 0x00, //0x00000be4 jmp          LBB0_421
	//0x00000be9 LBB0_148
	0x48, 0x89, 0x45, 0xb0, //0x00000be9 movq         %rax, $-80(%rbp)
	//0x00000bed LBB0_149
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000bed movq         $-1, %rax
	0x4d, 0x85, 0xed, //0x00000bf4 testq        %r13, %r13
	0x48, 0x8b, 0x75, 0xc8, //0x00000bf7 movq         $-56(%rbp), %rsi
	0x0f, 0x84, 0xd3, 0x17, 0x00, 0x00, //0x00000bfb je           LBB0_421
	//0x00000c01 LBB0_150
	0x4d, 0x85, 0xe4, //0x00000c01 testq        %r12, %r12
	0x0f, 0x84, 0xca, 0x17, 0x00, 0x00, //0x00000c04 je           LBB0_421
	0x4d, 0x85, 0xdb, //0x00000c0a testq        %r11, %r11
	0x0f, 0x84, 0xc1, 0x17, 0x00, 0x00, //0x00000c0d je           LBB0_421
	0x48, 0x8b, 0x4d, 0xb0, //0x00000c13 movq         $-80(%rbp), %rcx
	0x4c, 0x29, 0xd1, //0x00000c17 subq         %r10, %rcx
	0x48, 0x8d, 0x41, 0xff, //0x00000c1a leaq         $-1(%rcx), %rax
	0x49, 0x39, 0xc5, //0x00000c1e cmpq         %rax, %r13
	0x0f, 0x84, 0x49, 0x00, 0x00, 0x00, //0x00000c21 je           LBB0_158
	0x49, 0x39, 0xc3, //0x00000c27 cmpq         %rax, %r11
	0x0f, 0x84, 0x40, 0x00, 0x00, 0x00, //0x00000c2a je           LBB0_158
	0x49, 0x39, 0xc4, //0x00000c30 cmpq         %rax, %r12
	0x0f, 0x84, 0x37, 0x00, 0x00, 0x00, //0x00000c33 je           LBB0_158
	0x4d, 0x85, 0xe4, //0x00000c39 testq        %r12, %r12
	0x48, 0x8b, 0x55, 0xb8, //0x00000c3c movq         $-72(%rbp), %rdx
	0x48, 0x8b, 0x7d, 0xc0, //0x00000c40 movq         $-64(%rbp), %rdi
	0x0f, 0x8e, 0xa2, 0x02, 0x00, 0x00, //0x00000c44 jle          LBB0_188
	0x49, 0x8d, 0x44, 0x24, 0xff, //0x00000c4a leaq         $-1(%r12), %rax
	0x49, 0x39, 0xc5, //0x00000c4f cmpq         %rax, %r13
	0x0f, 0x84, 0x94, 0x02, 0x00, 0x00, //0x00000c52 je           LBB0_188
	0x49, 0xf7, 0xd4, //0x00000c58 notq         %r12
	0x4c, 0x89, 0xe1, //0x00000c5b movq         %r12, %rcx
	0x4c, 0x8b, 0x65, 0xd0, //0x00000c5e movq         $-48(%rbp), %r12
	0x48, 0x85, 0xc9, //0x00000c62 testq        %rcx, %rcx
	0x0f, 0x89, 0x1d, 0x00, 0x00, 0x00, //0x00000c65 jns          LBB0_268
	0xe9, 0x61, 0x17, 0x00, 0x00, //0x00000c6b jmp          LBB0_420
	//0x00000c70 LBB0_158
	0x48, 0xf7, 0xd9, //0x00000c70 negq         %rcx
	//0x00000c73 LBB0_159
	0x4c, 0x8b, 0x65, 0xd0, //0x00000c73 movq         $-48(%rbp), %r12
	0x48, 0x8b, 0x55, 0xb8, //0x00000c77 movq         $-72(%rbp), %rdx
	0x48, 0x8b, 0x7d, 0xc0, //0x00000c7b movq         $-64(%rbp), %rdi
	0x48, 0x85, 0xc9, //0x00000c7f testq        %rcx, %rcx
	0x0f, 0x88, 0x49, 0x17, 0x00, 0x00, //0x00000c82 js           LBB0_420
	//0x00000c88 LBB0_268
	0x4c, 0x01, 0xf9, //0x00000c88 addq         %r15, %rcx
	0x49, 0x89, 0xce, //0x00000c8b movq         %rcx, %r14
	//0x00000c8e LBB0_269
	0x4d, 0x89, 0xf3, //0x00000c8e movq         %r14, %r11
	0x4c, 0x89, 0x36, //0x00000c91 movq         %r14, (%rsi)
	0x4c, 0x89, 0xf8, //0x00000c94 movq         %r15, %rax
	0x4d, 0x85, 0xff, //0x00000c97 testq        %r15, %r15
	0x0f, 0x89, 0xc3, 0xf4, 0xff, 0xff, //0x00000c9a jns          LBB0_2
	0xe9, 0x3f, 0x17, 0x00, 0x00, //0x00000ca0 jmp          LBB0_424
	//0x00000ca5 LBB0_160
	0x48, 0x89, 0xc8, //0x00000ca5 movq         %rcx, %rax
	0x4c, 0x29, 0xd0, //0x00000ca8 subq         %r10, %rax
	0x0f, 0x84, 0xa6, 0x18, 0x00, 0x00, //0x00000cab je           LBB0_449
	0x4f, 0x8d, 0x1c, 0x14, //0x00000cb1 leaq         (%r12,%r10), %r11
	0x48, 0x83, 0xf8, 0x40, //0x00000cb5 cmpq         $64, %rax
	0x0f, 0x82, 0x9c, 0x11, 0x00, 0x00, //0x00000cb9 jb           LBB0_358
	0x89, 0xc2, //0x00000cbf movl         %eax, %edx
	0x83, 0xe2, 0x3f, //0x00000cc1 andl         $63, %edx
	0x48, 0x89, 0x55, 0xa8, //0x00000cc4 movq         %rdx, $-88(%rbp)
	0x49, 0x89, 0xcc, //0x00000cc8 movq         %rcx, %r12
	0x4d, 0x29, 0xfc, //0x00000ccb subq         %r15, %r12
	0x49, 0x83, 0xc4, 0xbf, //0x00000cce addq         $-65, %r12
	0x49, 0x83, 0xe4, 0xc0, //0x00000cd2 andq         $-64, %r12
	0x4d, 0x01, 0xd4, //0x00000cd6 addq         %r10, %r12
	0x4c, 0x03, 0x65, 0x88, //0x00000cd9 addq         $-120(%rbp), %r12
	0x49, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00000cdd movq         $-1, %r9
	0x45, 0x31, 0xc0, //0x00000ce4 xorl         %r8d, %r8d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000ce7 .p2align 4, 0x90
	//0x00000cf0 LBB0_163
	0xc4, 0xc1, 0x7a, 0x6f, 0x33, //0x00000cf0 vmovdqu      (%r11), %xmm6
	0xc4, 0xc1, 0x7a, 0x6f, 0x6b, 0x10, //0x00000cf5 vmovdqu      $16(%r11), %xmm5
	0xc4, 0xc1, 0x7a, 0x6f, 0x63, 0x20, //0x00000cfb vmovdqu      $32(%r11), %xmm4
	0xc4, 0xc1, 0x7a, 0x6f, 0x7b, 0x30, //0x00000d01 vmovdqu      $48(%r11), %xmm7
	0xc5, 0xc9, 0x74, 0xd0, //0x00000d07 vpcmpeqb     %xmm0, %xmm6, %xmm2
	0xc5, 0xf9, 0xd7, 0xf2, //0x00000d0b vpmovmskb    %xmm2, %esi
	0xc5, 0xd1, 0x74, 0xd0, //0x00000d0f vpcmpeqb     %xmm0, %xmm5, %xmm2
	0xc5, 0xf9, 0xd7, 0xda, //0x00000d13 vpmovmskb    %xmm2, %ebx
	0xc5, 0xd9, 0x74, 0xd0, //0x00000d17 vpcmpeqb     %xmm0, %xmm4, %xmm2
	0xc5, 0xf9, 0xd7, 0xca, //0x00000d1b vpmovmskb    %xmm2, %ecx
	0xc5, 0xc1, 0x74, 0xd0, //0x00000d1f vpcmpeqb     %xmm0, %xmm7, %xmm2
	0xc5, 0xf9, 0xd7, 0xfa, //0x00000d23 vpmovmskb    %xmm2, %edi
	0xc5, 0xc9, 0x74, 0xd1, //0x00000d27 vpcmpeqb     %xmm1, %xmm6, %xmm2
	0xc5, 0x79, 0xd7, 0xea, //0x00000d2b vpmovmskb    %xmm2, %r13d
	0xc5, 0xd1, 0x74, 0xd1, //0x00000d2f vpcmpeqb     %xmm1, %xmm5, %xmm2
	0xc5, 0xf9, 0xd7, 0xd2, //0x00000d33 vpmovmskb    %xmm2, %edx
	0xc5, 0xd9, 0x74, 0xd1, //0x00000d37 vpcmpeqb     %xmm1, %xmm4, %xmm2
	0x48, 0xc1, 0xe3, 0x10, //0x00000d3b shlq         $16, %rbx
	0x48, 0x09, 0xde, //0x00000d3f orq          %rbx, %rsi
	0xc5, 0xf9, 0xd7, 0xda, //0x00000d42 vpmovmskb    %xmm2, %ebx
	0xc5, 0xc1, 0x74, 0xd1, //0x00000d46 vpcmpeqb     %xmm1, %xmm7, %xmm2
	0x48, 0xc1, 0xe1, 0x20, //0x00000d4a shlq         $32, %rcx
	0x48, 0x09, 0xce, //0x00000d4e orq          %rcx, %rsi
	0xc5, 0xf9, 0xd7, 0xca, //0x00000d51 vpmovmskb    %xmm2, %ecx
	0xc5, 0x81, 0x64, 0xd6, //0x00000d55 vpcmpgtb     %xmm6, %xmm15, %xmm2
	0xc5, 0xc9, 0x64, 0xf3, //0x00000d59 vpcmpgtb     %xmm3, %xmm6, %xmm6
	0xc5, 0xc9, 0xdb, 0xd2, //0x00000d5d vpand        %xmm2, %xmm6, %xmm2
	0x48, 0xc1, 0xe2, 0x10, //0x00000d61 shlq         $16, %rdx
	0x49, 0x09, 0xd5, //0x00000d65 orq          %rdx, %r13
	0xc5, 0xf9, 0xd7, 0xd2, //0x00000d68 vpmovmskb    %xmm2, %edx
	0xc5, 0x81, 0x64, 0xd5, //0x00000d6c vpcmpgtb     %xmm5, %xmm15, %xmm2
	0xc5, 0xd1, 0x64, 0xeb, //0x00000d70 vpcmpgtb     %xmm3, %xmm5, %xmm5
	0xc5, 0xd1, 0xdb, 0xd2, //0x00000d74 vpand        %xmm2, %xmm5, %xmm2
	0x48, 0xc1, 0xe3, 0x20, //0x00000d78 shlq         $32, %rbx
	0x49, 0x09, 0xdd, //0x00000d7c orq          %rbx, %r13
	0xc5, 0xf9, 0xd7, 0xda, //0x00000d7f vpmovmskb    %xmm2, %ebx
	0xc5, 0x81, 0x64, 0xd4, //0x00000d83 vpcmpgtb     %xmm4, %xmm15, %xmm2
	0xc5, 0xd9, 0x64, 0xe3, //0x00000d87 vpcmpgtb     %xmm3, %xmm4, %xmm4
	0xc5, 0xd9, 0xdb, 0xd2, //0x00000d8b vpand        %xmm2, %xmm4, %xmm2
	0x48, 0xc1, 0xe1, 0x30, //0x00000d8f shlq         $48, %rcx
	0x49, 0x09, 0xcd, //0x00000d93 orq          %rcx, %r13
	0xc5, 0xf9, 0xd7, 0xca, //0x00000d96 vpmovmskb    %xmm2, %ecx
	0xc5, 0x81, 0x64, 0xd7, //0x00000d9a vpcmpgtb     %xmm7, %xmm15, %xmm2
	0xc5, 0xc1, 0x64, 0xe3, //0x00000d9e vpcmpgtb     %xmm3, %xmm7, %xmm4
	0xc5, 0xd9, 0xdb, 0xd2, //0x00000da2 vpand        %xmm2, %xmm4, %xmm2
	0x48, 0xc1, 0xe3, 0x10, //0x00000da6 shlq         $16, %rbx
	0x48, 0x09, 0xda, //0x00000daa orq          %rbx, %rdx
	0xc5, 0x79, 0xd7, 0xf2, //0x00000dad vpmovmskb    %xmm2, %r14d
	0x48, 0xc1, 0xe7, 0x30, //0x00000db1 shlq         $48, %rdi
	0x48, 0xc1, 0xe1, 0x20, //0x00000db5 shlq         $32, %rcx
	0x49, 0x83, 0xf9, 0xff, //0x00000db9 cmpq         $-1, %r9
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x00000dbd jne          LBB0_165
	0x4d, 0x85, 0xed, //0x00000dc3 testq        %r13, %r13
	0x0f, 0x85, 0xa4, 0x00, 0x00, 0x00, //0x00000dc6 jne          LBB0_180
	//0x00000dcc LBB0_165
	0x49, 0xc1, 0xe6, 0x30, //0x00000dcc shlq         $48, %r14
	0x48, 0x09, 0xca, //0x00000dd0 orq          %rcx, %rdx
	0x48, 0x09, 0xfe, //0x00000dd3 orq          %rdi, %rsi
	0x4c, 0x89, 0xe9, //0x00000dd6 movq         %r13, %rcx
	0x4c, 0x09, 0xc1, //0x00000dd9 orq          %r8, %rcx
	0x0f, 0x85, 0x2c, 0x00, 0x00, 0x00, //0x00000ddc jne          LBB0_181
	0x4c, 0x09, 0xf2, //0x00000de2 orq          %r14, %rdx
	0x48, 0x85, 0xf6, //0x00000de5 testq        %rsi, %rsi
	0x0f, 0x85, 0x95, 0x00, 0x00, 0x00, //0x00000de8 jne          LBB0_182
	//0x00000dee LBB0_167
	0x48, 0x85, 0xd2, //0x00000dee testq        %rdx, %rdx
	0x0f, 0x85, 0x17, 0x16, 0x00, 0x00, //0x00000df1 jne          LBB0_427
	0x48, 0x83, 0xc0, 0xc0, //0x00000df7 addq         $-64, %rax
	0x49, 0x83, 0xc3, 0x40, //0x00000dfb addq         $64, %r11
	0x48, 0x83, 0xf8, 0x3f, //0x00000dff cmpq         $63, %rax
	0x0f, 0x87, 0xe7, 0xfe, 0xff, 0xff, //0x00000e03 ja           LBB0_163
	0xe9, 0x7f, 0x0b, 0x00, 0x00, //0x00000e09 jmp          LBB0_169
	//0x00000e0e LBB0_181
	0x4c, 0x89, 0xc1, //0x00000e0e movq         %r8, %rcx
	0x48, 0xf7, 0xd1, //0x00000e11 notq         %rcx
	0x4c, 0x21, 0xe9, //0x00000e14 andq         %r13, %rcx
	0x4c, 0x89, 0x55, 0xb0, //0x00000e17 movq         %r10, $-80(%rbp)
	0x4c, 0x8d, 0x14, 0x09, //0x00000e1b leaq         (%rcx,%rcx), %r10
	0x4d, 0x09, 0xc2, //0x00000e1f orq          %r8, %r10
	0x4c, 0x89, 0xd3, //0x00000e22 movq         %r10, %rbx
	0x48, 0xf7, 0xd3, //0x00000e25 notq         %rbx
	0x4c, 0x21, 0xeb, //0x00000e28 andq         %r13, %rbx
	0x48, 0xbf, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00000e2b movabsq      $-6148914691236517206, %rdi
	0x48, 0x21, 0xfb, //0x00000e35 andq         %rdi, %rbx
	0x45, 0x31, 0xc0, //0x00000e38 xorl         %r8d, %r8d
	0x48, 0x01, 0xcb, //0x00000e3b addq         %rcx, %rbx
	0x41, 0x0f, 0x92, 0xc0, //0x00000e3e setb         %r8b
	0x48, 0x01, 0xdb, //0x00000e42 addq         %rbx, %rbx
	0x48, 0xb9, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00000e45 movabsq      $6148914691236517205, %rcx
	0x48, 0x31, 0xcb, //0x00000e4f xorq         %rcx, %rbx
	0x4c, 0x21, 0xd3, //0x00000e52 andq         %r10, %rbx
	0x4c, 0x8b, 0x55, 0xb0, //0x00000e55 movq         $-80(%rbp), %r10
	0x48, 0xf7, 0xd3, //0x00000e59 notq         %rbx
	0x48, 0x21, 0xde, //0x00000e5c andq         %rbx, %rsi
	0x4c, 0x09, 0xf2, //0x00000e5f orq          %r14, %rdx
	0x48, 0x85, 0xf6, //0x00000e62 testq        %rsi, %rsi
	0x0f, 0x84, 0x83, 0xff, 0xff, 0xff, //0x00000e65 je           LBB0_167
	0xe9, 0x13, 0x00, 0x00, 0x00, //0x00000e6b jmp          LBB0_182
	//0x00000e70 LBB0_180
	0x4c, 0x89, 0xdb, //0x00000e70 movq         %r11, %rbx
	0x48, 0x2b, 0x5d, 0xd0, //0x00000e73 subq         $-48(%rbp), %rbx
	0x4d, 0x0f, 0xbc, 0xcd, //0x00000e77 bsfq         %r13, %r9
	0x49, 0x01, 0xd9, //0x00000e7b addq         %rbx, %r9
	0xe9, 0x49, 0xff, 0xff, 0xff, //0x00000e7e jmp          LBB0_165
	//0x00000e83 LBB0_182
	0x48, 0x0f, 0xbc, 0xc6, //0x00000e83 bsfq         %rsi, %rax
	0x48, 0x85, 0xd2, //0x00000e87 testq        %rdx, %rdx
	0x4c, 0x8b, 0x65, 0xd0, //0x00000e8a movq         $-48(%rbp), %r12
	0x0f, 0x84, 0x88, 0x00, 0x00, 0x00, //0x00000e8e je           LBB0_191
	0x48, 0x0f, 0xbc, 0xca, //0x00000e94 bsfq         %rdx, %rcx
	0x4d, 0x29, 0xe3, //0x00000e98 subq         %r12, %r11
	0x48, 0x39, 0xc1, //0x00000e9b cmpq         %rax, %rcx
	0x0f, 0x83, 0x89, 0x00, 0x00, 0x00, //0x00000e9e jae          LBB0_192
	0xe9, 0xb7, 0x16, 0x00, 0x00, //0x00000ea4 jmp          LBB0_308
	//0x00000ea9 LBB0_184
	0xb9, 0x40, 0x00, 0x00, 0x00, //0x00000ea9 movl         $64, %ecx
	0x4c, 0x8b, 0x65, 0xd0, //0x00000eae movq         $-48(%rbp), %r12
	0x4d, 0x29, 0xe3, //0x00000eb2 subq         %r12, %r11
	0x48, 0x39, 0xc1, //0x00000eb5 cmpq         %rax, %rcx
	0x0f, 0x82, 0xa2, 0x16, 0x00, 0x00, //0x00000eb8 jb           LBB0_308
	//0x00000ebe LBB0_185
	0x4d, 0x8d, 0x5c, 0x03, 0x01, //0x00000ebe leaq         $1(%r11,%rax), %r11
	//0x00000ec3 LBB0_186
	0x4d, 0x85, 0xdb, //0x00000ec3 testq        %r11, %r11
	0x0f, 0x88, 0xdf, 0x14, 0x00, 0x00, //0x00000ec6 js           LBB0_415
	0x48, 0x8b, 0x75, 0xc8, //0x00000ecc movq         $-56(%rbp), %rsi
	0x4c, 0x89, 0x1e, //0x00000ed0 movq         %r11, (%rsi)
	0x4c, 0x89, 0xf8, //0x00000ed3 movq         %r15, %rax
	0x4d, 0x85, 0xd2, //0x00000ed6 testq        %r10, %r10
	0x48, 0x8b, 0x55, 0xb8, //0x00000ed9 movq         $-72(%rbp), %rdx
	0x48, 0x8b, 0x7d, 0xc0, //0x00000edd movq         $-64(%rbp), %rdi
	0x0f, 0x8f, 0x7c, 0xf2, 0xff, 0xff, //0x00000ee1 jg           LBB0_2
	0xe9, 0xf8, 0x14, 0x00, 0x00, //0x00000ee7 jmp          LBB0_424
	//0x00000eec LBB0_188
	0x4c, 0x89, 0xd8, //0x00000eec movq         %r11, %rax
	0x4c, 0x09, 0xe8, //0x00000eef orq          %r13, %rax
	0x4d, 0x39, 0xeb, //0x00000ef2 cmpq         %r13, %r11
	0x0f, 0x8c, 0x55, 0x05, 0x00, 0x00, //0x00000ef5 jl           LBB0_267
	0x48, 0x85, 0xc0, //0x00000efb testq        %rax, %rax
	0x0f, 0x88, 0x4c, 0x05, 0x00, 0x00, //0x00000efe js           LBB0_267
	0x49, 0xf7, 0xd3, //0x00000f04 notq         %r11
	0x4c, 0x89, 0xd9, //0x00000f07 movq         %r11, %rcx
	0x4c, 0x8b, 0x65, 0xd0, //0x00000f0a movq         $-48(%rbp), %r12
	0x48, 0x85, 0xc9, //0x00000f0e testq        %rcx, %rcx
	0x0f, 0x89, 0x71, 0xfd, 0xff, 0xff, //0x00000f11 jns          LBB0_268
	0xe9, 0xb5, 0x14, 0x00, 0x00, //0x00000f17 jmp          LBB0_420
	//0x00000f1c LBB0_191
	0xb9, 0x40, 0x00, 0x00, 0x00, //0x00000f1c movl         $64, %ecx
	0x4d, 0x29, 0xe3, //0x00000f21 subq         %r12, %r11
	0x48, 0x39, 0xc1, //0x00000f24 cmpq         %rax, %rcx
	0x0f, 0x82, 0x33, 0x16, 0x00, 0x00, //0x00000f27 jb           LBB0_308
	//0x00000f2d LBB0_192
	0x4d, 0x8d, 0x5c, 0x03, 0x01, //0x00000f2d leaq         $1(%r11,%rax), %r11
	//0x00000f32 LBB0_193
	0x4d, 0x85, 0xdb, //0x00000f32 testq        %r11, %r11
	0x0f, 0x88, 0x70, 0x14, 0x00, 0x00, //0x00000f35 js           LBB0_415
	0x48, 0x8b, 0x75, 0xc8, //0x00000f3b movq         $-56(%rbp), %rsi
	0x4c, 0x89, 0x1e, //0x00000f3f movq         %r11, (%rsi)
	0x4c, 0x89, 0xf8, //0x00000f42 movq         %r15, %rax
	0x4d, 0x85, 0xd2, //0x00000f45 testq        %r10, %r10
	0x48, 0x8b, 0x55, 0xb8, //0x00000f48 movq         $-72(%rbp), %rdx
	0x48, 0x8b, 0x7d, 0xc0, //0x00000f4c movq         $-64(%rbp), %rdi
	0x0f, 0x8e, 0x8e, 0x14, 0x00, 0x00, //0x00000f50 jle          LBB0_424
	0x48, 0x8b, 0x02, //0x00000f56 movq         (%rdx), %rax
	0x48, 0x3d, 0xff, 0x0f, 0x00, 0x00, //0x00000f59 cmpq         $4095, %rax
	0x0f, 0x8f, 0x3a, 0x14, 0x00, 0x00, //0x00000f5f jg           LBB0_432
	0x48, 0x8d, 0x48, 0x01, //0x00000f65 leaq         $1(%rax), %rcx
	0x48, 0x89, 0x0a, //0x00000f69 movq         %rcx, (%rdx)
	0x48, 0xc7, 0x44, 0xc2, 0x08, 0x04, 0x00, 0x00, 0x00, //0x00000f6c movq         $4, $8(%rdx,%rax,8)
	0xe9, 0xe9, 0xf1, 0xff, 0xff, //0x00000f75 jmp          LBB0_2
	//0x00000f7a LBB0_197
	0x4c, 0x8b, 0x47, 0x08, //0x00000f7a movq         $8(%rdi), %r8
	0xf6, 0x85, 0x78, 0xff, 0xff, 0xff, 0x20, //0x00000f7e testb        $32, $-136(%rbp)
	0x4c, 0x89, 0x45, 0xa0, //0x00000f85 movq         %r8, $-96(%rbp)
	0x0f, 0x85, 0xe8, 0x04, 0x00, 0x00, //0x00000f89 jne          LBB0_270
	0x4d, 0x89, 0xc4, //0x00000f8f movq         %r8, %r12
	0x4d, 0x29, 0xf4, //0x00000f92 subq         %r14, %r12
	0x0f, 0x84, 0xd7, 0x15, 0x00, 0x00, //0x00000f95 je           LBB0_448
	0x48, 0x8b, 0x45, 0xd0, //0x00000f9b movq         $-48(%rbp), %rax
	0x4e, 0x8d, 0x1c, 0x30, //0x00000f9f leaq         (%rax,%r14), %r11
	0x49, 0x83, 0xfc, 0x40, //0x00000fa3 cmpq         $64, %r12
	0x4c, 0x89, 0x75, 0xb0, //0x00000fa7 movq         %r14, $-80(%rbp)
	0x0f, 0x82, 0xea, 0x0e, 0x00, 0x00, //0x00000fab jb           LBB0_360
	0x44, 0x89, 0xe0, //0x00000fb1 movl         %r12d, %eax
	0x83, 0xe0, 0x3f, //0x00000fb4 andl         $63, %eax
	0x48, 0x89, 0x45, 0xa8, //0x00000fb7 movq         %rax, $-88(%rbp)
	0x4d, 0x29, 0xf8, //0x00000fbb subq         %r15, %r8
	0x49, 0x83, 0xc0, 0xbf, //0x00000fbe addq         $-65, %r8
	0x49, 0x83, 0xe0, 0xc0, //0x00000fc2 andq         $-64, %r8
	0x4d, 0x01, 0xf0, //0x00000fc6 addq         %r14, %r8
	0x4c, 0x03, 0x45, 0x88, //0x00000fc9 addq         $-120(%rbp), %r8
	0x49, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00000fcd movq         $-1, %r9
	0x45, 0x31, 0xd2, //0x00000fd4 xorl         %r10d, %r10d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000fd7 .p2align 4, 0x90
	//0x00000fe0 LBB0_201
	0xc4, 0xc1, 0x7a, 0x6f, 0x13, //0x00000fe0 vmovdqu      (%r11), %xmm2
	0xc4, 0xc1, 0x7a, 0x6f, 0x63, 0x10, //0x00000fe5 vmovdqu      $16(%r11), %xmm4
	0xc4, 0xc1, 0x7a, 0x6f, 0x6b, 0x20, //0x00000feb vmovdqu      $32(%r11), %xmm5
	0xc4, 0xc1, 0x7a, 0x6f, 0x73, 0x30, //0x00000ff1 vmovdqu      $48(%r11), %xmm6
	0xc5, 0xe9, 0x74, 0xf8, //0x00000ff7 vpcmpeqb     %xmm0, %xmm2, %xmm7
	0xc5, 0xf9, 0xd7, 0xff, //0x00000ffb vpmovmskb    %xmm7, %edi
	0xc5, 0xd9, 0x74, 0xf8, //0x00000fff vpcmpeqb     %xmm0, %xmm4, %xmm7
	0xc5, 0xf9, 0xd7, 0xcf, //0x00001003 vpmovmskb    %xmm7, %ecx
	0xc5, 0xd1, 0x74, 0xf8, //0x00001007 vpcmpeqb     %xmm0, %xmm5, %xmm7
	0xc5, 0xf9, 0xd7, 0xf7, //0x0000100b vpmovmskb    %xmm7, %esi
	0xc5, 0xc9, 0x74, 0xf8, //0x0000100f vpcmpeqb     %xmm0, %xmm6, %xmm7
	0xc5, 0x79, 0xd7, 0xef, //0x00001013 vpmovmskb    %xmm7, %r13d
	0xc5, 0xe9, 0x74, 0xd1, //0x00001017 vpcmpeqb     %xmm1, %xmm2, %xmm2
	0xc5, 0xf9, 0xd7, 0xd2, //0x0000101b vpmovmskb    %xmm2, %edx
	0xc5, 0xd9, 0x74, 0xd1, //0x0000101f vpcmpeqb     %xmm1, %xmm4, %xmm2
	0xc5, 0xf9, 0xd7, 0xc2, //0x00001023 vpmovmskb    %xmm2, %eax
	0xc5, 0xd1, 0x74, 0xd1, //0x00001027 vpcmpeqb     %xmm1, %xmm5, %xmm2
	0xc5, 0xf9, 0xd7, 0xda, //0x0000102b vpmovmskb    %xmm2, %ebx
	0xc5, 0xc9, 0x74, 0xd1, //0x0000102f vpcmpeqb     %xmm1, %xmm6, %xmm2
	0xc5, 0x79, 0xd7, 0xf2, //0x00001033 vpmovmskb    %xmm2, %r14d
	0x49, 0xc1, 0xe5, 0x30, //0x00001037 shlq         $48, %r13
	0x48, 0xc1, 0xe6, 0x20, //0x0000103b shlq         $32, %rsi
	0x48, 0xc1, 0xe1, 0x10, //0x0000103f shlq         $16, %rcx
	0x48, 0x09, 0xcf, //0x00001043 orq          %rcx, %rdi
	0x48, 0x09, 0xf7, //0x00001046 orq          %rsi, %rdi
	0x49, 0xc1, 0xe6, 0x30, //0x00001049 shlq         $48, %r14
	0x48, 0xc1, 0xe3, 0x20, //0x0000104d shlq         $32, %rbx
	0x48, 0xc1, 0xe0, 0x10, //0x00001051 shlq         $16, %rax
	0x48, 0x09, 0xc2, //0x00001055 orq          %rax, %rdx
	0x48, 0x09, 0xda, //0x00001058 orq          %rbx, %rdx
	0x4c, 0x09, 0xf2, //0x0000105b orq          %r14, %rdx
	0x49, 0x83, 0xf9, 0xff, //0x0000105e cmpq         $-1, %r9
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x00001062 jne          LBB0_203
	0x48, 0x85, 0xd2, //0x00001068 testq        %rdx, %rdx
	0x0f, 0x85, 0x2f, 0x00, 0x00, 0x00, //0x0000106b jne          LBB0_212
	//0x00001071 LBB0_203
	0x4c, 0x09, 0xef, //0x00001071 orq          %r13, %rdi
	0x48, 0x89, 0xd0, //0x00001074 movq         %rdx, %rax
	0x4c, 0x09, 0xd0, //0x00001077 orq          %r10, %rax
	0x0f, 0x85, 0x3d, 0x00, 0x00, 0x00, //0x0000107a jne          LBB0_213
	//0x00001080 LBB0_204
	0x48, 0x85, 0xff, //0x00001080 testq        %rdi, %rdi
	0x0f, 0x85, 0x86, 0x00, 0x00, 0x00, //0x00001083 jne          LBB0_214
	//0x00001089 LBB0_205
	0x49, 0x83, 0xc4, 0xc0, //0x00001089 addq         $-64, %r12
	0x49, 0x83, 0xc3, 0x40, //0x0000108d addq         $64, %r11
	0x49, 0x83, 0xfc, 0x3f, //0x00001091 cmpq         $63, %r12
	0x0f, 0x87, 0x45, 0xff, 0xff, 0xff, //0x00001095 ja           LBB0_201
	0xe9, 0x4e, 0x0b, 0x00, 0x00, //0x0000109b jmp          LBB0_206
	//0x000010a0 LBB0_212
	0x4c, 0x89, 0xd8, //0x000010a0 movq         %r11, %rax
	0x48, 0x2b, 0x45, 0xd0, //0x000010a3 subq         $-48(%rbp), %rax
	0x4c, 0x0f, 0xbc, 0xca, //0x000010a7 bsfq         %rdx, %r9
	0x49, 0x01, 0xc1, //0x000010ab addq         %rax, %r9
	0x4c, 0x09, 0xef, //0x000010ae orq          %r13, %rdi
	0x48, 0x89, 0xd0, //0x000010b1 movq         %rdx, %rax
	0x4c, 0x09, 0xd0, //0x000010b4 orq          %r10, %rax
	0x0f, 0x84, 0xc3, 0xff, 0xff, 0xff, //0x000010b7 je           LBB0_204
	//0x000010bd LBB0_213
	0x4c, 0x89, 0xd0, //0x000010bd movq         %r10, %rax
	0x48, 0xf7, 0xd0, //0x000010c0 notq         %rax
	0x48, 0x21, 0xd0, //0x000010c3 andq         %rdx, %rax
	0x48, 0x8d, 0x0c, 0x00, //0x000010c6 leaq         (%rax,%rax), %rcx
	0x4c, 0x09, 0xd1, //0x000010ca orq          %r10, %rcx
	0x48, 0x89, 0xce, //0x000010cd movq         %rcx, %rsi
	0x48, 0xf7, 0xd6, //0x000010d0 notq         %rsi
	0x48, 0x21, 0xd6, //0x000010d3 andq         %rdx, %rsi
	0x48, 0xba, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x000010d6 movabsq      $-6148914691236517206, %rdx
	0x48, 0x21, 0xd6, //0x000010e0 andq         %rdx, %rsi
	0x45, 0x31, 0xd2, //0x000010e3 xorl         %r10d, %r10d
	0x48, 0x01, 0xc6, //0x000010e6 addq         %rax, %rsi
	0x41, 0x0f, 0x92, 0xc2, //0x000010e9 setb         %r10b
	0x48, 0x01, 0xf6, //0x000010ed addq         %rsi, %rsi
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x000010f0 movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc6, //0x000010fa xorq         %rax, %rsi
	0x48, 0x21, 0xce, //0x000010fd andq         %rcx, %rsi
	0x48, 0xf7, 0xd6, //0x00001100 notq         %rsi
	0x48, 0x21, 0xf7, //0x00001103 andq         %rsi, %rdi
	0x48, 0x85, 0xff, //0x00001106 testq        %rdi, %rdi
	0x0f, 0x84, 0x7a, 0xff, 0xff, 0xff, //0x00001109 je           LBB0_205
	//0x0000110f LBB0_214
	0x48, 0x0f, 0xbc, 0xc7, //0x0000110f bsfq         %rdi, %rax
	//0x00001113 LBB0_215
	0x4c, 0x03, 0x5d, 0x90, //0x00001113 addq         $-112(%rbp), %r11
	0x49, 0x01, 0xc3, //0x00001117 addq         %rax, %r11
	0x4c, 0x8b, 0x65, 0xd0, //0x0000111a movq         $-48(%rbp), %r12
	0x4c, 0x8b, 0x55, 0xb0, //0x0000111e movq         $-80(%rbp), %r10
	0xe9, 0x9c, 0xfd, 0xff, 0xff, //0x00001122 jmp          LBB0_186
	//0x00001127 LBB0_216
	0x4c, 0x8b, 0x57, 0x08, //0x00001127 movq         $8(%rdi), %r10
	0x4d, 0x29, 0xf2, //0x0000112b subq         %r14, %r10
	0x0f, 0x84, 0xf0, 0x12, 0x00, 0x00, //0x0000112e je           LBB0_429
	0x4c, 0x89, 0x75, 0xb0, //0x00001134 movq         %r14, $-80(%rbp)
	0x4d, 0x01, 0xf4, //0x00001138 addq         %r14, %r12
	0x41, 0x80, 0x3c, 0x24, 0x30, //0x0000113b cmpb         $48, (%r12)
	0x0f, 0x85, 0x36, 0x00, 0x00, 0x00, //0x00001140 jne          LBB0_221
	0x41, 0xbb, 0x01, 0x00, 0x00, 0x00, //0x00001146 movl         $1, %r11d
	0x49, 0x83, 0xfa, 0x01, //0x0000114c cmpq         $1, %r10
	0x0f, 0x84, 0xb1, 0x05, 0x00, 0x00, //0x00001150 je           LBB0_305
	0x41, 0x8a, 0x44, 0x24, 0x01, //0x00001156 movb         $1(%r12), %al
	0x04, 0xd2, //0x0000115b addb         $-46, %al
	0x3c, 0x37, //0x0000115d cmpb         $55, %al
	0x0f, 0x87, 0xa2, 0x05, 0x00, 0x00, //0x0000115f ja           LBB0_305
	0x0f, 0xb6, 0xc0, //0x00001165 movzbl       %al, %eax
	0x48, 0xb9, 0x01, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, //0x00001168 movabsq      $36028797027352577, %rcx
	0x48, 0x0f, 0xa3, 0xc1, //0x00001172 btq          %rax, %rcx
	0x0f, 0x83, 0x8b, 0x05, 0x00, 0x00, //0x00001176 jae          LBB0_305
	//0x0000117c LBB0_221
	0x49, 0x83, 0xfa, 0x10, //0x0000117c cmpq         $16, %r10
	0x0f, 0x82, 0xee, 0x0c, 0x00, 0x00, //0x00001180 jb           LBB0_359
	0x4d, 0x8d, 0x4a, 0xf0, //0x00001186 leaq         $-16(%r10), %r9
	0x4c, 0x89, 0xc8, //0x0000118a movq         %r9, %rax
	0x48, 0x83, 0xe0, 0xf0, //0x0000118d andq         $-16, %rax
	0x4e, 0x8d, 0x44, 0x20, 0x10, //0x00001191 leaq         $16(%rax,%r12), %r8
	0x41, 0x83, 0xe1, 0x0f, //0x00001196 andl         $15, %r9d
	0x48, 0xc7, 0x45, 0xa8, 0xff, 0xff, 0xff, 0xff, //0x0000119a movq         $-1, $-88(%rbp)
	0x48, 0xc7, 0x45, 0xa0, 0xff, 0xff, 0xff, 0xff, //0x000011a2 movq         $-1, $-96(%rbp)
	0x49, 0xc7, 0xc6, 0xff, 0xff, 0xff, 0xff, //0x000011aa movq         $-1, %r14
	0x4d, 0x89, 0xe5, //0x000011b1 movq         %r12, %r13
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000011b4 .p2align 4, 0x90
	//0x000011c0 LBB0_223
	0xc4, 0xc1, 0x7a, 0x6f, 0x55, 0x00, //0x000011c0 vmovdqu      (%r13), %xmm2
	0xc4, 0xc1, 0x69, 0x64, 0xe0, //0x000011c6 vpcmpgtb     %xmm8, %xmm2, %xmm4
	0xc5, 0xb1, 0x64, 0xea, //0x000011cb vpcmpgtb     %xmm2, %xmm9, %xmm5
	0xc5, 0xd9, 0xdb, 0xe5, //0x000011cf vpand        %xmm5, %xmm4, %xmm4
	0xc5, 0xa9, 0x74, 0xea, //0x000011d3 vpcmpeqb     %xmm2, %xmm10, %xmm5
	0xc5, 0xa1, 0x74, 0xf2, //0x000011d7 vpcmpeqb     %xmm2, %xmm11, %xmm6
	0xc5, 0xc9, 0xeb, 0xed, //0x000011db vpor         %xmm5, %xmm6, %xmm5
	0xc5, 0x99, 0xeb, 0xf2, //0x000011df vpor         %xmm2, %xmm12, %xmm6
	0xc5, 0x91, 0x74, 0xd2, //0x000011e3 vpcmpeqb     %xmm2, %xmm13, %xmm2
	0xc5, 0x89, 0x74, 0xf6, //0x000011e7 vpcmpeqb     %xmm6, %xmm14, %xmm6
	0xc5, 0xc9, 0xeb, 0xfa, //0x000011eb vpor         %xmm2, %xmm6, %xmm7
	0xc5, 0xd1, 0xeb, 0xe4, //0x000011ef vpor         %xmm4, %xmm5, %xmm4
	0xc5, 0xc1, 0xeb, 0xe4, //0x000011f3 vpor         %xmm4, %xmm7, %xmm4
	0xc5, 0xf9, 0xd7, 0xc2, //0x000011f7 vpmovmskb    %xmm2, %eax
	0xc5, 0xf9, 0xd7, 0xde, //0x000011fb vpmovmskb    %xmm6, %ebx
	0xc5, 0xf9, 0xd7, 0xd5, //0x000011ff vpmovmskb    %xmm5, %edx
	0xc5, 0xf9, 0xd7, 0xcc, //0x00001203 vpmovmskb    %xmm4, %ecx
	0xbe, 0xaa, 0xaa, 0xaa, 0xaa, //0x00001207 movl         $2863311530, %esi
	0x48, 0x8d, 0xbe, 0x55, 0x55, 0x55, 0x55, //0x0000120c leaq         $1431655765(%rsi), %rdi
	0x48, 0x31, 0xcf, //0x00001213 xorq         %rcx, %rdi
	0x48, 0x0f, 0xbc, 0xcf, //0x00001216 bsfq         %rdi, %rcx
	0x83, 0xf9, 0x10, //0x0000121a cmpl         $16, %ecx
	0x0f, 0x84, 0x11, 0x00, 0x00, 0x00, //0x0000121d je           LBB0_225
	0xbf, 0xff, 0xff, 0xff, 0xff, //0x00001223 movl         $-1, %edi
	0xd3, 0xe7, //0x00001228 shll         %cl, %edi
	0xf7, 0xd7, //0x0000122a notl         %edi
	0x21, 0xf8, //0x0000122c andl         %edi, %eax
	0x21, 0xfb, //0x0000122e andl         %edi, %ebx
	0x21, 0xd7, //0x00001230 andl         %edx, %edi
	0x89, 0xfa, //0x00001232 movl         %edi, %edx
	//0x00001234 LBB0_225
	0x8d, 0x78, 0xff, //0x00001234 leal         $-1(%rax), %edi
	0x21, 0xc7, //0x00001237 andl         %eax, %edi
	0x0f, 0x85, 0x88, 0x09, 0x00, 0x00, //0x00001239 jne          LBB0_337
	0x8d, 0x7b, 0xff, //0x0000123f leal         $-1(%rbx), %edi
	0x21, 0xdf, //0x00001242 andl         %ebx, %edi
	0x0f, 0x85, 0x7d, 0x09, 0x00, 0x00, //0x00001244 jne          LBB0_337
	0x8d, 0x7a, 0xff, //0x0000124a leal         $-1(%rdx), %edi
	0x21, 0xd7, //0x0000124d andl         %edx, %edi
	0x0f, 0x85, 0x72, 0x09, 0x00, 0x00, //0x0000124f jne          LBB0_337
	0x85, 0xc0, //0x00001255 testl        %eax, %eax
	0x0f, 0x84, 0x1a, 0x00, 0x00, 0x00, //0x00001257 je           LBB0_231
	0x4c, 0x89, 0xef, //0x0000125d movq         %r13, %rdi
	0x4c, 0x29, 0xe7, //0x00001260 subq         %r12, %rdi
	0x44, 0x0f, 0xbc, 0xd8, //0x00001263 bsfl         %eax, %r11d
	0x49, 0x01, 0xfb, //0x00001267 addq         %rdi, %r11
	0x49, 0x83, 0xfe, 0xff, //0x0000126a cmpq         $-1, %r14
	0x0f, 0x85, 0x5d, 0x09, 0x00, 0x00, //0x0000126e jne          LBB0_338
	0x4d, 0x89, 0xde, //0x00001274 movq         %r11, %r14
	//0x00001277 LBB0_231
	0x85, 0xdb, //0x00001277 testl        %ebx, %ebx
	0x0f, 0x84, 0x1c, 0x00, 0x00, 0x00, //0x00001279 je           LBB0_234
	0x4c, 0x89, 0xe8, //0x0000127f movq         %r13, %rax
	0x4c, 0x29, 0xe0, //0x00001282 subq         %r12, %rax
	0x44, 0x0f, 0xbc, 0xdb, //0x00001285 bsfl         %ebx, %r11d
	0x49, 0x01, 0xc3, //0x00001289 addq         %rax, %r11
	0x48, 0x83, 0x7d, 0xa0, 0xff, //0x0000128c cmpq         $-1, $-96(%rbp)
	0x0f, 0x85, 0x3a, 0x09, 0x00, 0x00, //0x00001291 jne          LBB0_338
	0x4c, 0x89, 0x5d, 0xa0, //0x00001297 movq         %r11, $-96(%rbp)
	//0x0000129b LBB0_234
	0x85, 0xd2, //0x0000129b testl        %edx, %edx
	0x0f, 0x84, 0x1c, 0x00, 0x00, 0x00, //0x0000129d je           LBB0_237
	0x4c, 0x89, 0xe8, //0x000012a3 movq         %r13, %rax
	0x4c, 0x29, 0xe0, //0x000012a6 subq         %r12, %rax
	0x44, 0x0f, 0xbc, 0xda, //0x000012a9 bsfl         %edx, %r11d
	0x49, 0x01, 0xc3, //0x000012ad addq         %rax, %r11
	0x48, 0x83, 0x7d, 0xa8, 0xff, //0x000012b0 cmpq         $-1, $-88(%rbp)
	0x0f, 0x85, 0x16, 0x09, 0x00, 0x00, //0x000012b5 jne          LBB0_338
	0x4c, 0x89, 0x5d, 0xa8, //0x000012bb movq         %r11, $-88(%rbp)
	//0x000012bf LBB0_237
	0x83, 0xf9, 0x10, //0x000012bf cmpl         $16, %ecx
	0x0f, 0x85, 0xb1, 0x03, 0x00, 0x00, //0x000012c2 jne          LBB0_293
	0x49, 0x83, 0xc5, 0x10, //0x000012c8 addq         $16, %r13
	0x49, 0x83, 0xc2, 0xf0, //0x000012cc addq         $-16, %r10
	0x49, 0x83, 0xfa, 0x0f, //0x000012d0 cmpq         $15, %r10
	0x0f, 0x87, 0xe6, 0xfe, 0xff, 0xff, //0x000012d4 ja           LBB0_223
	0x4d, 0x85, 0xc9, //0x000012da testq        %r9, %r9
	0x48, 0x8d, 0x35, 0xb4, 0x14, 0x00, 0x00, //0x000012dd leaq         $5300(%rip), %rsi  /* LJTI0_2+0(%rip) */
	0x48, 0x8b, 0x5d, 0xa0, //0x000012e4 movq         $-96(%rbp), %rbx
	0x48, 0x8b, 0x4d, 0xa8, //0x000012e8 movq         $-88(%rbp), %rcx
	0x0f, 0x84, 0x95, 0x03, 0x00, 0x00, //0x000012ec je           LBB0_294
	//0x000012f2 LBB0_240
	0x4b, 0x8d, 0x3c, 0x08, //0x000012f2 leaq         (%r8,%r9), %rdi
	0xe9, 0x31, 0x00, 0x00, 0x00, //0x000012f6 jmp          LBB0_244
	//0x000012fb LBB0_241
	0x49, 0x89, 0xc3, //0x000012fb movq         %rax, %r11
	0x4d, 0x29, 0xe3, //0x000012fe subq         %r12, %r11
	0x48, 0x83, 0xf9, 0xff, //0x00001301 cmpq         $-1, %rcx
	0x0f, 0x85, 0x2f, 0x0b, 0x00, 0x00, //0x00001305 jne          LBB0_356
	0x49, 0xff, 0xcb, //0x0000130b decq         %r11
	0x4c, 0x89, 0xd9, //0x0000130e movq         %r11, %rcx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001311 .p2align 4, 0x90
	//0x00001320 LBB0_243
	0x49, 0x89, 0xc0, //0x00001320 movq         %rax, %r8
	0x49, 0xff, 0xc9, //0x00001323 decq         %r9
	0x0f, 0x84, 0x9f, 0x0a, 0x00, 0x00, //0x00001326 je           LBB0_341
	//0x0000132c LBB0_244
	0x41, 0x0f, 0xbe, 0x10, //0x0000132c movsbl       (%r8), %edx
	0x83, 0xc2, 0xd5, //0x00001330 addl         $-43, %edx
	0x83, 0xfa, 0x3a, //0x00001333 cmpl         $58, %edx
	0x0f, 0x87, 0x4b, 0x03, 0x00, 0x00, //0x00001336 ja           LBB0_294
	0x49, 0x8d, 0x40, 0x01, //0x0000133c leaq         $1(%r8), %rax
	0x48, 0x63, 0x14, 0x96, //0x00001340 movslq       (%rsi,%rdx,4), %rdx
	0x48, 0x01, 0xf2, //0x00001344 addq         %rsi, %rdx
	0xff, 0xe2, //0x00001347 jmpq         *%rdx
	//0x00001349 LBB0_246
	0x49, 0x89, 0xc3, //0x00001349 movq         %rax, %r11
	0x4d, 0x29, 0xe3, //0x0000134c subq         %r12, %r11
	0x48, 0x83, 0xfb, 0xff, //0x0000134f cmpq         $-1, %rbx
	0x0f, 0x85, 0xe1, 0x0a, 0x00, 0x00, //0x00001353 jne          LBB0_356
	0x49, 0xff, 0xcb, //0x00001359 decq         %r11
	0x4c, 0x89, 0xdb, //0x0000135c movq         %r11, %rbx
	0xe9, 0xbc, 0xff, 0xff, 0xff, //0x0000135f jmp          LBB0_243
	//0x00001364 LBB0_248
	0x49, 0x89, 0xc3, //0x00001364 movq         %rax, %r11
	0x4d, 0x29, 0xe3, //0x00001367 subq         %r12, %r11
	0x49, 0x83, 0xfe, 0xff, //0x0000136a cmpq         $-1, %r14
	0x0f, 0x85, 0xc6, 0x0a, 0x00, 0x00, //0x0000136e jne          LBB0_356
	0x49, 0xff, 0xcb, //0x00001374 decq         %r11
	0x4d, 0x89, 0xde, //0x00001377 movq         %r11, %r14
	0xe9, 0xa1, 0xff, 0xff, 0xff, //0x0000137a jmp          LBB0_243
	//0x0000137f LBB0_250
	0x48, 0x8b, 0x02, //0x0000137f movq         (%rdx), %rax
	0x48, 0x3d, 0xff, 0x0f, 0x00, 0x00, //0x00001382 cmpq         $4095, %rax
	0x0f, 0x8f, 0x11, 0x10, 0x00, 0x00, //0x00001388 jg           LBB0_432
	0x48, 0x8d, 0x48, 0x01, //0x0000138e leaq         $1(%rax), %rcx
	0x48, 0x89, 0x0a, //0x00001392 movq         %rcx, (%rdx)
	0x48, 0xc7, 0x44, 0xc2, 0x08, 0x05, 0x00, 0x00, 0x00, //0x00001395 movq         $5, $8(%rdx,%rax,8)
	0x4d, 0x89, 0xf3, //0x0000139e movq         %r14, %r11
	0xe9, 0xbd, 0xed, 0xff, 0xff, //0x000013a1 jmp          LBB0_2
	//0x000013a6 LBB0_252
	0x48, 0x8b, 0x4f, 0x08, //0x000013a6 movq         $8(%rdi), %rcx
	0x48, 0x8d, 0x59, 0xfc, //0x000013aa leaq         $-4(%rcx), %rbx
	0x49, 0x39, 0xdf, //0x000013ae cmpq         %rbx, %r15
	0x0f, 0x83, 0x3c, 0x10, 0x00, 0x00, //0x000013b1 jae          LBB0_431
	0x43, 0x8b, 0x0c, 0x34, //0x000013b7 movl         (%r12,%r14), %ecx
	0x81, 0xf9, 0x61, 0x6c, 0x73, 0x65, //0x000013bb cmpl         $1702063201, %ecx
	0x0f, 0x85, 0x6c, 0x10, 0x00, 0x00, //0x000013c1 jne          LBB0_433
	0x4d, 0x8d, 0x5f, 0x05, //0x000013c7 leaq         $5(%r15), %r11
	0xe9, 0x45, 0x00, 0x00, 0x00, //0x000013cb jmp          LBB0_264
	//0x000013d0 LBB0_255
	0x48, 0x8b, 0x4f, 0x08, //0x000013d0 movq         $8(%rdi), %rcx
	0x48, 0x8d, 0x59, 0xfd, //0x000013d4 leaq         $-3(%rcx), %rbx
	0x49, 0x39, 0xdf, //0x000013d8 cmpq         %rbx, %r15
	0x0f, 0x83, 0x12, 0x10, 0x00, 0x00, //0x000013db jae          LBB0_431
	0x41, 0x81, 0x3a, 0x6e, 0x75, 0x6c, 0x6c, //0x000013e1 cmpl         $1819047278, (%r10)
	0x0f, 0x84, 0x23, 0x00, 0x00, 0x00, //0x000013e8 je           LBB0_263
	0xe9, 0x95, 0x10, 0x00, 0x00, //0x000013ee jmp          LBB0_257
	//0x000013f3 LBB0_261
	0x48, 0x8b, 0x4f, 0x08, //0x000013f3 movq         $8(%rdi), %rcx
	0x48, 0x8d, 0x59, 0xfd, //0x000013f7 leaq         $-3(%rcx), %rbx
	0x49, 0x39, 0xdf, //0x000013fb cmpq         %rbx, %r15
	0x0f, 0x83, 0xef, 0x0f, 0x00, 0x00, //0x000013fe jae          LBB0_431
	0x41, 0x81, 0x3a, 0x74, 0x72, 0x75, 0x65, //0x00001404 cmpl         $1702195828, (%r10)
	0x0f, 0x85, 0xc9, 0x10, 0x00, 0x00, //0x0000140b jne          LBB0_438
	//0x00001411 LBB0_263
	0x4d, 0x8d, 0x5f, 0x04, //0x00001411 leaq         $4(%r15), %r11
	//0x00001415 LBB0_264
	0x4c, 0x89, 0x1e, //0x00001415 movq         %r11, (%rsi)
	0x4c, 0x89, 0xf8, //0x00001418 movq         %r15, %rax
	0x4d, 0x85, 0xf6, //0x0000141b testq        %r14, %r14
	0x0f, 0x8f, 0x3f, 0xed, 0xff, 0xff, //0x0000141e jg           LBB0_2
	0xe9, 0xbb, 0x0f, 0x00, 0x00, //0x00001424 jmp          LBB0_424
	//0x00001429 LBB0_265
	0x48, 0x8b, 0x02, //0x00001429 movq         (%rdx), %rax
	0x48, 0x3d, 0xff, 0x0f, 0x00, 0x00, //0x0000142c cmpq         $4095, %rax
	0x0f, 0x8f, 0x67, 0x0f, 0x00, 0x00, //0x00001432 jg           LBB0_432
	0x48, 0x8d, 0x48, 0x01, //0x00001438 leaq         $1(%rax), %rcx
	0x48, 0x89, 0x0a, //0x0000143c movq         %rcx, (%rdx)
	0x48, 0xc7, 0x44, 0xc2, 0x08, 0x06, 0x00, 0x00, 0x00, //0x0000143f movq         $6, $8(%rdx,%rax,8)
	0x4d, 0x89, 0xf3, //0x00001448 movq         %r14, %r11
	0xe9, 0x13, 0xed, 0xff, 0xff, //0x0000144b jmp          LBB0_2
	//0x00001450 LBB0_267
	0x48, 0x85, 0xc0, //0x00001450 testq        %rax, %rax
	0x49, 0x8d, 0x45, 0xff, //0x00001453 leaq         $-1(%r13), %rax
	0x49, 0xf7, 0xd5, //0x00001457 notq         %r13
	0x4c, 0x0f, 0x48, 0xe9, //0x0000145a cmovsq       %rcx, %r13
	0x49, 0x39, 0xc3, //0x0000145e cmpq         %rax, %r11
	0x49, 0x0f, 0x44, 0xcd, //0x00001461 cmoveq       %r13, %rcx
	0x4c, 0x8b, 0x65, 0xd0, //0x00001465 movq         $-48(%rbp), %r12
	0x48, 0x85, 0xc9, //0x00001469 testq        %rcx, %rcx
	0x0f, 0x89, 0x16, 0xf8, 0xff, 0xff, //0x0000146c jns          LBB0_268
	0xe9, 0x5a, 0x0f, 0x00, 0x00, //0x00001472 jmp          LBB0_420
	//0x00001477 LBB0_270
	0x4c, 0x89, 0xc0, //0x00001477 movq         %r8, %rax
	0x4d, 0x89, 0xf2, //0x0000147a movq         %r14, %r10
	0x4c, 0x29, 0xf0, //0x0000147d subq         %r14, %rax
	0x0f, 0x84, 0xd1, 0x10, 0x00, 0x00, //0x00001480 je           LBB0_449
	0x4f, 0x8d, 0x1c, 0x14, //0x00001486 leaq         (%r12,%r10), %r11
	0x48, 0x83, 0xf8, 0x40, //0x0000148a cmpq         $64, %rax
	0x0f, 0x82, 0x20, 0x0a, 0x00, 0x00, //0x0000148e jb           LBB0_361
	0x89, 0xc1, //0x00001494 movl         %eax, %ecx
	0x83, 0xe1, 0x3f, //0x00001496 andl         $63, %ecx
	0x48, 0x89, 0x4d, 0xa8, //0x00001499 movq         %rcx, $-88(%rbp)
	0x4d, 0x89, 0xc4, //0x0000149d movq         %r8, %r12
	0x4d, 0x29, 0xfc, //0x000014a0 subq         %r15, %r12
	0x49, 0x83, 0xc4, 0xbf, //0x000014a3 addq         $-65, %r12
	0x49, 0x83, 0xe4, 0xc0, //0x000014a7 andq         $-64, %r12
	0x4d, 0x01, 0xd4, //0x000014ab addq         %r10, %r12
	0x4c, 0x03, 0x65, 0x88, //0x000014ae addq         $-120(%rbp), %r12
	0x49, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x000014b2 movq         $-1, %r9
	0x45, 0x31, 0xc0, //0x000014b9 xorl         %r8d, %r8d
	0x90, 0x90, 0x90, 0x90, //0x000014bc .p2align 4, 0x90
	//0x000014c0 LBB0_273
	0xc4, 0xc1, 0x7a, 0x6f, 0x33, //0x000014c0 vmovdqu      (%r11), %xmm6
	0xc4, 0xc1, 0x7a, 0x6f, 0x6b, 0x10, //0x000014c5 vmovdqu      $16(%r11), %xmm5
	0xc4, 0xc1, 0x7a, 0x6f, 0x63, 0x20, //0x000014cb vmovdqu      $32(%r11), %xmm4
	0xc4, 0xc1, 0x7a, 0x6f, 0x7b, 0x30, //0x000014d1 vmovdqu      $48(%r11), %xmm7
	0xc5, 0xc9, 0x74, 0xd0, //0x000014d7 vpcmpeqb     %xmm0, %xmm6, %xmm2
	0xc5, 0xf9, 0xd7, 0xf2, //0x000014db vpmovmskb    %xmm2, %esi
	0xc5, 0xd1, 0x74, 0xd0, //0x000014df vpcmpeqb     %xmm0, %xmm5, %xmm2
	0xc5, 0xf9, 0xd7, 0xda, //0x000014e3 vpmovmskb    %xmm2, %ebx
	0xc5, 0xd9, 0x74, 0xd0, //0x000014e7 vpcmpeqb     %xmm0, %xmm4, %xmm2
	0xc5, 0xf9, 0xd7, 0xca, //0x000014eb vpmovmskb    %xmm2, %ecx
	0xc5, 0xc1, 0x74, 0xd0, //0x000014ef vpcmpeqb     %xmm0, %xmm7, %xmm2
	0xc5, 0xf9, 0xd7, 0xfa, //0x000014f3 vpmovmskb    %xmm2, %edi
	0xc5, 0xc9, 0x74, 0xd1, //0x000014f7 vpcmpeqb     %xmm1, %xmm6, %xmm2
	0xc5, 0x79, 0xd7, 0xea, //0x000014fb vpmovmskb    %xmm2, %r13d
	0xc5, 0xd1, 0x74, 0xd1, //0x000014ff vpcmpeqb     %xmm1, %xmm5, %xmm2
	0xc5, 0xf9, 0xd7, 0xd2, //0x00001503 vpmovmskb    %xmm2, %edx
	0xc5, 0xd9, 0x74, 0xd1, //0x00001507 vpcmpeqb     %xmm1, %xmm4, %xmm2
	0x48, 0xc1, 0xe3, 0x10, //0x0000150b shlq         $16, %rbx
	0x48, 0x09, 0xde, //0x0000150f orq          %rbx, %rsi
	0xc5, 0xf9, 0xd7, 0xda, //0x00001512 vpmovmskb    %xmm2, %ebx
	0xc5, 0xc1, 0x74, 0xd1, //0x00001516 vpcmpeqb     %xmm1, %xmm7, %xmm2
	0x48, 0xc1, 0xe1, 0x20, //0x0000151a shlq         $32, %rcx
	0x48, 0x09, 0xce, //0x0000151e orq          %rcx, %rsi
	0xc5, 0xf9, 0xd7, 0xca, //0x00001521 vpmovmskb    %xmm2, %ecx
	0xc5, 0x81, 0x64, 0xd6, //0x00001525 vpcmpgtb     %xmm6, %xmm15, %xmm2
	0xc5, 0xc9, 0x64, 0xf3, //0x00001529 vpcmpgtb     %xmm3, %xmm6, %xmm6
	0xc5, 0xc9, 0xdb, 0xd2, //0x0000152d vpand        %xmm2, %xmm6, %xmm2
	0x48, 0xc1, 0xe2, 0x10, //0x00001531 shlq         $16, %rdx
	0x49, 0x09, 0xd5, //0x00001535 orq          %rdx, %r13
	0xc5, 0xf9, 0xd7, 0xd2, //0x00001538 vpmovmskb    %xmm2, %edx
	0xc5, 0x81, 0x64, 0xd5, //0x0000153c vpcmpgtb     %xmm5, %xmm15, %xmm2
	0xc5, 0xd1, 0x64, 0xeb, //0x00001540 vpcmpgtb     %xmm3, %xmm5, %xmm5
	0xc5, 0xd1, 0xdb, 0xd2, //0x00001544 vpand        %xmm2, %xmm5, %xmm2
	0x48, 0xc1, 0xe3, 0x20, //0x00001548 shlq         $32, %rbx
	0x49, 0x09, 0xdd, //0x0000154c orq          %rbx, %r13
	0xc5, 0xf9, 0xd7, 0xda, //0x0000154f vpmovmskb    %xmm2, %ebx
	0xc5, 0x81, 0x64, 0xd4, //0x00001553 vpcmpgtb     %xmm4, %xmm15, %xmm2
	0xc5, 0xd9, 0x64, 0xe3, //0x00001557 vpcmpgtb     %xmm3, %xmm4, %xmm4
	0xc5, 0xd9, 0xdb, 0xd2, //0x0000155b vpand        %xmm2, %xmm4, %xmm2
	0x48, 0xc1, 0xe1, 0x30, //0x0000155f shlq         $48, %rcx
	0x49, 0x09, 0xcd, //0x00001563 orq          %rcx, %r13
	0xc5, 0xf9, 0xd7, 0xca, //0x00001566 vpmovmskb    %xmm2, %ecx
	0xc5, 0x81, 0x64, 0xd7, //0x0000156a vpcmpgtb     %xmm7, %xmm15, %xmm2
	0xc5, 0xc1, 0x64, 0xe3, //0x0000156e vpcmpgtb     %xmm3, %xmm7, %xmm4
	0xc5, 0xd9, 0xdb, 0xd2, //0x00001572 vpand        %xmm2, %xmm4, %xmm2
	0x48, 0xc1, 0xe3, 0x10, //0x00001576 shlq         $16, %rbx
	0x48, 0x09, 0xda, //0x0000157a orq          %rbx, %rdx
	0xc5, 0x79, 0xd7, 0xf2, //0x0000157d vpmovmskb    %xmm2, %r14d
	0x48, 0xc1, 0xe7, 0x30, //0x00001581 shlq         $48, %rdi
	0x48, 0xc1, 0xe1, 0x20, //0x00001585 shlq         $32, %rcx
	0x49, 0x83, 0xf9, 0xff, //0x00001589 cmpq         $-1, %r9
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x0000158d jne          LBB0_275
	0x4d, 0x85, 0xed, //0x00001593 testq        %r13, %r13
	0x0f, 0x85, 0xa4, 0x00, 0x00, 0x00, //0x00001596 jne          LBB0_289
	//0x0000159c LBB0_275
	0x49, 0xc1, 0xe6, 0x30, //0x0000159c shlq         $48, %r14
	0x48, 0x09, 0xca, //0x000015a0 orq          %rcx, %rdx
	0x48, 0x09, 0xfe, //0x000015a3 orq          %rdi, %rsi
	0x4c, 0x89, 0xe9, //0x000015a6 movq         %r13, %rcx
	0x4c, 0x09, 0xc1, //0x000015a9 orq          %r8, %rcx
	0x0f, 0x85, 0x2c, 0x00, 0x00, 0x00, //0x000015ac jne          LBB0_290
	0x4c, 0x09, 0xf2, //0x000015b2 orq          %r14, %rdx
	0x48, 0x85, 0xf6, //0x000015b5 testq        %rsi, %rsi
	0x0f, 0x85, 0x95, 0x00, 0x00, 0x00, //0x000015b8 jne          LBB0_291
	//0x000015be LBB0_277
	0x48, 0x85, 0xd2, //0x000015be testq        %rdx, %rdx
	0x0f, 0x85, 0x47, 0x0e, 0x00, 0x00, //0x000015c1 jne          LBB0_427
	0x48, 0x83, 0xc0, 0xc0, //0x000015c7 addq         $-64, %rax
	0x49, 0x83, 0xc3, 0x40, //0x000015cb addq         $64, %r11
	0x48, 0x83, 0xf8, 0x3f, //0x000015cf cmpq         $63, %rax
	0x0f, 0x87, 0xe7, 0xfe, 0xff, 0xff, //0x000015d3 ja           LBB0_273
	0xe9, 0x8b, 0x06, 0x00, 0x00, //0x000015d9 jmp          LBB0_279
	//0x000015de LBB0_290
	0x4c, 0x89, 0xc1, //0x000015de movq         %r8, %rcx
	0x48, 0xf7, 0xd1, //0x000015e1 notq         %rcx
	0x4c, 0x21, 0xe9, //0x000015e4 andq         %r13, %rcx
	0x4c, 0x89, 0x55, 0xb0, //0x000015e7 movq         %r10, $-80(%rbp)
	0x4c, 0x8d, 0x14, 0x09, //0x000015eb leaq         (%rcx,%rcx), %r10
	0x4d, 0x09, 0xc2, //0x000015ef orq          %r8, %r10
	0x4c, 0x89, 0xd7, //0x000015f2 movq         %r10, %rdi
	0x48, 0xf7, 0xd7, //0x000015f5 notq         %rdi
	0x4c, 0x21, 0xef, //0x000015f8 andq         %r13, %rdi
	0x48, 0xbb, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x000015fb movabsq      $-6148914691236517206, %rbx
	0x48, 0x21, 0xdf, //0x00001605 andq         %rbx, %rdi
	0x45, 0x31, 0xc0, //0x00001608 xorl         %r8d, %r8d
	0x48, 0x01, 0xcf, //0x0000160b addq         %rcx, %rdi
	0x41, 0x0f, 0x92, 0xc0, //0x0000160e setb         %r8b
	0x48, 0x01, 0xff, //0x00001612 addq         %rdi, %rdi
	0x48, 0xb9, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00001615 movabsq      $6148914691236517205, %rcx
	0x48, 0x31, 0xcf, //0x0000161f xorq         %rcx, %rdi
	0x4c, 0x21, 0xd7, //0x00001622 andq         %r10, %rdi
	0x4c, 0x8b, 0x55, 0xb0, //0x00001625 movq         $-80(%rbp), %r10
	0x48, 0xf7, 0xd7, //0x00001629 notq         %rdi
	0x48, 0x21, 0xfe, //0x0000162c andq         %rdi, %rsi
	0x4c, 0x09, 0xf2, //0x0000162f orq          %r14, %rdx
	0x48, 0x85, 0xf6, //0x00001632 testq        %rsi, %rsi
	0x0f, 0x84, 0x83, 0xff, 0xff, 0xff, //0x00001635 je           LBB0_277
	0xe9, 0x13, 0x00, 0x00, 0x00, //0x0000163b jmp          LBB0_291
	//0x00001640 LBB0_289
	0x4c, 0x89, 0xdb, //0x00001640 movq         %r11, %rbx
	0x48, 0x2b, 0x5d, 0xd0, //0x00001643 subq         $-48(%rbp), %rbx
	0x4d, 0x0f, 0xbc, 0xcd, //0x00001647 bsfq         %r13, %r9
	0x49, 0x01, 0xd9, //0x0000164b addq         %rbx, %r9
	0xe9, 0x49, 0xff, 0xff, 0xff, //0x0000164e jmp          LBB0_275
	//0x00001653 LBB0_291
	0x48, 0x0f, 0xbc, 0xc6, //0x00001653 bsfq         %rsi, %rax
	0x48, 0x85, 0xd2, //0x00001657 testq        %rdx, %rdx
	0x4c, 0x8b, 0x65, 0xd0, //0x0000165a movq         $-48(%rbp), %r12
	0x0f, 0x84, 0xc2, 0x00, 0x00, 0x00, //0x0000165e je           LBB0_306
	0x48, 0x0f, 0xbc, 0xca, //0x00001664 bsfq         %rdx, %rcx
	0x4d, 0x29, 0xe3, //0x00001668 subq         %r12, %r11
	0x48, 0x39, 0xc1, //0x0000166b cmpq         %rax, %rcx
	0x0f, 0x83, 0x4a, 0xf8, 0xff, 0xff, //0x0000166e jae          LBB0_185
	0xe9, 0xe7, 0x0e, 0x00, 0x00, //0x00001674 jmp          LBB0_308
	//0x00001679 LBB0_293
	0x49, 0x01, 0xcd, //0x00001679 addq         %rcx, %r13
	0x4d, 0x89, 0xe8, //0x0000167c movq         %r13, %r8
	0x48, 0x8b, 0x5d, 0xa0, //0x0000167f movq         $-96(%rbp), %rbx
	0x48, 0x8b, 0x4d, 0xa8, //0x00001683 movq         $-88(%rbp), %rcx
	//0x00001687 LBB0_294
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00001687 movq         $-1, %r11
	0x48, 0x85, 0xdb, //0x0000168e testq        %rbx, %rbx
	0x48, 0x8b, 0x75, 0xc8, //0x00001691 movq         $-56(%rbp), %rsi
	0x0f, 0x84, 0x90, 0x0d, 0x00, 0x00, //0x00001695 je           LBB0_430
	//0x0000169b LBB0_295
	0x48, 0x85, 0xc9, //0x0000169b testq        %rcx, %rcx
	0x0f, 0x84, 0x87, 0x0d, 0x00, 0x00, //0x0000169e je           LBB0_430
	0x4d, 0x85, 0xf6, //0x000016a4 testq        %r14, %r14
	0x48, 0x8b, 0x55, 0xb8, //0x000016a7 movq         $-72(%rbp), %rdx
	0x48, 0x8b, 0x7d, 0xc0, //0x000016ab movq         $-64(%rbp), %rdi
	0x0f, 0x84, 0x76, 0x0d, 0x00, 0x00, //0x000016af je           LBB0_430
	0x4d, 0x29, 0xe0, //0x000016b5 subq         %r12, %r8
	0x49, 0x8d, 0x40, 0xff, //0x000016b8 leaq         $-1(%r8), %rax
	0x48, 0x39, 0xc3, //0x000016bc cmpq         %rax, %rbx
	0x0f, 0x84, 0x33, 0x00, 0x00, 0x00, //0x000016bf je           LBB0_303
	0x49, 0x39, 0xc6, //0x000016c5 cmpq         %rax, %r14
	0x0f, 0x84, 0x2a, 0x00, 0x00, 0x00, //0x000016c8 je           LBB0_303
	0x48, 0x39, 0xc1, //0x000016ce cmpq         %rax, %rcx
	0x0f, 0x84, 0x21, 0x00, 0x00, 0x00, //0x000016d1 je           LBB0_303
	0x48, 0x85, 0xc9, //0x000016d7 testq        %rcx, %rcx
	0x0f, 0x8e, 0x71, 0x00, 0x00, 0x00, //0x000016da jle          LBB0_310
	0x48, 0x8d, 0x41, 0xff, //0x000016e0 leaq         $-1(%rcx), %rax
	0x48, 0x39, 0xc3, //0x000016e4 cmpq         %rax, %rbx
	0x0f, 0x84, 0x64, 0x00, 0x00, 0x00, //0x000016e7 je           LBB0_310
	0x48, 0xf7, 0xd1, //0x000016ed notq         %rcx
	0x49, 0x89, 0xcb, //0x000016f0 movq         %rcx, %r11
	0xe9, 0x06, 0x00, 0x00, 0x00, //0x000016f3 jmp          LBB0_304
	//0x000016f8 LBB0_303
	0x49, 0xf7, 0xd8, //0x000016f8 negq         %r8
	0x4d, 0x89, 0xc3, //0x000016fb movq         %r8, %r11
	//0x000016fe LBB0_304
	0x4d, 0x85, 0xdb, //0x000016fe testq        %r11, %r11
	0x0f, 0x88, 0x24, 0x0d, 0x00, 0x00, //0x00001701 js           LBB0_430
	//0x00001707 LBB0_305
	0x48, 0x8b, 0x4d, 0xb0, //0x00001707 movq         $-80(%rbp), %rcx
	0x49, 0x01, 0xcb, //0x0000170b addq         %rcx, %r11
	0x4c, 0x89, 0x1e, //0x0000170e movq         %r11, (%rsi)
	0x4c, 0x89, 0xf8, //0x00001711 movq         %r15, %rax
	0x48, 0x85, 0xc9, //0x00001714 testq        %rcx, %rcx
	0x4c, 0x8b, 0x65, 0xd0, //0x00001717 movq         $-48(%rbp), %r12
	0x0f, 0x8f, 0x42, 0xea, 0xff, 0xff, //0x0000171b jg           LBB0_2
	0xe9, 0xbe, 0x0c, 0x00, 0x00, //0x00001721 jmp          LBB0_424
	//0x00001726 LBB0_306
	0xb9, 0x40, 0x00, 0x00, 0x00, //0x00001726 movl         $64, %ecx
	0x4d, 0x29, 0xe3, //0x0000172b subq         %r12, %r11
	0x48, 0x39, 0xc1, //0x0000172e cmpq         %rax, %rcx
	0x0f, 0x83, 0x87, 0xf7, 0xff, 0xff, //0x00001731 jae          LBB0_185
	0xe9, 0x24, 0x0e, 0x00, 0x00, //0x00001737 jmp          LBB0_308
	//0x0000173c LBB0_309
	0x4d, 0x29, 0xd6, //0x0000173c subq         %r10, %r14
	0x0f, 0xbc, 0xce, //0x0000173f bsfl         %esi, %ecx
	0x4c, 0x01, 0xf1, //0x00001742 addq         %r14, %rcx
	0x48, 0xf7, 0xd1, //0x00001745 notq         %rcx
	0x48, 0x8b, 0x75, 0xc8, //0x00001748 movq         $-56(%rbp), %rsi
	0xe9, 0x22, 0xf5, 0xff, 0xff, //0x0000174c jmp          LBB0_159
	//0x00001751 LBB0_310
	0x4c, 0x89, 0xf0, //0x00001751 movq         %r14, %rax
	0x48, 0x09, 0xd8, //0x00001754 orq          %rbx, %rax
	0x49, 0x39, 0xde, //0x00001757 cmpq         %rbx, %r14
	0x0f, 0x8c, 0x68, 0x01, 0x00, 0x00, //0x0000175a jl           LBB0_313
	0x48, 0x85, 0xc0, //0x00001760 testq        %rax, %rax
	0x0f, 0x88, 0x5f, 0x01, 0x00, 0x00, //0x00001763 js           LBB0_313
	0x49, 0xf7, 0xd6, //0x00001769 notq         %r14
	0x4d, 0x89, 0xf3, //0x0000176c movq         %r14, %r11
	0xe9, 0x8a, 0xff, 0xff, 0xff, //0x0000176f jmp          LBB0_304
	//0x00001774 LBB0_48
	0x4c, 0x8b, 0x5d, 0x80, //0x00001774 movq         $-128(%rbp), %r11
	0x4c, 0x8b, 0x6d, 0xa8, //0x00001778 movq         $-88(%rbp), %r13
	0x49, 0x83, 0xfd, 0x20, //0x0000177c cmpq         $32, %r13
	0x0f, 0x82, 0xeb, 0x07, 0x00, 0x00, //0x00001780 jb           LBB0_366
	//0x00001786 LBB0_49
	0xc4, 0xc1, 0x7a, 0x6f, 0x13, //0x00001786 vmovdqu      (%r11), %xmm2
	0xc4, 0xc1, 0x7a, 0x6f, 0x63, 0x10, //0x0000178b vmovdqu      $16(%r11), %xmm4
	0xc5, 0xe9, 0x74, 0xe8, //0x00001791 vpcmpeqb     %xmm0, %xmm2, %xmm5
	0xc5, 0xf9, 0xd7, 0xcd, //0x00001795 vpmovmskb    %xmm5, %ecx
	0xc5, 0xd9, 0x74, 0xe8, //0x00001799 vpcmpeqb     %xmm0, %xmm4, %xmm5
	0xc5, 0xf9, 0xd7, 0xd5, //0x0000179d vpmovmskb    %xmm5, %edx
	0xc5, 0xe9, 0x74, 0xd1, //0x000017a1 vpcmpeqb     %xmm1, %xmm2, %xmm2
	0xc5, 0xf9, 0xd7, 0xfa, //0x000017a5 vpmovmskb    %xmm2, %edi
	0xc5, 0xd9, 0x74, 0xd1, //0x000017a9 vpcmpeqb     %xmm1, %xmm4, %xmm2
	0xc5, 0xf9, 0xd7, 0xc2, //0x000017ad vpmovmskb    %xmm2, %eax
	0x48, 0xc1, 0xe2, 0x10, //0x000017b1 shlq         $16, %rdx
	0x48, 0xc1, 0xe0, 0x10, //0x000017b5 shlq         $16, %rax
	0x48, 0x09, 0xc7, //0x000017b9 orq          %rax, %rdi
	0x49, 0x83, 0xf9, 0xff, //0x000017bc cmpq         $-1, %r9
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x000017c0 jne          LBB0_51
	0x48, 0x85, 0xff, //0x000017c6 testq        %rdi, %rdi
	0x0f, 0x85, 0x41, 0x07, 0x00, 0x00, //0x000017c9 jne          LBB0_363
	//0x000017cf LBB0_51
	0x48, 0x09, 0xca, //0x000017cf orq          %rcx, %rdx
	0x48, 0x89, 0xf8, //0x000017d2 movq         %rdi, %rax
	0x4c, 0x09, 0xe0, //0x000017d5 orq          %r12, %rax
	0x0f, 0x85, 0x4f, 0x07, 0x00, 0x00, //0x000017d8 jne          LBB0_364
	//0x000017de LBB0_52
	0x48, 0x85, 0xd2, //0x000017de testq        %rdx, %rdx
	0x0f, 0x84, 0x82, 0x07, 0x00, 0x00, //0x000017e1 je           LBB0_365
	//0x000017e7 LBB0_53
	0x48, 0x0f, 0xbc, 0xc2, //0x000017e7 bsfq         %rdx, %rax
	0xe9, 0x43, 0xed, 0xff, 0xff, //0x000017eb jmp          LBB0_57
	//0x000017f0 LBB0_113
	0x4c, 0x8b, 0x5d, 0x80, //0x000017f0 movq         $-128(%rbp), %r11
	0x48, 0x8b, 0x45, 0xa8, //0x000017f4 movq         $-88(%rbp), %rax
	0x4c, 0x8b, 0x65, 0xd0, //0x000017f8 movq         $-48(%rbp), %r12
	0x48, 0x83, 0xf8, 0x20, //0x000017fc cmpq         $32, %rax
	0x0f, 0x82, 0x6f, 0x02, 0x00, 0x00, //0x00001800 jb           LBB0_318
	//0x00001806 LBB0_114
	0xc4, 0xc1, 0x7a, 0x6f, 0x13, //0x00001806 vmovdqu      (%r11), %xmm2
	0xc4, 0xc1, 0x7a, 0x6f, 0x63, 0x10, //0x0000180b vmovdqu      $16(%r11), %xmm4
	0xc5, 0xe9, 0x74, 0xe8, //0x00001811 vpcmpeqb     %xmm0, %xmm2, %xmm5
	0xc5, 0xf9, 0xd7, 0xcd, //0x00001815 vpmovmskb    %xmm5, %ecx
	0xc5, 0xd9, 0x74, 0xe8, //0x00001819 vpcmpeqb     %xmm0, %xmm4, %xmm5
	0xc5, 0xf9, 0xd7, 0xf5, //0x0000181d vpmovmskb    %xmm5, %esi
	0xc5, 0xe9, 0x74, 0xe9, //0x00001821 vpcmpeqb     %xmm1, %xmm2, %xmm5
	0xc5, 0xf9, 0xd7, 0xd5, //0x00001825 vpmovmskb    %xmm5, %edx
	0xc5, 0xd9, 0x74, 0xe9, //0x00001829 vpcmpeqb     %xmm1, %xmm4, %xmm5
	0xc5, 0xf9, 0xd7, 0xdd, //0x0000182d vpmovmskb    %xmm5, %ebx
	0xc5, 0x81, 0x64, 0xea, //0x00001831 vpcmpgtb     %xmm2, %xmm15, %xmm5
	0xc5, 0xe9, 0x64, 0xd3, //0x00001835 vpcmpgtb     %xmm3, %xmm2, %xmm2
	0xc5, 0xe9, 0xdb, 0xd5, //0x00001839 vpand        %xmm5, %xmm2, %xmm2
	0xc5, 0x79, 0xd7, 0xf2, //0x0000183d vpmovmskb    %xmm2, %r14d
	0xc5, 0x81, 0x64, 0xd4, //0x00001841 vpcmpgtb     %xmm4, %xmm15, %xmm2
	0xc5, 0xd9, 0x64, 0xe3, //0x00001845 vpcmpgtb     %xmm3, %xmm4, %xmm4
	0xc5, 0xd9, 0xdb, 0xd2, //0x00001849 vpand        %xmm2, %xmm4, %xmm2
	0xc5, 0xf9, 0xd7, 0xfa, //0x0000184d vpmovmskb    %xmm2, %edi
	0x48, 0xc1, 0xe6, 0x10, //0x00001851 shlq         $16, %rsi
	0x48, 0xc1, 0xe3, 0x10, //0x00001855 shlq         $16, %rbx
	0x48, 0x09, 0xda, //0x00001859 orq          %rbx, %rdx
	0x49, 0x83, 0xf9, 0xff, //0x0000185c cmpq         $-1, %r9
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x00001860 jne          LBB0_116
	0x48, 0x85, 0xd2, //0x00001866 testq        %rdx, %rdx
	0x0f, 0x85, 0x7c, 0x07, 0x00, 0x00, //0x00001869 jne          LBB0_373
	//0x0000186f LBB0_116
	0x48, 0xc1, 0xe7, 0x10, //0x0000186f shlq         $16, %rdi
	0x48, 0x09, 0xce, //0x00001873 orq          %rcx, %rsi
	0x48, 0x89, 0xd1, //0x00001876 movq         %rdx, %rcx
	0x4c, 0x09, 0xc1, //0x00001879 orq          %r8, %rcx
	0x0f, 0x85, 0x4f, 0x06, 0x00, 0x00, //0x0000187c jne          LBB0_362
	//0x00001882 LBB0_117
	0x4c, 0x09, 0xf7, //0x00001882 orq          %r14, %rdi
	0xb9, 0x40, 0x00, 0x00, 0x00, //0x00001885 movl         $64, %ecx
	0xba, 0x40, 0x00, 0x00, 0x00, //0x0000188a movl         $64, %edx
	0x48, 0x85, 0xf6, //0x0000188f testq        %rsi, %rsi
	0x0f, 0x84, 0x04, 0x00, 0x00, 0x00, //0x00001892 je           LBB0_119
	0x48, 0x0f, 0xbc, 0xd6, //0x00001898 bsfq         %rsi, %rdx
	//0x0000189c LBB0_119
	0x48, 0x85, 0xff, //0x0000189c testq        %rdi, %rdi
	0x0f, 0x84, 0x04, 0x00, 0x00, 0x00, //0x0000189f je           LBB0_121
	0x48, 0x0f, 0xbc, 0xcf, //0x000018a5 bsfq         %rdi, %rcx
	//0x000018a9 LBB0_121
	0x48, 0x85, 0xf6, //0x000018a9 testq        %rsi, %rsi
	0x0f, 0x84, 0xb2, 0x01, 0x00, 0x00, //0x000018ac je           LBB0_316
	//0x000018b2 LBB0_122
	0x4d, 0x29, 0xe3, //0x000018b2 subq         %r12, %r11
	0x48, 0x39, 0xd1, //0x000018b5 cmpq         %rdx, %rcx
	0x0f, 0x82, 0xa2, 0x0c, 0x00, 0x00, //0x000018b8 jb           LBB0_308
	0x4d, 0x8d, 0x5c, 0x13, 0x01, //0x000018be leaq         $1(%r11,%rdx), %r11
	0xe9, 0xfb, 0xf5, 0xff, 0xff, //0x000018c3 jmp          LBB0_186
	//0x000018c8 LBB0_313
	0x48, 0x85, 0xc0, //0x000018c8 testq        %rax, %rax
	0x48, 0x8d, 0x43, 0xff, //0x000018cb leaq         $-1(%rbx), %rax
	0x48, 0xf7, 0xd3, //0x000018cf notq         %rbx
	0x49, 0x0f, 0x48, 0xd8, //0x000018d2 cmovsq       %r8, %rbx
	0x49, 0x39, 0xc6, //0x000018d6 cmpq         %rax, %r14
	0x49, 0x0f, 0x45, 0xd8, //0x000018d9 cmovneq      %r8, %rbx
	0x49, 0x89, 0xdb, //0x000018dd movq         %rbx, %r11
	0xe9, 0x19, 0xfe, 0xff, 0xff, //0x000018e0 jmp          LBB0_304
	//0x000018e5 LBB0_314
	0x48, 0xf7, 0xd0, //0x000018e5 notq         %rax
	0x48, 0x89, 0xc1, //0x000018e8 movq         %rax, %rcx
	0x48, 0x8b, 0x75, 0xc8, //0x000018eb movq         $-56(%rbp), %rsi
	0xe9, 0x7f, 0xf3, 0xff, 0xff, //0x000018ef jmp          LBB0_159
	//0x000018f4 LBB0_315
	0x48, 0x89, 0x4d, 0xb0, //0x000018f4 movq         %rcx, $-80(%rbp)
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x000018f8 movq         $-1, %rax
	0x4d, 0x85, 0xed, //0x000018ff testq        %r13, %r13
	0x48, 0x8b, 0x75, 0xc8, //0x00001902 movq         $-56(%rbp), %rsi
	0x0f, 0x85, 0xf5, 0xf2, 0xff, 0xff, //0x00001906 jne          LBB0_150
	0xe9, 0xc3, 0x0a, 0x00, 0x00, //0x0000190c jmp          LBB0_421
	//0x00001911 LBB0_134
	0x4c, 0x8b, 0x5d, 0x80, //0x00001911 movq         $-128(%rbp), %r11
	0x4c, 0x8b, 0x6d, 0xa8, //0x00001915 movq         $-88(%rbp), %r13
	0x49, 0x83, 0xfd, 0x20, //0x00001919 cmpq         $32, %r13
	0x0f, 0x82, 0xda, 0x07, 0x00, 0x00, //0x0000191d jb           LBB0_382
	//0x00001923 LBB0_135
	0xc4, 0xc1, 0x7a, 0x6f, 0x13, //0x00001923 vmovdqu      (%r11), %xmm2
	0xc4, 0xc1, 0x7a, 0x6f, 0x63, 0x10, //0x00001928 vmovdqu      $16(%r11), %xmm4
	0xc5, 0xe9, 0x74, 0xe8, //0x0000192e vpcmpeqb     %xmm0, %xmm2, %xmm5
	0xc5, 0xf9, 0xd7, 0xcd, //0x00001932 vpmovmskb    %xmm5, %ecx
	0xc5, 0xd9, 0x74, 0xe8, //0x00001936 vpcmpeqb     %xmm0, %xmm4, %xmm5
	0xc5, 0xf9, 0xd7, 0xd5, //0x0000193a vpmovmskb    %xmm5, %edx
	0xc5, 0xe9, 0x74, 0xd1, //0x0000193e vpcmpeqb     %xmm1, %xmm2, %xmm2
	0xc5, 0xf9, 0xd7, 0xfa, //0x00001942 vpmovmskb    %xmm2, %edi
	0xc5, 0xd9, 0x74, 0xd1, //0x00001946 vpcmpeqb     %xmm1, %xmm4, %xmm2
	0xc5, 0xf9, 0xd7, 0xc2, //0x0000194a vpmovmskb    %xmm2, %eax
	0x48, 0xc1, 0xe2, 0x10, //0x0000194e shlq         $16, %rdx
	0x48, 0xc1, 0xe0, 0x10, //0x00001952 shlq         $16, %rax
	0x48, 0x09, 0xc7, //0x00001956 orq          %rax, %rdi
	0x49, 0x83, 0xf9, 0xff, //0x00001959 cmpq         $-1, %r9
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x0000195d jne          LBB0_137
	0x48, 0x85, 0xff, //0x00001963 testq        %rdi, %rdi
	0x0f, 0x85, 0x30, 0x07, 0x00, 0x00, //0x00001966 jne          LBB0_379
	//0x0000196c LBB0_137
	0x48, 0x09, 0xca, //0x0000196c orq          %rcx, %rdx
	0x48, 0x89, 0xf8, //0x0000196f movq         %rdi, %rax
	0x4c, 0x09, 0xe0, //0x00001972 orq          %r12, %rax
	0x0f, 0x85, 0x3e, 0x07, 0x00, 0x00, //0x00001975 jne          LBB0_380
	//0x0000197b LBB0_138
	0x48, 0x85, 0xd2, //0x0000197b testq        %rdx, %rdx
	0x0f, 0x84, 0x71, 0x07, 0x00, 0x00, //0x0000197e je           LBB0_381
	//0x00001984 LBB0_139
	0x48, 0x0f, 0xbc, 0xc2, //0x00001984 bsfq         %rdx, %rax
	0xe9, 0x06, 0xf2, 0xff, 0xff, //0x00001988 jmp          LBB0_143
	//0x0000198d LBB0_169
	0x4d, 0x89, 0xe3, //0x0000198d movq         %r12, %r11
	0x48, 0x8b, 0x45, 0xa8, //0x00001990 movq         $-88(%rbp), %rax
	0x4c, 0x8b, 0x65, 0xd0, //0x00001994 movq         $-48(%rbp), %r12
	0x48, 0x83, 0xf8, 0x20, //0x00001998 cmpq         $32, %rax
	0x0f, 0x82, 0x7a, 0x01, 0x00, 0x00, //0x0000199c jb           LBB0_329
	//0x000019a2 LBB0_170
	0xc4, 0xc1, 0x7a, 0x6f, 0x13, //0x000019a2 vmovdqu      (%r11), %xmm2
	0xc4, 0xc1, 0x7a, 0x6f, 0x63, 0x10, //0x000019a7 vmovdqu      $16(%r11), %xmm4
	0xc5, 0xe9, 0x74, 0xe8, //0x000019ad vpcmpeqb     %xmm0, %xmm2, %xmm5
	0xc5, 0xf9, 0xd7, 0xcd, //0x000019b1 vpmovmskb    %xmm5, %ecx
	0xc5, 0xd9, 0x74, 0xe8, //0x000019b5 vpcmpeqb     %xmm0, %xmm4, %xmm5
	0xc5, 0xf9, 0xd7, 0xf5, //0x000019b9 vpmovmskb    %xmm5, %esi
	0xc5, 0xe9, 0x74, 0xe9, //0x000019bd vpcmpeqb     %xmm1, %xmm2, %xmm5
	0xc5, 0xf9, 0xd7, 0xd5, //0x000019c1 vpmovmskb    %xmm5, %edx
	0xc5, 0xd9, 0x74, 0xe9, //0x000019c5 vpcmpeqb     %xmm1, %xmm4, %xmm5
	0xc5, 0xf9, 0xd7, 0xdd, //0x000019c9 vpmovmskb    %xmm5, %ebx
	0xc5, 0x81, 0x64, 0xea, //0x000019cd vpcmpgtb     %xmm2, %xmm15, %xmm5
	0xc5, 0xe9, 0x64, 0xd3, //0x000019d1 vpcmpgtb     %xmm3, %xmm2, %xmm2
	0xc5, 0xe9, 0xdb, 0xd5, //0x000019d5 vpand        %xmm5, %xmm2, %xmm2
	0xc5, 0x79, 0xd7, 0xf2, //0x000019d9 vpmovmskb    %xmm2, %r14d
	0xc5, 0x81, 0x64, 0xd4, //0x000019dd vpcmpgtb     %xmm4, %xmm15, %xmm2
	0xc5, 0xd9, 0x64, 0xe3, //0x000019e1 vpcmpgtb     %xmm3, %xmm4, %xmm4
	0xc5, 0xd9, 0xdb, 0xd2, //0x000019e5 vpand        %xmm2, %xmm4, %xmm2
	0xc5, 0xf9, 0xd7, 0xfa, //0x000019e9 vpmovmskb    %xmm2, %edi
	0x48, 0xc1, 0xe6, 0x10, //0x000019ed shlq         $16, %rsi
	0x48, 0xc1, 0xe3, 0x10, //0x000019f1 shlq         $16, %rbx
	0x48, 0x09, 0xda, //0x000019f5 orq          %rbx, %rdx
	0x49, 0x83, 0xf9, 0xff, //0x000019f8 cmpq         $-1, %r9
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x000019fc jne          LBB0_172
	0x48, 0x85, 0xd2, //0x00001a02 testq        %rdx, %rdx
	0x0f, 0x85, 0x77, 0x07, 0x00, 0x00, //0x00001a05 jne          LBB0_390
	//0x00001a0b LBB0_172
	0x48, 0xc1, 0xe7, 0x10, //0x00001a0b shlq         $16, %rdi
	0x48, 0x09, 0xce, //0x00001a0f orq          %rcx, %rsi
	0x48, 0x89, 0xd1, //0x00001a12 movq         %rdx, %rcx
	0x4c, 0x09, 0xc1, //0x00001a15 orq          %r8, %rcx
	0x0f, 0x85, 0xdf, 0x05, 0x00, 0x00, //0x00001a18 jne          LBB0_374
	//0x00001a1e LBB0_173
	0x4c, 0x09, 0xf7, //0x00001a1e orq          %r14, %rdi
	0xb9, 0x40, 0x00, 0x00, 0x00, //0x00001a21 movl         $64, %ecx
	0xba, 0x40, 0x00, 0x00, 0x00, //0x00001a26 movl         $64, %edx
	0x48, 0x85, 0xf6, //0x00001a2b testq        %rsi, %rsi
	0x0f, 0x84, 0x04, 0x00, 0x00, 0x00, //0x00001a2e je           LBB0_175
	0x48, 0x0f, 0xbc, 0xd6, //0x00001a34 bsfq         %rsi, %rdx
	//0x00001a38 LBB0_175
	0x48, 0x85, 0xff, //0x00001a38 testq        %rdi, %rdi
	0x0f, 0x84, 0x04, 0x00, 0x00, 0x00, //0x00001a3b je           LBB0_177
	0x48, 0x0f, 0xbc, 0xcf, //0x00001a41 bsfq         %rdi, %rcx
	//0x00001a45 LBB0_177
	0x48, 0x85, 0xf6, //0x00001a45 testq        %rsi, %rsi
	0x0f, 0x84, 0xbd, 0x00, 0x00, 0x00, //0x00001a48 je           LBB0_327
	0x4d, 0x29, 0xe3, //0x00001a4e subq         %r12, %r11
	0x48, 0x39, 0xd1, //0x00001a51 cmpq         %rdx, %rcx
	0x0f, 0x82, 0x06, 0x0b, 0x00, 0x00, //0x00001a54 jb           LBB0_308
	0x4d, 0x8d, 0x5c, 0x13, 0x01, //0x00001a5a leaq         $1(%r11,%rdx), %r11
	0xe9, 0xce, 0xf4, 0xff, 0xff, //0x00001a5f jmp          LBB0_193
	//0x00001a64 LBB0_316
	0x48, 0x85, 0xff, //0x00001a64 testq        %rdi, %rdi
	0x0f, 0x85, 0x0e, 0x0b, 0x00, 0x00, //0x00001a67 jne          LBB0_450
	0x49, 0x83, 0xc3, 0x20, //0x00001a6d addq         $32, %r11
	0x48, 0x83, 0xc0, 0xe0, //0x00001a71 addq         $-32, %rax
	//0x00001a75 LBB0_318
	0x4d, 0x85, 0xc0, //0x00001a75 testq        %r8, %r8
	0x0f, 0x85, 0xee, 0x05, 0x00, 0x00, //0x00001a78 jne          LBB0_377
	0x48, 0x85, 0xc0, //0x00001a7e testq        %rax, %rax
	0x0f, 0x84, 0x2e, 0x09, 0x00, 0x00, //0x00001a81 je           LBB0_416
	//0x00001a87 LBB0_320
	0x41, 0x0f, 0xb6, 0x0b, //0x00001a87 movzbl       (%r11), %ecx
	0x80, 0xf9, 0x22, //0x00001a8b cmpb         $34, %cl
	0x0f, 0x84, 0x2e, 0x03, 0x00, 0x00, //0x00001a8e je           LBB0_355
	0x80, 0xf9, 0x5c, //0x00001a94 cmpb         $92, %cl
	0x0f, 0x84, 0x26, 0x00, 0x00, 0x00, //0x00001a97 je           LBB0_324
	0x80, 0xf9, 0x1f, //0x00001a9d cmpb         $31, %cl
	0x0f, 0x86, 0xe1, 0x0a, 0x00, 0x00, //0x00001aa0 jbe          LBB0_451
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00001aa6 movq         $-1, %rcx
	0xba, 0x01, 0x00, 0x00, 0x00, //0x00001aad movl         $1, %edx
	0x49, 0x01, 0xd3, //0x00001ab2 addq         %rdx, %r11
	0x48, 0x01, 0xc8, //0x00001ab5 addq         %rcx, %rax
	0x0f, 0x85, 0xc9, 0xff, 0xff, 0xff, //0x00001ab8 jne          LBB0_320
	0xe9, 0xf2, 0x08, 0x00, 0x00, //0x00001abe jmp          LBB0_416
	//0x00001ac3 LBB0_324
	0x48, 0x83, 0xf8, 0x01, //0x00001ac3 cmpq         $1, %rax
	0x0f, 0x84, 0xe8, 0x08, 0x00, 0x00, //0x00001ac7 je           LBB0_416
	0x4c, 0x89, 0xd9, //0x00001acd movq         %r11, %rcx
	0x4c, 0x8b, 0x65, 0xd0, //0x00001ad0 movq         $-48(%rbp), %r12
	0x4c, 0x29, 0xe1, //0x00001ad4 subq         %r12, %rcx
	0x49, 0x83, 0xf9, 0xff, //0x00001ad7 cmpq         $-1, %r9
	0x4c, 0x0f, 0x44, 0xc9, //0x00001adb cmoveq       %rcx, %r9
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x00001adf movq         $-2, %rcx
	0xba, 0x02, 0x00, 0x00, 0x00, //0x00001ae6 movl         $2, %edx
	0x49, 0x01, 0xd3, //0x00001aeb addq         %rdx, %r11
	0x48, 0x01, 0xc8, //0x00001aee addq         %rcx, %rax
	0x0f, 0x85, 0x90, 0xff, 0xff, 0xff, //0x00001af1 jne          LBB0_320
	0xe9, 0xb9, 0x08, 0x00, 0x00, //0x00001af7 jmp          LBB0_416
	//0x00001afc LBB0_326
	0x48, 0xf7, 0xd6, //0x00001afc notq         %rsi
	0x48, 0x89, 0xf1, //0x00001aff movq         %rsi, %rcx
	0x48, 0x8b, 0x75, 0xc8, //0x00001b02 movq         $-56(%rbp), %rsi
	0xe9, 0x68, 0xf1, 0xff, 0xff, //0x00001b06 jmp          LBB0_159
	//0x00001b0b LBB0_327
	0x48, 0x85, 0xff, //0x00001b0b testq        %rdi, %rdi
	0x0f, 0x85, 0x67, 0x0a, 0x00, 0x00, //0x00001b0e jne          LBB0_450
	0x49, 0x83, 0xc3, 0x20, //0x00001b14 addq         $32, %r11
	0x48, 0x83, 0xc0, 0xe0, //0x00001b18 addq         $-32, %rax
	//0x00001b1c LBB0_329
	0x4d, 0x85, 0xc0, //0x00001b1c testq        %r8, %r8
	0x0f, 0x85, 0x9f, 0x06, 0x00, 0x00, //0x00001b1f jne          LBB0_393
	0x48, 0x85, 0xc0, //0x00001b25 testq        %rax, %rax
	0x0f, 0x84, 0x87, 0x08, 0x00, 0x00, //0x00001b28 je           LBB0_416
	//0x00001b2e LBB0_331
	0x41, 0x0f, 0xb6, 0x0b, //0x00001b2e movzbl       (%r11), %ecx
	0x80, 0xf9, 0x22, //0x00001b32 cmpb         $34, %cl
	0x0f, 0x84, 0xaa, 0x00, 0x00, 0x00, //0x00001b35 je           LBB0_340
	0x80, 0xf9, 0x5c, //0x00001b3b cmpb         $92, %cl
	0x0f, 0x84, 0x26, 0x00, 0x00, 0x00, //0x00001b3e je           LBB0_335
	0x80, 0xf9, 0x1f, //0x00001b44 cmpb         $31, %cl
	0x0f, 0x86, 0x3a, 0x0a, 0x00, 0x00, //0x00001b47 jbe          LBB0_451
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00001b4d movq         $-1, %rcx
	0xba, 0x01, 0x00, 0x00, 0x00, //0x00001b54 movl         $1, %edx
	0x49, 0x01, 0xd3, //0x00001b59 addq         %rdx, %r11
	0x48, 0x01, 0xc8, //0x00001b5c addq         %rcx, %rax
	0x0f, 0x85, 0xc9, 0xff, 0xff, 0xff, //0x00001b5f jne          LBB0_331
	0xe9, 0x4b, 0x08, 0x00, 0x00, //0x00001b65 jmp          LBB0_416
	//0x00001b6a LBB0_335
	0x48, 0x83, 0xf8, 0x01, //0x00001b6a cmpq         $1, %rax
	0x0f, 0x84, 0x41, 0x08, 0x00, 0x00, //0x00001b6e je           LBB0_416
	0x4c, 0x89, 0xd9, //0x00001b74 movq         %r11, %rcx
	0x4c, 0x8b, 0x65, 0xd0, //0x00001b77 movq         $-48(%rbp), %r12
	0x4c, 0x29, 0xe1, //0x00001b7b subq         %r12, %rcx
	0x49, 0x83, 0xf9, 0xff, //0x00001b7e cmpq         $-1, %r9
	0x4c, 0x0f, 0x44, 0xc9, //0x00001b82 cmoveq       %rcx, %r9
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x00001b86 movq         $-2, %rcx
	0xba, 0x02, 0x00, 0x00, 0x00, //0x00001b8d movl         $2, %edx
	0x49, 0x01, 0xd3, //0x00001b92 addq         %rdx, %r11
	0x48, 0x01, 0xc8, //0x00001b95 addq         %rcx, %rax
	0x0f, 0x85, 0x90, 0xff, 0xff, 0xff, //0x00001b98 jne          LBB0_331
	0xe9, 0x12, 0x08, 0x00, 0x00, //0x00001b9e jmp          LBB0_416
	//0x00001ba3 LBB0_419
	0x48, 0xf7, 0xd8, //0x00001ba3 negq         %rax
	0x48, 0x8b, 0x75, 0xc8, //0x00001ba6 movq         $-56(%rbp), %rsi
	0x4c, 0x8b, 0x65, 0xd0, //0x00001baa movq         $-48(%rbp), %r12
	0x48, 0x8b, 0x55, 0xb8, //0x00001bae movq         $-72(%rbp), %rdx
	0x48, 0x8b, 0x7d, 0xc0, //0x00001bb2 movq         $-64(%rbp), %rdi
	0x48, 0x89, 0xc1, //0x00001bb6 movq         %rax, %rcx
	0x48, 0x85, 0xc9, //0x00001bb9 testq        %rcx, %rcx
	0x0f, 0x89, 0xc6, 0xf0, 0xff, 0xff, //0x00001bbc jns          LBB0_268
	0xe9, 0x0a, 0x08, 0x00, 0x00, //0x00001bc2 jmp          LBB0_420
	//0x00001bc7 LBB0_337
	0x4d, 0x29, 0xe5, //0x00001bc7 subq         %r12, %r13
	0x44, 0x0f, 0xbc, 0xdf, //0x00001bca bsfl         %edi, %r11d
	0x4d, 0x01, 0xeb, //0x00001bce addq         %r13, %r11
	//0x00001bd1 LBB0_338
	0x49, 0xf7, 0xd3, //0x00001bd1 notq         %r11
	//0x00001bd4 LBB0_339
	0x48, 0x8b, 0x75, 0xc8, //0x00001bd4 movq         $-56(%rbp), %rsi
	0x48, 0x8b, 0x55, 0xb8, //0x00001bd8 movq         $-72(%rbp), %rdx
	0x48, 0x8b, 0x7d, 0xc0, //0x00001bdc movq         $-64(%rbp), %rdi
	0xe9, 0x19, 0xfb, 0xff, 0xff, //0x00001be0 jmp          LBB0_304
	//0x00001be5 LBB0_340
	0x4c, 0x03, 0x5d, 0x90, //0x00001be5 addq         $-112(%rbp), %r11
	0xe9, 0x44, 0xf3, 0xff, 0xff, //0x00001be9 jmp          LBB0_193
	//0x00001bee LBB0_206
	0x4d, 0x89, 0xc3, //0x00001bee movq         %r8, %r11
	0x4c, 0x8b, 0x65, 0xa8, //0x00001bf1 movq         $-88(%rbp), %r12
	0x49, 0x83, 0xfc, 0x20, //0x00001bf5 cmpq         $32, %r12
	0x0f, 0x82, 0x99, 0x06, 0x00, 0x00, //0x00001bf9 jb           LBB0_399
	//0x00001bff LBB0_207
	0xc4, 0xc1, 0x7a, 0x6f, 0x13, //0x00001bff vmovdqu      (%r11), %xmm2
	0xc4, 0xc1, 0x7a, 0x6f, 0x63, 0x10, //0x00001c04 vmovdqu      $16(%r11), %xmm4
	0xc5, 0xe9, 0x74, 0xe8, //0x00001c0a vpcmpeqb     %xmm0, %xmm2, %xmm5
	0xc5, 0xf9, 0xd7, 0xcd, //0x00001c0e vpmovmskb    %xmm5, %ecx
	0xc5, 0xd9, 0x74, 0xe8, //0x00001c12 vpcmpeqb     %xmm0, %xmm4, %xmm5
	0xc5, 0xf9, 0xd7, 0xd5, //0x00001c16 vpmovmskb    %xmm5, %edx
	0xc5, 0xe9, 0x74, 0xd1, //0x00001c1a vpcmpeqb     %xmm1, %xmm2, %xmm2
	0xc5, 0xf9, 0xd7, 0xfa, //0x00001c1e vpmovmskb    %xmm2, %edi
	0xc5, 0xd9, 0x74, 0xd1, //0x00001c22 vpcmpeqb     %xmm1, %xmm4, %xmm2
	0xc5, 0xf9, 0xd7, 0xc2, //0x00001c26 vpmovmskb    %xmm2, %eax
	0x48, 0xc1, 0xe2, 0x10, //0x00001c2a shlq         $16, %rdx
	0x48, 0xc1, 0xe0, 0x10, //0x00001c2e shlq         $16, %rax
	0x48, 0x09, 0xc7, //0x00001c32 orq          %rax, %rdi
	0x49, 0x83, 0xf9, 0xff, //0x00001c35 cmpq         $-1, %r9
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x00001c39 jne          LBB0_209
	0x48, 0x85, 0xff, //0x00001c3f testq        %rdi, %rdi
	0x0f, 0x85, 0xed, 0x05, 0x00, 0x00, //0x00001c42 jne          LBB0_396
	//0x00001c48 LBB0_209
	0x48, 0x09, 0xca, //0x00001c48 orq          %rcx, %rdx
	0x48, 0x89, 0xf8, //0x00001c4b movq         %rdi, %rax
	0x4c, 0x09, 0xd0, //0x00001c4e orq          %r10, %rax
	0x0f, 0x85, 0xfb, 0x05, 0x00, 0x00, //0x00001c51 jne          LBB0_397
	//0x00001c57 LBB0_210
	0x48, 0x85, 0xd2, //0x00001c57 testq        %rdx, %rdx
	0x0f, 0x84, 0x30, 0x06, 0x00, 0x00, //0x00001c5a je           LBB0_398
	//0x00001c60 LBB0_211
	0x48, 0x0f, 0xbc, 0xc2, //0x00001c60 bsfq         %rdx, %rax
	0xe9, 0xaa, 0xf4, 0xff, 0xff, //0x00001c64 jmp          LBB0_215
	//0x00001c69 LBB0_279
	0x4d, 0x89, 0xe3, //0x00001c69 movq         %r12, %r11
	0x48, 0x8b, 0x45, 0xa8, //0x00001c6c movq         $-88(%rbp), %rax
	0x48, 0x83, 0xf8, 0x20, //0x00001c70 cmpq         $32, %rax
	0x4c, 0x8b, 0x65, 0xd0, //0x00001c74 movq         $-48(%rbp), %r12
	0x0f, 0x82, 0xbd, 0x00, 0x00, 0x00, //0x00001c78 jb           LBB0_345
	//0x00001c7e LBB0_280
	0xc4, 0xc1, 0x7a, 0x6f, 0x13, //0x00001c7e vmovdqu      (%r11), %xmm2
	0xc4, 0xc1, 0x7a, 0x6f, 0x63, 0x10, //0x00001c83 vmovdqu      $16(%r11), %xmm4
	0xc5, 0xe9, 0x74, 0xe8, //0x00001c89 vpcmpeqb     %xmm0, %xmm2, %xmm5
	0xc5, 0xf9, 0xd7, 0xcd, //0x00001c8d vpmovmskb    %xmm5, %ecx
	0xc5, 0xd9, 0x74, 0xe8, //0x00001c91 vpcmpeqb     %xmm0, %xmm4, %xmm5
	0xc5, 0xf9, 0xd7, 0xf5, //0x00001c95 vpmovmskb    %xmm5, %esi
	0xc5, 0xe9, 0x74, 0xe9, //0x00001c99 vpcmpeqb     %xmm1, %xmm2, %xmm5
	0xc5, 0xf9, 0xd7, 0xd5, //0x00001c9d vpmovmskb    %xmm5, %edx
	0xc5, 0xd9, 0x74, 0xe9, //0x00001ca1 vpcmpeqb     %xmm1, %xmm4, %xmm5
	0xc5, 0xf9, 0xd7, 0xdd, //0x00001ca5 vpmovmskb    %xmm5, %ebx
	0xc5, 0x81, 0x64, 0xea, //0x00001ca9 vpcmpgtb     %xmm2, %xmm15, %xmm5
	0xc5, 0xe9, 0x64, 0xd3, //0x00001cad vpcmpgtb     %xmm3, %xmm2, %xmm2
	0xc5, 0xe9, 0xdb, 0xd5, //0x00001cb1 vpand        %xmm5, %xmm2, %xmm2
	0xc5, 0x79, 0xd7, 0xf2, //0x00001cb5 vpmovmskb    %xmm2, %r14d
	0xc5, 0x81, 0x64, 0xd4, //0x00001cb9 vpcmpgtb     %xmm4, %xmm15, %xmm2
	0xc5, 0xd9, 0x64, 0xe3, //0x00001cbd vpcmpgtb     %xmm3, %xmm4, %xmm4
	0xc5, 0xd9, 0xdb, 0xd2, //0x00001cc1 vpand        %xmm2, %xmm4, %xmm2
	0xc5, 0xf9, 0xd7, 0xfa, //0x00001cc5 vpmovmskb    %xmm2, %edi
	0x48, 0xc1, 0xe6, 0x10, //0x00001cc9 shlq         $16, %rsi
	0x48, 0xc1, 0xe3, 0x10, //0x00001ccd shlq         $16, %rbx
	0x48, 0x09, 0xda, //0x00001cd1 orq          %rbx, %rdx
	0x49, 0x83, 0xf9, 0xff, //0x00001cd4 cmpq         $-1, %r9
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x00001cd8 jne          LBB0_282
	0x48, 0x85, 0xd2, //0x00001cde testq        %rdx, %rdx
	0x0f, 0x85, 0x37, 0x06, 0x00, 0x00, //0x00001ce1 jne          LBB0_408
	//0x00001ce7 LBB0_282
	0x48, 0xc1, 0xe7, 0x10, //0x00001ce7 shlq         $16, %rdi
	0x48, 0x09, 0xce, //0x00001ceb orq          %rcx, %rsi
	0x48, 0x89, 0xd1, //0x00001cee movq         %rdx, %rcx
	0x4c, 0x09, 0xc1, //0x00001cf1 orq          %r8, %rcx
	0x0f, 0x85, 0xfa, 0x04, 0x00, 0x00, //0x00001cf4 jne          LBB0_395
	//0x00001cfa LBB0_283
	0x4c, 0x09, 0xf7, //0x00001cfa orq          %r14, %rdi
	0xb9, 0x40, 0x00, 0x00, 0x00, //0x00001cfd movl         $64, %ecx
	0xba, 0x40, 0x00, 0x00, 0x00, //0x00001d02 movl         $64, %edx
	0x48, 0x85, 0xf6, //0x00001d07 testq        %rsi, %rsi
	0x0f, 0x84, 0x04, 0x00, 0x00, 0x00, //0x00001d0a je           LBB0_285
	0x48, 0x0f, 0xbc, 0xd6, //0x00001d10 bsfq         %rsi, %rdx
	//0x00001d14 LBB0_285
	0x48, 0x85, 0xff, //0x00001d14 testq        %rdi, %rdi
	0x0f, 0x84, 0x04, 0x00, 0x00, 0x00, //0x00001d17 je           LBB0_287
	0x48, 0x0f, 0xbc, 0xcf, //0x00001d1d bsfq         %rdi, %rcx
	//0x00001d21 LBB0_287
	0x48, 0x85, 0xf6, //0x00001d21 testq        %rsi, %rsi
	0x0f, 0x85, 0x88, 0xfb, 0xff, 0xff, //0x00001d24 jne          LBB0_122
	0x48, 0x85, 0xff, //0x00001d2a testq        %rdi, %rdi
	0x0f, 0x85, 0x48, 0x08, 0x00, 0x00, //0x00001d2d jne          LBB0_450
	0x49, 0x83, 0xc3, 0x20, //0x00001d33 addq         $32, %r11
	0x48, 0x83, 0xc0, 0xe0, //0x00001d37 addq         $-32, %rax
	//0x00001d3b LBB0_345
	0x4d, 0x85, 0xc0, //0x00001d3b testq        %r8, %r8
	0x0f, 0x85, 0xec, 0x05, 0x00, 0x00, //0x00001d3e jne          LBB0_409
	0x48, 0x85, 0xc0, //0x00001d44 testq        %rax, %rax
	0x0f, 0x84, 0x68, 0x06, 0x00, 0x00, //0x00001d47 je           LBB0_416
	//0x00001d4d LBB0_347
	0x41, 0x0f, 0xb6, 0x0b, //0x00001d4d movzbl       (%r11), %ecx
	0x80, 0xf9, 0x22, //0x00001d51 cmpb         $34, %cl
	0x0f, 0x84, 0x68, 0x00, 0x00, 0x00, //0x00001d54 je           LBB0_355
	0x80, 0xf9, 0x5c, //0x00001d5a cmpb         $92, %cl
	0x0f, 0x84, 0x26, 0x00, 0x00, 0x00, //0x00001d5d je           LBB0_351
	0x80, 0xf9, 0x1f, //0x00001d63 cmpb         $31, %cl
	0x0f, 0x86, 0x1b, 0x08, 0x00, 0x00, //0x00001d66 jbe          LBB0_451
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00001d6c movq         $-1, %rcx
	0xba, 0x01, 0x00, 0x00, 0x00, //0x00001d73 movl         $1, %edx
	0x49, 0x01, 0xd3, //0x00001d78 addq         %rdx, %r11
	0x48, 0x01, 0xc8, //0x00001d7b addq         %rcx, %rax
	0x0f, 0x85, 0xc9, 0xff, 0xff, 0xff, //0x00001d7e jne          LBB0_347
	0xe9, 0x2c, 0x06, 0x00, 0x00, //0x00001d84 jmp          LBB0_416
	//0x00001d89 LBB0_351
	0x48, 0x83, 0xf8, 0x01, //0x00001d89 cmpq         $1, %rax
	0x0f, 0x84, 0x22, 0x06, 0x00, 0x00, //0x00001d8d je           LBB0_416
	0x4c, 0x89, 0xd9, //0x00001d93 movq         %r11, %rcx
	0x4c, 0x8b, 0x65, 0xd0, //0x00001d96 movq         $-48(%rbp), %r12
	0x4c, 0x29, 0xe1, //0x00001d9a subq         %r12, %rcx
	0x49, 0x83, 0xf9, 0xff, //0x00001d9d cmpq         $-1, %r9
	0x4c, 0x0f, 0x44, 0xc9, //0x00001da1 cmoveq       %rcx, %r9
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x00001da5 movq         $-2, %rcx
	0xba, 0x02, 0x00, 0x00, 0x00, //0x00001dac movl         $2, %edx
	0x49, 0x01, 0xd3, //0x00001db1 addq         %rdx, %r11
	0x48, 0x01, 0xc8, //0x00001db4 addq         %rcx, %rax
	0x0f, 0x85, 0x90, 0xff, 0xff, 0xff, //0x00001db7 jne          LBB0_347
	0xe9, 0xf3, 0x05, 0x00, 0x00, //0x00001dbd jmp          LBB0_416
	//0x00001dc2 LBB0_355
	0x4c, 0x03, 0x5d, 0x90, //0x00001dc2 addq         $-112(%rbp), %r11
	0xe9, 0xf8, 0xf0, 0xff, 0xff, //0x00001dc6 jmp          LBB0_186
	//0x00001dcb LBB0_341
	0x49, 0x89, 0xf8, //0x00001dcb movq         %rdi, %r8
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00001dce movq         $-1, %r11
	0x48, 0x85, 0xdb, //0x00001dd5 testq        %rbx, %rbx
	0x48, 0x8b, 0x75, 0xc8, //0x00001dd8 movq         $-56(%rbp), %rsi
	0x0f, 0x85, 0xb9, 0xf8, 0xff, 0xff, //0x00001ddc jne          LBB0_295
	0xe9, 0x44, 0x06, 0x00, 0x00, //0x00001de2 jmp          LBB0_430
	//0x00001de7 LBB0_342
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00001de7 movq         $-1, %r11
	0x4c, 0x89, 0x55, 0xb0, //0x00001dee movq         %r10, $-80(%rbp)
	0x4c, 0x89, 0xcf, //0x00001df2 movq         %r9, %rdi
	0x49, 0xc7, 0xc5, 0xff, 0xff, 0xff, 0xff, //0x00001df5 movq         $-1, %r13
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x00001dfc movq         $-1, %r12
	0xe9, 0x68, 0xe9, 0xff, 0xff, //0x00001e03 jmp          LBB0_92
	//0x00001e08 LBB0_353
	0x49, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00001e08 movq         $-1, %r9
	0x45, 0x31, 0xe4, //0x00001e0f xorl         %r12d, %r12d
	0x49, 0x83, 0xfd, 0x20, //0x00001e12 cmpq         $32, %r13
	0x0f, 0x83, 0x6a, 0xf9, 0xff, 0xff, //0x00001e16 jae          LBB0_49
	0xe9, 0x50, 0x01, 0x00, 0x00, //0x00001e1c jmp          LBB0_366
	//0x00001e21 LBB0_354
	0x49, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00001e21 movq         $-1, %r9
	0x45, 0x31, 0xc0, //0x00001e28 xorl         %r8d, %r8d
	0x48, 0x83, 0xf8, 0x20, //0x00001e2b cmpq         $32, %rax
	0x0f, 0x83, 0xd1, 0xf9, 0xff, 0xff, //0x00001e2f jae          LBB0_114
	0xe9, 0x3b, 0xfc, 0xff, 0xff, //0x00001e35 jmp          LBB0_318
	//0x00001e3a LBB0_356
	0x49, 0xf7, 0xdb, //0x00001e3a negq         %r11
	0xe9, 0x92, 0xfd, 0xff, 0xff, //0x00001e3d jmp          LBB0_339
	//0x00001e42 LBB0_357
	0x49, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00001e42 movq         $-1, %r9
	0x45, 0x31, 0xe4, //0x00001e49 xorl         %r12d, %r12d
	0x49, 0x83, 0xfd, 0x20, //0x00001e4c cmpq         $32, %r13
	0x0f, 0x83, 0xcd, 0xfa, 0xff, 0xff, //0x00001e50 jae          LBB0_135
	0xe9, 0xa2, 0x02, 0x00, 0x00, //0x00001e56 jmp          LBB0_382
	//0x00001e5b LBB0_358
	0x49, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00001e5b movq         $-1, %r9
	0x45, 0x31, 0xc0, //0x00001e62 xorl         %r8d, %r8d
	0x48, 0x83, 0xf8, 0x20, //0x00001e65 cmpq         $32, %rax
	0x0f, 0x83, 0x33, 0xfb, 0xff, 0xff, //0x00001e69 jae          LBB0_170
	0xe9, 0xa8, 0xfc, 0xff, 0xff, //0x00001e6f jmp          LBB0_329
	//0x00001e74 LBB0_359
	0x49, 0xc7, 0xc6, 0xff, 0xff, 0xff, 0xff, //0x00001e74 movq         $-1, %r14
	0x4d, 0x89, 0xe0, //0x00001e7b movq         %r12, %r8
	0x4d, 0x89, 0xd1, //0x00001e7e movq         %r10, %r9
	0x48, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00001e81 movq         $-1, %rbx
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00001e88 movq         $-1, %rcx
	0x48, 0x8d, 0x35, 0x02, 0x09, 0x00, 0x00, //0x00001e8f leaq         $2306(%rip), %rsi  /* LJTI0_2+0(%rip) */
	0xe9, 0x57, 0xf4, 0xff, 0xff, //0x00001e96 jmp          LBB0_240
	//0x00001e9b LBB0_360
	0x49, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00001e9b movq         $-1, %r9
	0x45, 0x31, 0xd2, //0x00001ea2 xorl         %r10d, %r10d
	0x49, 0x83, 0xfc, 0x20, //0x00001ea5 cmpq         $32, %r12
	0x0f, 0x83, 0x50, 0xfd, 0xff, 0xff, //0x00001ea9 jae          LBB0_207
	0xe9, 0xe4, 0x03, 0x00, 0x00, //0x00001eaf jmp          LBB0_399
	//0x00001eb4 LBB0_361
	0x49, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00001eb4 movq         $-1, %r9
	0x45, 0x31, 0xc0, //0x00001ebb xorl         %r8d, %r8d
	0x48, 0x83, 0xf8, 0x20, //0x00001ebe cmpq         $32, %rax
	0x4c, 0x8b, 0x65, 0xd0, //0x00001ec2 movq         $-48(%rbp), %r12
	0x0f, 0x83, 0xb2, 0xfd, 0xff, 0xff, //0x00001ec6 jae          LBB0_280
	0xe9, 0x6a, 0xfe, 0xff, 0xff, //0x00001ecc jmp          LBB0_345
	//0x00001ed1 LBB0_362
	0x44, 0x89, 0xc1, //0x00001ed1 movl         %r8d, %ecx
	0xf7, 0xd1, //0x00001ed4 notl         %ecx
	0x21, 0xd1, //0x00001ed6 andl         %edx, %ecx
	0x44, 0x8d, 0x24, 0x09, //0x00001ed8 leal         (%rcx,%rcx), %r12d
	0x45, 0x09, 0xc4, //0x00001edc orl          %r8d, %r12d
	0xbb, 0xaa, 0xaa, 0xaa, 0xaa, //0x00001edf movl         $2863311530, %ebx
	0x44, 0x31, 0xe3, //0x00001ee4 xorl         %r12d, %ebx
	0x21, 0xd3, //0x00001ee7 andl         %edx, %ebx
	0x81, 0xe3, 0xaa, 0xaa, 0xaa, 0xaa, //0x00001ee9 andl         $-1431655766, %ebx
	0x45, 0x31, 0xc0, //0x00001eef xorl         %r8d, %r8d
	0x01, 0xcb, //0x00001ef2 addl         %ecx, %ebx
	0x41, 0x0f, 0x92, 0xc0, //0x00001ef4 setb         %r8b
	0x01, 0xdb, //0x00001ef8 addl         %ebx, %ebx
	0x81, 0xf3, 0x55, 0x55, 0x55, 0x55, //0x00001efa xorl         $1431655765, %ebx
	0x44, 0x21, 0xe3, //0x00001f00 andl         %r12d, %ebx
	0x4c, 0x8b, 0x65, 0xd0, //0x00001f03 movq         $-48(%rbp), %r12
	0xf7, 0xd3, //0x00001f07 notl         %ebx
	0x21, 0xde, //0x00001f09 andl         %ebx, %esi
	0xe9, 0x72, 0xf9, 0xff, 0xff, //0x00001f0b jmp          LBB0_117
	//0x00001f10 LBB0_363
	0x4c, 0x89, 0xd8, //0x00001f10 movq         %r11, %rax
	0x48, 0x2b, 0x45, 0xd0, //0x00001f13 subq         $-48(%rbp), %rax
	0x4c, 0x0f, 0xbc, 0xcf, //0x00001f17 bsfq         %rdi, %r9
	0x49, 0x01, 0xc1, //0x00001f1b addq         %rax, %r9
	0x48, 0x09, 0xca, //0x00001f1e orq          %rcx, %rdx
	0x48, 0x89, 0xf8, //0x00001f21 movq         %rdi, %rax
	0x4c, 0x09, 0xe0, //0x00001f24 orq          %r12, %rax
	0x0f, 0x84, 0xb1, 0xf8, 0xff, 0xff, //0x00001f27 je           LBB0_52
	//0x00001f2d LBB0_364
	0x44, 0x89, 0xe0, //0x00001f2d movl         %r12d, %eax
	0xf7, 0xd0, //0x00001f30 notl         %eax
	0x21, 0xf8, //0x00001f32 andl         %edi, %eax
	0x8d, 0x0c, 0x00, //0x00001f34 leal         (%rax,%rax), %ecx
	0x44, 0x09, 0xe1, //0x00001f37 orl          %r12d, %ecx
	0xbe, 0xaa, 0xaa, 0xaa, 0xaa, //0x00001f3a movl         $2863311530, %esi
	0x31, 0xce, //0x00001f3f xorl         %ecx, %esi
	0x21, 0xfe, //0x00001f41 andl         %edi, %esi
	0x81, 0xe6, 0xaa, 0xaa, 0xaa, 0xaa, //0x00001f43 andl         $-1431655766, %esi
	0x45, 0x31, 0xe4, //0x00001f49 xorl         %r12d, %r12d
	0x01, 0xc6, //0x00001f4c addl         %eax, %esi
	0x41, 0x0f, 0x92, 0xc4, //0x00001f4e setb         %r12b
	0x01, 0xf6, //0x00001f52 addl         %esi, %esi
	0x81, 0xf6, 0x55, 0x55, 0x55, 0x55, //0x00001f54 xorl         $1431655765, %esi
	0x21, 0xce, //0x00001f5a andl         %ecx, %esi
	0xf7, 0xd6, //0x00001f5c notl         %esi
	0x21, 0xf2, //0x00001f5e andl         %esi, %edx
	0x48, 0x85, 0xd2, //0x00001f60 testq        %rdx, %rdx
	0x0f, 0x85, 0x7e, 0xf8, 0xff, 0xff, //0x00001f63 jne          LBB0_53
	//0x00001f69 LBB0_365
	0x49, 0x83, 0xc3, 0x20, //0x00001f69 addq         $32, %r11
	0x49, 0x83, 0xc5, 0xe0, //0x00001f6d addq         $-32, %r13
	//0x00001f71 LBB0_366
	0x4d, 0x85, 0xe4, //0x00001f71 testq        %r12, %r12
	0x0f, 0x85, 0xc2, 0x00, 0x00, 0x00, //0x00001f74 jne          LBB0_375
	0x4c, 0x8b, 0x65, 0xd0, //0x00001f7a movq         $-48(%rbp), %r12
	0x4d, 0x85, 0xed, //0x00001f7e testq        %r13, %r13
	0x0f, 0x84, 0x2e, 0x04, 0x00, 0x00, //0x00001f81 je           LBB0_416
	//0x00001f87 LBB0_368
	0x49, 0x8d, 0x4b, 0x01, //0x00001f87 leaq         $1(%r11), %rcx
	0x41, 0x0f, 0xb6, 0x1b, //0x00001f8b movzbl       (%r11), %ebx
	0x80, 0xfb, 0x22, //0x00001f8f cmpb         $34, %bl
	0x0f, 0x84, 0x7b, 0x03, 0x00, 0x00, //0x00001f92 je           LBB0_407
	0x49, 0x8d, 0x55, 0xff, //0x00001f98 leaq         $-1(%r13), %rdx
	0x80, 0xfb, 0x5c, //0x00001f9c cmpb         $92, %bl
	0x0f, 0x84, 0x14, 0x00, 0x00, 0x00, //0x00001f9f je           LBB0_371
	0x49, 0x89, 0xd5, //0x00001fa5 movq         %rdx, %r13
	0x49, 0x89, 0xcb, //0x00001fa8 movq         %rcx, %r11
	0x48, 0x85, 0xd2, //0x00001fab testq        %rdx, %rdx
	0x0f, 0x85, 0xd3, 0xff, 0xff, 0xff, //0x00001fae jne          LBB0_368
	0xe9, 0xfc, 0x03, 0x00, 0x00, //0x00001fb4 jmp          LBB0_416
	//0x00001fb9 LBB0_371
	0x48, 0x85, 0xd2, //0x00001fb9 testq        %rdx, %rdx
	0x0f, 0x84, 0xf3, 0x03, 0x00, 0x00, //0x00001fbc je           LBB0_416
	0x48, 0x03, 0x4d, 0x98, //0x00001fc2 addq         $-104(%rbp), %rcx
	0x49, 0x83, 0xf9, 0xff, //0x00001fc6 cmpq         $-1, %r9
	0x4c, 0x0f, 0x44, 0xc9, //0x00001fca cmoveq       %rcx, %r9
	0x49, 0x83, 0xc3, 0x02, //0x00001fce addq         $2, %r11
	0x49, 0x83, 0xc5, 0xfe, //0x00001fd2 addq         $-2, %r13
	0x4c, 0x89, 0xea, //0x00001fd6 movq         %r13, %rdx
	0x4c, 0x8b, 0x65, 0xd0, //0x00001fd9 movq         $-48(%rbp), %r12
	0x48, 0x85, 0xd2, //0x00001fdd testq        %rdx, %rdx
	0x0f, 0x85, 0xa1, 0xff, 0xff, 0xff, //0x00001fe0 jne          LBB0_368
	0xe9, 0xca, 0x03, 0x00, 0x00, //0x00001fe6 jmp          LBB0_416
	//0x00001feb LBB0_373
	0x4c, 0x89, 0xdb, //0x00001feb movq         %r11, %rbx
	0x4c, 0x29, 0xe3, //0x00001fee subq         %r12, %rbx
	0x4c, 0x0f, 0xbc, 0xca, //0x00001ff1 bsfq         %rdx, %r9
	0x49, 0x01, 0xd9, //0x00001ff5 addq         %rbx, %r9
	0xe9, 0x72, 0xf8, 0xff, 0xff, //0x00001ff8 jmp          LBB0_116
	//0x00001ffd LBB0_374
	0x44, 0x89, 0xc1, //0x00001ffd movl         %r8d, %ecx
	0xf7, 0xd1, //0x00002000 notl         %ecx
	0x21, 0xd1, //0x00002002 andl         %edx, %ecx
	0x44, 0x8d, 0x24, 0x09, //0x00002004 leal         (%rcx,%rcx), %r12d
	0x45, 0x09, 0xc4, //0x00002008 orl          %r8d, %r12d
	0xbb, 0xaa, 0xaa, 0xaa, 0xaa, //0x0000200b movl         $2863311530, %ebx
	0x44, 0x31, 0xe3, //0x00002010 xorl         %r12d, %ebx
	0x21, 0xd3, //0x00002013 andl         %edx, %ebx
	0x81, 0xe3, 0xaa, 0xaa, 0xaa, 0xaa, //0x00002015 andl         $-1431655766, %ebx
	0x45, 0x31, 0xc0, //0x0000201b xorl         %r8d, %r8d
	0x01, 0xcb, //0x0000201e addl         %ecx, %ebx
	0x41, 0x0f, 0x92, 0xc0, //0x00002020 setb         %r8b
	0x01, 0xdb, //0x00002024 addl         %ebx, %ebx
	0x81, 0xf3, 0x55, 0x55, 0x55, 0x55, //0x00002026 xorl         $1431655765, %ebx
	0x44, 0x21, 0xe3, //0x0000202c andl         %r12d, %ebx
	0x4c, 0x8b, 0x65, 0xd0, //0x0000202f movq         $-48(%rbp), %r12
	0xf7, 0xd3, //0x00002033 notl         %ebx
	0x21, 0xde, //0x00002035 andl         %ebx, %esi
	0xe9, 0xe2, 0xf9, 0xff, 0xff, //0x00002037 jmp          LBB0_173
	//0x0000203c LBB0_375
	0x4d, 0x85, 0xed, //0x0000203c testq        %r13, %r13
	0x0f, 0x84, 0x70, 0x03, 0x00, 0x00, //0x0000203f je           LBB0_416
	0x48, 0x8b, 0x45, 0x98, //0x00002045 movq         $-104(%rbp), %rax
	0x4c, 0x01, 0xd8, //0x00002049 addq         %r11, %rax
	0x49, 0x83, 0xf9, 0xff, //0x0000204c cmpq         $-1, %r9
	0x4c, 0x0f, 0x44, 0xc8, //0x00002050 cmoveq       %rax, %r9
	0x49, 0xff, 0xc3, //0x00002054 incq         %r11
	0x49, 0xff, 0xcd, //0x00002057 decq         %r13
	0x4c, 0x8b, 0x65, 0xd0, //0x0000205a movq         $-48(%rbp), %r12
	0x4d, 0x85, 0xed, //0x0000205e testq        %r13, %r13
	0x0f, 0x85, 0x20, 0xff, 0xff, 0xff, //0x00002061 jne          LBB0_368
	0xe9, 0x49, 0x03, 0x00, 0x00, //0x00002067 jmp          LBB0_416
	//0x0000206c LBB0_377
	0x48, 0x85, 0xc0, //0x0000206c testq        %rax, %rax
	0x0f, 0x84, 0x40, 0x03, 0x00, 0x00, //0x0000206f je           LBB0_416
	0x48, 0x8b, 0x4d, 0x98, //0x00002075 movq         $-104(%rbp), %rcx
	0x4c, 0x01, 0xd9, //0x00002079 addq         %r11, %rcx
	0x49, 0x83, 0xf9, 0xff, //0x0000207c cmpq         $-1, %r9
	0x4c, 0x0f, 0x44, 0xc9, //0x00002080 cmoveq       %rcx, %r9
	0x49, 0xff, 0xc3, //0x00002084 incq         %r11
	0x48, 0xff, 0xc8, //0x00002087 decq         %rax
	0x4c, 0x8b, 0x65, 0xd0, //0x0000208a movq         $-48(%rbp), %r12
	0x48, 0x85, 0xc0, //0x0000208e testq        %rax, %rax
	0x0f, 0x85, 0xf0, 0xf9, 0xff, 0xff, //0x00002091 jne          LBB0_320
	0xe9, 0x19, 0x03, 0x00, 0x00, //0x00002097 jmp          LBB0_416
	//0x0000209c LBB0_379
	0x4c, 0x89, 0xd8, //0x0000209c movq         %r11, %rax
	0x48, 0x2b, 0x45, 0xd0, //0x0000209f subq         $-48(%rbp), %rax
	0x4c, 0x0f, 0xbc, 0xcf, //0x000020a3 bsfq         %rdi, %r9
	0x49, 0x01, 0xc1, //0x000020a7 addq         %rax, %r9
	0x48, 0x09, 0xca, //0x000020aa orq          %rcx, %rdx
	0x48, 0x89, 0xf8, //0x000020ad movq         %rdi, %rax
	0x4c, 0x09, 0xe0, //0x000020b0 orq          %r12, %rax
	0x0f, 0x84, 0xc2, 0xf8, 0xff, 0xff, //0x000020b3 je           LBB0_138
	//0x000020b9 LBB0_380
	0x44, 0x89, 0xe0, //0x000020b9 movl         %r12d, %eax
	0xf7, 0xd0, //0x000020bc notl         %eax
	0x21, 0xf8, //0x000020be andl         %edi, %eax
	0x8d, 0x0c, 0x00, //0x000020c0 leal         (%rax,%rax), %ecx
	0x44, 0x09, 0xe1, //0x000020c3 orl          %r12d, %ecx
	0xbe, 0xaa, 0xaa, 0xaa, 0xaa, //0x000020c6 movl         $2863311530, %esi
	0x31, 0xce, //0x000020cb xorl         %ecx, %esi
	0x21, 0xfe, //0x000020cd andl         %edi, %esi
	0x81, 0xe6, 0xaa, 0xaa, 0xaa, 0xaa, //0x000020cf andl         $-1431655766, %esi
	0x45, 0x31, 0xe4, //0x000020d5 xorl         %r12d, %r12d
	0x01, 0xc6, //0x000020d8 addl         %eax, %esi
	0x41, 0x0f, 0x92, 0xc4, //0x000020da setb         %r12b
	0x01, 0xf6, //0x000020de addl         %esi, %esi
	0x81, 0xf6, 0x55, 0x55, 0x55, 0x55, //0x000020e0 xorl         $1431655765, %esi
	0x21, 0xce, //0x000020e6 andl         %ecx, %esi
	0xf7, 0xd6, //0x000020e8 notl         %esi
	0x21, 0xf2, //0x000020ea andl         %esi, %edx
	0x48, 0x85, 0xd2, //0x000020ec testq        %rdx, %rdx
	0x0f, 0x85, 0x8f, 0xf8, 0xff, 0xff, //0x000020ef jne          LBB0_139
	//0x000020f5 LBB0_381
	0x49, 0x83, 0xc3, 0x20, //0x000020f5 addq         $32, %r11
	0x49, 0x83, 0xc5, 0xe0, //0x000020f9 addq         $-32, %r13
	//0x000020fd LBB0_382
	0x4d, 0x85, 0xe4, //0x000020fd testq        %r12, %r12
	0x0f, 0x85, 0x8e, 0x00, 0x00, 0x00, //0x00002100 jne          LBB0_391
	0x4c, 0x8b, 0x65, 0xd0, //0x00002106 movq         $-48(%rbp), %r12
	0x4d, 0x85, 0xed, //0x0000210a testq        %r13, %r13
	0x0f, 0x84, 0xa2, 0x02, 0x00, 0x00, //0x0000210d je           LBB0_416
	//0x00002113 LBB0_384
	0x49, 0x8d, 0x4b, 0x01, //0x00002113 leaq         $1(%r11), %rcx
	0x41, 0x0f, 0xb6, 0x1b, //0x00002117 movzbl       (%r11), %ebx
	0x80, 0xfb, 0x22, //0x0000211b cmpb         $34, %bl
	0x0f, 0x84, 0x53, 0x00, 0x00, 0x00, //0x0000211e je           LBB0_389
	0x49, 0x8d, 0x55, 0xff, //0x00002124 leaq         $-1(%r13), %rdx
	0x80, 0xfb, 0x5c, //0x00002128 cmpb         $92, %bl
	0x0f, 0x84, 0x14, 0x00, 0x00, 0x00, //0x0000212b je           LBB0_387
	0x49, 0x89, 0xd5, //0x00002131 movq         %rdx, %r13
	0x49, 0x89, 0xcb, //0x00002134 movq         %rcx, %r11
	0x48, 0x85, 0xd2, //0x00002137 testq        %rdx, %rdx
	0x0f, 0x85, 0xd3, 0xff, 0xff, 0xff, //0x0000213a jne          LBB0_384
	0xe9, 0x70, 0x02, 0x00, 0x00, //0x00002140 jmp          LBB0_416
	//0x00002145 LBB0_387
	0x48, 0x85, 0xd2, //0x00002145 testq        %rdx, %rdx
	0x0f, 0x84, 0x67, 0x02, 0x00, 0x00, //0x00002148 je           LBB0_416
	0x48, 0x03, 0x4d, 0x98, //0x0000214e addq         $-104(%rbp), %rcx
	0x49, 0x83, 0xf9, 0xff, //0x00002152 cmpq         $-1, %r9
	0x4c, 0x0f, 0x44, 0xc9, //0x00002156 cmoveq       %rcx, %r9
	0x49, 0x83, 0xc3, 0x02, //0x0000215a addq         $2, %r11
	0x49, 0x83, 0xc5, 0xfe, //0x0000215e addq         $-2, %r13
	0x4c, 0x89, 0xea, //0x00002162 movq         %r13, %rdx
	0x4c, 0x8b, 0x65, 0xd0, //0x00002165 movq         $-48(%rbp), %r12
	0x48, 0x85, 0xd2, //0x00002169 testq        %rdx, %rdx
	0x0f, 0x85, 0xa1, 0xff, 0xff, 0xff, //0x0000216c jne          LBB0_384
	0xe9, 0x3e, 0x02, 0x00, 0x00, //0x00002172 jmp          LBB0_416
	//0x00002177 LBB0_389
	0x4c, 0x29, 0xe1, //0x00002177 subq         %r12, %rcx
	0x49, 0x89, 0xcb, //0x0000217a movq         %rcx, %r11
	0xe9, 0xb0, 0xed, 0xff, 0xff, //0x0000217d jmp          LBB0_193
	//0x00002182 LBB0_390
	0x4c, 0x89, 0xdb, //0x00002182 movq         %r11, %rbx
	0x4c, 0x29, 0xe3, //0x00002185 subq         %r12, %rbx
	0x4c, 0x0f, 0xbc, 0xca, //0x00002188 bsfq         %rdx, %r9
	0x49, 0x01, 0xd9, //0x0000218c addq         %rbx, %r9
	0xe9, 0x77, 0xf8, 0xff, 0xff, //0x0000218f jmp          LBB0_172
	//0x00002194 LBB0_391
	0x4d, 0x85, 0xed, //0x00002194 testq        %r13, %r13
	0x0f, 0x84, 0x18, 0x02, 0x00, 0x00, //0x00002197 je           LBB0_416
	0x48, 0x8b, 0x45, 0x98, //0x0000219d movq         $-104(%rbp), %rax
	0x4c, 0x01, 0xd8, //0x000021a1 addq         %r11, %rax
	0x49, 0x83, 0xf9, 0xff, //0x000021a4 cmpq         $-1, %r9
	0x4c, 0x0f, 0x44, 0xc8, //0x000021a8 cmoveq       %rax, %r9
	0x49, 0xff, 0xc3, //0x000021ac incq         %r11
	0x49, 0xff, 0xcd, //0x000021af decq         %r13
	0x4c, 0x8b, 0x65, 0xd0, //0x000021b2 movq         $-48(%rbp), %r12
	0x4d, 0x85, 0xed, //0x000021b6 testq        %r13, %r13
	0x0f, 0x85, 0x54, 0xff, 0xff, 0xff, //0x000021b9 jne          LBB0_384
	0xe9, 0xf1, 0x01, 0x00, 0x00, //0x000021bf jmp          LBB0_416
	//0x000021c4 LBB0_393
	0x48, 0x85, 0xc0, //0x000021c4 testq        %rax, %rax
	0x0f, 0x84, 0xe8, 0x01, 0x00, 0x00, //0x000021c7 je           LBB0_416
	0x48, 0x8b, 0x4d, 0x98, //0x000021cd movq         $-104(%rbp), %rcx
	0x4c, 0x01, 0xd9, //0x000021d1 addq         %r11, %rcx
	0x49, 0x83, 0xf9, 0xff, //0x000021d4 cmpq         $-1, %r9
	0x4c, 0x0f, 0x44, 0xc9, //0x000021d8 cmoveq       %rcx, %r9
	0x49, 0xff, 0xc3, //0x000021dc incq         %r11
	0x48, 0xff, 0xc8, //0x000021df decq         %rax
	0x4c, 0x8b, 0x65, 0xd0, //0x000021e2 movq         $-48(%rbp), %r12
	0x48, 0x85, 0xc0, //0x000021e6 testq        %rax, %rax
	0x0f, 0x85, 0x3f, 0xf9, 0xff, 0xff, //0x000021e9 jne          LBB0_331
	0xe9, 0xc1, 0x01, 0x00, 0x00, //0x000021ef jmp          LBB0_416
	//0x000021f4 LBB0_395
	0x44, 0x89, 0xc1, //0x000021f4 movl         %r8d, %ecx
	0xf7, 0xd1, //0x000021f7 notl         %ecx
	0x21, 0xd1, //0x000021f9 andl         %edx, %ecx
	0x4d, 0x89, 0xd5, //0x000021fb movq         %r10, %r13
	0x44, 0x8d, 0x14, 0x09, //0x000021fe leal         (%rcx,%rcx), %r10d
	0x45, 0x09, 0xc2, //0x00002202 orl          %r8d, %r10d
	0xbb, 0xaa, 0xaa, 0xaa, 0xaa, //0x00002205 movl         $2863311530, %ebx
	0x44, 0x31, 0xd3, //0x0000220a xorl         %r10d, %ebx
	0x21, 0xd3, //0x0000220d andl         %edx, %ebx
	0x81, 0xe3, 0xaa, 0xaa, 0xaa, 0xaa, //0x0000220f andl         $-1431655766, %ebx
	0x45, 0x31, 0xc0, //0x00002215 xorl         %r8d, %r8d
	0x01, 0xcb, //0x00002218 addl         %ecx, %ebx
	0x41, 0x0f, 0x92, 0xc0, //0x0000221a setb         %r8b
	0x01, 0xdb, //0x0000221e addl         %ebx, %ebx
	0x81, 0xf3, 0x55, 0x55, 0x55, 0x55, //0x00002220 xorl         $1431655765, %ebx
	0x44, 0x21, 0xd3, //0x00002226 andl         %r10d, %ebx
	0x4d, 0x89, 0xea, //0x00002229 movq         %r13, %r10
	0xf7, 0xd3, //0x0000222c notl         %ebx
	0x21, 0xde, //0x0000222e andl         %ebx, %esi
	0xe9, 0xc5, 0xfa, 0xff, 0xff, //0x00002230 jmp          LBB0_283
	//0x00002235 LBB0_396
	0x4c, 0x89, 0xd8, //0x00002235 movq         %r11, %rax
	0x48, 0x2b, 0x45, 0xd0, //0x00002238 subq         $-48(%rbp), %rax
	0x4c, 0x0f, 0xbc, 0xcf, //0x0000223c bsfq         %rdi, %r9
	0x49, 0x01, 0xc1, //0x00002240 addq         %rax, %r9
	0x48, 0x09, 0xca, //0x00002243 orq          %rcx, %rdx
	0x48, 0x89, 0xf8, //0x00002246 movq         %rdi, %rax
	0x4c, 0x09, 0xd0, //0x00002249 orq          %r10, %rax
	0x0f, 0x84, 0x05, 0xfa, 0xff, 0xff, //0x0000224c je           LBB0_210
	//0x00002252 LBB0_397
	0x44, 0x89, 0xd0, //0x00002252 movl         %r10d, %eax
	0xf7, 0xd0, //0x00002255 notl         %eax
	0x21, 0xf8, //0x00002257 andl         %edi, %eax
	0x8d, 0x0c, 0x00, //0x00002259 leal         (%rax,%rax), %ecx
	0x44, 0x09, 0xd1, //0x0000225c orl          %r10d, %ecx
	0x89, 0xce, //0x0000225f movl         %ecx, %esi
	0xbb, 0xaa, 0xaa, 0xaa, 0xaa, //0x00002261 movl         $2863311530, %ebx
	0x31, 0xde, //0x00002266 xorl         %ebx, %esi
	0x21, 0xfe, //0x00002268 andl         %edi, %esi
	0x81, 0xe6, 0xaa, 0xaa, 0xaa, 0xaa, //0x0000226a andl         $-1431655766, %esi
	0x45, 0x31, 0xd2, //0x00002270 xorl         %r10d, %r10d
	0x01, 0xc6, //0x00002273 addl         %eax, %esi
	0x41, 0x0f, 0x92, 0xc2, //0x00002275 setb         %r10b
	0x01, 0xf6, //0x00002279 addl         %esi, %esi
	0x81, 0xf6, 0x55, 0x55, 0x55, 0x55, //0x0000227b xorl         $1431655765, %esi
	0x21, 0xce, //0x00002281 andl         %ecx, %esi
	0xf7, 0xd6, //0x00002283 notl         %esi
	0x21, 0xf2, //0x00002285 andl         %esi, %edx
	0x48, 0x85, 0xd2, //0x00002287 testq        %rdx, %rdx
	0x0f, 0x85, 0xd0, 0xf9, 0xff, 0xff, //0x0000228a jne          LBB0_211
	//0x00002290 LBB0_398
	0x49, 0x83, 0xc3, 0x20, //0x00002290 addq         $32, %r11
	0x49, 0x83, 0xc4, 0xe0, //0x00002294 addq         $-32, %r12
	//0x00002298 LBB0_399
	0x4d, 0x85, 0xd2, //0x00002298 testq        %r10, %r10
	0x0f, 0x85, 0xbf, 0x00, 0x00, 0x00, //0x0000229b jne          LBB0_411
	0x4c, 0x8b, 0x55, 0xb0, //0x000022a1 movq         $-80(%rbp), %r10
	0x4d, 0x85, 0xe4, //0x000022a5 testq        %r12, %r12
	0x0f, 0x84, 0x07, 0x01, 0x00, 0x00, //0x000022a8 je           LBB0_416
	//0x000022ae LBB0_401
	0x49, 0x8d, 0x4b, 0x01, //0x000022ae leaq         $1(%r11), %rcx
	0x41, 0x0f, 0xb6, 0x1b, //0x000022b2 movzbl       (%r11), %ebx
	0x80, 0xfb, 0x22, //0x000022b6 cmpb         $34, %bl
	0x0f, 0x84, 0x50, 0x00, 0x00, 0x00, //0x000022b9 je           LBB0_406
	0x49, 0x8d, 0x54, 0x24, 0xff, //0x000022bf leaq         $-1(%r12), %rdx
	0x80, 0xfb, 0x5c, //0x000022c4 cmpb         $92, %bl
	0x0f, 0x84, 0x14, 0x00, 0x00, 0x00, //0x000022c7 je           LBB0_404
	0x49, 0x89, 0xd4, //0x000022cd movq         %rdx, %r12
	0x49, 0x89, 0xcb, //0x000022d0 movq         %rcx, %r11
	0x48, 0x85, 0xd2, //0x000022d3 testq        %rdx, %rdx
	0x0f, 0x85, 0xd2, 0xff, 0xff, 0xff, //0x000022d6 jne          LBB0_401
	0xe9, 0xd4, 0x00, 0x00, 0x00, //0x000022dc jmp          LBB0_416
	//0x000022e1 LBB0_404
	0x48, 0x85, 0xd2, //0x000022e1 testq        %rdx, %rdx
	0x0f, 0x84, 0xcb, 0x00, 0x00, 0x00, //0x000022e4 je           LBB0_416
	0x48, 0x03, 0x4d, 0x98, //0x000022ea addq         $-104(%rbp), %rcx
	0x49, 0x83, 0xf9, 0xff, //0x000022ee cmpq         $-1, %r9
	0x4c, 0x0f, 0x44, 0xc9, //0x000022f2 cmoveq       %rcx, %r9
	0x49, 0x83, 0xc3, 0x02, //0x000022f6 addq         $2, %r11
	0x49, 0x83, 0xc4, 0xfe, //0x000022fa addq         $-2, %r12
	0x4c, 0x89, 0xe2, //0x000022fe movq         %r12, %rdx
	0x48, 0x85, 0xd2, //0x00002301 testq        %rdx, %rdx
	0x0f, 0x85, 0xa4, 0xff, 0xff, 0xff, //0x00002304 jne          LBB0_401
	0xe9, 0xa6, 0x00, 0x00, 0x00, //0x0000230a jmp          LBB0_416
	//0x0000230f LBB0_406
	0x4c, 0x8b, 0x65, 0xd0, //0x0000230f movq         $-48(%rbp), %r12
	//0x00002313 LBB0_407
	0x4c, 0x29, 0xe1, //0x00002313 subq         %r12, %rcx
	0x49, 0x89, 0xcb, //0x00002316 movq         %rcx, %r11
	0xe9, 0xa5, 0xeb, 0xff, 0xff, //0x00002319 jmp          LBB0_186
	//0x0000231e LBB0_408
	0x4c, 0x89, 0xdb, //0x0000231e movq         %r11, %rbx
	0x4c, 0x29, 0xe3, //0x00002321 subq         %r12, %rbx
	0x4c, 0x0f, 0xbc, 0xca, //0x00002324 bsfq         %rdx, %r9
	0x49, 0x01, 0xd9, //0x00002328 addq         %rbx, %r9
	0xe9, 0xb7, 0xf9, 0xff, 0xff, //0x0000232b jmp          LBB0_282
	//0x00002330 LBB0_409
	0x48, 0x85, 0xc0, //0x00002330 testq        %rax, %rax
	0x0f, 0x84, 0x7c, 0x00, 0x00, 0x00, //0x00002333 je           LBB0_416
	0x48, 0x8b, 0x4d, 0x98, //0x00002339 movq         $-104(%rbp), %rcx
	0x4c, 0x01, 0xd9, //0x0000233d addq         %r11, %rcx
	0x49, 0x83, 0xf9, 0xff, //0x00002340 cmpq         $-1, %r9
	0x4c, 0x0f, 0x44, 0xc9, //0x00002344 cmoveq       %rcx, %r9
	0x49, 0xff, 0xc3, //0x00002348 incq         %r11
	0x48, 0xff, 0xc8, //0x0000234b decq         %rax
	0x4c, 0x8b, 0x65, 0xd0, //0x0000234e movq         $-48(%rbp), %r12
	0x48, 0x85, 0xc0, //0x00002352 testq        %rax, %rax
	0x0f, 0x85, 0xf2, 0xf9, 0xff, 0xff, //0x00002355 jne          LBB0_347
	0xe9, 0x55, 0x00, 0x00, 0x00, //0x0000235b jmp          LBB0_416
	//0x00002360 LBB0_411
	0x4d, 0x85, 0xe4, //0x00002360 testq        %r12, %r12
	0x0f, 0x84, 0x4c, 0x00, 0x00, 0x00, //0x00002363 je           LBB0_416
	0x48, 0x8b, 0x45, 0x98, //0x00002369 movq         $-104(%rbp), %rax
	0x4c, 0x01, 0xd8, //0x0000236d addq         %r11, %rax
	0x49, 0x83, 0xf9, 0xff, //0x00002370 cmpq         $-1, %r9
	0x4c, 0x0f, 0x44, 0xc8, //0x00002374 cmoveq       %rax, %r9
	0x49, 0xff, 0xc3, //0x00002378 incq         %r11
	0x49, 0xff, 0xcc, //0x0000237b decq         %r12
	0x4c, 0x8b, 0x55, 0xb0, //0x0000237e movq         $-80(%rbp), %r10
	0x4d, 0x85, 0xe4, //0x00002382 testq        %r12, %r12
	0x0f, 0x85, 0x23, 0xff, 0xff, 0xff, //0x00002385 jne          LBB0_401
	0xe9, 0x25, 0x00, 0x00, 0x00, //0x0000238b jmp          LBB0_416
	//0x00002390 LBB0_413
	0x48, 0x89, 0x0e, //0x00002390 movq         %rcx, (%rsi)
	//0x00002393 LBB0_414
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00002393 movq         $-1, %rax
	0xe9, 0x45, 0x00, 0x00, 0x00, //0x0000239a jmp          LBB0_424
	//0x0000239f LBB0_432
	0x48, 0xc7, 0xc0, 0xf9, 0xff, 0xff, 0xff, //0x0000239f movq         $-7, %rax
	0xe9, 0x39, 0x00, 0x00, 0x00, //0x000023a6 jmp          LBB0_424
	//0x000023ab LBB0_415
	0x49, 0x83, 0xfb, 0xff, //0x000023ab cmpq         $-1, %r11
	0x0f, 0x85, 0x93, 0x01, 0x00, 0x00, //0x000023af jne          LBB0_417
	//0x000023b5 LBB0_416
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x000023b5 movq         $-1, %r11
	0x4c, 0x8b, 0x4d, 0xa0, //0x000023bc movq         $-96(%rbp), %r9
	0xe9, 0x83, 0x01, 0x00, 0x00, //0x000023c0 jmp          LBB0_417
	//0x000023c5 LBB0_418
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x000023c5 movq         $-1, %rax
	0xe9, 0x03, 0x00, 0x00, 0x00, //0x000023cc jmp          LBB0_421
	//0x000023d1 LBB0_420
	0x48, 0x89, 0xc8, //0x000023d1 movq         %rcx, %rax
	//0x000023d4 LBB0_421
	0x48, 0xf7, 0xd0, //0x000023d4 notq         %rax
	0x49, 0x01, 0xc7, //0x000023d7 addq         %rax, %r15
	//0x000023da LBB0_422
	0x4c, 0x89, 0x3e, //0x000023da movq         %r15, (%rsi)
	//0x000023dd LBB0_423
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x000023dd movq         $-2, %rax
	//0x000023e4 LBB0_424
	0x48, 0x83, 0xc4, 0x70, //0x000023e4 addq         $112, %rsp
	0x5b, //0x000023e8 popq         %rbx
	0x41, 0x5c, //0x000023e9 popq         %r12
	0x41, 0x5d, //0x000023eb popq         %r13
	0x41, 0x5e, //0x000023ed popq         %r14
	0x41, 0x5f, //0x000023ef popq         %r15
	0x5d, //0x000023f1 popq         %rbp
	0xc3, //0x000023f2 retq         
	//0x000023f3 LBB0_431
	0x48, 0x89, 0x0e, //0x000023f3 movq         %rcx, (%rsi)
	0xe9, 0xe9, 0xff, 0xff, 0xff, //0x000023f6 jmp          LBB0_424
	//0x000023fb LBB0_425
	0x49, 0x83, 0xf9, 0xff, //0x000023fb cmpq         $-1, %r9
	0x0f, 0x85, 0x13, 0x00, 0x00, 0x00, //0x000023ff jne          LBB0_426
	0x49, 0x0f, 0xbc, 0xc4, //0x00002405 bsfq         %r12, %rax
	0xe9, 0x29, 0x01, 0x00, 0x00, //0x00002409 jmp          LBB0_445
	//0x0000240e LBB0_427
	0x49, 0x83, 0xf9, 0xff, //0x0000240e cmpq         $-1, %r9
	0x0f, 0x84, 0x1b, 0x01, 0x00, 0x00, //0x00002412 je           LBB0_444
	//0x00002418 LBB0_426
	0x49, 0xc7, 0xc3, 0xfe, 0xff, 0xff, 0xff, //0x00002418 movq         $-2, %r11
	0xe9, 0x24, 0x01, 0x00, 0x00, //0x0000241f jmp          LBB0_417
	//0x00002424 LBB0_429
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00002424 movq         $-1, %r11
	//0x0000242b LBB0_430
	0x4d, 0x29, 0xdf, //0x0000242b subq         %r11, %r15
	0xe9, 0xa7, 0xff, 0xff, 0xff, //0x0000242e jmp          LBB0_422
	//0x00002433 LBB0_433
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x00002433 movq         $-2, %rax
	0x80, 0xf9, 0x61, //0x0000243a cmpb         $97, %cl
	0x0f, 0x85, 0xa1, 0xff, 0xff, 0xff, //0x0000243d jne          LBB0_424
	0x49, 0x8d, 0x4f, 0x02, //0x00002443 leaq         $2(%r15), %rcx
	0x48, 0x89, 0x0e, //0x00002447 movq         %rcx, (%rsi)
	0x43, 0x80, 0x7c, 0x3c, 0x02, 0x6c, //0x0000244a cmpb         $108, $2(%r12,%r15)
	0x0f, 0x85, 0x8e, 0xff, 0xff, 0xff, //0x00002450 jne          LBB0_424
	0x49, 0x8d, 0x4f, 0x03, //0x00002456 leaq         $3(%r15), %rcx
	0x48, 0x89, 0x0e, //0x0000245a movq         %rcx, (%rsi)
	0x43, 0x80, 0x7c, 0x3c, 0x03, 0x73, //0x0000245d cmpb         $115, $3(%r12,%r15)
	0x0f, 0x85, 0x7b, 0xff, 0xff, 0xff, //0x00002463 jne          LBB0_424
	0x49, 0x8d, 0x4f, 0x04, //0x00002469 leaq         $4(%r15), %rcx
	0x48, 0x89, 0x0e, //0x0000246d movq         %rcx, (%rsi)
	0x43, 0x80, 0x7c, 0x3c, 0x04, 0x65, //0x00002470 cmpb         $101, $4(%r12,%r15)
	0x0f, 0x85, 0x68, 0xff, 0xff, 0xff, //0x00002476 jne          LBB0_424
	0x49, 0x83, 0xc7, 0x05, //0x0000247c addq         $5, %r15
	0x4c, 0x89, 0x3e, //0x00002480 movq         %r15, (%rsi)
	0xe9, 0x5c, 0xff, 0xff, 0xff, //0x00002483 jmp          LBB0_424
	//0x00002488 LBB0_257
	0x4c, 0x89, 0x3e, //0x00002488 movq         %r15, (%rsi)
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x0000248b movq         $-2, %rax
	0x41, 0x80, 0x3a, 0x6e, //0x00002492 cmpb         $110, (%r10)
	0x0f, 0x85, 0x48, 0xff, 0xff, 0xff, //0x00002496 jne          LBB0_424
	0x49, 0x8d, 0x4f, 0x01, //0x0000249c leaq         $1(%r15), %rcx
	0x48, 0x89, 0x0e, //0x000024a0 movq         %rcx, (%rsi)
	0x43, 0x80, 0x7c, 0x3c, 0x01, 0x75, //0x000024a3 cmpb         $117, $1(%r12,%r15)
	0x0f, 0x85, 0x35, 0xff, 0xff, 0xff, //0x000024a9 jne          LBB0_424
	0x49, 0x8d, 0x4f, 0x02, //0x000024af leaq         $2(%r15), %rcx
	0x48, 0x89, 0x0e, //0x000024b3 movq         %rcx, (%rsi)
	0x43, 0x80, 0x7c, 0x3c, 0x02, 0x6c, //0x000024b6 cmpb         $108, $2(%r12,%r15)
	0x0f, 0x85, 0x22, 0xff, 0xff, 0xff, //0x000024bc jne          LBB0_424
	0x49, 0x8d, 0x4f, 0x03, //0x000024c2 leaq         $3(%r15), %rcx
	0x48, 0x89, 0x0e, //0x000024c6 movq         %rcx, (%rsi)
	0x43, 0x80, 0x7c, 0x3c, 0x03, 0x6c, //0x000024c9 cmpb         $108, $3(%r12,%r15)
	0x0f, 0x85, 0x0f, 0xff, 0xff, 0xff, //0x000024cf jne          LBB0_424
	0xe9, 0x4d, 0x00, 0x00, 0x00, //0x000024d5 jmp          LBB0_442
	//0x000024da LBB0_438
	0x4c, 0x89, 0x3e, //0x000024da movq         %r15, (%rsi)
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x000024dd movq         $-2, %rax
	0x41, 0x80, 0x3a, 0x74, //0x000024e4 cmpb         $116, (%r10)
	0x0f, 0x85, 0xf6, 0xfe, 0xff, 0xff, //0x000024e8 jne          LBB0_424
	0x49, 0x8d, 0x4f, 0x01, //0x000024ee leaq         $1(%r15), %rcx
	0x48, 0x89, 0x0e, //0x000024f2 movq         %rcx, (%rsi)
	0x43, 0x80, 0x7c, 0x3c, 0x01, 0x72, //0x000024f5 cmpb         $114, $1(%r12,%r15)
	0x0f, 0x85, 0xe3, 0xfe, 0xff, 0xff, //0x000024fb jne          LBB0_424
	0x49, 0x8d, 0x4f, 0x02, //0x00002501 leaq         $2(%r15), %rcx
	0x48, 0x89, 0x0e, //0x00002505 movq         %rcx, (%rsi)
	0x43, 0x80, 0x7c, 0x3c, 0x02, 0x75, //0x00002508 cmpb         $117, $2(%r12,%r15)
	0x0f, 0x85, 0xd0, 0xfe, 0xff, 0xff, //0x0000250e jne          LBB0_424
	0x49, 0x8d, 0x4f, 0x03, //0x00002514 leaq         $3(%r15), %rcx
	0x48, 0x89, 0x0e, //0x00002518 movq         %rcx, (%rsi)
	0x43, 0x80, 0x7c, 0x3c, 0x03, 0x65, //0x0000251b cmpb         $101, $3(%r12,%r15)
	0x0f, 0x85, 0xbd, 0xfe, 0xff, 0xff, //0x00002521 jne          LBB0_424
	//0x00002527 LBB0_442
	0x49, 0x83, 0xc7, 0x04, //0x00002527 addq         $4, %r15
	0x4c, 0x89, 0x3e, //0x0000252b movq         %r15, (%rsi)
	0xe9, 0xb1, 0xfe, 0xff, 0xff, //0x0000252e jmp          LBB0_424
	//0x00002533 LBB0_444
	0x48, 0x0f, 0xbc, 0xc2, //0x00002533 bsfq         %rdx, %rax
	//0x00002537 LBB0_445
	0x4c, 0x2b, 0x5d, 0xd0, //0x00002537 subq         $-48(%rbp), %r11
	//0x0000253b LBB0_446
	0x49, 0x01, 0xc3, //0x0000253b addq         %rax, %r11
	//0x0000253e LBB0_447
	0x4d, 0x89, 0xd9, //0x0000253e movq         %r11, %r9
	0x49, 0xc7, 0xc3, 0xfe, 0xff, 0xff, 0xff, //0x00002541 movq         $-2, %r11
	//0x00002548 LBB0_417
	0x48, 0x8b, 0x45, 0xc8, //0x00002548 movq         $-56(%rbp), %rax
	0x4c, 0x89, 0x08, //0x0000254c movq         %r9, (%rax)
	0x4c, 0x89, 0xd8, //0x0000254f movq         %r11, %rax
	0xe9, 0x8d, 0xfe, 0xff, 0xff, //0x00002552 jmp          LBB0_424
	//0x00002557 LBB0_449
	0x4c, 0x89, 0x55, 0xa0, //0x00002557 movq         %r10, $-96(%rbp)
	0xe9, 0x55, 0xfe, 0xff, 0xff, //0x0000255b jmp          LBB0_416
	//0x00002560 LBB0_308
	0x4c, 0x01, 0xd9, //0x00002560 addq         %r11, %rcx
	0x49, 0xc7, 0xc3, 0xfe, 0xff, 0xff, 0xff, //0x00002563 movq         $-2, %r11
	0x49, 0x89, 0xc9, //0x0000256a movq         %rcx, %r9
	0xe9, 0xd6, 0xff, 0xff, 0xff, //0x0000256d jmp          LBB0_417
	//0x00002572 LBB0_448
	0x4c, 0x89, 0x75, 0xa0, //0x00002572 movq         %r14, $-96(%rbp)
	0xe9, 0x3a, 0xfe, 0xff, 0xff, //0x00002576 jmp          LBB0_416
	//0x0000257b LBB0_450
	0x48, 0x0f, 0xbc, 0xc7, //0x0000257b bsfq         %rdi, %rax
	0x4d, 0x29, 0xe3, //0x0000257f subq         %r12, %r11
	0xe9, 0xb4, 0xff, 0xff, 0xff, //0x00002582 jmp          LBB0_446
	//0x00002587 LBB0_451
	0x4d, 0x29, 0xe3, //0x00002587 subq         %r12, %r11
	0xe9, 0xaf, 0xff, 0xff, 0xff, //0x0000258a jmp          LBB0_447
	0x90, //0x0000258f .p2align 2, 0x90
	// // .set L0_0_set_33, LBB0_33-LJTI0_0
	// // .set L0_0_set_35, LBB0_35-LJTI0_0
	// // .set L0_0_set_38, LBB0_38-LJTI0_0
	// // .set L0_0_set_58, LBB0_58-LJTI0_0
	// // .set L0_0_set_60, LBB0_60-LJTI0_0
	// // .set L0_0_set_63, LBB0_63-LJTI0_0
	//0x00002590 LJTI0_0
	0xb2, 0xdd, 0xff, 0xff, //0x00002590 .long L0_0_set_33
	0xcb, 0xdd, 0xff, 0xff, //0x00002594 .long L0_0_set_35
	0xf7, 0xdd, 0xff, 0xff, //0x00002598 .long L0_0_set_38
	0xb3, 0xdf, 0xff, 0xff, //0x0000259c .long L0_0_set_58
	0xca, 0xdf, 0xff, 0xff, //0x000025a0 .long L0_0_set_60
	0xf4, 0xdf, 0xff, 0xff, //0x000025a4 .long L0_0_set_63
	// // .set L0_1_set_424, LBB0_424-LJTI0_1
	// // .set L0_1_set_423, LBB0_423-LJTI0_1
	// // .set L0_1_set_197, LBB0_197-LJTI0_1
	// // .set L0_1_set_216, LBB0_216-LJTI0_1
	// // .set L0_1_set_68, LBB0_68-LJTI0_1
	// // .set L0_1_set_250, LBB0_250-LJTI0_1
	// // .set L0_1_set_252, LBB0_252-LJTI0_1
	// // .set L0_1_set_255, LBB0_255-LJTI0_1
	// // .set L0_1_set_261, LBB0_261-LJTI0_1
	// // .set L0_1_set_265, LBB0_265-LJTI0_1
	//0x000025a8 LJTI0_1
	0x3c, 0xfe, 0xff, 0xff, //0x000025a8 .long L0_1_set_424
	0x35, 0xfe, 0xff, 0xff, //0x000025ac .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x000025b0 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x000025b4 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x000025b8 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x000025bc .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x000025c0 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x000025c4 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x000025c8 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x000025cc .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x000025d0 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x000025d4 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x000025d8 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x000025dc .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x000025e0 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x000025e4 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x000025e8 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x000025ec .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x000025f0 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x000025f4 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x000025f8 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x000025fc .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x00002600 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x00002604 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x00002608 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x0000260c .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x00002610 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x00002614 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x00002618 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x0000261c .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x00002620 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x00002624 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x00002628 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x0000262c .long L0_1_set_423
	0xd2, 0xe9, 0xff, 0xff, //0x00002630 .long L0_1_set_197
	0x35, 0xfe, 0xff, 0xff, //0x00002634 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x00002638 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x0000263c .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x00002640 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x00002644 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x00002648 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x0000264c .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x00002650 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x00002654 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x00002658 .long L0_1_set_423
	0x7f, 0xeb, 0xff, 0xff, //0x0000265c .long L0_1_set_216
	0x35, 0xfe, 0xff, 0xff, //0x00002660 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x00002664 .long L0_1_set_423
	0x24, 0xe0, 0xff, 0xff, //0x00002668 .long L0_1_set_68
	0x24, 0xe0, 0xff, 0xff, //0x0000266c .long L0_1_set_68
	0x24, 0xe0, 0xff, 0xff, //0x00002670 .long L0_1_set_68
	0x24, 0xe0, 0xff, 0xff, //0x00002674 .long L0_1_set_68
	0x24, 0xe0, 0xff, 0xff, //0x00002678 .long L0_1_set_68
	0x24, 0xe0, 0xff, 0xff, //0x0000267c .long L0_1_set_68
	0x24, 0xe0, 0xff, 0xff, //0x00002680 .long L0_1_set_68
	0x24, 0xe0, 0xff, 0xff, //0x00002684 .long L0_1_set_68
	0x24, 0xe0, 0xff, 0xff, //0x00002688 .long L0_1_set_68
	0x24, 0xe0, 0xff, 0xff, //0x0000268c .long L0_1_set_68
	0x35, 0xfe, 0xff, 0xff, //0x00002690 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x00002694 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x00002698 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x0000269c .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x000026a0 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x000026a4 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x000026a8 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x000026ac .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x000026b0 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x000026b4 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x000026b8 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x000026bc .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x000026c0 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x000026c4 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x000026c8 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x000026cc .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x000026d0 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x000026d4 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x000026d8 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x000026dc .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x000026e0 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x000026e4 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x000026e8 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x000026ec .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x000026f0 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x000026f4 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x000026f8 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x000026fc .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x00002700 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x00002704 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x00002708 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x0000270c .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x00002710 .long L0_1_set_423
	0xd7, 0xed, 0xff, 0xff, //0x00002714 .long L0_1_set_250
	0x35, 0xfe, 0xff, 0xff, //0x00002718 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x0000271c .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x00002720 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x00002724 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x00002728 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x0000272c .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x00002730 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x00002734 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x00002738 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x0000273c .long L0_1_set_423
	0xfe, 0xed, 0xff, 0xff, //0x00002740 .long L0_1_set_252
	0x35, 0xfe, 0xff, 0xff, //0x00002744 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x00002748 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x0000274c .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x00002750 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x00002754 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x00002758 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x0000275c .long L0_1_set_423
	0x28, 0xee, 0xff, 0xff, //0x00002760 .long L0_1_set_255
	0x35, 0xfe, 0xff, 0xff, //0x00002764 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x00002768 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x0000276c .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x00002770 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x00002774 .long L0_1_set_423
	0x4b, 0xee, 0xff, 0xff, //0x00002778 .long L0_1_set_261
	0x35, 0xfe, 0xff, 0xff, //0x0000277c .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x00002780 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x00002784 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x00002788 .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x0000278c .long L0_1_set_423
	0x35, 0xfe, 0xff, 0xff, //0x00002790 .long L0_1_set_423
	0x81, 0xee, 0xff, 0xff, //0x00002794 .long L0_1_set_265
	// // .set L0_2_set_241, LBB0_241-LJTI0_2
	// // .set L0_2_set_294, LBB0_294-LJTI0_2
	// // .set L0_2_set_248, LBB0_248-LJTI0_2
	// // .set L0_2_set_243, LBB0_243-LJTI0_2
	// // .set L0_2_set_246, LBB0_246-LJTI0_2
	//0x00002798 LJTI0_2
	0x63, 0xeb, 0xff, 0xff, //0x00002798 .long L0_2_set_241
	0xef, 0xee, 0xff, 0xff, //0x0000279c .long L0_2_set_294
	0x63, 0xeb, 0xff, 0xff, //0x000027a0 .long L0_2_set_241
	0xcc, 0xeb, 0xff, 0xff, //0x000027a4 .long L0_2_set_248
	0xef, 0xee, 0xff, 0xff, //0x000027a8 .long L0_2_set_294
	0x88, 0xeb, 0xff, 0xff, //0x000027ac .long L0_2_set_243
	0x88, 0xeb, 0xff, 0xff, //0x000027b0 .long L0_2_set_243
	0x88, 0xeb, 0xff, 0xff, //0x000027b4 .long L0_2_set_243
	0x88, 0xeb, 0xff, 0xff, //0x000027b8 .long L0_2_set_243
	0x88, 0xeb, 0xff, 0xff, //0x000027bc .long L0_2_set_243
	0x88, 0xeb, 0xff, 0xff, //0x000027c0 .long L0_2_set_243
	0x88, 0xeb, 0xff, 0xff, //0x000027c4 .long L0_2_set_243
	0x88, 0xeb, 0xff, 0xff, //0x000027c8 .long L0_2_set_243
	0x88, 0xeb, 0xff, 0xff, //0x000027cc .long L0_2_set_243
	0x88, 0xeb, 0xff, 0xff, //0x000027d0 .long L0_2_set_243
	0xef, 0xee, 0xff, 0xff, //0x000027d4 .long L0_2_set_294
	0xef, 0xee, 0xff, 0xff, //0x000027d8 .long L0_2_set_294
	0xef, 0xee, 0xff, 0xff, //0x000027dc .long L0_2_set_294
	0xef, 0xee, 0xff, 0xff, //0x000027e0 .long L0_2_set_294
	0xef, 0xee, 0xff, 0xff, //0x000027e4 .long L0_2_set_294
	0xef, 0xee, 0xff, 0xff, //0x000027e8 .long L0_2_set_294
	0xef, 0xee, 0xff, 0xff, //0x000027ec .long L0_2_set_294
	0xef, 0xee, 0xff, 0xff, //0x000027f0 .long L0_2_set_294
	0xef, 0xee, 0xff, 0xff, //0x000027f4 .long L0_2_set_294
	0xef, 0xee, 0xff, 0xff, //0x000027f8 .long L0_2_set_294
	0xef, 0xee, 0xff, 0xff, //0x000027fc .long L0_2_set_294
	0xb1, 0xeb, 0xff, 0xff, //0x00002800 .long L0_2_set_246
	0xef, 0xee, 0xff, 0xff, //0x00002804 .long L0_2_set_294
	0xef, 0xee, 0xff, 0xff, //0x00002808 .long L0_2_set_294
	0xef, 0xee, 0xff, 0xff, //0x0000280c .long L0_2_set_294
	0xef, 0xee, 0xff, 0xff, //0x00002810 .long L0_2_set_294
	0xef, 0xee, 0xff, 0xff, //0x00002814 .long L0_2_set_294
	0xef, 0xee, 0xff, 0xff, //0x00002818 .long L0_2_set_294
	0xef, 0xee, 0xff, 0xff, //0x0000281c .long L0_2_set_294
	0xef, 0xee, 0xff, 0xff, //0x00002820 .long L0_2_set_294
	0xef, 0xee, 0xff, 0xff, //0x00002824 .long L0_2_set_294
	0xef, 0xee, 0xff, 0xff, //0x00002828 .long L0_2_set_294
	0xef, 0xee, 0xff, 0xff, //0x0000282c .long L0_2_set_294
	0xef, 0xee, 0xff, 0xff, //0x00002830 .long L0_2_set_294
	0xef, 0xee, 0xff, 0xff, //0x00002834 .long L0_2_set_294
	0xef, 0xee, 0xff, 0xff, //0x00002838 .long L0_2_set_294
	0xef, 0xee, 0xff, 0xff, //0x0000283c .long L0_2_set_294
	0xef, 0xee, 0xff, 0xff, //0x00002840 .long L0_2_set_294
	0xef, 0xee, 0xff, 0xff, //0x00002844 .long L0_2_set_294
	0xef, 0xee, 0xff, 0xff, //0x00002848 .long L0_2_set_294
	0xef, 0xee, 0xff, 0xff, //0x0000284c .long L0_2_set_294
	0xef, 0xee, 0xff, 0xff, //0x00002850 .long L0_2_set_294
	0xef, 0xee, 0xff, 0xff, //0x00002854 .long L0_2_set_294
	0xef, 0xee, 0xff, 0xff, //0x00002858 .long L0_2_set_294
	0xef, 0xee, 0xff, 0xff, //0x0000285c .long L0_2_set_294
	0xef, 0xee, 0xff, 0xff, //0x00002860 .long L0_2_set_294
	0xef, 0xee, 0xff, 0xff, //0x00002864 .long L0_2_set_294
	0xef, 0xee, 0xff, 0xff, //0x00002868 .long L0_2_set_294
	0xef, 0xee, 0xff, 0xff, //0x0000286c .long L0_2_set_294
	0xef, 0xee, 0xff, 0xff, //0x00002870 .long L0_2_set_294
	0xef, 0xee, 0xff, 0xff, //0x00002874 .long L0_2_set_294
	0xef, 0xee, 0xff, 0xff, //0x00002878 .long L0_2_set_294
	0xef, 0xee, 0xff, 0xff, //0x0000287c .long L0_2_set_294
	0xb1, 0xeb, 0xff, 0xff, //0x00002880 .long L0_2_set_246
	// // .set L0_3_set_93, LBB0_93-LJTI0_3
	// // .set L0_3_set_148, LBB0_148-LJTI0_3
	// // .set L0_3_set_100, LBB0_100-LJTI0_3
	// // .set L0_3_set_95, LBB0_95-LJTI0_3
	// // .set L0_3_set_98, LBB0_98-LJTI0_3
	//0x00002884 LJTI0_3
	0x00, 0xdf, 0xff, 0xff, //0x00002884 .long L0_3_set_93
	0x65, 0xe3, 0xff, 0xff, //0x00002888 .long L0_3_set_148
	0x00, 0xdf, 0xff, 0xff, //0x0000288c .long L0_3_set_93
	0x5f, 0xdf, 0xff, 0xff, //0x00002890 .long L0_3_set_100
	0x65, 0xe3, 0xff, 0xff, //0x00002894 .long L0_3_set_148
	0x1c, 0xdf, 0xff, 0xff, //0x00002898 .long L0_3_set_95
	0x1c, 0xdf, 0xff, 0xff, //0x0000289c .long L0_3_set_95
	0x1c, 0xdf, 0xff, 0xff, //0x000028a0 .long L0_3_set_95
	0x1c, 0xdf, 0xff, 0xff, //0x000028a4 .long L0_3_set_95
	0x1c, 0xdf, 0xff, 0xff, //0x000028a8 .long L0_3_set_95
	0x1c, 0xdf, 0xff, 0xff, //0x000028ac .long L0_3_set_95
	0x1c, 0xdf, 0xff, 0xff, //0x000028b0 .long L0_3_set_95
	0x1c, 0xdf, 0xff, 0xff, //0x000028b4 .long L0_3_set_95
	0x1c, 0xdf, 0xff, 0xff, //0x000028b8 .long L0_3_set_95
	0x1c, 0xdf, 0xff, 0xff, //0x000028bc .long L0_3_set_95
	0x65, 0xe3, 0xff, 0xff, //0x000028c0 .long L0_3_set_148
	0x65, 0xe3, 0xff, 0xff, //0x000028c4 .long L0_3_set_148
	0x65, 0xe3, 0xff, 0xff, //0x000028c8 .long L0_3_set_148
	0x65, 0xe3, 0xff, 0xff, //0x000028cc .long L0_3_set_148
	0x65, 0xe3, 0xff, 0xff, //0x000028d0 .long L0_3_set_148
	0x65, 0xe3, 0xff, 0xff, //0x000028d4 .long L0_3_set_148
	0x65, 0xe3, 0xff, 0xff, //0x000028d8 .long L0_3_set_148
	0x65, 0xe3, 0xff, 0xff, //0x000028dc .long L0_3_set_148
	0x65, 0xe3, 0xff, 0xff, //0x000028e0 .long L0_3_set_148
	0x65, 0xe3, 0xff, 0xff, //0x000028e4 .long L0_3_set_148
	0x65, 0xe3, 0xff, 0xff, //0x000028e8 .long L0_3_set_148
	0x44, 0xdf, 0xff, 0xff, //0x000028ec .long L0_3_set_98
	0x65, 0xe3, 0xff, 0xff, //0x000028f0 .long L0_3_set_148
	0x65, 0xe3, 0xff, 0xff, //0x000028f4 .long L0_3_set_148
	0x65, 0xe3, 0xff, 0xff, //0x000028f8 .long L0_3_set_148
	0x65, 0xe3, 0xff, 0xff, //0x000028fc .long L0_3_set_148
	0x65, 0xe3, 0xff, 0xff, //0x00002900 .long L0_3_set_148
	0x65, 0xe3, 0xff, 0xff, //0x00002904 .long L0_3_set_148
	0x65, 0xe3, 0xff, 0xff, //0x00002908 .long L0_3_set_148
	0x65, 0xe3, 0xff, 0xff, //0x0000290c .long L0_3_set_148
	0x65, 0xe3, 0xff, 0xff, //0x00002910 .long L0_3_set_148
	0x65, 0xe3, 0xff, 0xff, //0x00002914 .long L0_3_set_148
	0x65, 0xe3, 0xff, 0xff, //0x00002918 .long L0_3_set_148
	0x65, 0xe3, 0xff, 0xff, //0x0000291c .long L0_3_set_148
	0x65, 0xe3, 0xff, 0xff, //0x00002920 .long L0_3_set_148
	0x65, 0xe3, 0xff, 0xff, //0x00002924 .long L0_3_set_148
	0x65, 0xe3, 0xff, 0xff, //0x00002928 .long L0_3_set_148
	0x65, 0xe3, 0xff, 0xff, //0x0000292c .long L0_3_set_148
	0x65, 0xe3, 0xff, 0xff, //0x00002930 .long L0_3_set_148
	0x65, 0xe3, 0xff, 0xff, //0x00002934 .long L0_3_set_148
	0x65, 0xe3, 0xff, 0xff, //0x00002938 .long L0_3_set_148
	0x65, 0xe3, 0xff, 0xff, //0x0000293c .long L0_3_set_148
	0x65, 0xe3, 0xff, 0xff, //0x00002940 .long L0_3_set_148
	0x65, 0xe3, 0xff, 0xff, //0x00002944 .long L0_3_set_148
	0x65, 0xe3, 0xff, 0xff, //0x00002948 .long L0_3_set_148
	0x65, 0xe3, 0xff, 0xff, //0x0000294c .long L0_3_set_148
	0x65, 0xe3, 0xff, 0xff, //0x00002950 .long L0_3_set_148
	0x65, 0xe3, 0xff, 0xff, //0x00002954 .long L0_3_set_148
	0x65, 0xe3, 0xff, 0xff, //0x00002958 .long L0_3_set_148
	0x65, 0xe3, 0xff, 0xff, //0x0000295c .long L0_3_set_148
	0x65, 0xe3, 0xff, 0xff, //0x00002960 .long L0_3_set_148
	0x65, 0xe3, 0xff, 0xff, //0x00002964 .long L0_3_set_148
	0x65, 0xe3, 0xff, 0xff, //0x00002968 .long L0_3_set_148
	0x44, 0xdf, 0xff, 0xff, //0x0000296c .long L0_3_set_98
	//0x00002970 .p2align 2, 0x00
	//0x00002970 _MASK_USE_NUMBER
	0x02, 0x00, 0x00, 0x00, //0x00002970 .long 2
}
 
