// Code generated by internal/cmd/generator. DO NOT EDIT!
package vm_indent

import (
	"math"
	"reflect"
	"sort"
	"unsafe"

	"github.com/goccy/go-json/internal/encoder"
	"github.com/goccy/go-json/internal/runtime"
)

func Run(ctx *encoder.RuntimeContext, b []byte, codeSet *encoder.OpcodeSet) ([]byte, error) {
	recursiveLevel := 0
	ptrOffset := uintptr(0)
	ctxptr := ctx.Ptr()
	var code *encoder.Opcode
	if (ctx.Option.Flag & encoder.HTMLEscapeOption) != 0 {
		code = codeSet.EscapeKeyCode
	} else {
		code = codeSet.NoescapeKeyCode
	}

	for {
		switch code.Op {
		default:
			return nil, errUnimplementedOp(code.Op)
		case encoder.OpPtr:
			p := load(ctxptr, code.Idx)
			code = code.Next
			store(ctxptr, code.Idx, ptrToPtr(p))
		case encoder.OpIntPtr:
			p := loadNPtr(ctxptr, code.Idx, code.PtrNum)
			if p == 0 {
				b = appendNullComma(ctx, b)
				code = code.Next
				break
			}
			store(ctxptr, code.Idx, p)
			fallthrough
		case encoder.OpInt:
			b = appendInt(ctx, b, load(ctxptr, code.Idx), code)
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpUintPtr:
			p := loadNPtr(ctxptr, code.Idx, code.PtrNum)
			if p == 0 {
				b = appendNullComma(ctx, b)
				code = code.Next
				break
			}
			store(ctxptr, code.Idx, p)
			fallthrough
		case encoder.OpUint:
			b = appendUint(ctx, b, load(ctxptr, code.Idx), code)
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpIntString:
			b = append(b, '"')
			b = appendInt(ctx, b, load(ctxptr, code.Idx), code)
			b = append(b, '"')
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpUintString:
			b = append(b, '"')
			b = appendUint(ctx, b, load(ctxptr, code.Idx), code)
			b = append(b, '"')
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpFloat32Ptr:
			p := loadNPtr(ctxptr, code.Idx, code.PtrNum)
			if p == 0 {
				b = appendNull(ctx, b)
				b = appendComma(ctx, b)
				code = code.Next
				break
			}
			store(ctxptr, code.Idx, p)
			fallthrough
		case encoder.OpFloat32:
			b = appendFloat32(ctx, b, ptrToFloat32(load(ctxptr, code.Idx)))
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpFloat64Ptr:
			p := loadNPtr(ctxptr, code.Idx, code.PtrNum)
			if p == 0 {
				b = appendNullComma(ctx, b)
				code = code.Next
				break
			}
			store(ctxptr, code.Idx, p)
			fallthrough
		case encoder.OpFloat64:
			v := ptrToFloat64(load(ctxptr, code.Idx))
			if math.IsInf(v, 0) || math.IsNaN(v) {
				return nil, errUnsupportedFloat(v)
			}
			b = appendFloat64(ctx, b, v)
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpStringPtr:
			p := loadNPtr(ctxptr, code.Idx, code.PtrNum)
			if p == 0 {
				b = appendNullComma(ctx, b)
				code = code.Next
				break
			}
			store(ctxptr, code.Idx, p)
			fallthrough
		case encoder.OpString:
			b = appendString(ctx, b, ptrToString(load(ctxptr, code.Idx)))
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpBoolPtr:
			p := loadNPtr(ctxptr, code.Idx, code.PtrNum)
			if p == 0 {
				b = appendNullComma(ctx, b)
				code = code.Next
				break
			}
			store(ctxptr, code.Idx, p)
			fallthrough
		case encoder.OpBool:
			b = appendBool(ctx, b, ptrToBool(load(ctxptr, code.Idx)))
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpBytesPtr:
			p := loadNPtr(ctxptr, code.Idx, code.PtrNum)
			if p == 0 {
				b = appendNullComma(ctx, b)
				code = code.Next
				break
			}
			store(ctxptr, code.Idx, p)
			fallthrough
		case encoder.OpBytes:
			b = appendByteSlice(ctx, b, ptrToBytes(load(ctxptr, code.Idx)))
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpNumberPtr:
			p := loadNPtr(ctxptr, code.Idx, code.PtrNum)
			if p == 0 {
				b = appendNullComma(ctx, b)
				code = code.Next
				break
			}
			store(ctxptr, code.Idx, p)
			fallthrough
		case encoder.OpNumber:
			bb, err := appendNumber(ctx, b, ptrToNumber(load(ctxptr, code.Idx)))
			if err != nil {
				return nil, err
			}
			b = appendComma(ctx, bb)
			code = code.Next
		case encoder.OpInterfacePtr:
			p := loadNPtr(ctxptr, code.Idx, code.PtrNum)
			if p == 0 {
				b = appendNullComma(ctx, b)
				code = code.Next
				break
			}
			store(ctxptr, code.Idx, p)
			fallthrough
		case encoder.OpInterface:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				b = appendNullComma(ctx, b)
				code = code.Next
				break
			}
			if recursiveLevel > encoder.StartDetectingCyclesAfter {
				for _, seen := range ctx.SeenPtr {
					if p == seen {
						return nil, errUnsupportedValue(code, p)
					}
				}
			}
			ctx.SeenPtr = append(ctx.SeenPtr, p)
			var (
				typ      *runtime.Type
				ifacePtr unsafe.Pointer
			)
			up := ptrToUnsafePtr(p)
			if code.Flags&encoder.NonEmptyInterfaceFlags != 0 {
				iface := (*nonEmptyInterface)(up)
				ifacePtr = iface.ptr
				if iface.itab != nil {
					typ = iface.itab.typ
				}
			} else {
				iface := (*emptyInterface)(up)
				ifacePtr = iface.ptr
				typ = iface.typ
			}
			if ifacePtr == nil {
				isDirectedNil := typ != nil && typ.Kind() == reflect.Struct && !runtime.IfaceIndir(typ)
				if !isDirectedNil {
					b = appendNullComma(ctx, b)
					code = code.Next
					break
				}
			}
			ctx.KeepRefs = append(ctx.KeepRefs, up)
			ifaceCodeSet, err := encoder.CompileToGetCodeSet(ctx, uintptr(unsafe.Pointer(typ)))
			if err != nil {
				return nil, err
			}

			totalLength := uintptr(code.Length) + 3
			nextTotalLength := uintptr(ifaceCodeSet.CodeLength) + 3

			var c *encoder.Opcode
			if (ctx.Option.Flag & encoder.HTMLEscapeOption) != 0 {
				c = ifaceCodeSet.InterfaceEscapeKeyCode
			} else {
				c = ifaceCodeSet.InterfaceNoescapeKeyCode
			}
			curlen := uintptr(len(ctx.Ptrs))
			offsetNum := ptrOffset / uintptrSize
			oldOffset := ptrOffset
			ptrOffset += totalLength * uintptrSize
			oldBaseIndent := ctx.BaseIndent
			ctx.BaseIndent += code.Indent

			newLen := offsetNum + totalLength + nextTotalLength
			if curlen < newLen {
				ctx.Ptrs = append(ctx.Ptrs, make([]uintptr, newLen-curlen)...)
			}
			ctxptr = ctx.Ptr() + ptrOffset // assign new ctxptr

			end := ifaceCodeSet.EndCode
			store(ctxptr, c.Idx, uintptr(ifacePtr))
			store(ctxptr, end.Idx, oldOffset)
			store(ctxptr, end.ElemIdx, uintptr(unsafe.Pointer(code.Next)))
			storeIndent(ctxptr, end, uintptr(oldBaseIndent))
			code = c
			recursiveLevel++
		case encoder.OpInterfaceEnd:
			recursiveLevel--

			// restore ctxptr
			offset := load(ctxptr, code.Idx)
			restoreIndent(ctx, code, ctxptr)
			ctx.SeenPtr = ctx.SeenPtr[:len(ctx.SeenPtr)-1]

			codePtr := load(ctxptr, code.ElemIdx)
			code = (*encoder.Opcode)(ptrToUnsafePtr(codePtr))
			ctxptr = ctx.Ptr() + offset
			ptrOffset = offset
		case encoder.OpMarshalJSONPtr:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				b = appendNullComma(ctx, b)
				code = code.Next
				break
			}
			store(ctxptr, code.Idx, ptrToPtr(p))
			fallthrough
		case encoder.OpMarshalJSON:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				b = appendNullComma(ctx, b)
				code = code.Next
				break
			}
			if (code.Flags&encoder.IsNilableTypeFlags) != 0 && (code.Flags&encoder.IndirectFlags) != 0 {
				p = ptrToPtr(p)
			}
			bb, err := appendMarshalJSON(ctx, code, b, ptrToInterface(code, p))
			if err != nil {
				return nil, err
			}
			b = appendComma(ctx, bb)
			code = code.Next
		case encoder.OpMarshalTextPtr:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				b = appendNullComma(ctx, b)
				code = code.Next
				break
			}
			store(ctxptr, code.Idx, ptrToPtr(p))
			fallthrough
		case encoder.OpMarshalText:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				b = append(b, `""`...)
				b = appendComma(ctx, b)
				code = code.Next
				break
			}
			if (code.Flags&encoder.IsNilableTypeFlags) != 0 && (code.Flags&encoder.IndirectFlags) != 0 {
				p = ptrToPtr(p)
			}
			bb, err := appendMarshalText(ctx, code, b, ptrToInterface(code, p))
			if err != nil {
				return nil, err
			}
			b = appendComma(ctx, bb)
			code = code.Next
		case encoder.OpSlicePtr:
			p := loadNPtr(ctxptr, code.Idx, code.PtrNum)
			if p == 0 {
				b = appendNullComma(ctx, b)
				code = code.End.Next
				break
			}
			store(ctxptr, code.Idx, p)
			fallthrough
		case encoder.OpSlice:
			p := load(ctxptr, code.Idx)
			slice := ptrToSlice(p)
			if p == 0 || slice.Data == nil {
				b = appendNullComma(ctx, b)
				code = code.End.Next
				break
			}
			store(ctxptr, code.ElemIdx, 0)
			store(ctxptr, code.Length, uintptr(slice.Len))
			store(ctxptr, code.Idx, uintptr(slice.Data))
			if slice.Len > 0 {
				b = appendArrayHead(ctx, code, b)
				code = code.Next
				store(ctxptr, code.Idx, uintptr(slice.Data))
			} else {
				b = appendEmptyArray(ctx, b)
				code = code.End.Next
			}
		case encoder.OpSliceElem:
			idx := load(ctxptr, code.ElemIdx)
			length := load(ctxptr, code.Length)
			idx++
			if idx < length {
				b = appendArrayElemIndent(ctx, code, b)
				store(ctxptr, code.ElemIdx, idx)
				data := load(ctxptr, code.Idx)
				size := uintptr(code.Size)
				code = code.Next
				store(ctxptr, code.Idx, data+idx*size)
			} else {
				b = appendArrayEnd(ctx, code, b)
				code = code.End.Next
			}
		case encoder.OpArrayPtr:
			p := loadNPtr(ctxptr, code.Idx, code.PtrNum)
			if p == 0 {
				b = appendNullComma(ctx, b)
				code = code.End.Next
				break
			}
			store(ctxptr, code.Idx, p)
			fallthrough
		case encoder.OpArray:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				b = appendNullComma(ctx, b)
				code = code.End.Next
				break
			}
			if code.Length > 0 {
				b = appendArrayHead(ctx, code, b)
				store(ctxptr, code.ElemIdx, 0)
				code = code.Next
				store(ctxptr, code.Idx, p)
			} else {
				b = appendEmptyArray(ctx, b)
				code = code.End.Next
			}
		case encoder.OpArrayElem:
			idx := load(ctxptr, code.ElemIdx)
			idx++
			if idx < uintptr(code.Length) {
				b = appendArrayElemIndent(ctx, code, b)
				store(ctxptr, code.ElemIdx, idx)
				p := load(ctxptr, code.Idx)
				size := uintptr(code.Size)
				code = code.Next
				store(ctxptr, code.Idx, p+idx*size)
			} else {
				b = appendArrayEnd(ctx, code, b)
				code = code.End.Next
			}
		case encoder.OpMapPtr:
			p := loadNPtr(ctxptr, code.Idx, code.PtrNum)
			if p == 0 {
				b = appendNullComma(ctx, b)
				code = code.End.Next
				break
			}
			store(ctxptr, code.Idx, p)
			fallthrough
		case encoder.OpMap:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				b = appendNullComma(ctx, b)
				code = code.End.Next
				break
			}
			uptr := ptrToUnsafePtr(p)
			mlen := maplen(uptr)
			if mlen <= 0 {
				b = appendEmptyObject(ctx, b)
				code = code.End.Next
				break
			}
			b = appendStructHead(ctx, b)
			unorderedMap := (ctx.Option.Flag & encoder.UnorderedMapOption) != 0
			mapCtx := encoder.NewMapContext(mlen, unorderedMap)
			mapiterinit(code.Type, uptr, &mapCtx.Iter)
			store(ctxptr, code.Idx, uintptr(unsafe.Pointer(mapCtx)))
			ctx.KeepRefs = append(ctx.KeepRefs, unsafe.Pointer(mapCtx))
			if unorderedMap {
				b = appendMapKeyIndent(ctx, code.Next, b)
			} else {
				mapCtx.Start = len(b)
				mapCtx.First = len(b)
			}
			key := mapiterkey(&mapCtx.Iter)
			store(ctxptr, code.Next.Idx, uintptr(key))
			code = code.Next
		case encoder.OpMapKey:
			mapCtx := (*encoder.MapContext)(ptrToUnsafePtr(load(ctxptr, code.Idx)))
			idx := mapCtx.Idx
			idx++
			if (ctx.Option.Flag & encoder.UnorderedMapOption) != 0 {
				if idx < mapCtx.Len {
					b = appendMapKeyIndent(ctx, code, b)
					mapCtx.Idx = int(idx)
					key := mapiterkey(&mapCtx.Iter)
					store(ctxptr, code.Next.Idx, uintptr(key))
					code = code.Next
				} else {
					b = appendObjectEnd(ctx, code, b)
					encoder.ReleaseMapContext(mapCtx)
					code = code.End.Next
				}
			} else {
				mapCtx.Slice.Items[mapCtx.Idx].Value = b[mapCtx.Start:len(b)]
				if idx < mapCtx.Len {
					mapCtx.Idx = int(idx)
					mapCtx.Start = len(b)
					key := mapiterkey(&mapCtx.Iter)
					store(ctxptr, code.Next.Idx, uintptr(key))
					code = code.Next
				} else {
					code = code.End
				}
			}
		case encoder.OpMapValue:
			mapCtx := (*encoder.MapContext)(ptrToUnsafePtr(load(ctxptr, code.Idx)))
			if (ctx.Option.Flag & encoder.UnorderedMapOption) != 0 {
				b = appendColon(ctx, b)
			} else {
				mapCtx.Slice.Items[mapCtx.Idx].Key = b[mapCtx.Start:len(b)]
				mapCtx.Start = len(b)
			}
			value := mapitervalue(&mapCtx.Iter)
			store(ctxptr, code.Next.Idx, uintptr(value))
			mapiternext(&mapCtx.Iter)
			code = code.Next
		case encoder.OpMapEnd:
			// this operation only used by sorted map.
			mapCtx := (*encoder.MapContext)(ptrToUnsafePtr(load(ctxptr, code.Idx)))
			sort.Sort(mapCtx.Slice)
			buf := mapCtx.Buf
			for _, item := range mapCtx.Slice.Items {
				buf = appendMapKeyValue(ctx, code, buf, item.Key, item.Value)
			}
			buf = appendMapEnd(ctx, code, buf)
			b = b[:mapCtx.First]
			b = append(b, buf...)
			mapCtx.Buf = buf
			encoder.ReleaseMapContext(mapCtx)
			code = code.Next
		case encoder.OpRecursivePtr:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				code = code.Next
				break
			}
			store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			fallthrough
		case encoder.OpRecursive:
			ptr := load(ctxptr, code.Idx)
			if ptr != 0 {
				if recursiveLevel > encoder.StartDetectingCyclesAfter {
					for _, seen := range ctx.SeenPtr {
						if ptr == seen {
							return nil, errUnsupportedValue(code, ptr)
						}
					}
				}
			}
			ctx.SeenPtr = append(ctx.SeenPtr, ptr)
			c := code.Jmp.Code
			curlen := uintptr(len(ctx.Ptrs))
			offsetNum := ptrOffset / uintptrSize
			oldOffset := ptrOffset
			ptrOffset += code.Jmp.CurLen * uintptrSize
			oldBaseIndent := ctx.BaseIndent
			indentDiffFromTop := c.Indent - 1
			ctx.BaseIndent += code.Indent - indentDiffFromTop

			newLen := offsetNum + code.Jmp.CurLen + code.Jmp.NextLen
			if curlen < newLen {
				ctx.Ptrs = append(ctx.Ptrs, make([]uintptr, newLen-curlen)...)
			}
			ctxptr = ctx.Ptr() + ptrOffset // assign new ctxptr

			store(ctxptr, c.Idx, ptr)
			store(ctxptr, c.End.Next.Idx, oldOffset)
			store(ctxptr, c.End.Next.ElemIdx, uintptr(unsafe.Pointer(code.Next)))
			storeIndent(ctxptr, c.End.Next, uintptr(oldBaseIndent))
			code = c
			recursiveLevel++
		case encoder.OpRecursiveEnd:
			recursiveLevel--

			// restore ctxptr
			restoreIndent(ctx, code, ctxptr)
			offset := load(ctxptr, code.Idx)
			ctx.SeenPtr = ctx.SeenPtr[:len(ctx.SeenPtr)-1]

			codePtr := load(ctxptr, code.ElemIdx)
			code = (*encoder.Opcode)(ptrToUnsafePtr(codePtr))
			ctxptr = ctx.Ptr() + offset
			ptrOffset = offset
		case encoder.OpStructPtrHead:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			fallthrough
		case encoder.OpStructHead:
			p := load(ctxptr, code.Idx)
			if p == 0 && ((code.Flags&encoder.IndirectFlags) != 0 || code.Next.Op == encoder.OpStructEnd) {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			if len(code.Key) > 0 {
				if (code.Flags&encoder.IsTaggedKeyFlags) != 0 || code.Flags&encoder.AnonymousKeyFlags == 0 {
					b = appendStructKey(ctx, code, b)
				}
			}
			p += uintptr(code.Offset)
			code = code.Next
			store(ctxptr, code.Idx, p)
		case encoder.OpStructPtrHeadOmitEmpty:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			fallthrough
		case encoder.OpStructHeadOmitEmpty:
			p := load(ctxptr, code.Idx)
			if p == 0 && ((code.Flags&encoder.IndirectFlags) != 0 || code.Next.Op == encoder.OpStructEnd) {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			p += uintptr(code.Offset)
			if p == 0 || (ptrToPtr(p) == 0 && (code.Flags&encoder.IsNextOpPtrTypeFlags) != 0) {
				code = code.NextField
			} else {
				b = appendStructKey(ctx, code, b)
				code = code.Next
				store(ctxptr, code.Idx, p)
			}
		case encoder.OpStructPtrHeadInt:
			if (code.Flags & encoder.IndirectFlags) != 0 {
				p := load(ctxptr, code.Idx)
				if p == 0 {
					if code.Flags&encoder.AnonymousHeadFlags == 0 {
						b = appendNullComma(ctx, b)
					}
					code = code.End.Next
					break
				}
				store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			}
			fallthrough
		case encoder.OpStructHeadInt:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			b = appendStructKey(ctx, code, b)
			b = appendInt(ctx, b, p+uintptr(code.Offset), code)
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpStructPtrHeadOmitEmptyInt:
			if (code.Flags & encoder.IndirectFlags) != 0 {
				p := load(ctxptr, code.Idx)
				if p == 0 {
					if code.Flags&encoder.AnonymousHeadFlags == 0 {
						b = appendNullComma(ctx, b)
					}
					code = code.End.Next
					break
				}
				store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			}
			fallthrough
		case encoder.OpStructHeadOmitEmptyInt:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			u64 := ptrToUint64(p+uintptr(code.Offset), code.NumBitSize)
			v := u64 & ((1 << code.NumBitSize) - 1)
			if v == 0 {
				code = code.NextField
			} else {
				b = appendStructKey(ctx, code, b)
				b = appendInt(ctx, b, p+uintptr(code.Offset), code)
				b = appendComma(ctx, b)
				code = code.Next
			}
		case encoder.OpStructPtrHeadIntString:
			if (code.Flags & encoder.IndirectFlags) != 0 {
				p := load(ctxptr, code.Idx)
				if p == 0 {
					if code.Flags&encoder.AnonymousHeadFlags == 0 {
						b = appendNullComma(ctx, b)
					}
					code = code.End.Next
					break
				}
				store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			}
			fallthrough
		case encoder.OpStructHeadIntString:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			b = appendStructKey(ctx, code, b)
			b = append(b, '"')
			b = appendInt(ctx, b, p+uintptr(code.Offset), code)
			b = append(b, '"')
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpStructPtrHeadOmitEmptyIntString:
			if (code.Flags & encoder.IndirectFlags) != 0 {
				p := load(ctxptr, code.Idx)
				if p == 0 {
					if code.Flags&encoder.AnonymousHeadFlags == 0 {
						b = appendNullComma(ctx, b)
					}
					code = code.End.Next
					break
				}
				store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			}
			fallthrough
		case encoder.OpStructHeadOmitEmptyIntString:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			p += uintptr(code.Offset)
			u64 := ptrToUint64(p, code.NumBitSize)
			v := u64 & ((1 << code.NumBitSize) - 1)
			if v == 0 {
				code = code.NextField
			} else {
				b = appendStructKey(ctx, code, b)
				b = append(b, '"')
				b = appendInt(ctx, b, p, code)
				b = append(b, '"')
				b = appendComma(ctx, b)
				code = code.Next
			}
		case encoder.OpStructPtrHeadIntPtr:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			fallthrough
		case encoder.OpStructHeadIntPtr:
			p := load(ctxptr, code.Idx)
			if p == 0 && (code.Flags&encoder.IndirectFlags) != 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			b = appendStructKey(ctx, code, b)
			if (code.Flags & encoder.IndirectFlags) != 0 {
				p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			}
			if p == 0 {
				b = appendNull(ctx, b)
			} else {
				b = appendInt(ctx, b, p, code)
			}
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpStructPtrHeadOmitEmptyIntPtr:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			fallthrough
		case encoder.OpStructHeadOmitEmptyIntPtr:
			p := load(ctxptr, code.Idx)
			if p == 0 && (code.Flags&encoder.IndirectFlags) != 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			if (code.Flags & encoder.IndirectFlags) != 0 {
				p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			}
			if p != 0 {
				b = appendStructKey(ctx, code, b)
				b = appendInt(ctx, b, p, code)
				b = appendComma(ctx, b)
			}
			code = code.Next
		case encoder.OpStructPtrHeadIntPtrString:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			fallthrough
		case encoder.OpStructHeadIntPtrString:
			p := load(ctxptr, code.Idx)
			if p == 0 && (code.Flags&encoder.IndirectFlags) != 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			b = appendStructKey(ctx, code, b)
			if (code.Flags & encoder.IndirectFlags) != 0 {
				p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			}
			if p == 0 {
				b = appendNull(ctx, b)
			} else {
				b = append(b, '"')
				b = appendInt(ctx, b, p, code)
				b = append(b, '"')
			}
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpStructPtrHeadOmitEmptyIntPtrString:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			fallthrough
		case encoder.OpStructHeadOmitEmptyIntPtrString:
			p := load(ctxptr, code.Idx)
			if p == 0 && (code.Flags&encoder.IndirectFlags) != 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			if (code.Flags & encoder.IndirectFlags) != 0 {
				p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			}
			if p != 0 {
				b = appendStructKey(ctx, code, b)
				b = append(b, '"')
				b = appendInt(ctx, b, p, code)
				b = append(b, '"')
				b = appendComma(ctx, b)
			}
			code = code.Next
		case encoder.OpStructPtrHeadUint:
			if (code.Flags & encoder.IndirectFlags) != 0 {
				p := load(ctxptr, code.Idx)
				if p == 0 {
					if code.Flags&encoder.AnonymousHeadFlags == 0 {
						b = appendNullComma(ctx, b)
					}
					code = code.End.Next
					break
				}
				store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			}
			fallthrough
		case encoder.OpStructHeadUint:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			b = appendStructKey(ctx, code, b)
			b = appendUint(ctx, b, p+uintptr(code.Offset), code)
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpStructPtrHeadOmitEmptyUint:
			if (code.Flags & encoder.IndirectFlags) != 0 {
				p := load(ctxptr, code.Idx)
				if p == 0 {
					if code.Flags&encoder.AnonymousHeadFlags == 0 {
						b = appendNullComma(ctx, b)
					}
					code = code.End.Next
					break
				}
				store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			}
			fallthrough
		case encoder.OpStructHeadOmitEmptyUint:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			u64 := ptrToUint64(p+uintptr(code.Offset), code.NumBitSize)
			v := u64 & ((1 << code.NumBitSize) - 1)
			if v == 0 {
				code = code.NextField
			} else {
				b = appendStructKey(ctx, code, b)
				b = appendUint(ctx, b, p+uintptr(code.Offset), code)
				b = appendComma(ctx, b)
				code = code.Next
			}
		case encoder.OpStructPtrHeadUintString:
			if (code.Flags & encoder.IndirectFlags) != 0 {
				p := load(ctxptr, code.Idx)
				if p == 0 {
					if code.Flags&encoder.AnonymousHeadFlags == 0 {
						b = appendNullComma(ctx, b)
					}
					code = code.End.Next
					break
				}
				store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			}
			fallthrough
		case encoder.OpStructHeadUintString:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			b = appendStructKey(ctx, code, b)
			b = append(b, '"')
			b = appendUint(ctx, b, p+uintptr(code.Offset), code)
			b = append(b, '"')
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpStructPtrHeadOmitEmptyUintString:
			if (code.Flags & encoder.IndirectFlags) != 0 {
				p := load(ctxptr, code.Idx)
				if p == 0 {
					if code.Flags&encoder.AnonymousHeadFlags == 0 {
						b = appendNullComma(ctx, b)
					}
					code = code.End.Next
					break
				}
				store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			}
			fallthrough
		case encoder.OpStructHeadOmitEmptyUintString:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			u64 := ptrToUint64(p+uintptr(code.Offset), code.NumBitSize)
			v := u64 & ((1 << code.NumBitSize) - 1)
			if v == 0 {
				code = code.NextField
			} else {
				b = appendStructKey(ctx, code, b)
				b = append(b, '"')
				b = appendUint(ctx, b, p+uintptr(code.Offset), code)
				b = append(b, '"')
				b = appendComma(ctx, b)
				code = code.Next
			}
		case encoder.OpStructPtrHeadUintPtr:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			fallthrough
		case encoder.OpStructHeadUintPtr:
			p := load(ctxptr, code.Idx)
			if p == 0 && (code.Flags&encoder.IndirectFlags) != 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			b = appendStructKey(ctx, code, b)
			if (code.Flags & encoder.IndirectFlags) != 0 {
				p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			}
			if p == 0 {
				b = appendNull(ctx, b)
			} else {
				b = appendUint(ctx, b, p, code)
			}
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpStructPtrHeadOmitEmptyUintPtr:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			fallthrough
		case encoder.OpStructHeadOmitEmptyUintPtr:
			p := load(ctxptr, code.Idx)
			if p == 0 && (code.Flags&encoder.IndirectFlags) != 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			if (code.Flags & encoder.IndirectFlags) != 0 {
				p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			}
			if p != 0 {
				b = appendStructKey(ctx, code, b)
				b = appendUint(ctx, b, p, code)
				b = appendComma(ctx, b)
			}
			code = code.Next
		case encoder.OpStructPtrHeadUintPtrString:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			fallthrough
		case encoder.OpStructHeadUintPtrString:
			p := load(ctxptr, code.Idx)
			if p == 0 && (code.Flags&encoder.IndirectFlags) != 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			b = appendStructKey(ctx, code, b)
			if (code.Flags & encoder.IndirectFlags) != 0 {
				p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			}
			if p == 0 {
				b = appendNull(ctx, b)
			} else {
				b = append(b, '"')
				b = appendUint(ctx, b, p, code)
				b = append(b, '"')
			}
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpStructPtrHeadOmitEmptyUintPtrString:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			fallthrough
		case encoder.OpStructHeadOmitEmptyUintPtrString:
			p := load(ctxptr, code.Idx)
			if p == 0 && (code.Flags&encoder.IndirectFlags) != 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			if (code.Flags & encoder.IndirectFlags) != 0 {
				p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			}
			if p != 0 {
				b = appendStructKey(ctx, code, b)
				b = append(b, '"')
				b = appendUint(ctx, b, p, code)
				b = append(b, '"')
				b = appendComma(ctx, b)
			}
			code = code.Next
		case encoder.OpStructPtrHeadFloat32:
			if (code.Flags & encoder.IndirectFlags) != 0 {
				p := load(ctxptr, code.Idx)
				if p == 0 {
					if code.Flags&encoder.AnonymousHeadFlags == 0 {
						b = appendNullComma(ctx, b)
					}
					code = code.End.Next
					break
				}
				store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			}
			fallthrough
		case encoder.OpStructHeadFloat32:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			b = appendStructKey(ctx, code, b)
			b = appendFloat32(ctx, b, ptrToFloat32(p+uintptr(code.Offset)))
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpStructPtrHeadOmitEmptyFloat32:
			if (code.Flags & encoder.IndirectFlags) != 0 {
				p := load(ctxptr, code.Idx)
				if p == 0 {
					if code.Flags&encoder.AnonymousHeadFlags == 0 {
						b = appendNullComma(ctx, b)
					}
					code = code.End.Next
					break
				}
				store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			}
			fallthrough
		case encoder.OpStructHeadOmitEmptyFloat32:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			v := ptrToFloat32(p + uintptr(code.Offset))
			if v == 0 {
				code = code.NextField
			} else {
				b = appendStructKey(ctx, code, b)
				b = appendFloat32(ctx, b, v)
				b = appendComma(ctx, b)
				code = code.Next
			}
		case encoder.OpStructPtrHeadFloat32String:
			if (code.Flags & encoder.IndirectFlags) != 0 {
				p := load(ctxptr, code.Idx)
				if p == 0 {
					if code.Flags&encoder.AnonymousHeadFlags == 0 {
						b = appendNullComma(ctx, b)
					}
					code = code.End.Next
					break
				}
				store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			}
			fallthrough
		case encoder.OpStructHeadFloat32String:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			b = appendStructKey(ctx, code, b)
			b = append(b, '"')
			b = appendFloat32(ctx, b, ptrToFloat32(p+uintptr(code.Offset)))
			b = append(b, '"')
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpStructPtrHeadOmitEmptyFloat32String:
			if (code.Flags & encoder.IndirectFlags) != 0 {
				p := load(ctxptr, code.Idx)
				if p == 0 {
					if code.Flags&encoder.AnonymousHeadFlags == 0 {
						b = appendNullComma(ctx, b)
					}
					code = code.End.Next
					break
				}
				store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			}
			fallthrough
		case encoder.OpStructHeadOmitEmptyFloat32String:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			v := ptrToFloat32(p + uintptr(code.Offset))
			if v == 0 {
				code = code.NextField
			} else {
				b = appendStructKey(ctx, code, b)
				b = append(b, '"')
				b = appendFloat32(ctx, b, v)
				b = append(b, '"')
				b = appendComma(ctx, b)
				code = code.Next
			}
		case encoder.OpStructPtrHeadFloat32Ptr:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			fallthrough
		case encoder.OpStructHeadFloat32Ptr:
			p := load(ctxptr, code.Idx)
			if p == 0 && (code.Flags&encoder.IndirectFlags) != 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			b = appendStructKey(ctx, code, b)
			if (code.Flags & encoder.IndirectFlags) != 0 {
				p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			}
			if p == 0 {
				b = appendNull(ctx, b)
			} else {
				b = appendFloat32(ctx, b, ptrToFloat32(p))
			}
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpStructPtrHeadOmitEmptyFloat32Ptr:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			fallthrough
		case encoder.OpStructHeadOmitEmptyFloat32Ptr:
			p := load(ctxptr, code.Idx)
			if p == 0 && (code.Flags&encoder.IndirectFlags) != 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			if (code.Flags & encoder.IndirectFlags) != 0 {
				p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			}
			if p != 0 {
				b = appendStructKey(ctx, code, b)
				b = appendFloat32(ctx, b, ptrToFloat32(p))
				b = appendComma(ctx, b)
			}
			code = code.Next
		case encoder.OpStructPtrHeadFloat32PtrString:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			fallthrough
		case encoder.OpStructHeadFloat32PtrString:
			p := load(ctxptr, code.Idx)
			if p == 0 && (code.Flags&encoder.IndirectFlags) != 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			b = appendStructKey(ctx, code, b)
			if (code.Flags & encoder.IndirectFlags) != 0 {
				p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			}
			if p == 0 {
				b = appendNull(ctx, b)
			} else {
				b = append(b, '"')
				b = appendFloat32(ctx, b, ptrToFloat32(p))
				b = append(b, '"')
			}
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpStructPtrHeadOmitEmptyFloat32PtrString:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			fallthrough
		case encoder.OpStructHeadOmitEmptyFloat32PtrString:
			p := load(ctxptr, code.Idx)
			if p == 0 && (code.Flags&encoder.IndirectFlags) != 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			if (code.Flags & encoder.IndirectFlags) != 0 {
				p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			}
			if p != 0 {
				b = appendStructKey(ctx, code, b)
				b = append(b, '"')
				b = appendFloat32(ctx, b, ptrToFloat32(p))
				b = append(b, '"')
				b = appendComma(ctx, b)
			}
			code = code.Next
		case encoder.OpStructPtrHeadFloat64:
			if (code.Flags & encoder.IndirectFlags) != 0 {
				p := load(ctxptr, code.Idx)
				if p == 0 {
					if code.Flags&encoder.AnonymousHeadFlags == 0 {
						b = appendNullComma(ctx, b)
					}
					code = code.End.Next
					break
				}
				store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			}
			fallthrough
		case encoder.OpStructHeadFloat64:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			v := ptrToFloat64(p + uintptr(code.Offset))
			if math.IsInf(v, 0) || math.IsNaN(v) {
				return nil, errUnsupportedFloat(v)
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			b = appendStructKey(ctx, code, b)
			b = appendFloat64(ctx, b, v)
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpStructPtrHeadOmitEmptyFloat64:
			if (code.Flags & encoder.IndirectFlags) != 0 {
				p := load(ctxptr, code.Idx)
				if p == 0 {
					if code.Flags&encoder.AnonymousHeadFlags == 0 {
						b = appendNullComma(ctx, b)
					}
					code = code.End.Next
					break
				}
				store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			}
			fallthrough
		case encoder.OpStructHeadOmitEmptyFloat64:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			v := ptrToFloat64(p + uintptr(code.Offset))
			if v == 0 {
				code = code.NextField
			} else {
				if math.IsInf(v, 0) || math.IsNaN(v) {
					return nil, errUnsupportedFloat(v)
				}
				b = appendStructKey(ctx, code, b)
				b = appendFloat64(ctx, b, v)
				b = appendComma(ctx, b)
				code = code.Next
			}
		case encoder.OpStructPtrHeadFloat64String:
			if (code.Flags & encoder.IndirectFlags) != 0 {
				p := load(ctxptr, code.Idx)
				if p == 0 {
					if code.Flags&encoder.AnonymousHeadFlags == 0 {
						b = appendNullComma(ctx, b)
					}
					code = code.End.Next
					break
				}
				store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			}
			fallthrough
		case encoder.OpStructHeadFloat64String:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			v := ptrToFloat64(p + uintptr(code.Offset))
			if math.IsInf(v, 0) || math.IsNaN(v) {
				return nil, errUnsupportedFloat(v)
			}
			b = appendStructKey(ctx, code, b)
			b = append(b, '"')
			b = appendFloat64(ctx, b, v)
			b = append(b, '"')
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpStructPtrHeadOmitEmptyFloat64String:
			if (code.Flags & encoder.IndirectFlags) != 0 {
				p := load(ctxptr, code.Idx)
				if p == 0 {
					if code.Flags&encoder.AnonymousHeadFlags == 0 {
						b = appendNullComma(ctx, b)
					}
					code = code.End.Next
					break
				}
				store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			}
			fallthrough
		case encoder.OpStructHeadOmitEmptyFloat64String:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			v := ptrToFloat64(p + uintptr(code.Offset))
			if v == 0 {
				code = code.NextField
			} else {
				if math.IsInf(v, 0) || math.IsNaN(v) {
					return nil, errUnsupportedFloat(v)
				}
				b = appendStructKey(ctx, code, b)
				b = append(b, '"')
				b = appendFloat64(ctx, b, v)
				b = append(b, '"')
				b = appendComma(ctx, b)
				code = code.Next
			}
		case encoder.OpStructPtrHeadFloat64Ptr:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			fallthrough
		case encoder.OpStructHeadFloat64Ptr:
			p := load(ctxptr, code.Idx)
			if p == 0 && (code.Flags&encoder.IndirectFlags) != 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			b = appendStructKey(ctx, code, b)
			if (code.Flags & encoder.IndirectFlags) != 0 {
				p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			}
			if p == 0 {
				b = appendNull(ctx, b)
			} else {
				v := ptrToFloat64(p)
				if math.IsInf(v, 0) || math.IsNaN(v) {
					return nil, errUnsupportedFloat(v)
				}
				b = appendFloat64(ctx, b, v)
			}
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpStructPtrHeadOmitEmptyFloat64Ptr:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			fallthrough
		case encoder.OpStructHeadOmitEmptyFloat64Ptr:
			p := load(ctxptr, code.Idx)
			if p == 0 && (code.Flags&encoder.IndirectFlags) != 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			if (code.Flags & encoder.IndirectFlags) != 0 {
				p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			}
			if p != 0 {
				b = appendStructKey(ctx, code, b)
				v := ptrToFloat64(p)
				if math.IsInf(v, 0) || math.IsNaN(v) {
					return nil, errUnsupportedFloat(v)
				}
				b = appendFloat64(ctx, b, v)
				b = appendComma(ctx, b)
			}
			code = code.Next
		case encoder.OpStructPtrHeadFloat64PtrString:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			fallthrough
		case encoder.OpStructHeadFloat64PtrString:
			p := load(ctxptr, code.Idx)
			if p == 0 && (code.Flags&encoder.IndirectFlags) != 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			b = appendStructKey(ctx, code, b)
			if (code.Flags & encoder.IndirectFlags) != 0 {
				p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			}
			if p == 0 {
				b = appendNull(ctx, b)
			} else {
				b = append(b, '"')
				v := ptrToFloat64(p)
				if math.IsInf(v, 0) || math.IsNaN(v) {
					return nil, errUnsupportedFloat(v)
				}
				b = appendFloat64(ctx, b, v)
				b = append(b, '"')
			}
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpStructPtrHeadOmitEmptyFloat64PtrString:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			fallthrough
		case encoder.OpStructHeadOmitEmptyFloat64PtrString:
			p := load(ctxptr, code.Idx)
			if p == 0 && (code.Flags&encoder.IndirectFlags) != 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			if (code.Flags & encoder.IndirectFlags) != 0 {
				p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			}
			if p != 0 {
				b = appendStructKey(ctx, code, b)
				b = append(b, '"')
				v := ptrToFloat64(p)
				if math.IsInf(v, 0) || math.IsNaN(v) {
					return nil, errUnsupportedFloat(v)
				}
				b = appendFloat64(ctx, b, v)
				b = append(b, '"')
				b = appendComma(ctx, b)
			}
			code = code.Next
		case encoder.OpStructPtrHeadString:
			if (code.Flags & encoder.IndirectFlags) != 0 {
				p := load(ctxptr, code.Idx)
				if p == 0 {
					if code.Flags&encoder.AnonymousHeadFlags == 0 {
						b = appendNullComma(ctx, b)
					}
					code = code.End.Next
					break
				}
				store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			}
			fallthrough
		case encoder.OpStructHeadString:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNull(ctx, b)
					b = appendComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			b = appendStructKey(ctx, code, b)
			b = appendString(ctx, b, ptrToString(p+uintptr(code.Offset)))
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpStructPtrHeadOmitEmptyString:
			if (code.Flags & encoder.IndirectFlags) != 0 {
				p := load(ctxptr, code.Idx)
				if p == 0 {
					if code.Flags&encoder.AnonymousHeadFlags == 0 {
						b = appendNullComma(ctx, b)
					}
					code = code.End.Next
					break
				}
				store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			}
			fallthrough
		case encoder.OpStructHeadOmitEmptyString:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			v := ptrToString(p + uintptr(code.Offset))
			if v == "" {
				code = code.NextField
			} else {
				b = appendStructKey(ctx, code, b)
				b = appendString(ctx, b, v)
				b = appendComma(ctx, b)
				code = code.Next
			}
		case encoder.OpStructPtrHeadStringString:
			if (code.Flags & encoder.IndirectFlags) != 0 {
				p := load(ctxptr, code.Idx)
				if p == 0 {
					if code.Flags&encoder.AnonymousHeadFlags == 0 {
						b = appendNullComma(ctx, b)
					}
					code = code.End.Next
					break
				}
				store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			}
			fallthrough
		case encoder.OpStructHeadStringString:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			b = appendStructKey(ctx, code, b)
			b = appendString(ctx, b, string(appendString(ctx, []byte{}, ptrToString(p+uintptr(code.Offset)))))
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpStructPtrHeadOmitEmptyStringString:
			if (code.Flags & encoder.IndirectFlags) != 0 {
				p := load(ctxptr, code.Idx)
				if p == 0 {
					if code.Flags&encoder.AnonymousHeadFlags == 0 {
						b = appendNullComma(ctx, b)
					}
					code = code.End.Next
					break
				}
				store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			}
			fallthrough
		case encoder.OpStructHeadOmitEmptyStringString:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			v := ptrToString(p + uintptr(code.Offset))
			if v == "" {
				code = code.NextField
			} else {
				b = appendStructKey(ctx, code, b)
				b = appendString(ctx, b, string(appendString(ctx, []byte{}, v)))
				b = appendComma(ctx, b)
				code = code.Next
			}
		case encoder.OpStructPtrHeadStringPtr:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			fallthrough
		case encoder.OpStructHeadStringPtr:
			p := load(ctxptr, code.Idx)
			if p == 0 && (code.Flags&encoder.IndirectFlags) != 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			b = appendStructKey(ctx, code, b)
			if (code.Flags & encoder.IndirectFlags) != 0 {
				p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			}
			if p == 0 {
				b = appendNull(ctx, b)
			} else {
				b = appendString(ctx, b, ptrToString(p))
			}
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpStructPtrHeadOmitEmptyStringPtr:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			fallthrough
		case encoder.OpStructHeadOmitEmptyStringPtr:
			p := load(ctxptr, code.Idx)
			if p == 0 && (code.Flags&encoder.IndirectFlags) != 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			if (code.Flags & encoder.IndirectFlags) != 0 {
				p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			}
			if p != 0 {
				b = appendStructKey(ctx, code, b)
				b = appendString(ctx, b, ptrToString(p))
				b = appendComma(ctx, b)
			}
			code = code.Next
		case encoder.OpStructPtrHeadStringPtrString:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			fallthrough
		case encoder.OpStructHeadStringPtrString:
			p := load(ctxptr, code.Idx)
			if p == 0 && (code.Flags&encoder.IndirectFlags) != 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			b = appendStructKey(ctx, code, b)
			if (code.Flags & encoder.IndirectFlags) != 0 {
				p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			}
			if p == 0 {
				b = appendNull(ctx, b)
			} else {
				b = appendString(ctx, b, string(appendString(ctx, []byte{}, ptrToString(p))))
			}
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpStructPtrHeadOmitEmptyStringPtrString:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			fallthrough
		case encoder.OpStructHeadOmitEmptyStringPtrString:
			p := load(ctxptr, code.Idx)
			if p == 0 && (code.Flags&encoder.IndirectFlags) != 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			if (code.Flags & encoder.IndirectFlags) != 0 {
				p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			}
			if p != 0 {
				b = appendStructKey(ctx, code, b)
				b = appendString(ctx, b, string(appendString(ctx, []byte{}, ptrToString(p))))
				b = appendComma(ctx, b)
			}
			code = code.Next
		case encoder.OpStructPtrHeadBool:
			if (code.Flags & encoder.IndirectFlags) != 0 {
				p := load(ctxptr, code.Idx)
				if p == 0 {
					if code.Flags&encoder.AnonymousHeadFlags == 0 {
						b = appendNullComma(ctx, b)
					}
					code = code.End.Next
					break
				}
				store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			}
			fallthrough
		case encoder.OpStructHeadBool:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			b = appendStructKey(ctx, code, b)
			b = appendBool(ctx, b, ptrToBool(p+uintptr(code.Offset)))
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpStructPtrHeadOmitEmptyBool:
			if (code.Flags & encoder.IndirectFlags) != 0 {
				p := load(ctxptr, code.Idx)
				if p == 0 {
					if code.Flags&encoder.AnonymousHeadFlags == 0 {
						b = appendNullComma(ctx, b)
					}
					code = code.End.Next
					break
				}
				store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			}
			fallthrough
		case encoder.OpStructHeadOmitEmptyBool:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			v := ptrToBool(p + uintptr(code.Offset))
			if v {
				b = appendStructKey(ctx, code, b)
				b = appendBool(ctx, b, v)
				b = appendComma(ctx, b)
				code = code.Next
			} else {
				code = code.NextField
			}
		case encoder.OpStructPtrHeadBoolString:
			if (code.Flags & encoder.IndirectFlags) != 0 {
				p := load(ctxptr, code.Idx)
				if p == 0 {
					if code.Flags&encoder.AnonymousHeadFlags == 0 {
						b = appendNullComma(ctx, b)
					}
					code = code.End.Next
					break
				}
				store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			}
			fallthrough
		case encoder.OpStructHeadBoolString:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			b = appendStructKey(ctx, code, b)
			b = append(b, '"')
			b = appendBool(ctx, b, ptrToBool(p+uintptr(code.Offset)))
			b = append(b, '"')
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpStructPtrHeadOmitEmptyBoolString:
			if (code.Flags & encoder.IndirectFlags) != 0 {
				p := load(ctxptr, code.Idx)
				if p == 0 {
					if code.Flags&encoder.AnonymousHeadFlags == 0 {
						b = appendNullComma(ctx, b)
					}
					code = code.End.Next
					break
				}
				store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			}
			fallthrough
		case encoder.OpStructHeadOmitEmptyBoolString:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			v := ptrToBool(p + uintptr(code.Offset))
			if v {
				b = appendStructKey(ctx, code, b)
				b = append(b, '"')
				b = appendBool(ctx, b, v)
				b = append(b, '"')
				b = appendComma(ctx, b)
				code = code.Next
			} else {
				code = code.NextField
			}
		case encoder.OpStructPtrHeadBoolPtr:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			fallthrough
		case encoder.OpStructHeadBoolPtr:
			p := load(ctxptr, code.Idx)
			if p == 0 && (code.Flags&encoder.IndirectFlags) != 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			b = appendStructKey(ctx, code, b)
			if (code.Flags & encoder.IndirectFlags) != 0 {
				p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			}
			if p == 0 {
				b = appendNull(ctx, b)
			} else {
				b = appendBool(ctx, b, ptrToBool(p))
			}
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpStructPtrHeadOmitEmptyBoolPtr:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			fallthrough
		case encoder.OpStructHeadOmitEmptyBoolPtr:
			p := load(ctxptr, code.Idx)
			if p == 0 && (code.Flags&encoder.IndirectFlags) != 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			if (code.Flags & encoder.IndirectFlags) != 0 {
				p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			}
			if p != 0 {
				b = appendStructKey(ctx, code, b)
				b = appendBool(ctx, b, ptrToBool(p))
				b = appendComma(ctx, b)
			}
			code = code.Next
		case encoder.OpStructPtrHeadBoolPtrString:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			fallthrough
		case encoder.OpStructHeadBoolPtrString:
			p := load(ctxptr, code.Idx)
			if p == 0 && (code.Flags&encoder.IndirectFlags) != 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			b = appendStructKey(ctx, code, b)
			if (code.Flags & encoder.IndirectFlags) != 0 {
				p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			}
			if p == 0 {
				b = appendNull(ctx, b)
			} else {
				b = append(b, '"')
				b = appendBool(ctx, b, ptrToBool(p))
				b = append(b, '"')
			}
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpStructPtrHeadOmitEmptyBoolPtrString:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			fallthrough
		case encoder.OpStructHeadOmitEmptyBoolPtrString:
			p := load(ctxptr, code.Idx)
			if p == 0 && (code.Flags&encoder.IndirectFlags) != 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			if (code.Flags & encoder.IndirectFlags) != 0 {
				p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			}
			if p != 0 {
				b = appendStructKey(ctx, code, b)
				b = append(b, '"')
				b = appendBool(ctx, b, ptrToBool(p))
				b = append(b, '"')
				b = appendComma(ctx, b)
			}
			code = code.Next
		case encoder.OpStructPtrHeadBytes:
			if (code.Flags & encoder.IndirectFlags) != 0 {
				p := load(ctxptr, code.Idx)
				if p == 0 {
					if code.Flags&encoder.AnonymousHeadFlags == 0 {
						b = appendNullComma(ctx, b)
					}
					code = code.End.Next
					break
				}
				store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			}
			fallthrough
		case encoder.OpStructHeadBytes:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			b = appendStructKey(ctx, code, b)
			b = appendByteSlice(ctx, b, ptrToBytes(p+uintptr(code.Offset)))
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpStructPtrHeadOmitEmptyBytes:
			if (code.Flags & encoder.IndirectFlags) != 0 {
				p := load(ctxptr, code.Idx)
				if p == 0 {
					if code.Flags&encoder.AnonymousHeadFlags == 0 {
						b = appendNullComma(ctx, b)
					}
					code = code.End.Next
					break
				}
				store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			}
			fallthrough
		case encoder.OpStructHeadOmitEmptyBytes:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			v := ptrToBytes(p + uintptr(code.Offset))
			if len(v) == 0 {
				code = code.NextField
			} else {
				b = appendStructKey(ctx, code, b)
				b = appendByteSlice(ctx, b, v)
				b = appendComma(ctx, b)
				code = code.Next
			}
		case encoder.OpStructPtrHeadBytesPtr:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			fallthrough
		case encoder.OpStructHeadBytesPtr:
			p := load(ctxptr, code.Idx)
			if p == 0 && (code.Flags&encoder.IndirectFlags) != 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			b = appendStructKey(ctx, code, b)
			if (code.Flags & encoder.IndirectFlags) != 0 {
				p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			}
			if p == 0 {
				b = appendNull(ctx, b)
			} else {
				b = appendByteSlice(ctx, b, ptrToBytes(p))
			}
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpStructPtrHeadOmitEmptyBytesPtr:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			fallthrough
		case encoder.OpStructHeadOmitEmptyBytesPtr:
			p := load(ctxptr, code.Idx)
			if p == 0 && (code.Flags&encoder.IndirectFlags) != 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			if (code.Flags & encoder.IndirectFlags) != 0 {
				p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			}
			if p != 0 {
				b = appendStructKey(ctx, code, b)
				b = appendByteSlice(ctx, b, ptrToBytes(p))
				b = appendComma(ctx, b)
			}
			code = code.Next
		case encoder.OpStructPtrHeadNumber:
			if (code.Flags & encoder.IndirectFlags) != 0 {
				p := load(ctxptr, code.Idx)
				if p == 0 {
					if code.Flags&encoder.AnonymousHeadFlags == 0 {
						b = appendNullComma(ctx, b)
					}
					code = code.End.Next
					break
				}
				store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			}
			fallthrough
		case encoder.OpStructHeadNumber:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			b = appendStructKey(ctx, code, b)
			bb, err := appendNumber(ctx, b, ptrToNumber(p+uintptr(code.Offset)))
			if err != nil {
				return nil, err
			}
			b = appendComma(ctx, bb)
			code = code.Next
		case encoder.OpStructPtrHeadOmitEmptyNumber:
			if (code.Flags & encoder.IndirectFlags) != 0 {
				p := load(ctxptr, code.Idx)
				if p == 0 {
					if code.Flags&encoder.AnonymousHeadFlags == 0 {
						b = appendNullComma(ctx, b)
					}
					code = code.End.Next
					break
				}
				store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			}
			fallthrough
		case encoder.OpStructHeadOmitEmptyNumber:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			v := ptrToNumber(p + uintptr(code.Offset))
			if v == "" {
				code = code.NextField
			} else {
				b = appendStructKey(ctx, code, b)
				bb, err := appendNumber(ctx, b, v)
				if err != nil {
					return nil, err
				}
				b = appendComma(ctx, bb)
				code = code.Next
			}
		case encoder.OpStructPtrHeadNumberString:
			if (code.Flags & encoder.IndirectFlags) != 0 {
				p := load(ctxptr, code.Idx)
				if p == 0 {
					if code.Flags&encoder.AnonymousHeadFlags == 0 {
						b = appendNullComma(ctx, b)
					}
					code = code.End.Next
					break
				}
				store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			}
			fallthrough
		case encoder.OpStructHeadNumberString:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			b = appendStructKey(ctx, code, b)
			b = append(b, '"')
			bb, err := appendNumber(ctx, b, ptrToNumber(p+uintptr(code.Offset)))
			if err != nil {
				return nil, err
			}
			b = append(bb, '"')
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpStructPtrHeadOmitEmptyNumberString:
			if (code.Flags & encoder.IndirectFlags) != 0 {
				p := load(ctxptr, code.Idx)
				if p == 0 {
					if code.Flags&encoder.AnonymousHeadFlags == 0 {
						b = appendNullComma(ctx, b)
					}
					code = code.End.Next
					break
				}
				store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			}
			fallthrough
		case encoder.OpStructHeadOmitEmptyNumberString:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			v := ptrToNumber(p + uintptr(code.Offset))
			if v == "" {
				code = code.NextField
			} else {
				b = appendStructKey(ctx, code, b)
				b = append(b, '"')
				bb, err := appendNumber(ctx, b, v)
				if err != nil {
					return nil, err
				}
				b = append(bb, '"')
				b = appendComma(ctx, b)
				code = code.Next
			}
		case encoder.OpStructPtrHeadNumberPtr:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			fallthrough
		case encoder.OpStructHeadNumberPtr:
			p := load(ctxptr, code.Idx)
			if p == 0 && (code.Flags&encoder.IndirectFlags) != 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			b = appendStructKey(ctx, code, b)
			if (code.Flags & encoder.IndirectFlags) != 0 {
				p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			}
			if p == 0 {
				b = appendNull(ctx, b)
			} else {
				bb, err := appendNumber(ctx, b, ptrToNumber(p))
				if err != nil {
					return nil, err
				}
				b = bb
			}
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpStructPtrHeadOmitEmptyNumberPtr:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			fallthrough
		case encoder.OpStructHeadOmitEmptyNumberPtr:
			p := load(ctxptr, code.Idx)
			if p == 0 && (code.Flags&encoder.IndirectFlags) != 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			if (code.Flags & encoder.IndirectFlags) != 0 {
				p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			}
			if p != 0 {
				b = appendStructKey(ctx, code, b)
				bb, err := appendNumber(ctx, b, ptrToNumber(p))
				if err != nil {
					return nil, err
				}
				b = appendComma(ctx, bb)
			}
			code = code.Next
		case encoder.OpStructPtrHeadNumberPtrString:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			fallthrough
		case encoder.OpStructHeadNumberPtrString:
			p := load(ctxptr, code.Idx)
			if p == 0 && (code.Flags&encoder.IndirectFlags) != 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			b = appendStructKey(ctx, code, b)
			if (code.Flags & encoder.IndirectFlags) != 0 {
				p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			}
			if p == 0 {
				b = appendNull(ctx, b)
			} else {
				b = append(b, '"')
				bb, err := appendNumber(ctx, b, ptrToNumber(p))
				if err != nil {
					return nil, err
				}
				b = append(bb, '"')
			}
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpStructPtrHeadOmitEmptyNumberPtrString:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			fallthrough
		case encoder.OpStructHeadOmitEmptyNumberPtrString:
			p := load(ctxptr, code.Idx)
			if p == 0 && (code.Flags&encoder.IndirectFlags) != 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			if (code.Flags & encoder.IndirectFlags) != 0 {
				p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			}
			if p != 0 {
				b = appendStructKey(ctx, code, b)
				b = append(b, '"')
				bb, err := appendNumber(ctx, b, ptrToNumber(p))
				if err != nil {
					return nil, err
				}
				b = append(bb, '"')
				b = appendComma(ctx, b)
			}
			code = code.Next
		case encoder.OpStructPtrHeadArray, encoder.OpStructPtrHeadSlice:
			if (code.Flags & encoder.IndirectFlags) != 0 {
				p := load(ctxptr, code.Idx)
				if p == 0 {
					if code.Flags&encoder.AnonymousHeadFlags == 0 {
						b = appendNullComma(ctx, b)
					}
					code = code.End.Next
					break
				}
				store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			}
			fallthrough
		case encoder.OpStructHeadArray, encoder.OpStructHeadSlice:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			b = appendStructKey(ctx, code, b)
			p += uintptr(code.Offset)
			code = code.Next
			store(ctxptr, code.Idx, p)
		case encoder.OpStructPtrHeadOmitEmptyArray:
			if (code.Flags & encoder.IndirectFlags) != 0 {
				p := load(ctxptr, code.Idx)
				if p == 0 {
					if code.Flags&encoder.AnonymousHeadFlags == 0 {
						b = appendNullComma(ctx, b)
					}
					code = code.End.Next
					break
				}
				store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			}
			fallthrough
		case encoder.OpStructHeadOmitEmptyArray:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			p += uintptr(code.Offset)
			b = appendStructKey(ctx, code, b)
			code = code.Next
			store(ctxptr, code.Idx, p)
		case encoder.OpStructPtrHeadOmitEmptySlice:
			if (code.Flags & encoder.IndirectFlags) != 0 {
				p := load(ctxptr, code.Idx)
				if p == 0 {
					if code.Flags&encoder.AnonymousHeadFlags == 0 {
						b = appendNullComma(ctx, b)
					}
					code = code.End.Next
					break
				}
				store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			}
			fallthrough
		case encoder.OpStructHeadOmitEmptySlice:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			p += uintptr(code.Offset)
			slice := ptrToSlice(p)
			if slice.Len == 0 {
				code = code.NextField
			} else {
				b = appendStructKey(ctx, code, b)
				code = code.Next
				store(ctxptr, code.Idx, p)
			}
		case encoder.OpStructPtrHeadArrayPtr, encoder.OpStructPtrHeadSlicePtr:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			fallthrough
		case encoder.OpStructHeadArrayPtr, encoder.OpStructHeadSlicePtr:
			p := load(ctxptr, code.Idx)
			if p == 0 && (code.Flags&encoder.IndirectFlags) != 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			b = appendStructKey(ctx, code, b)
			if (code.Flags & encoder.IndirectFlags) != 0 {
				p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			}
			if p == 0 {
				b = appendNullComma(ctx, b)
				code = code.NextField
			} else {
				code = code.Next
				store(ctxptr, code.Idx, p)
			}
		case encoder.OpStructPtrHeadOmitEmptyArrayPtr, encoder.OpStructPtrHeadOmitEmptySlicePtr:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			fallthrough
		case encoder.OpStructHeadOmitEmptyArrayPtr, encoder.OpStructHeadOmitEmptySlicePtr:
			p := load(ctxptr, code.Idx)
			if p == 0 && (code.Flags&encoder.IndirectFlags) != 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			if (code.Flags & encoder.IndirectFlags) != 0 {
				p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			}
			if p == 0 {
				code = code.NextField
			} else {
				b = appendStructKey(ctx, code, b)
				code = code.Next
				store(ctxptr, code.Idx, p)
			}
		case encoder.OpStructPtrHeadMap:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			fallthrough
		case encoder.OpStructHeadMap:
			p := load(ctxptr, code.Idx)
			if p == 0 && (code.Flags&encoder.IndirectFlags) != 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			b = appendStructKey(ctx, code, b)
			if p != 0 && (code.Flags&encoder.IndirectFlags) != 0 {
				p = ptrToPtr(p + uintptr(code.Offset))
			}
			code = code.Next
			store(ctxptr, code.Idx, p)
		case encoder.OpStructPtrHeadOmitEmptyMap:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			fallthrough
		case encoder.OpStructHeadOmitEmptyMap:
			p := load(ctxptr, code.Idx)
			if p == 0 && (code.Flags&encoder.IndirectFlags) != 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			if p != 0 && (code.Flags&encoder.IndirectFlags) != 0 {
				p = ptrToPtr(p + uintptr(code.Offset))
			}
			if maplen(ptrToUnsafePtr(p)) == 0 {
				code = code.NextField
			} else {
				b = appendStructKey(ctx, code, b)
				code = code.Next
				store(ctxptr, code.Idx, p)
			}
		case encoder.OpStructPtrHeadMapPtr:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			fallthrough
		case encoder.OpStructHeadMapPtr:
			p := load(ctxptr, code.Idx)
			if p == 0 && (code.Flags&encoder.IndirectFlags) != 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			b = appendStructKey(ctx, code, b)
			if p == 0 {
				b = appendNullComma(ctx, b)
				code = code.NextField
				break
			}
			p = ptrToPtr(p + uintptr(code.Offset))
			if p == 0 {
				b = appendNullComma(ctx, b)
				code = code.NextField
			} else {
				if (code.Flags & encoder.IndirectFlags) != 0 {
					p = ptrToNPtr(p, code.PtrNum)
				}
				code = code.Next
				store(ctxptr, code.Idx, p)
			}
		case encoder.OpStructPtrHeadOmitEmptyMapPtr:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			fallthrough
		case encoder.OpStructHeadOmitEmptyMapPtr:
			p := load(ctxptr, code.Idx)
			if p == 0 && (code.Flags&encoder.IndirectFlags) != 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			if p == 0 {
				code = code.NextField
				break
			}
			p = ptrToPtr(p + uintptr(code.Offset))
			if p == 0 {
				code = code.NextField
			} else {
				if (code.Flags & encoder.IndirectFlags) != 0 {
					p = ptrToNPtr(p, code.PtrNum)
				}
				b = appendStructKey(ctx, code, b)
				code = code.Next
				store(ctxptr, code.Idx, p)
			}
		case encoder.OpStructPtrHeadMarshalJSON:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if (code.Flags & encoder.IndirectFlags) != 0 {
				store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			}
			fallthrough
		case encoder.OpStructHeadMarshalJSON:
			p := load(ctxptr, code.Idx)
			if p == 0 && (code.Flags&encoder.IndirectFlags) != 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			b = appendStructKey(ctx, code, b)
			p += uintptr(code.Offset)
			if (code.Flags & encoder.IsNilableTypeFlags) != 0 {
				if (code.Flags&encoder.IndirectFlags) != 0 || code.Op == encoder.OpStructPtrHeadMarshalJSON {
					p = ptrToPtr(p)
				}
			}
			if p == 0 && (code.Flags&encoder.NilCheckFlags) != 0 {
				b = appendNull(ctx, b)
			} else {
				bb, err := appendMarshalJSON(ctx, code, b, ptrToInterface(code, p))
				if err != nil {
					return nil, err
				}
				b = bb
			}
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpStructPtrHeadOmitEmptyMarshalJSON:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if (code.Flags & encoder.IndirectFlags) != 0 {
				store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			}
			fallthrough
		case encoder.OpStructHeadOmitEmptyMarshalJSON:
			p := load(ctxptr, code.Idx)
			if p == 0 && (code.Flags&encoder.IndirectFlags) != 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			p += uintptr(code.Offset)
			if (code.Flags & encoder.IsNilableTypeFlags) != 0 {
				if (code.Flags&encoder.IndirectFlags) != 0 || code.Op == encoder.OpStructPtrHeadOmitEmptyMarshalJSON {
					p = ptrToPtr(p)
				}
			}
			iface := ptrToInterface(code, p)
			if (code.Flags&encoder.NilCheckFlags) != 0 && encoder.IsNilForMarshaler(iface) {
				code = code.NextField
			} else {
				b = appendStructKey(ctx, code, b)
				bb, err := appendMarshalJSON(ctx, code, b, iface)
				if err != nil {
					return nil, err
				}
				b = bb
				b = appendComma(ctx, b)
				code = code.Next
			}
		case encoder.OpStructPtrHeadMarshalJSONPtr:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			fallthrough
		case encoder.OpStructHeadMarshalJSONPtr:
			p := load(ctxptr, code.Idx)
			if p == 0 && (code.Flags&encoder.IndirectFlags) != 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			b = appendStructKey(ctx, code, b)
			if (code.Flags & encoder.IndirectFlags) != 0 {
				p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			}
			if p == 0 {
				b = appendNull(ctx, b)
			} else {
				bb, err := appendMarshalJSON(ctx, code, b, ptrToInterface(code, p))
				if err != nil {
					return nil, err
				}
				b = bb
			}
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpStructPtrHeadOmitEmptyMarshalJSONPtr:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			fallthrough
		case encoder.OpStructHeadOmitEmptyMarshalJSONPtr:
			p := load(ctxptr, code.Idx)
			if p == 0 && (code.Flags&encoder.IndirectFlags) != 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if (code.Flags & encoder.IndirectFlags) != 0 {
				p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			if p == 0 {
				code = code.NextField
			} else {
				b = appendStructKey(ctx, code, b)
				bb, err := appendMarshalJSON(ctx, code, b, ptrToInterface(code, p))
				if err != nil {
					return nil, err
				}
				b = bb
				b = appendComma(ctx, b)
				code = code.Next
			}
		case encoder.OpStructPtrHeadMarshalText:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if (code.Flags & encoder.IndirectFlags) != 0 {
				store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			}
			fallthrough
		case encoder.OpStructHeadMarshalText:
			p := load(ctxptr, code.Idx)
			if p == 0 && (code.Flags&encoder.IndirectFlags) != 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			b = appendStructKey(ctx, code, b)
			p += uintptr(code.Offset)
			if (code.Flags & encoder.IsNilableTypeFlags) != 0 {
				if (code.Flags&encoder.IndirectFlags) != 0 || code.Op == encoder.OpStructPtrHeadMarshalText {
					p = ptrToPtr(p)
				}
			}
			if p == 0 && (code.Flags&encoder.NilCheckFlags) != 0 {
				b = appendNull(ctx, b)
			} else {
				bb, err := appendMarshalText(ctx, code, b, ptrToInterface(code, p))
				if err != nil {
					return nil, err
				}
				b = bb
			}
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpStructPtrHeadOmitEmptyMarshalText:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if (code.Flags & encoder.IndirectFlags) != 0 {
				store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			}
			fallthrough
		case encoder.OpStructHeadOmitEmptyMarshalText:
			p := load(ctxptr, code.Idx)
			if p == 0 && (code.Flags&encoder.IndirectFlags) != 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			p += uintptr(code.Offset)
			if (code.Flags & encoder.IsNilableTypeFlags) != 0 {
				if (code.Flags&encoder.IndirectFlags) != 0 || code.Op == encoder.OpStructPtrHeadOmitEmptyMarshalText {
					p = ptrToPtr(p)
				}
			}
			if p == 0 && (code.Flags&encoder.NilCheckFlags) != 0 {
				code = code.NextField
			} else {
				b = appendStructKey(ctx, code, b)
				bb, err := appendMarshalText(ctx, code, b, ptrToInterface(code, p))
				if err != nil {
					return nil, err
				}
				b = bb
				b = appendComma(ctx, b)
				code = code.Next
			}
		case encoder.OpStructPtrHeadMarshalTextPtr:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			fallthrough
		case encoder.OpStructHeadMarshalTextPtr:
			p := load(ctxptr, code.Idx)
			if p == 0 && (code.Flags&encoder.IndirectFlags) != 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			b = appendStructKey(ctx, code, b)
			if (code.Flags & encoder.IndirectFlags) != 0 {
				p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			}
			if p == 0 {
				b = appendNull(ctx, b)
			} else {
				bb, err := appendMarshalText(ctx, code, b, ptrToInterface(code, p))
				if err != nil {
					return nil, err
				}
				b = bb
			}
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpStructPtrHeadOmitEmptyMarshalTextPtr:
			p := load(ctxptr, code.Idx)
			if p == 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			store(ctxptr, code.Idx, ptrToNPtr(p, code.PtrNum))
			fallthrough
		case encoder.OpStructHeadOmitEmptyMarshalTextPtr:
			p := load(ctxptr, code.Idx)
			if p == 0 && (code.Flags&encoder.IndirectFlags) != 0 {
				if code.Flags&encoder.AnonymousHeadFlags == 0 {
					b = appendNullComma(ctx, b)
				}
				code = code.End.Next
				break
			}
			if (code.Flags & encoder.IndirectFlags) != 0 {
				p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			}
			if code.Flags&encoder.AnonymousHeadFlags == 0 {
				b = appendStructHead(ctx, b)
			}
			if p == 0 {
				code = code.NextField
			} else {
				b = appendStructKey(ctx, code, b)
				bb, err := appendMarshalText(ctx, code, b, ptrToInterface(code, p))
				if err != nil {
					return nil, err
				}
				b = bb
				b = appendComma(ctx, b)
				code = code.Next
			}
		case encoder.OpStructField:
			if code.Flags&encoder.IsTaggedKeyFlags != 0 || code.Flags&encoder.AnonymousKeyFlags == 0 {
				b = appendStructKey(ctx, code, b)
			}
			p := load(ctxptr, code.Idx) + uintptr(code.Offset)
			code = code.Next
			store(ctxptr, code.Idx, p)
		case encoder.OpStructFieldOmitEmpty:
			p := load(ctxptr, code.Idx)
			p += uintptr(code.Offset)
			if ptrToPtr(p) == 0 && (code.Flags&encoder.IsNextOpPtrTypeFlags) != 0 {
				code = code.NextField
			} else {
				b = appendStructKey(ctx, code, b)
				code = code.Next
				store(ctxptr, code.Idx, p)
			}
		case encoder.OpStructFieldInt:
			p := load(ctxptr, code.Idx)
			b = appendStructKey(ctx, code, b)
			b = appendInt(ctx, b, p+uintptr(code.Offset), code)
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpStructFieldOmitEmptyInt:
			p := load(ctxptr, code.Idx)
			u64 := ptrToUint64(p+uintptr(code.Offset), code.NumBitSize)
			v := u64 & ((1 << code.NumBitSize) - 1)
			if v != 0 {
				b = appendStructKey(ctx, code, b)
				b = appendInt(ctx, b, p+uintptr(code.Offset), code)
				b = appendComma(ctx, b)
			}
			code = code.Next
		case encoder.OpStructFieldIntString:
			p := load(ctxptr, code.Idx)
			b = appendStructKey(ctx, code, b)
			b = append(b, '"')
			b = appendInt(ctx, b, p+uintptr(code.Offset), code)
			b = append(b, '"')
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpStructFieldOmitEmptyIntString:
			p := load(ctxptr, code.Idx)
			u64 := ptrToUint64(p+uintptr(code.Offset), code.NumBitSize)
			v := u64 & ((1 << code.NumBitSize) - 1)
			if v != 0 {
				b = appendStructKey(ctx, code, b)
				b = append(b, '"')
				b = appendInt(ctx, b, p+uintptr(code.Offset), code)
				b = append(b, '"')
				b = appendComma(ctx, b)
			}
			code = code.Next
		case encoder.OpStructFieldIntPtr:
			p := load(ctxptr, code.Idx)
			p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			b = appendStructKey(ctx, code, b)
			if p == 0 {
				b = appendNull(ctx, b)
			} else {
				b = appendInt(ctx, b, p, code)
			}
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpStructFieldOmitEmptyIntPtr:
			p := load(ctxptr, code.Idx)
			p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			if p != 0 {
				b = appendStructKey(ctx, code, b)
				b = appendInt(ctx, b, p, code)
				b = appendComma(ctx, b)
			}
			code = code.Next
		case encoder.OpStructFieldIntPtrString:
			p := load(ctxptr, code.Idx)
			p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			b = appendStructKey(ctx, code, b)
			if p == 0 {
				b = appendNull(ctx, b)
			} else {
				b = append(b, '"')
				b = appendInt(ctx, b, p, code)
				b = append(b, '"')
			}
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpStructFieldOmitEmptyIntPtrString:
			p := load(ctxptr, code.Idx)
			p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			if p != 0 {
				b = appendStructKey(ctx, code, b)
				b = append(b, '"')
				b = appendInt(ctx, b, p, code)
				b = append(b, '"')
				b = appendComma(ctx, b)
			}
			code = code.Next
		case encoder.OpStructFieldUint:
			p := load(ctxptr, code.Idx)
			b = appendStructKey(ctx, code, b)
			b = appendUint(ctx, b, p+uintptr(code.Offset), code)
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpStructFieldOmitEmptyUint:
			p := load(ctxptr, code.Idx)
			u64 := ptrToUint64(p+uintptr(code.Offset), code.NumBitSize)
			v := u64 & ((1 << code.NumBitSize) - 1)
			if v != 0 {
				b = appendStructKey(ctx, code, b)
				b = appendUint(ctx, b, p+uintptr(code.Offset), code)
				b = appendComma(ctx, b)
			}
			code = code.Next
		case encoder.OpStructFieldUintString:
			p := load(ctxptr, code.Idx)
			b = appendStructKey(ctx, code, b)
			b = append(b, '"')
			b = appendUint(ctx, b, p+uintptr(code.Offset), code)
			b = append(b, '"')
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpStructFieldOmitEmptyUintString:
			p := load(ctxptr, code.Idx)
			u64 := ptrToUint64(p+uintptr(code.Offset), code.NumBitSize)
			v := u64 & ((1 << code.NumBitSize) - 1)
			if v != 0 {
				b = appendStructKey(ctx, code, b)
				b = append(b, '"')
				b = appendUint(ctx, b, p+uintptr(code.Offset), code)
				b = append(b, '"')
				b = appendComma(ctx, b)
			}
			code = code.Next
		case encoder.OpStructFieldUintPtr:
			p := load(ctxptr, code.Idx)
			p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			b = appendStructKey(ctx, code, b)
			if p == 0 {
				b = appendNull(ctx, b)
			} else {
				b = appendUint(ctx, b, p, code)
			}
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpStructFieldOmitEmptyUintPtr:
			p := load(ctxptr, code.Idx)
			p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			if p != 0 {
				b = appendStructKey(ctx, code, b)
				b = appendUint(ctx, b, p, code)
				b = appendComma(ctx, b)
			}
			code = code.Next
		case encoder.OpStructFieldUintPtrString:
			p := load(ctxptr, code.Idx)
			p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			b = appendStructKey(ctx, code, b)
			if p == 0 {
				b = appendNull(ctx, b)
			} else {
				b = append(b, '"')
				b = appendUint(ctx, b, p, code)
				b = append(b, '"')
			}
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpStructFieldOmitEmptyUintPtrString:
			p := load(ctxptr, code.Idx)
			p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			if p != 0 {
				b = appendStructKey(ctx, code, b)
				b = append(b, '"')
				b = appendUint(ctx, b, p, code)
				b = append(b, '"')
				b = appendComma(ctx, b)
			}
			code = code.Next
		case encoder.OpStructFieldFloat32:
			p := load(ctxptr, code.Idx)
			b = appendStructKey(ctx, code, b)
			b = appendFloat32(ctx, b, ptrToFloat32(p+uintptr(code.Offset)))
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpStructFieldOmitEmptyFloat32:
			p := load(ctxptr, code.Idx)
			v := ptrToFloat32(p + uintptr(code.Offset))
			if v != 0 {
				b = appendStructKey(ctx, code, b)
				b = appendFloat32(ctx, b, v)
				b = appendComma(ctx, b)
			}
			code = code.Next
		case encoder.OpStructFieldFloat32String:
			p := load(ctxptr, code.Idx)
			b = appendStructKey(ctx, code, b)
			b = append(b, '"')
			b = appendFloat32(ctx, b, ptrToFloat32(p+uintptr(code.Offset)))
			b = append(b, '"')
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpStructFieldOmitEmptyFloat32String:
			p := load(ctxptr, code.Idx)
			v := ptrToFloat32(p + uintptr(code.Offset))
			if v != 0 {
				b = appendStructKey(ctx, code, b)
				b = append(b, '"')
				b = appendFloat32(ctx, b, v)
				b = append(b, '"')
				b = appendComma(ctx, b)
			}
			code = code.Next
		case encoder.OpStructFieldFloat32Ptr:
			p := load(ctxptr, code.Idx)
			p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			b = appendStructKey(ctx, code, b)
			if p == 0 {
				b = appendNull(ctx, b)
			} else {
				b = appendFloat32(ctx, b, ptrToFloat32(p))
			}
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpStructFieldOmitEmptyFloat32Ptr:
			p := load(ctxptr, code.Idx)
			p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			if p != 0 {
				b = appendStructKey(ctx, code, b)
				b = appendFloat32(ctx, b, ptrToFloat32(p))
				b = appendComma(ctx, b)
			}
			code = code.Next
		case encoder.OpStructFieldFloat32PtrString:
			p := load(ctxptr, code.Idx)
			p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			b = appendStructKey(ctx, code, b)
			if p == 0 {
				b = appendNull(ctx, b)
			} else {
				b = append(b, '"')
				b = appendFloat32(ctx, b, ptrToFloat32(p))
				b = append(b, '"')
			}
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpStructFieldOmitEmptyFloat32PtrString:
			p := load(ctxptr, code.Idx)
			p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			if p != 0 {
				b = appendStructKey(ctx, code, b)
				b = append(b, '"')
				b = appendFloat32(ctx, b, ptrToFloat32(p))
				b = append(b, '"')
				b = appendComma(ctx, b)
			}
			code = code.Next
		case encoder.OpStructFieldFloat64:
			p := load(ctxptr, code.Idx)
			b = appendStructKey(ctx, code, b)
			v := ptrToFloat64(p + uintptr(code.Offset))
			if math.IsInf(v, 0) || math.IsNaN(v) {
				return nil, errUnsupportedFloat(v)
			}
			b = appendFloat64(ctx, b, v)
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpStructFieldOmitEmptyFloat64:
			p := load(ctxptr, code.Idx)
			v := ptrToFloat64(p + uintptr(code.Offset))
			if v != 0 {
				if math.IsInf(v, 0) || math.IsNaN(v) {
					return nil, errUnsupportedFloat(v)
				}
				b = appendStructKey(ctx, code, b)
				b = appendFloat64(ctx, b, v)
				b = appendComma(ctx, b)
			}
			code = code.Next
		case encoder.OpStructFieldFloat64String:
			p := load(ctxptr, code.Idx)
			v := ptrToFloat64(p + uintptr(code.Offset))
			if math.IsInf(v, 0) || math.IsNaN(v) {
				return nil, errUnsupportedFloat(v)
			}
			b = appendStructKey(ctx, code, b)
			b = append(b, '"')
			b = appendFloat64(ctx, b, v)
			b = append(b, '"')
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpStructFieldOmitEmptyFloat64String:
			p := load(ctxptr, code.Idx)
			v := ptrToFloat64(p + uintptr(code.Offset))
			if v != 0 {
				if math.IsInf(v, 0) || math.IsNaN(v) {
					return nil, errUnsupportedFloat(v)
				}
				b = appendStructKey(ctx, code, b)
				b = append(b, '"')
				b = appendFloat64(ctx, b, v)
				b = append(b, '"')
				b = appendComma(ctx, b)
			}
			code = code.Next
		case encoder.OpStructFieldFloat64Ptr:
			p := load(ctxptr, code.Idx)
			p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			b = appendStructKey(ctx, code, b)
			if p == 0 {
				b = appendNullComma(ctx, b)
				code = code.Next
				break
			}
			v := ptrToFloat64(p)
			if math.IsInf(v, 0) || math.IsNaN(v) {
				return nil, errUnsupportedFloat(v)
			}
			b = appendFloat64(ctx, b, v)
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpStructFieldOmitEmptyFloat64Ptr:
			p := load(ctxptr, code.Idx)
			p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			if p != 0 {
				b = appendStructKey(ctx, code, b)
				v := ptrToFloat64(p)
				if math.IsInf(v, 0) || math.IsNaN(v) {
					return nil, errUnsupportedFloat(v)
				}
				b = appendFloat64(ctx, b, v)
				b = appendComma(ctx, b)
			}
			code = code.Next
		case encoder.OpStructFieldFloat64PtrString:
			p := load(ctxptr, code.Idx)
			p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			b = appendStructKey(ctx, code, b)
			if p == 0 {
				b = appendNull(ctx, b)
			} else {
				v := ptrToFloat64(p)
				if math.IsInf(v, 0) || math.IsNaN(v) {
					return nil, errUnsupportedFloat(v)
				}
				b = append(b, '"')
				b = appendFloat64(ctx, b, v)
				b = append(b, '"')
			}
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpStructFieldOmitEmptyFloat64PtrString:
			p := load(ctxptr, code.Idx)
			p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			if p != 0 {
				b = appendStructKey(ctx, code, b)
				b = append(b, '"')
				v := ptrToFloat64(p)
				if math.IsInf(v, 0) || math.IsNaN(v) {
					return nil, errUnsupportedFloat(v)
				}
				b = appendFloat64(ctx, b, v)
				b = append(b, '"')
				b = appendComma(ctx, b)
			}
			code = code.Next
		case encoder.OpStructFieldString:
			p := load(ctxptr, code.Idx)
			b = appendStructKey(ctx, code, b)
			b = appendString(ctx, b, ptrToString(p+uintptr(code.Offset)))
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpStructFieldOmitEmptyString:
			p := load(ctxptr, code.Idx)
			v := ptrToString(p + uintptr(code.Offset))
			if v != "" {
				b = appendStructKey(ctx, code, b)
				b = appendString(ctx, b, v)
				b = appendComma(ctx, b)
			}
			code = code.Next
		case encoder.OpStructFieldStringString:
			p := load(ctxptr, code.Idx)
			s := ptrToString(p + uintptr(code.Offset))
			b = appendStructKey(ctx, code, b)
			b = appendString(ctx, b, string(appendString(ctx, []byte{}, s)))
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpStructFieldOmitEmptyStringString:
			p := load(ctxptr, code.Idx)
			v := ptrToString(p + uintptr(code.Offset))
			if v != "" {
				b = appendStructKey(ctx, code, b)
				b = appendString(ctx, b, string(appendString(ctx, []byte{}, v)))
				b = appendComma(ctx, b)
			}
			code = code.Next
		case encoder.OpStructFieldStringPtr:
			p := load(ctxptr, code.Idx)
			p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			b = appendStructKey(ctx, code, b)
			if p == 0 {
				b = appendNull(ctx, b)
			} else {
				b = appendString(ctx, b, ptrToString(p))
			}
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpStructFieldOmitEmptyStringPtr:
			p := load(ctxptr, code.Idx)
			p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			if p != 0 {
				b = appendStructKey(ctx, code, b)
				b = appendString(ctx, b, ptrToString(p))
				b = appendComma(ctx, b)
			}
			code = code.Next
		case encoder.OpStructFieldStringPtrString:
			p := load(ctxptr, code.Idx)
			p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			b = appendStructKey(ctx, code, b)
			if p == 0 {
				b = appendNull(ctx, b)
			} else {
				b = appendString(ctx, b, string(appendString(ctx, []byte{}, ptrToString(p))))
			}
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpStructFieldOmitEmptyStringPtrString:
			p := load(ctxptr, code.Idx)
			p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			if p != 0 {
				b = appendStructKey(ctx, code, b)
				b = appendString(ctx, b, string(appendString(ctx, []byte{}, ptrToString(p))))
				b = appendComma(ctx, b)
			}
			code = code.Next
		case encoder.OpStructFieldBool:
			p := load(ctxptr, code.Idx)
			b = appendStructKey(ctx, code, b)
			b = appendBool(ctx, b, ptrToBool(p+uintptr(code.Offset)))
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpStructFieldOmitEmptyBool:
			p := load(ctxptr, code.Idx)
			v := ptrToBool(p + uintptr(code.Offset))
			if v {
				b = appendStructKey(ctx, code, b)
				b = appendBool(ctx, b, v)
				b = appendComma(ctx, b)
			}
			code = code.Next
		case encoder.OpStructFieldBoolString:
			p := load(ctxptr, code.Idx)
			b = appendStructKey(ctx, code, b)
			b = append(b, '"')
			b = appendBool(ctx, b, ptrToBool(p+uintptr(code.Offset)))
			b = append(b, '"')
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpStructFieldOmitEmptyBoolString:
			p := load(ctxptr, code.Idx)
			v := ptrToBool(p + uintptr(code.Offset))
			if v {
				b = appendStructKey(ctx, code, b)
				b = append(b, '"')
				b = appendBool(ctx, b, v)
				b = append(b, '"')
				b = appendComma(ctx, b)
			}
			code = code.Next
		case encoder.OpStructFieldBoolPtr:
			p := load(ctxptr, code.Idx)
			p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			b = appendStructKey(ctx, code, b)
			if p == 0 {
				b = appendNull(ctx, b)
			} else {
				b = appendBool(ctx, b, ptrToBool(p))
			}
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpStructFieldOmitEmptyBoolPtr:
			p := load(ctxptr, code.Idx)
			p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			if p != 0 {
				b = appendStructKey(ctx, code, b)
				b = appendBool(ctx, b, ptrToBool(p))
				b = appendComma(ctx, b)
			}
			code = code.Next
		case encoder.OpStructFieldBoolPtrString:
			p := load(ctxptr, code.Idx)
			p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			b = appendStructKey(ctx, code, b)
			if p == 0 {
				b = appendNull(ctx, b)
			} else {
				b = append(b, '"')
				b = appendBool(ctx, b, ptrToBool(p))
				b = append(b, '"')
			}
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpStructFieldOmitEmptyBoolPtrString:
			p := load(ctxptr, code.Idx)
			p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			if p != 0 {
				b = appendStructKey(ctx, code, b)
				b = append(b, '"')
				b = appendBool(ctx, b, ptrToBool(p))
				b = append(b, '"')
				b = appendComma(ctx, b)
			}
			code = code.Next
		case encoder.OpStructFieldBytes:
			p := load(ctxptr, code.Idx)
			b = appendStructKey(ctx, code, b)
			b = appendByteSlice(ctx, b, ptrToBytes(p+uintptr(code.Offset)))
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpStructFieldOmitEmptyBytes:
			p := load(ctxptr, code.Idx)
			v := ptrToBytes(p + uintptr(code.Offset))
			if len(v) > 0 {
				b = appendStructKey(ctx, code, b)
				b = appendByteSlice(ctx, b, v)
				b = appendComma(ctx, b)
			}
			code = code.Next
		case encoder.OpStructFieldBytesPtr:
			p := load(ctxptr, code.Idx)
			p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			b = appendStructKey(ctx, code, b)
			if p == 0 {
				b = appendNull(ctx, b)
			} else {
				b = appendByteSlice(ctx, b, ptrToBytes(p))
			}
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpStructFieldOmitEmptyBytesPtr:
			p := load(ctxptr, code.Idx)
			p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			if p != 0 {
				b = appendStructKey(ctx, code, b)
				b = appendByteSlice(ctx, b, ptrToBytes(p))
				b = appendComma(ctx, b)
			}
			code = code.Next
		case encoder.OpStructFieldNumber:
			p := load(ctxptr, code.Idx)
			b = appendStructKey(ctx, code, b)
			bb, err := appendNumber(ctx, b, ptrToNumber(p+uintptr(code.Offset)))
			if err != nil {
				return nil, err
			}
			b = appendComma(ctx, bb)
			code = code.Next
		case encoder.OpStructFieldOmitEmptyNumber:
			p := load(ctxptr, code.Idx)
			v := ptrToNumber(p + uintptr(code.Offset))
			if v != "" {
				b = appendStructKey(ctx, code, b)
				bb, err := appendNumber(ctx, b, v)
				if err != nil {
					return nil, err
				}
				b = appendComma(ctx, bb)
			}
			code = code.Next
		case encoder.OpStructFieldNumberString:
			p := load(ctxptr, code.Idx)
			b = appendStructKey(ctx, code, b)
			b = append(b, '"')
			bb, err := appendNumber(ctx, b, ptrToNumber(p+uintptr(code.Offset)))
			if err != nil {
				return nil, err
			}
			b = append(bb, '"')
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpStructFieldOmitEmptyNumberString:
			p := load(ctxptr, code.Idx)
			v := ptrToNumber(p + uintptr(code.Offset))
			if v != "" {
				b = appendStructKey(ctx, code, b)
				b = append(b, '"')
				bb, err := appendNumber(ctx, b, v)
				if err != nil {
					return nil, err
				}
				b = append(bb, '"')
				b = appendComma(ctx, b)
			}
			code = code.Next
		case encoder.OpStructFieldNumberPtr:
			p := load(ctxptr, code.Idx)
			p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			b = appendStructKey(ctx, code, b)
			if p == 0 {
				b = appendNull(ctx, b)
			} else {
				bb, err := appendNumber(ctx, b, ptrToNumber(p))
				if err != nil {
					return nil, err
				}
				b = bb
			}
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpStructFieldOmitEmptyNumberPtr:
			p := load(ctxptr, code.Idx)
			p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			if p != 0 {
				b = appendStructKey(ctx, code, b)
				bb, err := appendNumber(ctx, b, ptrToNumber(p))
				if err != nil {
					return nil, err
				}
				b = appendComma(ctx, bb)
			}
			code = code.Next
		case encoder.OpStructFieldNumberPtrString:
			p := load(ctxptr, code.Idx)
			p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			b = appendStructKey(ctx, code, b)
			if p == 0 {
				b = appendNull(ctx, b)
			} else {
				b = append(b, '"')
				bb, err := appendNumber(ctx, b, ptrToNumber(p))
				if err != nil {
					return nil, err
				}
				b = append(bb, '"')
			}
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpStructFieldOmitEmptyNumberPtrString:
			p := load(ctxptr, code.Idx)
			p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			if p != 0 {
				b = appendStructKey(ctx, code, b)
				b = append(b, '"')
				bb, err := appendNumber(ctx, b, ptrToNumber(p))
				if err != nil {
					return nil, err
				}
				b = append(bb, '"')
				b = appendComma(ctx, b)
			}
			code = code.Next
		case encoder.OpStructFieldMarshalJSON:
			p := load(ctxptr, code.Idx)
			b = appendStructKey(ctx, code, b)
			p += uintptr(code.Offset)
			if (code.Flags & encoder.IsNilableTypeFlags) != 0 {
				p = ptrToPtr(p)
			}
			if p == 0 && (code.Flags&encoder.NilCheckFlags) != 0 {
				b = appendNull(ctx, b)
			} else {
				bb, err := appendMarshalJSON(ctx, code, b, ptrToInterface(code, p))
				if err != nil {
					return nil, err
				}
				b = bb
			}
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpStructFieldOmitEmptyMarshalJSON:
			p := load(ctxptr, code.Idx)
			p += uintptr(code.Offset)
			if (code.Flags & encoder.IsNilableTypeFlags) != 0 {
				p = ptrToPtr(p)
			}
			if p == 0 && (code.Flags&encoder.NilCheckFlags) != 0 {
				code = code.NextField
				break
			}
			iface := ptrToInterface(code, p)
			if (code.Flags&encoder.NilCheckFlags) != 0 && encoder.IsNilForMarshaler(iface) {
				code = code.NextField
				break
			}
			b = appendStructKey(ctx, code, b)
			bb, err := appendMarshalJSON(ctx, code, b, iface)
			if err != nil {
				return nil, err
			}
			b = appendComma(ctx, bb)
			code = code.Next
		case encoder.OpStructFieldMarshalJSONPtr:
			p := load(ctxptr, code.Idx)
			b = appendStructKey(ctx, code, b)
			p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			if p == 0 {
				b = appendNull(ctx, b)
			} else {
				bb, err := appendMarshalJSON(ctx, code, b, ptrToInterface(code, p))
				if err != nil {
					return nil, err
				}
				b = bb
			}
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpStructFieldOmitEmptyMarshalJSONPtr:
			p := load(ctxptr, code.Idx)
			p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			if p != 0 {
				b = appendStructKey(ctx, code, b)
				bb, err := appendMarshalJSON(ctx, code, b, ptrToInterface(code, p))
				if err != nil {
					return nil, err
				}
				b = appendComma(ctx, bb)
			}
			code = code.Next
		case encoder.OpStructFieldMarshalText:
			p := load(ctxptr, code.Idx)
			b = appendStructKey(ctx, code, b)
			p += uintptr(code.Offset)
			if (code.Flags & encoder.IsNilableTypeFlags) != 0 {
				p = ptrToPtr(p)
			}
			if p == 0 && (code.Flags&encoder.NilCheckFlags) != 0 {
				b = appendNull(ctx, b)
			} else {
				bb, err := appendMarshalText(ctx, code, b, ptrToInterface(code, p))
				if err != nil {
					return nil, err
				}
				b = bb
			}
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpStructFieldOmitEmptyMarshalText:
			p := load(ctxptr, code.Idx)
			p += uintptr(code.Offset)
			if (code.Flags & encoder.IsNilableTypeFlags) != 0 {
				p = ptrToPtr(p)
			}
			if p == 0 && (code.Flags&encoder.NilCheckFlags) != 0 {
				code = code.NextField
				break
			}
			b = appendStructKey(ctx, code, b)
			bb, err := appendMarshalText(ctx, code, b, ptrToInterface(code, p))
			if err != nil {
				return nil, err
			}
			b = appendComma(ctx, bb)
			code = code.Next
		case encoder.OpStructFieldMarshalTextPtr:
			p := load(ctxptr, code.Idx)
			b = appendStructKey(ctx, code, b)
			p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			if p == 0 {
				b = appendNull(ctx, b)
			} else {
				bb, err := appendMarshalText(ctx, code, b, ptrToInterface(code, p))
				if err != nil {
					return nil, err
				}
				b = bb
			}
			b = appendComma(ctx, b)
			code = code.Next
		case encoder.OpStructFieldOmitEmptyMarshalTextPtr:
			p := load(ctxptr, code.Idx)
			p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			if p != 0 {
				b = appendStructKey(ctx, code, b)
				bb, err := appendMarshalText(ctx, code, b, ptrToInterface(code, p))
				if err != nil {
					return nil, err
				}
				b = appendComma(ctx, bb)
			}
			code = code.Next
		case encoder.OpStructFieldArray:
			b = appendStructKey(ctx, code, b)
			p := load(ctxptr, code.Idx)
			p += uintptr(code.Offset)
			code = code.Next
			store(ctxptr, code.Idx, p)
		case encoder.OpStructFieldOmitEmptyArray:
			b = appendStructKey(ctx, code, b)
			p := load(ctxptr, code.Idx)
			p += uintptr(code.Offset)
			code = code.Next
			store(ctxptr, code.Idx, p)
		case encoder.OpStructFieldArrayPtr:
			b = appendStructKey(ctx, code, b)
			p := load(ctxptr, code.Idx)
			p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			code = code.Next
			store(ctxptr, code.Idx, p)
		case encoder.OpStructFieldOmitEmptyArrayPtr:
			p := load(ctxptr, code.Idx)
			p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			if p != 0 {
				b = appendStructKey(ctx, code, b)
				code = code.Next
				store(ctxptr, code.Idx, p)
			} else {
				code = code.NextField
			}
		case encoder.OpStructFieldSlice:
			b = appendStructKey(ctx, code, b)
			p := load(ctxptr, code.Idx)
			p += uintptr(code.Offset)
			code = code.Next
			store(ctxptr, code.Idx, p)
		case encoder.OpStructFieldOmitEmptySlice:
			p := load(ctxptr, code.Idx)
			p += uintptr(code.Offset)
			slice := ptrToSlice(p)
			if slice.Len == 0 {
				code = code.NextField
			} else {
				b = appendStructKey(ctx, code, b)
				code = code.Next
				store(ctxptr, code.Idx, p)
			}
		case encoder.OpStructFieldSlicePtr:
			b = appendStructKey(ctx, code, b)
			p := load(ctxptr, code.Idx)
			p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			code = code.Next
			store(ctxptr, code.Idx, p)
		case encoder.OpStructFieldOmitEmptySlicePtr:
			p := load(ctxptr, code.Idx)
			p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			if p != 0 {
				b = appendStructKey(ctx, code, b)
				code = code.Next
				store(ctxptr, code.Idx, p)
			} else {
				code = code.NextField
			}
		case encoder.OpStructFieldMap:
			b = appendStructKey(ctx, code, b)
			p := load(ctxptr, code.Idx)
			p = ptrToPtr(p + uintptr(code.Offset))
			code = code.Next
			store(ctxptr, code.Idx, p)
		case encoder.OpStructFieldOmitEmptyMap:
			p := load(ctxptr, code.Idx)
			p = ptrToPtr(p + uintptr(code.Offset))
			if p == 0 || maplen(ptrToUnsafePtr(p)) == 0 {
				code = code.NextField
			} else {
				b = appendStructKey(ctx, code, b)
				code = code.Next
				store(ctxptr, code.Idx, p)
			}
		case encoder.OpStructFieldMapPtr:
			b = appendStructKey(ctx, code, b)
			p := load(ctxptr, code.Idx)
			p = ptrToPtr(p + uintptr(code.Offset))
			if p != 0 {
				p = ptrToNPtr(p, code.PtrNum)
			}
			code = code.Next
			store(ctxptr, code.Idx, p)
		case encoder.OpStructFieldOmitEmptyMapPtr:
			p := load(ctxptr, code.Idx)
			p = ptrToPtr(p + uintptr(code.Offset))
			if p != 0 {
				p = ptrToNPtr(p, code.PtrNum)
			}
			if p != 0 {
				b = appendStructKey(ctx, code, b)
				code = code.Next
				store(ctxptr, code.Idx, p)
			} else {
				code = code.NextField
			}
		case encoder.OpStructFieldStruct:
			b = appendStructKey(ctx, code, b)
			p := load(ctxptr, code.Idx)
			p += uintptr(code.Offset)
			code = code.Next
			store(ctxptr, code.Idx, p)
		case encoder.OpStructFieldOmitEmptyStruct:
			p := load(ctxptr, code.Idx)
			p += uintptr(code.Offset)
			if ptrToPtr(p) == 0 && (code.Flags&encoder.IsNextOpPtrTypeFlags) != 0 {
				code = code.NextField
			} else {
				b = appendStructKey(ctx, code, b)
				code = code.Next
				store(ctxptr, code.Idx, p)
			}
		case encoder.OpStructEnd:
			b = appendStructEndSkipLast(ctx, code, b)
			code = code.Next
		case encoder.OpStructEndInt:
			p := load(ctxptr, code.Idx)
			b = appendStructKey(ctx, code, b)
			b = appendInt(ctx, b, p+uintptr(code.Offset), code)
			b = appendStructEnd(ctx, code, b)
			code = code.Next
		case encoder.OpStructEndOmitEmptyInt:
			p := load(ctxptr, code.Idx)
			u64 := ptrToUint64(p+uintptr(code.Offset), code.NumBitSize)
			v := u64 & ((1 << code.NumBitSize) - 1)
			if v != 0 {
				b = appendStructKey(ctx, code, b)
				b = appendInt(ctx, b, p+uintptr(code.Offset), code)
				b = appendStructEnd(ctx, code, b)
			} else {
				b = appendStructEndSkipLast(ctx, code, b)
			}
			code = code.Next
		case encoder.OpStructEndIntString:
			p := load(ctxptr, code.Idx)
			b = appendStructKey(ctx, code, b)
			b = append(b, '"')
			b = appendInt(ctx, b, p+uintptr(code.Offset), code)
			b = append(b, '"')
			b = appendStructEnd(ctx, code, b)
			code = code.Next
		case encoder.OpStructEndOmitEmptyIntString:
			p := load(ctxptr, code.Idx)
			u64 := ptrToUint64(p+uintptr(code.Offset), code.NumBitSize)
			v := u64 & ((1 << code.NumBitSize) - 1)
			if v != 0 {
				b = appendStructKey(ctx, code, b)
				b = append(b, '"')
				b = appendInt(ctx, b, p+uintptr(code.Offset), code)
				b = append(b, '"')
				b = appendStructEnd(ctx, code, b)
			} else {
				b = appendStructEndSkipLast(ctx, code, b)
			}
			code = code.Next
		case encoder.OpStructEndIntPtr:
			b = appendStructKey(ctx, code, b)
			p := load(ctxptr, code.Idx)
			p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			if p == 0 {
				b = appendNull(ctx, b)
			} else {
				b = appendInt(ctx, b, p, code)
			}
			b = appendStructEnd(ctx, code, b)
			code = code.Next
		case encoder.OpStructEndOmitEmptyIntPtr:
			p := load(ctxptr, code.Idx)
			p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			if p != 0 {
				b = appendStructKey(ctx, code, b)
				b = appendInt(ctx, b, p, code)
				b = appendStructEnd(ctx, code, b)
			} else {
				b = appendStructEndSkipLast(ctx, code, b)
			}
			code = code.Next
		case encoder.OpStructEndIntPtrString:
			b = appendStructKey(ctx, code, b)
			p := load(ctxptr, code.Idx)
			p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			if p == 0 {
				b = appendNull(ctx, b)
			} else {
				b = append(b, '"')
				b = appendInt(ctx, b, p, code)
				b = append(b, '"')
			}
			b = appendStructEnd(ctx, code, b)
			code = code.Next
		case encoder.OpStructEndOmitEmptyIntPtrString:
			p := load(ctxptr, code.Idx)
			p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			if p != 0 {
				b = appendStructKey(ctx, code, b)
				b = append(b, '"')
				b = appendInt(ctx, b, p, code)
				b = append(b, '"')
				b = appendStructEnd(ctx, code, b)
			} else {
				b = appendStructEndSkipLast(ctx, code, b)
			}
			code = code.Next
		case encoder.OpStructEndUint:
			p := load(ctxptr, code.Idx)
			b = appendStructKey(ctx, code, b)
			b = appendUint(ctx, b, p+uintptr(code.Offset), code)
			b = appendStructEnd(ctx, code, b)
			code = code.Next
		case encoder.OpStructEndOmitEmptyUint:
			p := load(ctxptr, code.Idx)
			u64 := ptrToUint64(p+uintptr(code.Offset), code.NumBitSize)
			v := u64 & ((1 << code.NumBitSize) - 1)
			if v != 0 {
				b = appendStructKey(ctx, code, b)
				b = appendUint(ctx, b, p+uintptr(code.Offset), code)
				b = appendStructEnd(ctx, code, b)
			} else {
				b = appendStructEndSkipLast(ctx, code, b)
			}
			code = code.Next
		case encoder.OpStructEndUintString:
			p := load(ctxptr, code.Idx)
			b = appendStructKey(ctx, code, b)
			b = append(b, '"')
			b = appendUint(ctx, b, p+uintptr(code.Offset), code)
			b = append(b, '"')
			b = appendStructEnd(ctx, code, b)
			code = code.Next
		case encoder.OpStructEndOmitEmptyUintString:
			p := load(ctxptr, code.Idx)
			u64 := ptrToUint64(p+uintptr(code.Offset), code.NumBitSize)
			v := u64 & ((1 << code.NumBitSize) - 1)
			if v != 0 {
				b = appendStructKey(ctx, code, b)
				b = append(b, '"')
				b = appendUint(ctx, b, p+uintptr(code.Offset), code)
				b = append(b, '"')
				b = appendStructEnd(ctx, code, b)
			} else {
				b = appendStructEndSkipLast(ctx, code, b)
			}
			code = code.Next
		case encoder.OpStructEndUintPtr:
			b = appendStructKey(ctx, code, b)
			p := load(ctxptr, code.Idx)
			p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			if p == 0 {
				b = appendNull(ctx, b)
			} else {
				b = appendUint(ctx, b, p, code)
			}
			b = appendStructEnd(ctx, code, b)
			code = code.Next
		case encoder.OpStructEndOmitEmptyUintPtr:
			p := load(ctxptr, code.Idx)
			p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			if p != 0 {
				b = appendStructKey(ctx, code, b)
				b = appendUint(ctx, b, p, code)
				b = appendStructEnd(ctx, code, b)
			} else {
				b = appendStructEndSkipLast(ctx, code, b)
			}
			code = code.Next
		case encoder.OpStructEndUintPtrString:
			b = appendStructKey(ctx, code, b)
			p := load(ctxptr, code.Idx)
			p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			if p == 0 {
				b = appendNull(ctx, b)
			} else {
				b = append(b, '"')
				b = appendUint(ctx, b, p, code)
				b = append(b, '"')
			}
			b = appendStructEnd(ctx, code, b)
			code = code.Next
		case encoder.OpStructEndOmitEmptyUintPtrString:
			p := load(ctxptr, code.Idx)
			p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			if p != 0 {
				b = appendStructKey(ctx, code, b)
				b = append(b, '"')
				b = appendUint(ctx, b, p, code)
				b = append(b, '"')
				b = appendStructEnd(ctx, code, b)
			} else {
				b = appendStructEndSkipLast(ctx, code, b)
			}
			code = code.Next
		case encoder.OpStructEndFloat32:
			p := load(ctxptr, code.Idx)
			b = appendStructKey(ctx, code, b)
			b = appendFloat32(ctx, b, ptrToFloat32(p+uintptr(code.Offset)))
			b = appendStructEnd(ctx, code, b)
			code = code.Next
		case encoder.OpStructEndOmitEmptyFloat32:
			p := load(ctxptr, code.Idx)
			v := ptrToFloat32(p + uintptr(code.Offset))
			if v != 0 {
				b = appendStructKey(ctx, code, b)
				b = appendFloat32(ctx, b, v)
				b = appendStructEnd(ctx, code, b)
			} else {
				b = appendStructEndSkipLast(ctx, code, b)
			}
			code = code.Next
		case encoder.OpStructEndFloat32String:
			p := load(ctxptr, code.Idx)
			b = appendStructKey(ctx, code, b)
			b = append(b, '"')
			b = appendFloat32(ctx, b, ptrToFloat32(p+uintptr(code.Offset)))
			b = append(b, '"')
			b = appendStructEnd(ctx, code, b)
			code = code.Next
		case encoder.OpStructEndOmitEmptyFloat32String:
			p := load(ctxptr, code.Idx)
			v := ptrToFloat32(p + uintptr(code.Offset))
			if v != 0 {
				b = appendStructKey(ctx, code, b)
				b = append(b, '"')
				b = appendFloat32(ctx, b, v)
				b = append(b, '"')
				b = appendStructEnd(ctx, code, b)
			} else {
				b = appendStructEndSkipLast(ctx, code, b)
			}
			code = code.Next
		case encoder.OpStructEndFloat32Ptr:
			b = appendStructKey(ctx, code, b)
			p := load(ctxptr, code.Idx)
			p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			if p == 0 {
				b = appendNull(ctx, b)
			} else {
				b = appendFloat32(ctx, b, ptrToFloat32(p))
			}
			b = appendStructEnd(ctx, code, b)
			code = code.Next
		case encoder.OpStructEndOmitEmptyFloat32Ptr:
			p := load(ctxptr, code.Idx)
			p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			if p != 0 {
				b = appendStructKey(ctx, code, b)
				b = appendFloat32(ctx, b, ptrToFloat32(p))
				b = appendStructEnd(ctx, code, b)
			} else {
				b = appendStructEndSkipLast(ctx, code, b)
			}
			code = code.Next
		case encoder.OpStructEndFloat32PtrString:
			b = appendStructKey(ctx, code, b)
			p := load(ctxptr, code.Idx)
			p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			if p == 0 {
				b = appendNull(ctx, b)
			} else {
				b = append(b, '"')
				b = appendFloat32(ctx, b, ptrToFloat32(p))
				b = append(b, '"')
			}
			b = appendStructEnd(ctx, code, b)
			code = code.Next
		case encoder.OpStructEndOmitEmptyFloat32PtrString:
			p := load(ctxptr, code.Idx)
			p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			if p != 0 {
				b = appendStructKey(ctx, code, b)
				b = append(b, '"')
				b = appendFloat32(ctx, b, ptrToFloat32(p))
				b = append(b, '"')
				b = appendStructEnd(ctx, code, b)
			} else {
				b = appendStructEndSkipLast(ctx, code, b)
			}
			code = code.Next
		case encoder.OpStructEndFloat64:
			p := load(ctxptr, code.Idx)
			v := ptrToFloat64(p + uintptr(code.Offset))
			if math.IsInf(v, 0) || math.IsNaN(v) {
				return nil, errUnsupportedFloat(v)
			}
			b = appendStructKey(ctx, code, b)
			b = appendFloat64(ctx, b, v)
			b = appendStructEnd(ctx, code, b)
			code = code.Next
		case encoder.OpStructEndOmitEmptyFloat64:
			p := load(ctxptr, code.Idx)
			v := ptrToFloat64(p + uintptr(code.Offset))
			if v != 0 {
				if math.IsInf(v, 0) || math.IsNaN(v) {
					return nil, errUnsupportedFloat(v)
				}
				b = appendStructKey(ctx, code, b)
				b = appendFloat64(ctx, b, v)
				b = appendStructEnd(ctx, code, b)
			} else {
				b = appendStructEndSkipLast(ctx, code, b)
			}
			code = code.Next
		case encoder.OpStructEndFloat64String:
			p := load(ctxptr, code.Idx)
			v := ptrToFloat64(p + uintptr(code.Offset))
			if math.IsInf(v, 0) || math.IsNaN(v) {
				return nil, errUnsupportedFloat(v)
			}
			b = appendStructKey(ctx, code, b)
			b = append(b, '"')
			b = appendFloat64(ctx, b, v)
			b = append(b, '"')
			b = appendStructEnd(ctx, code, b)
			code = code.Next
		case encoder.OpStructEndOmitEmptyFloat64String:
			p := load(ctxptr, code.Idx)
			v := ptrToFloat64(p + uintptr(code.Offset))
			if v != 0 {
				if math.IsInf(v, 0) || math.IsNaN(v) {
					return nil, errUnsupportedFloat(v)
				}
				b = appendStructKey(ctx, code, b)
				b = append(b, '"')
				b = appendFloat64(ctx, b, v)
				b = append(b, '"')
				b = appendStructEnd(ctx, code, b)
			} else {
				b = appendStructEndSkipLast(ctx, code, b)
			}
			code = code.Next
		case encoder.OpStructEndFloat64Ptr:
			b = appendStructKey(ctx, code, b)
			p := load(ctxptr, code.Idx)
			p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			if p == 0 {
				b = appendNull(ctx, b)
				b = appendStructEnd(ctx, code, b)
				code = code.Next
				break
			}
			v := ptrToFloat64(p)
			if math.IsInf(v, 0) || math.IsNaN(v) {
				return nil, errUnsupportedFloat(v)
			}
			b = appendFloat64(ctx, b, v)
			b = appendStructEnd(ctx, code, b)
			code = code.Next
		case encoder.OpStructEndOmitEmptyFloat64Ptr:
			p := load(ctxptr, code.Idx)
			p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			if p != 0 {
				b = appendStructKey(ctx, code, b)
				v := ptrToFloat64(p)
				if math.IsInf(v, 0) || math.IsNaN(v) {
					return nil, errUnsupportedFloat(v)
				}
				b = appendFloat64(ctx, b, v)
				b = appendStructEnd(ctx, code, b)
			} else {
				b = appendStructEndSkipLast(ctx, code, b)
			}
			code = code.Next
		case encoder.OpStructEndFloat64PtrString:
			b = appendStructKey(ctx, code, b)
			p := load(ctxptr, code.Idx)
			p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			if p == 0 {
				b = appendNull(ctx, b)
			} else {
				b = append(b, '"')
				v := ptrToFloat64(p)
				if math.IsInf(v, 0) || math.IsNaN(v) {
					return nil, errUnsupportedFloat(v)
				}
				b = appendFloat64(ctx, b, v)
				b = append(b, '"')
			}
			b = appendStructEnd(ctx, code, b)
			code = code.Next
		case encoder.OpStructEndOmitEmptyFloat64PtrString:
			p := load(ctxptr, code.Idx)
			p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			if p != 0 {
				b = appendStructKey(ctx, code, b)
				v := ptrToFloat64(p)
				if math.IsInf(v, 0) || math.IsNaN(v) {
					return nil, errUnsupportedFloat(v)
				}
				b = append(b, '"')
				b = appendFloat64(ctx, b, v)
				b = append(b, '"')
				b = appendStructEnd(ctx, code, b)
			} else {
				b = appendStructEndSkipLast(ctx, code, b)
			}
			code = code.Next
		case encoder.OpStructEndString:
			p := load(ctxptr, code.Idx)
			b = appendStructKey(ctx, code, b)
			b = appendString(ctx, b, ptrToString(p+uintptr(code.Offset)))
			b = appendStructEnd(ctx, code, b)
			code = code.Next
		case encoder.OpStructEndOmitEmptyString:
			p := load(ctxptr, code.Idx)
			v := ptrToString(p + uintptr(code.Offset))
			if v != "" {
				b = appendStructKey(ctx, code, b)
				b = appendString(ctx, b, v)
				b = appendStructEnd(ctx, code, b)
			} else {
				b = appendStructEndSkipLast(ctx, code, b)
			}
			code = code.Next
		case encoder.OpStructEndStringString:
			p := load(ctxptr, code.Idx)
			b = appendStructKey(ctx, code, b)
			s := ptrToString(p + uintptr(code.Offset))
			b = appendString(ctx, b, string(appendString(ctx, []byte{}, s)))
			b = appendStructEnd(ctx, code, b)
			code = code.Next
		case encoder.OpStructEndOmitEmptyStringString:
			p := load(ctxptr, code.Idx)
			v := ptrToString(p + uintptr(code.Offset))
			if v != "" {
				b = appendStructKey(ctx, code, b)
				b = appendString(ctx, b, string(appendString(ctx, []byte{}, v)))
				b = appendStructEnd(ctx, code, b)
			} else {
				b = appendStructEndSkipLast(ctx, code, b)
			}
			code = code.Next
		case encoder.OpStructEndStringPtr:
			b = appendStructKey(ctx, code, b)
			p := load(ctxptr, code.Idx)
			p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			if p == 0 {
				b = appendNull(ctx, b)
			} else {
				b = appendString(ctx, b, ptrToString(p))
			}
			b = appendStructEnd(ctx, code, b)
			code = code.Next
		case encoder.OpStructEndOmitEmptyStringPtr:
			p := load(ctxptr, code.Idx)
			p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			if p != 0 {
				b = appendStructKey(ctx, code, b)
				b = appendString(ctx, b, ptrToString(p))
				b = appendStructEnd(ctx, code, b)
			} else {
				b = appendStructEndSkipLast(ctx, code, b)
			}
			code = code.Next
		case encoder.OpStructEndStringPtrString:
			b = appendStructKey(ctx, code, b)
			p := load(ctxptr, code.Idx)
			p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			if p == 0 {
				b = appendNull(ctx, b)
			} else {
				b = appendString(ctx, b, string(appendString(ctx, []byte{}, ptrToString(p))))
			}
			b = appendStructEnd(ctx, code, b)
			code = code.Next
		case encoder.OpStructEndOmitEmptyStringPtrString:
			p := load(ctxptr, code.Idx)
			p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			if p != 0 {
				b = appendStructKey(ctx, code, b)
				b = appendString(ctx, b, string(appendString(ctx, []byte{}, ptrToString(p))))
				b = appendStructEnd(ctx, code, b)
			} else {
				b = appendStructEndSkipLast(ctx, code, b)
			}
			code = code.Next
		case encoder.OpStructEndBool:
			p := load(ctxptr, code.Idx)
			b = appendStructKey(ctx, code, b)
			b = appendBool(ctx, b, ptrToBool(p+uintptr(code.Offset)))
			b = appendStructEnd(ctx, code, b)
			code = code.Next
		case encoder.OpStructEndOmitEmptyBool:
			p := load(ctxptr, code.Idx)
			v := ptrToBool(p + uintptr(code.Offset))
			if v {
				b = appendStructKey(ctx, code, b)
				b = appendBool(ctx, b, v)
				b = appendStructEnd(ctx, code, b)
			} else {
				b = appendStructEndSkipLast(ctx, code, b)
			}
			code = code.Next
		case encoder.OpStructEndBoolString:
			p := load(ctxptr, code.Idx)
			b = appendStructKey(ctx, code, b)
			b = append(b, '"')
			b = appendBool(ctx, b, ptrToBool(p+uintptr(code.Offset)))
			b = append(b, '"')
			b = appendStructEnd(ctx, code, b)
			code = code.Next
		case encoder.OpStructEndOmitEmptyBoolString:
			p := load(ctxptr, code.Idx)
			v := ptrToBool(p + uintptr(code.Offset))
			if v {
				b = appendStructKey(ctx, code, b)
				b = append(b, '"')
				b = appendBool(ctx, b, v)
				b = append(b, '"')
				b = appendStructEnd(ctx, code, b)
			} else {
				b = appendStructEndSkipLast(ctx, code, b)
			}
			code = code.Next
		case encoder.OpStructEndBoolPtr:
			b = appendStructKey(ctx, code, b)
			p := load(ctxptr, code.Idx)
			p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			if p == 0 {
				b = appendNull(ctx, b)
			} else {
				b = appendBool(ctx, b, ptrToBool(p))
			}
			b = appendStructEnd(ctx, code, b)
			code = code.Next
		case encoder.OpStructEndOmitEmptyBoolPtr:
			p := load(ctxptr, code.Idx)
			p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			if p != 0 {
				b = appendStructKey(ctx, code, b)
				b = appendBool(ctx, b, ptrToBool(p))
				b = appendStructEnd(ctx, code, b)
			} else {
				b = appendStructEndSkipLast(ctx, code, b)
			}
			code = code.Next
		case encoder.OpStructEndBoolPtrString:
			b = appendStructKey(ctx, code, b)
			p := load(ctxptr, code.Idx)
			p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			if p == 0 {
				b = appendNull(ctx, b)
			} else {
				b = append(b, '"')
				b = appendBool(ctx, b, ptrToBool(p))
				b = append(b, '"')
			}
			b = appendStructEnd(ctx, code, b)
			code = code.Next
		case encoder.OpStructEndOmitEmptyBoolPtrString:
			p := load(ctxptr, code.Idx)
			p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			if p != 0 {
				b = appendStructKey(ctx, code, b)
				b = append(b, '"')
				b = appendBool(ctx, b, ptrToBool(p))
				b = append(b, '"')
				b = appendStructEnd(ctx, code, b)
			} else {
				b = appendStructEndSkipLast(ctx, code, b)
			}
			code = code.Next
		case encoder.OpStructEndBytes:
			p := load(ctxptr, code.Idx)
			b = appendStructKey(ctx, code, b)
			b = appendByteSlice(ctx, b, ptrToBytes(p+uintptr(code.Offset)))
			b = appendStructEnd(ctx, code, b)
			code = code.Next
		case encoder.OpStructEndOmitEmptyBytes:
			p := load(ctxptr, code.Idx)
			v := ptrToBytes(p + uintptr(code.Offset))
			if len(v) > 0 {
				b = appendStructKey(ctx, code, b)
				b = appendByteSlice(ctx, b, v)
				b = appendStructEnd(ctx, code, b)
			} else {
				b = appendStructEndSkipLast(ctx, code, b)
			}
			code = code.Next
		case encoder.OpStructEndBytesPtr:
			b = appendStructKey(ctx, code, b)
			p := load(ctxptr, code.Idx)
			p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			if p == 0 {
				b = appendNull(ctx, b)
			} else {
				b = appendByteSlice(ctx, b, ptrToBytes(p))
			}
			b = appendStructEnd(ctx, code, b)
			code = code.Next
		case encoder.OpStructEndOmitEmptyBytesPtr:
			p := load(ctxptr, code.Idx)
			p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			if p != 0 {
				b = appendStructKey(ctx, code, b)
				b = appendByteSlice(ctx, b, ptrToBytes(p))
				b = appendStructEnd(ctx, code, b)
			} else {
				b = appendStructEndSkipLast(ctx, code, b)
			}
			code = code.Next
		case encoder.OpStructEndNumber:
			p := load(ctxptr, code.Idx)
			b = appendStructKey(ctx, code, b)
			bb, err := appendNumber(ctx, b, ptrToNumber(p+uintptr(code.Offset)))
			if err != nil {
				return nil, err
			}
			b = appendStructEnd(ctx, code, bb)
			code = code.Next
		case encoder.OpStructEndOmitEmptyNumber:
			p := load(ctxptr, code.Idx)
			v := ptrToNumber(p + uintptr(code.Offset))
			if v != "" {
				b = appendStructKey(ctx, code, b)
				bb, err := appendNumber(ctx, b, v)
				if err != nil {
					return nil, err
				}
				b = appendStructEnd(ctx, code, bb)
			} else {
				b = appendStructEndSkipLast(ctx, code, b)
			}
			code = code.Next
		case encoder.OpStructEndNumberString:
			p := load(ctxptr, code.Idx)
			b = appendStructKey(ctx, code, b)
			b = append(b, '"')
			bb, err := appendNumber(ctx, b, ptrToNumber(p+uintptr(code.Offset)))
			if err != nil {
				return nil, err
			}
			b = append(bb, '"')
			b = appendStructEnd(ctx, code, b)
			code = code.Next
		case encoder.OpStructEndOmitEmptyNumberString:
			p := load(ctxptr, code.Idx)
			v := ptrToNumber(p + uintptr(code.Offset))
			if v != "" {
				b = appendStructKey(ctx, code, b)
				b = append(b, '"')
				bb, err := appendNumber(ctx, b, v)
				if err != nil {
					return nil, err
				}
				b = append(bb, '"')
				b = appendStructEnd(ctx, code, b)
			} else {
				b = appendStructEndSkipLast(ctx, code, b)
			}
			code = code.Next
		case encoder.OpStructEndNumberPtr:
			b = appendStructKey(ctx, code, b)
			p := load(ctxptr, code.Idx)
			p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			if p == 0 {
				b = appendNull(ctx, b)
			} else {
				bb, err := appendNumber(ctx, b, ptrToNumber(p))
				if err != nil {
					return nil, err
				}
				b = bb
			}
			b = appendStructEnd(ctx, code, b)
			code = code.Next
		case encoder.OpStructEndOmitEmptyNumberPtr:
			p := load(ctxptr, code.Idx)
			p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			if p != 0 {
				b = appendStructKey(ctx, code, b)
				bb, err := appendNumber(ctx, b, ptrToNumber(p))
				if err != nil {
					return nil, err
				}
				b = appendStructEnd(ctx, code, bb)
			} else {
				b = appendStructEndSkipLast(ctx, code, b)
			}
			code = code.Next
		case encoder.OpStructEndNumberPtrString:
			b = appendStructKey(ctx, code, b)
			p := load(ctxptr, code.Idx)
			p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			if p == 0 {
				b = appendNull(ctx, b)
			} else {
				b = append(b, '"')
				bb, err := appendNumber(ctx, b, ptrToNumber(p))
				if err != nil {
					return nil, err
				}
				b = append(bb, '"')
			}
			b = appendStructEnd(ctx, code, b)
			code = code.Next
		case encoder.OpStructEndOmitEmptyNumberPtrString:
			p := load(ctxptr, code.Idx)
			p = ptrToNPtr(p+uintptr(code.Offset), code.PtrNum)
			if p != 0 {
				b = appendStructKey(ctx, code, b)
				b = append(b, '"')
				bb, err := appendNumber(ctx, b, ptrToNumber(p))
				if err != nil {
					return nil, err
				}
				b = append(bb, '"')
				b = appendStructEnd(ctx, code, b)
			} else {
				b = appendStructEndSkipLast(ctx, code, b)
			}
			code = code.Next
		case encoder.OpEnd:
			goto END
		}
	}
END:
	return b, nil
}
