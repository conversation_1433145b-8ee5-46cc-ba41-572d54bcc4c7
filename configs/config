# SSL证书管理系统 - 生产环境配置

# 应用环境配置
NODE_ENV=production
PORT=3001
LOG_LEVEL=info

# 数据库配置
DB_HOST=************
DB_PORT=3306
DB_NAME=ssl_cert_system
DB_USER=ssl_manager
DB_PASSWORD=169wh7bSJbqANl2PSnkwg+BauB8hlKn9GWtiCMSoJNg=
DB_MAX_OPEN_CONNS=20
DB_MAX_IDLE_CONNS=10

# JWT配置
JWT_SECRET=B8hlKn9GWtiCMSoJNg=

# ACME配置 (Let's Encrypt生产环境)
ACME_SERVER=https://acme-v02.api.letsencrypt.org/directory
ACME_EMAIL=<EMAIL>
ACME_STORAGE_PATH=/app/storage/certs

# ACME挑战类型配置
# 支持的类型: http-01, dns-01
ACME_CHALLENGE_TYPE=http-01

# HTTP-01验证配置 (当ACME_CHALLENGE_TYPE=http-01时使用)
ACME_HTTP_PORT=80

# DNS-01验证配置 (当ACME_CHALLENGE_TYPE=dns-01时使用)
# DNS传播等待时间(秒) - DNS记录生效需要时间
ACME_DNS_PROPAGATION_WAIT=300

# 应用版本 (构建时自动设置)
APP_VERSION=1.0.2
