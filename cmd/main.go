package main

import (
	"context"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"

	_ "github.com/go-sql-driver/mysql" // 显式导入MySQL驱动
	"github.com/httpsok/internal/auth"
	"github.com/httpsok/internal/config"
	"github.com/httpsok/internal/database"
	"github.com/httpsok/internal/logger"
	"github.com/httpsok/internal/server"
	"go.uber.org/zap"
)

func main() {
	// 初始化配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// 初始化日志
	logConfig := logger.LogConfig{
		Level:      cfg.Log.Level,
		Path:       cfg.Log.Path,
		MaxSize:    cfg.Log.MaxSize,
		MaxBackups: cfg.Log.MaxBackups,
		MaxAge:     cfg.Log.MaxAge,
		Compress:   cfg.Log.Compress,
	}
	
	appLogger, err := logger.NewLogger(logConfig)
	if err != nil {
		log.Fatalf("Failed to initialize logger: %v", err)
	}
	defer appLogger.Sync()

	appLogger.Info("Starting httpsok service...")

	// 初始化数据库连接
	dbConfig := database.DBConfig{
		Host:            cfg.Database.Host,
		Port:            cfg.Database.Port,
		User:            cfg.Database.User,
		Password:        cfg.Database.Password,
		DBName:          cfg.Database.DBName,
		SSLMode:         cfg.Database.SSLMode,
		MaxOpenConns:    cfg.Database.MaxOpenConns,
		MaxIdleConns:    cfg.Database.MaxIdleConns,
		ConnMaxLifetime: time.Duration(cfg.Database.ConnMaxLifetime) * time.Minute,
	}

	db, err := database.NewConnection(dbConfig)
	if err != nil {
		appLogger.Fatal("Failed to connect to database", zap.Error(err))
	}
	defer db.Close()
	appLogger.Info("Database connection established")

	// 运行数据库迁移
	if err := db.Migrate("migrations"); err != nil {
		appLogger.Warn("Database migration failed", zap.Error(err))
	} else {
		appLogger.Info("Database migration completed")
	}

	// 初始化JWT管理器
	jwtManager := auth.NewJWTManager(
		cfg.JWT.SecretKey,
		time.Duration(cfg.JWT.AccessTokenExpiry)*time.Hour,
		time.Duration(cfg.JWT.RefreshTokenExpiry)*24*time.Hour,
		cfg.JWT.Issuer,
		cfg.JWT.Algorithm,
	)

	// 初始化密码管理器
	passwordManager := auth.NewPasswordManager(12) // bcrypt cost

	// 初始化HTTP服务器
	srv := server.NewServer(cfg, appLogger, db, jwtManager, passwordManager)

	// 启动HTTP服务器
	go func() {
		appLogger.Infof("HTTP server starting on %s:%d", cfg.Server.Address, cfg.Server.Port)
		if err := srv.Start(); err != nil {
			appLogger.Fatal("Failed to start server", zap.Error(err))
		}
	}()

	// 优雅关闭
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	appLogger.Info("Shutting down server...")

	// 创建关闭上下文，超时时间为30秒
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := srv.Shutdown(ctx); err != nil {
		appLogger.Error("Server shutdown error", zap.Error(err))
	}

	appLogger.Info("Server stopped")
}


