package database

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	_ "github.com/go-sql-driver/mysql"
	"github.com/golang-migrate/migrate/v4"
	"github.com/golang-migrate/migrate/v4/database/mysql"
	_ "github.com/golang-migrate/migrate/v4/source/file"
)

// DBConfig 数据库配置
type DBConfig struct {
	Host            string
	Port            int
	User            string
	Password        string
	DBName          string
	SSLMode         string
	MaxOpenConns    int
	MaxIdleConns    int
	ConnMaxLifetime time.Duration
}

// Connection 数据库连接
type Connection struct {
	*sql.DB
}

// NewConnection 创建新的数据库连接
func NewConnection(config DBConfig) (*Connection, error) {
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local&multiStatements=true",
		config.User, config.Password, config.Host, config.Port, config.DBName)
	
	if config.SSLMode != "" {
		dsn = fmt.Sprintf("%s&tls=%s", dsn, config.SSLMode)
	}

	db, err := sql.Open("mysql", dsn)
	if err != nil {
		return nil, fmt.Errorf("failed to open database connection: %v", err)
	}

	// 设置连接池参数
	db.SetMaxOpenConns(config.MaxOpenConns)
	db.SetMaxIdleConns(config.MaxIdleConns)
	db.SetConnMaxLifetime(config.ConnMaxLifetime)

	// 测试连接
	if err := db.Ping(); err != nil {
		return nil, fmt.Errorf("failed to ping database: %v", err)
	}

	return &Connection{db}, nil
}

// Close 关闭数据库连接
func (c *Connection) Close() error {
	return c.DB.Close()
}

// Transaction 执行事务
func (c *Connection) Transaction(fn func(*sql.Tx) error) error {
	tx, err := c.Begin()
	if err != nil {
		return err
	}

	defer func() {
		if p := recover(); p != nil {
			tx.Rollback()
			panic(p) // 重新抛出panic
		}
	}()

	if err := fn(tx); err != nil {
		tx.Rollback()
		return err
	}

	return tx.Commit()
}

// Migrate 执行数据库迁移
func (c *Connection) Migrate(migrationsPath string) error {
	driver, err := mysql.WithInstance(c.DB, &mysql.Config{})
	if err != nil {
		return fmt.Errorf("failed to create migration driver: %v", err)
	}

	m, err := migrate.NewWithDatabaseInstance(
		fmt.Sprintf("file://%s", migrationsPath),
		"mysql",
		driver,
	)
	if err != nil {
		return fmt.Errorf("failed to create migration instance: %v", err)
	}

	if err := m.Up(); err != nil && err != migrate.ErrNoChange {
		return fmt.Errorf("failed to run migrations: %v", err)
	}

	return nil
}

// GetVersion 获取数据库迁移版本
func (c *Connection) GetVersion(migrationsPath string) (uint, bool, error) {
	driver, err := mysql.WithInstance(c.DB, &mysql.Config{})
	if err != nil {
		return 0, false, fmt.Errorf("failed to create migration driver: %v", err)
	}

	m, err := migrate.NewWithDatabaseInstance(
		fmt.Sprintf("file://%s", migrationsPath),
		"mysql",
		driver,
	)
	if err != nil {
		return 0, false, fmt.Errorf("failed to create migration instance: %v", err)
	}

	return m.Version()
}

// HealthCheck 健康检查
func (c *Connection) HealthCheck() error {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := c.PingContext(ctx); err != nil {
		return fmt.Errorf("database health check failed: %v", err)
	}

	return nil
}

// Stats 获取连接池统计信息
func (c *Connection) Stats() sql.DBStats {
	return c.DB.Stats()
}

