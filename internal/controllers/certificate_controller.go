package controllers

import (
	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/httpsok/internal/database"
	"github.com/httpsok/internal/errors"
	"github.com/httpsok/internal/logger"
	"github.com/httpsok/internal/models"
	"go.uber.org/zap"
)

// CertificateController 证书控制器
type CertificateController struct {
	db     *database.Connection
	logger logger.Logger
}

// NewCertificateController 创建证书控制器
func NewCertificateController(db *database.Connection, log logger.Logger) *CertificateController {
	return &CertificateController{
		db:     db,
		logger: log,
	}
}

// ListCertificates 获取证书列表
func (c *CertificateController) ListCertificates(ctx *gin.Context) {
	userID := ctx.GetUint("user_id")
	
	var pagination models.PaginationRequest
	if err := ctx.ShouldBindQuery(&pagination); err != nil {
		ctx.Error(errors.WrapError(errors.ErrCodeValidation, "Invalid pagination parameters", http.StatusBadRequest, err))
		return
	}

	// 构建查询
	query := `SELECT id, user_id, name, domains, ca_provider, encryption_type, key_size, 
			  status, cert_path, key_path, fullchain_path, issued_at, expires_at, 
			  auto_renew, renew_before_days, last_check_at, error_message, created_at, updated_at
			  FROM certificates WHERE user_id = ? ORDER BY ` + pagination.Sort + ` ` + pagination.Order + ` LIMIT ? OFFSET ?`

	offset := (pagination.Page - 1) * pagination.PageSize
	rows, err := c.db.Query(query, userID, pagination.PageSize, offset)
	if err != nil {
		c.logger.Error("Failed to query certificates", zap.Error(err))
		ctx.Error(errors.WrapError(errors.ErrCodeDatabaseError, "Failed to query certificates", http.StatusInternalServerError, err))
		return
	}
	defer rows.Close()

	var certificates []models.Certificate
	for rows.Next() {
		var cert models.Certificate
		err := rows.Scan(&cert.ID, &cert.UserID, &cert.Name, &cert.Domains, &cert.CAProvider,
			&cert.EncryptionType, &cert.KeySize, &cert.Status, &cert.CertPath, &cert.KeyPath,
			&cert.FullchainPath, &cert.IssuedAt, &cert.ExpiresAt, &cert.AutoRenew,
			&cert.RenewBeforeDays, &cert.LastCheckAt, &cert.ErrorMessage, &cert.CreatedAt, &cert.UpdatedAt)
		if err != nil {
			c.logger.Error("Failed to scan certificate", zap.Error(err))
			continue
		}
		certificates = append(certificates, cert)
	}

	// 获取总数
	var total int64
	err = c.db.QueryRow("SELECT COUNT(*) FROM certificates WHERE user_id = ?", userID).Scan(&total)
	if err != nil {
		c.logger.Error("Failed to count certificates", zap.Error(err))
		ctx.Error(errors.WrapError(errors.ErrCodeDatabaseError, "Failed to count certificates", http.StatusInternalServerError, err))
		return
	}

	totalPages := int(total) / pagination.PageSize
	if int(total)%pagination.PageSize > 0 {
		totalPages++
	}

	response := models.PaginationResponse{
		Page:       pagination.Page,
		PageSize:   pagination.PageSize,
		Total:      total,
		TotalPages: totalPages,
		Data:       certificates,
	}

	ctx.JSON(http.StatusOK, response)
}

// GetCertificate 获取单个证书
func (c *CertificateController) GetCertificate(ctx *gin.Context) {
	userID := ctx.GetUint("user_id")
	certID, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.Error(errors.NewAppError(errors.ErrCodeInvalidRequest, "Invalid certificate ID", http.StatusBadRequest))
		return
	}

	var cert models.Certificate
	query := `SELECT id, user_id, name, domains, ca_provider, encryption_type, key_size, 
			  status, cert_path, key_path, fullchain_path, issued_at, expires_at, 
			  auto_renew, renew_before_days, last_check_at, error_message, created_at, updated_at
			  FROM certificates WHERE id = ? AND user_id = ?`

	err = c.db.QueryRow(query, certID, userID).Scan(&cert.ID, &cert.UserID, &cert.Name, &cert.Domains,
		&cert.CAProvider, &cert.EncryptionType, &cert.KeySize, &cert.Status, &cert.CertPath,
		&cert.KeyPath, &cert.FullchainPath, &cert.IssuedAt, &cert.ExpiresAt, &cert.AutoRenew,
		&cert.RenewBeforeDays, &cert.LastCheckAt, &cert.ErrorMessage, &cert.CreatedAt, &cert.UpdatedAt)

	if err != nil {
		if err.Error() == "sql: no rows in result set" {
			ctx.Error(errors.ErrCertNotFound)
		} else {
			c.logger.Error("Failed to query certificate", zap.Error(err))
			ctx.Error(errors.WrapError(errors.ErrCodeDatabaseError, "Failed to query certificate", http.StatusInternalServerError, err))
		}
		return
	}

	ctx.JSON(http.StatusOK, cert)
}

// CreateCertificate 创建证书
func (c *CertificateController) CreateCertificate(ctx *gin.Context) {
	userID := ctx.GetUint("user_id")
	
	var req models.CreateCertificateRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.Error(errors.WrapError(errors.ErrCodeValidation, "Invalid request data", http.StatusBadRequest, err))
		return
	}

	// 创建证书记录
	query := `INSERT INTO certificates (user_id, name, domains, ca_provider, encryption_type, 
			  key_size, auto_renew, renew_before_days, status) 
			  VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'pending')`

	result, err := c.db.Exec(query, userID, req.Name, models.StringSlice(req.Domains),
		req.CAProvider, req.EncryptionType, req.KeySize, req.AutoRenew, req.RenewBeforeDays)
	if err != nil {
		c.logger.Error("Failed to create certificate", zap.Error(err))
		ctx.Error(errors.WrapError(errors.ErrCodeDatabaseError, "Failed to create certificate", http.StatusInternalServerError, err))
		return
	}

	_, _ = result.LastInsertId()
	
	// 返回创建的证书
	c.GetCertificate(ctx)
}

// UpdateCertificate 更新证书
func (c *CertificateController) UpdateCertificate(ctx *gin.Context) {
	userID := ctx.GetUint("user_id")
	certID, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.Error(errors.NewAppError(errors.ErrCodeInvalidRequest, "Invalid certificate ID", http.StatusBadRequest))
		return
	}

	var req models.UpdateCertificateRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.Error(errors.WrapError(errors.ErrCodeValidation, "Invalid request data", http.StatusBadRequest, err))
		return
	}

	// 构建更新查询
	setParts := []string{}
	args := []interface{}{}

	if req.Name != nil {
		setParts = append(setParts, "name = ?")
		args = append(args, *req.Name)
	}
	if req.Domains != nil {
		setParts = append(setParts, "domains = ?")
		args = append(args, models.StringSlice(req.Domains))
	}
	if req.AutoRenew != nil {
		setParts = append(setParts, "auto_renew = ?")
		args = append(args, *req.AutoRenew)
	}
	if req.RenewBeforeDays != nil {
		setParts = append(setParts, "renew_before_days = ?")
		args = append(args, *req.RenewBeforeDays)
	}

	if len(setParts) == 0 {
		ctx.Error(errors.NewAppError(errors.ErrCodeInvalidRequest, "No fields to update", http.StatusBadRequest))
		return
	}

	setParts = append(setParts, "updated_at = NOW()")
	args = append(args, certID, userID)

	query := "UPDATE certificates SET " + strings.Join(setParts, ", ") + " WHERE id = ? AND user_id = ?"
	_, err = c.db.Exec(query, args...)
	if err != nil {
		c.logger.Error("Failed to update certificate", zap.Error(err))
		ctx.Error(errors.WrapError(errors.ErrCodeDatabaseError, "Failed to update certificate", http.StatusInternalServerError, err))
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"message": "Certificate updated successfully"})
}

// DeleteCertificate 删除证书
func (c *CertificateController) DeleteCertificate(ctx *gin.Context) {
	userID := ctx.GetUint("user_id")
	certID, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.Error(errors.NewAppError(errors.ErrCodeInvalidRequest, "Invalid certificate ID", http.StatusBadRequest))
		return
	}

	query := "DELETE FROM certificates WHERE id = ? AND user_id = ?"
	result, err := c.db.Exec(query, certID, userID)
	if err != nil {
		c.logger.Error("Failed to delete certificate", zap.Error(err))
		ctx.Error(errors.WrapError(errors.ErrCodeDatabaseError, "Failed to delete certificate", http.StatusInternalServerError, err))
		return
	}

	rowsAffected, _ := result.RowsAffected()
	if rowsAffected == 0 {
		ctx.Error(errors.ErrCertNotFound)
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"message": "Certificate deleted successfully"})
}

// VerifyDNS DNS验证
func (c *CertificateController) VerifyDNS(ctx *gin.Context) {
	// TODO: 实现DNS验证逻辑
	ctx.JSON(http.StatusOK, gin.H{"message": "DNS verification started"})
}

// IssueCertificate 签发证书
func (c *CertificateController) IssueCertificate(ctx *gin.Context) {
	// TODO: 实现证书签发逻辑
	ctx.JSON(http.StatusOK, gin.H{"message": "Certificate issuance started"})
}

// RenewCertificate 续期证书
func (c *CertificateController) RenewCertificate(ctx *gin.Context) {
	// TODO: 实现证书续期逻辑
	ctx.JSON(http.StatusOK, gin.H{"message": "Certificate renewal started"})
}

// DownloadCertificate 下载证书
func (c *CertificateController) DownloadCertificate(ctx *gin.Context) {
	// TODO: 实现证书下载逻辑
	ctx.JSON(http.StatusOK, gin.H{"message": "Certificate download"})
}

