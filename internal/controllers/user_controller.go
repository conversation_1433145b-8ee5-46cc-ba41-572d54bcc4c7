package controllers

import (
	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/httpsok/internal/database"
	"github.com/httpsok/internal/errors"
	"github.com/httpsok/internal/logger"
	"github.com/httpsok/internal/models"
	"go.uber.org/zap"
)

// UserController 用户控制器
type UserController struct {
	db     *database.Connection
	logger logger.Logger
}

// NewUserController 创建用户控制器
func NewUserController(db *database.Connection, log logger.Logger) *UserController {
	return &UserController{
		db:     db,
		logger: log,
	}
}

// Login 用户登录
func (c *UserController) Login(ctx *gin.Context) {
	var req models.LoginRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.Error(errors.WrapError(errors.ErrCodeValidation, "Invalid request data", http.StatusBadRequest, err))
		return
	}

	// TODO: 实现登录逻辑
	ctx.JSON(http.StatusOK, gin.H{"message": "Login successful"})
}

// Register 用户注册
func (c *UserController) Register(ctx *gin.Context) {
	var req models.CreateUserRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.Error(errors.WrapError(errors.ErrCodeValidation, "Invalid request data", http.StatusBadRequest, err))
		return
	}

	// TODO: 实现注册逻辑
	ctx.JSON(http.StatusOK, gin.H{"message": "Registration successful"})
}

// GetCurrentUser 获取当前用户信息
func (c *UserController) GetCurrentUser(ctx *gin.Context) {
	userID := ctx.GetUint("user_id")
	
	var user models.User
	query := `SELECT id, username, email, role, status, failed_login_attempts, 
			  locked_until, last_login_at, created_at, updated_at 
			  FROM users WHERE id = ?`

	err := c.db.QueryRow(query, userID).Scan(&user.ID, &user.Username, &user.Email,
		&user.Role, &user.Status, &user.FailedLoginAttempts, &user.LockedUntil,
		&user.LastLoginAt, &user.CreatedAt, &user.UpdatedAt)

	if err != nil {
		c.logger.Error("Failed to query user", zap.Error(err))
		ctx.Error(errors.WrapError(errors.ErrCodeDatabaseError, "Failed to query user", http.StatusInternalServerError, err))
		return
	}

	ctx.JSON(http.StatusOK, user)
}

// UpdateCurrentUser 更新当前用户信息
func (c *UserController) UpdateCurrentUser(ctx *gin.Context) {
	userID := ctx.GetUint("user_id")
	
	var req models.UpdateUserRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.Error(errors.WrapError(errors.ErrCodeValidation, "Invalid request data", http.StatusBadRequest, err))
		return
	}

	// 构建更新查询
	setParts := []string{}
	args := []interface{}{}

	if req.Username != nil {
		setParts = append(setParts, "username = ?")
		args = append(args, *req.Username)
	}
	if req.Email != nil {
		setParts = append(setParts, "email = ?")
		args = append(args, *req.Email)
	}

	if len(setParts) == 0 {
		ctx.Error(errors.NewAppError(errors.ErrCodeInvalidRequest, "No fields to update", http.StatusBadRequest))
		return
	}

	setParts = append(setParts, "updated_at = NOW()")
	args = append(args, userID)

	query := "UPDATE users SET " + strings.Join(setParts, ", ") + " WHERE id = ?"
	_, err := c.db.Exec(query, args...)
	if err != nil {
		c.logger.Error("Failed to update user", zap.Error(err))
		ctx.Error(errors.WrapError(errors.ErrCodeDatabaseError, "Failed to update user", http.StatusInternalServerError, err))
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"message": "User updated successfully"})
}

// ChangePassword 修改密码
func (c *UserController) ChangePassword(ctx *gin.Context) {
	var req models.ChangePasswordRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.Error(errors.WrapError(errors.ErrCodeValidation, "Invalid request data", http.StatusBadRequest, err))
		return
	}

	// TODO: 实现密码修改逻辑
	ctx.JSON(http.StatusOK, gin.H{"message": "Password changed successfully"})
}

// ListUsers 获取用户列表（管理员）
func (c *UserController) ListUsers(ctx *gin.Context) {
	var pagination models.PaginationRequest
	if err := ctx.ShouldBindQuery(&pagination); err != nil {
		ctx.Error(errors.WrapError(errors.ErrCodeValidation, "Invalid pagination parameters", http.StatusBadRequest, err))
		return
	}

	// 构建查询
	query := `SELECT id, username, email, role, status, failed_login_attempts, 
			  locked_until, last_login_at, created_at, updated_at
			  FROM users ORDER BY ` + pagination.Sort + ` ` + pagination.Order + ` LIMIT ? OFFSET ?`

	offset := (pagination.Page - 1) * pagination.PageSize
	rows, err := c.db.Query(query, pagination.PageSize, offset)
	if err != nil {
		c.logger.Error("Failed to query users", zap.Error(err))
		ctx.Error(errors.WrapError(errors.ErrCodeDatabaseError, "Failed to query users", http.StatusInternalServerError, err))
		return
	}
	defer rows.Close()

	var users []models.User
	for rows.Next() {
		var user models.User
		err := rows.Scan(&user.ID, &user.Username, &user.Email, &user.Role, &user.Status,
			&user.FailedLoginAttempts, &user.LockedUntil, &user.LastLoginAt, &user.CreatedAt, &user.UpdatedAt)
		if err != nil {
			c.logger.Error("Failed to scan user", zap.Error(err))
			continue
		}
		users = append(users, user)
	}

	// 获取总数
	var total int64
	err = c.db.QueryRow("SELECT COUNT(*) FROM users").Scan(&total)
	if err != nil {
		c.logger.Error("Failed to count users", zap.Error(err))
		ctx.Error(errors.WrapError(errors.ErrCodeDatabaseError, "Failed to count users", http.StatusInternalServerError, err))
		return
	}

	totalPages := int(total) / pagination.PageSize
	if int(total)%pagination.PageSize > 0 {
		totalPages++
	}

	response := models.PaginationResponse{
		Page:       pagination.Page,
		PageSize:   pagination.PageSize,
		Total:      total,
		TotalPages: totalPages,
		Data:       users,
	}

	ctx.JSON(http.StatusOK, response)
}

// CreateUser 创建用户（管理员）
func (c *UserController) CreateUser(ctx *gin.Context) {
	var req models.CreateUserRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.Error(errors.WrapError(errors.ErrCodeValidation, "Invalid request data", http.StatusBadRequest, err))
		return
	}

	// TODO: 实现用户创建逻辑
	ctx.JSON(http.StatusOK, gin.H{"message": "User created successfully"})
}

// GetUser 获取用户信息（管理员）
func (c *UserController) GetUser(ctx *gin.Context) {
	userID, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.Error(errors.NewAppError(errors.ErrCodeInvalidRequest, "Invalid user ID", http.StatusBadRequest))
		return
	}

	var user models.User
	query := `SELECT id, username, email, role, status, failed_login_attempts, 
			  locked_until, last_login_at, created_at, updated_at 
			  FROM users WHERE id = ?`

	err = c.db.QueryRow(query, userID).Scan(&user.ID, &user.Username, &user.Email,
		&user.Role, &user.Status, &user.FailedLoginAttempts, &user.LockedUntil,
		&user.LastLoginAt, &user.CreatedAt, &user.UpdatedAt)

	if err != nil {
		if err.Error() == "sql: no rows in result set" {
			ctx.Error(errors.ErrNotFound)
		} else {
			c.logger.Error("Failed to query user", zap.Error(err))
			ctx.Error(errors.WrapError(errors.ErrCodeDatabaseError, "Failed to query user", http.StatusInternalServerError, err))
		}
		return
	}

	ctx.JSON(http.StatusOK, user)
}

// UpdateUser 更新用户信息（管理员）
func (c *UserController) UpdateUser(ctx *gin.Context) {
	targetUserID, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.Error(errors.NewAppError(errors.ErrCodeInvalidRequest, "Invalid user ID", http.StatusBadRequest))
		return
	}

	var req models.UpdateUserRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.Error(errors.WrapError(errors.ErrCodeValidation, "Invalid request data", http.StatusBadRequest, err))
		return
	}

	// TODO: 实现用户更新逻辑
	_ = targetUserID // 避免未使用变量错误
	ctx.JSON(http.StatusOK, gin.H{"message": "User updated successfully"})
}

// DeleteUser 删除用户（管理员）
func (c *UserController) DeleteUser(ctx *gin.Context) {
	targetUserID, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.Error(errors.NewAppError(errors.ErrCodeInvalidRequest, "Invalid user ID", http.StatusBadRequest))
		return
	}

	query := "DELETE FROM users WHERE id = ?"
	result, err := c.db.Exec(query, targetUserID)
	if err != nil {
		c.logger.Error("Failed to delete user", zap.Error(err))
		ctx.Error(errors.WrapError(errors.ErrCodeDatabaseError, "Failed to delete user", http.StatusInternalServerError, err))
		return
	}

	rowsAffected, _ := result.RowsAffected()
	if rowsAffected == 0 {
		ctx.Error(errors.ErrNotFound)
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"message": "User deleted successfully"})
}

// GetSettings 获取系统设置
func (c *UserController) GetSettings(ctx *gin.Context) {
	// TODO: 实现获取系统设置逻辑
	ctx.JSON(http.StatusOK, gin.H{"message": "Get settings"})
}

// UpdateSettings 更新系统设置
func (c *UserController) UpdateSettings(ctx *gin.Context) {
	// TODO: 实现更新系统设置逻辑
	ctx.JSON(http.StatusOK, gin.H{"message": "Settings updated successfully"})
}

