package models

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"
)

// User 用户模型
type User struct {
	ID                   uint       `json:"id" db:"id"`
	Username             string     `json:"username" db:"username"`
	Email                string     `json:"email" db:"email"`
	PasswordHash         string     `json:"-" db:"password_hash"`
	Role                 string     `json:"role" db:"role"`
	Status               string     `json:"status" db:"status"`
	FailedLoginAttempts  int        `json:"failed_login_attempts" db:"failed_login_attempts"`
	LockedUntil          *time.Time `json:"locked_until,omitempty" db:"locked_until"`
	LastLoginAt          *time.Time `json:"last_login_at,omitempty" db:"last_login_at"`
	CreatedAt            time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt            time.Time  `json:"updated_at" db:"updated_at"`
}

// RefreshToken 刷新令牌模型
type RefreshToken struct {
	ID        uint      `json:"id" db:"id"`
	UserID    uint      `json:"user_id" db:"user_id"`
	TokenHash string    `json:"-" db:"token_hash"`
	ExpiresAt time.Time `json:"expires_at" db:"expires_at"`
	CreatedAt time.Time `json:"created_at" db:"created_at"`
}

// StringSlice 字符串切片类型，用于JSON序列化
type StringSlice []string

// Value 实现driver.Valuer接口
func (s StringSlice) Value() (driver.Value, error) {
	if s == nil {
		return nil, nil
	}
	return json.Marshal(s)
}

// Scan 实现sql.Scanner接口
func (s *StringSlice) Scan(value interface{}) error {
	if value == nil {
		*s = nil
		return nil
	}
	
	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("cannot scan %T into StringSlice", value)
	}
	
	return json.Unmarshal(bytes, s)
}

// IntSlice 整数切片类型，用于JSON序列化
type IntSlice []int

// Value 实现driver.Valuer接口
func (s IntSlice) Value() (driver.Value, error) {
	if s == nil {
		return nil, nil
	}
	return json.Marshal(s)
}

// Scan 实现sql.Scanner接口
func (s *IntSlice) Scan(value interface{}) error {
	if value == nil {
		*s = nil
		return nil
	}
	
	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("cannot scan %T into IntSlice", value)
	}
	
	return json.Unmarshal(bytes, s)
}

// Certificate 证书模型
type Certificate struct {
	ID               uint        `json:"id" db:"id"`
	UserID           uint        `json:"user_id" db:"user_id"`
	Name             string      `json:"name" db:"name"`
	Domains          StringSlice `json:"domains" db:"domains"`
	CAProvider       string      `json:"ca_provider" db:"ca_provider"`
	EncryptionType   string      `json:"encryption_type" db:"encryption_type"`
	KeySize          int         `json:"key_size" db:"key_size"`
	Status           string      `json:"status" db:"status"`
	CertPath         *string     `json:"cert_path,omitempty" db:"cert_path"`
	KeyPath          *string     `json:"key_path,omitempty" db:"key_path"`
	FullchainPath    *string     `json:"fullchain_path,omitempty" db:"fullchain_path"`
	IssuedAt         *time.Time  `json:"issued_at,omitempty" db:"issued_at"`
	ExpiresAt        *time.Time  `json:"expires_at,omitempty" db:"expires_at"`
	AutoRenew        bool        `json:"auto_renew" db:"auto_renew"`
	RenewBeforeDays  int         `json:"renew_before_days" db:"renew_before_days"`
	LastCheckAt      *time.Time  `json:"last_check_at,omitempty" db:"last_check_at"`
	ErrorMessage     *string     `json:"error_message,omitempty" db:"error_message"`
	CreatedAt        time.Time   `json:"created_at" db:"created_at"`
	UpdatedAt        time.Time   `json:"updated_at" db:"updated_at"`
}

// Server 服务器模型
type Server struct {
	ID                    uint       `json:"id" db:"id"`
	UserID                uint       `json:"user_id" db:"user_id"`
	Name                  string     `json:"name" db:"name"`
	Host                  string     `json:"host" db:"host"`
	Port                  int        `json:"port" db:"port"`
	Username              string     `json:"username" db:"username"`
	AuthType              string     `json:"auth_type" db:"auth_type"`
	Password              *string    `json:"-" db:"password"`
	PrivateKeyPath        *string    `json:"private_key_path,omitempty" db:"private_key_path"`
	WebServerType         string     `json:"web_server_type" db:"web_server_type"`
	WebServerConfigPath   *string    `json:"web_server_config_path,omitempty" db:"web_server_config_path"`
	Status                string     `json:"status" db:"status"`
	LastConnectedAt       *time.Time `json:"last_connected_at,omitempty" db:"last_connected_at"`
	ErrorMessage          *string    `json:"error_message,omitempty" db:"error_message"`
	CreatedAt             time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt             time.Time  `json:"updated_at" db:"updated_at"`
}

// CertificateDeployment 证书部署记录模型
type CertificateDeployment struct {
	ID            uint       `json:"id" db:"id"`
	CertificateID uint       `json:"certificate_id" db:"certificate_id"`
	ServerID      uint       `json:"server_id" db:"server_id"`
	Status        string     `json:"status" db:"status"`
	DeployedAt    *time.Time `json:"deployed_at,omitempty" db:"deployed_at"`
	ErrorMessage  *string    `json:"error_message,omitempty" db:"error_message"`
	CreatedAt     time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt     time.Time  `json:"updated_at" db:"updated_at"`
}

// Monitor 监控配置模型
type Monitor struct {
	ID            uint       `json:"id" db:"id"`
	UserID        uint       `json:"user_id" db:"user_id"`
	Name          string     `json:"name" db:"name"`
	URL           string     `json:"url" db:"url"`
	CheckInterval int        `json:"check_interval" db:"check_interval"`
	Timeout       int        `json:"timeout" db:"timeout"`
	AlertBeforeDays IntSlice `json:"alert_before_days" db:"alert_before_days"`
	EmailAlerts   bool       `json:"email_alerts" db:"email_alerts"`
	WebhookURL    *string    `json:"webhook_url,omitempty" db:"webhook_url"`
	Status        string     `json:"status" db:"status"`
	LastCheckAt   *time.Time `json:"last_check_at,omitempty" db:"last_check_at"`
	NextCheckAt   *time.Time `json:"next_check_at,omitempty" db:"next_check_at"`
	CreatedAt     time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt     time.Time  `json:"updated_at" db:"updated_at"`
}

// MonitorResult 监控结果模型
type MonitorResult struct {
	ID                  uint       `json:"id" db:"id"`
	MonitorID           uint       `json:"monitor_id" db:"monitor_id"`
	CheckTime           time.Time  `json:"check_time" db:"check_time"`
	Status              string     `json:"status" db:"status"`
	ResponseTime        *int       `json:"response_time,omitempty" db:"response_time"`
	CertValid           *bool      `json:"cert_valid,omitempty" db:"cert_valid"`
	CertExpiresAt       *time.Time `json:"cert_expires_at,omitempty" db:"cert_expires_at"`
	CertDaysUntilExpiry *int       `json:"cert_days_until_expiry,omitempty" db:"cert_days_until_expiry"`
	ErrorMessage        *string    `json:"error_message,omitempty" db:"error_message"`
	CreatedAt           time.Time  `json:"created_at" db:"created_at"`
}

// Alert 告警记录模型
type Alert struct {
	ID        uint       `json:"id" db:"id"`
	UserID    uint       `json:"user_id" db:"user_id"`
	Type      string     `json:"type" db:"type"`
	Title     string     `json:"title" db:"title"`
	Message   string     `json:"message" db:"message"`
	Level     string     `json:"level" db:"level"`
	Status    string     `json:"status" db:"status"`
	RelatedID *uint      `json:"related_id,omitempty" db:"related_id"`
	SentAt    *time.Time `json:"sent_at,omitempty" db:"sent_at"`
	CreatedAt time.Time  `json:"created_at" db:"created_at"`
}

// Setting 系统设置模型
type Setting struct {
	ID          uint      `json:"id" db:"id"`
	KeyName     string    `json:"key_name" db:"key_name"`
	Value       *string   `json:"value,omitempty" db:"value"`
	Description *string   `json:"description,omitempty" db:"description"`
	CreatedAt   time.Time `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time `json:"updated_at" db:"updated_at"`
}

// OperationLog 操作日志模型
type OperationLog struct {
	ID           uint      `json:"id" db:"id"`
	UserID       *uint     `json:"user_id,omitempty" db:"user_id"`
	Action       string    `json:"action" db:"action"`
	ResourceType string    `json:"resource_type" db:"resource_type"`
	ResourceID   *uint     `json:"resource_id,omitempty" db:"resource_id"`
	Details      *string   `json:"details,omitempty" db:"details"`
	IPAddress    *string   `json:"ip_address,omitempty" db:"ip_address"`
	UserAgent    *string   `json:"user_agent,omitempty" db:"user_agent"`
	CreatedAt    time.Time `json:"created_at" db:"created_at"`
}

// CreateUserRequest 创建用户请求
type CreateUserRequest struct {
	Username string `json:"username" binding:"required,min=3,max=50"`
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required,min=8"`
	Role     string `json:"role" binding:"required,oneof=admin user"`
}

// UpdateUserRequest 更新用户请求
type UpdateUserRequest struct {
	Username *string `json:"username,omitempty" binding:"omitempty,min=3,max=50"`
	Email    *string `json:"email,omitempty" binding:"omitempty,email"`
	Role     *string `json:"role,omitempty" binding:"omitempty,oneof=admin user"`
	Status   *string `json:"status,omitempty" binding:"omitempty,oneof=active inactive locked"`
}

// ChangePasswordRequest 修改密码请求
type ChangePasswordRequest struct {
	OldPassword string `json:"old_password" binding:"required"`
	NewPassword string `json:"new_password" binding:"required,min=8"`
}

// LoginRequest 登录请求
type LoginRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

// RefreshTokenRequest 刷新令牌请求
type RefreshTokenRequest struct {
	RefreshToken string `json:"refresh_token" binding:"required"`
}

// CreateCertificateRequest 创建证书请求
type CreateCertificateRequest struct {
	Name           string   `json:"name" binding:"required,min=1,max=100"`
	Domains        []string `json:"domains" binding:"required,min=1"`
	CAProvider     string   `json:"ca_provider" binding:"required,oneof=letsencrypt zerossl google"`
	EncryptionType string   `json:"encryption_type" binding:"required,oneof=RSA ECC"`
	KeySize        int      `json:"key_size" binding:"required,oneof=256 384 521 2048 3072 4096"`
	AutoRenew      bool     `json:"auto_renew"`
	RenewBeforeDays int     `json:"renew_before_days" binding:"min=1,max=90"`
}

// UpdateCertificateRequest 更新证书请求
type UpdateCertificateRequest struct {
	Name            *string  `json:"name,omitempty" binding:"omitempty,min=1,max=100"`
	Domains         []string `json:"domains,omitempty" binding:"omitempty,min=1"`
	AutoRenew       *bool    `json:"auto_renew,omitempty"`
	RenewBeforeDays *int     `json:"renew_before_days,omitempty" binding:"omitempty,min=1,max=90"`
}

// CreateServerRequest 创建服务器请求
type CreateServerRequest struct {
	Name                string  `json:"name" binding:"required,min=1,max=100"`
	Host                string  `json:"host" binding:"required"`
	Port                int     `json:"port" binding:"required,min=1,max=65535"`
	Username            string  `json:"username" binding:"required"`
	AuthType            string  `json:"auth_type" binding:"required,oneof=password key"`
	Password            *string `json:"password,omitempty"`
	PrivateKeyPath      *string `json:"private_key_path,omitempty"`
	WebServerType       string  `json:"web_server_type" binding:"required,oneof=nginx apache"`
	WebServerConfigPath *string `json:"web_server_config_path,omitempty"`
}

// UpdateServerRequest 更新服务器请求
type UpdateServerRequest struct {
	Name                *string `json:"name,omitempty" binding:"omitempty,min=1,max=100"`
	Host                *string `json:"host,omitempty"`
	Port                *int    `json:"port,omitempty" binding:"omitempty,min=1,max=65535"`
	Username            *string `json:"username,omitempty"`
	AuthType            *string `json:"auth_type,omitempty" binding:"omitempty,oneof=password key"`
	Password            *string `json:"password,omitempty"`
	PrivateKeyPath      *string `json:"private_key_path,omitempty"`
	WebServerType       *string `json:"web_server_type,omitempty" binding:"omitempty,oneof=nginx apache"`
	WebServerConfigPath *string `json:"web_server_config_path,omitempty"`
	Status              *string `json:"status,omitempty" binding:"omitempty,oneof=active inactive error"`
}

// CreateMonitorRequest 创建监控请求
type CreateMonitorRequest struct {
	Name            string   `json:"name" binding:"required,min=1,max=100"`
	URL             string   `json:"url" binding:"required,url"`
	CheckInterval   int      `json:"check_interval" binding:"required,min=60"`
	Timeout         int      `json:"timeout" binding:"required,min=1,max=300"`
	AlertBeforeDays []int    `json:"alert_before_days" binding:"required,min=1"`
	EmailAlerts     bool     `json:"email_alerts"`
	WebhookURL      *string  `json:"webhook_url,omitempty" binding:"omitempty,url"`
}

// UpdateMonitorRequest 更新监控请求
type UpdateMonitorRequest struct {
	Name            *string `json:"name,omitempty" binding:"omitempty,min=1,max=100"`
	URL             *string `json:"url,omitempty" binding:"omitempty,url"`
	CheckInterval   *int    `json:"check_interval,omitempty" binding:"omitempty,min=60"`
	Timeout         *int    `json:"timeout,omitempty" binding:"omitempty,min=1,max=300"`
	AlertBeforeDays []int   `json:"alert_before_days,omitempty" binding:"omitempty,min=1"`
	EmailAlerts     *bool   `json:"email_alerts,omitempty"`
	WebhookURL      *string `json:"webhook_url,omitempty" binding:"omitempty,url"`
	Status          *string `json:"status,omitempty" binding:"omitempty,oneof=active inactive"`
}

// PaginationRequest 分页请求
type PaginationRequest struct {
	Page     int    `form:"page,default=1" binding:"min=1"`
	PageSize int    `form:"page_size,default=20" binding:"min=1,max=100"`
	Sort     string `form:"sort,default=id"`
	Order    string `form:"order,default=desc" binding:"oneof=asc desc"`
}

// PaginationResponse 分页响应
type PaginationResponse struct {
	Page       int         `json:"page"`
	PageSize   int         `json:"page_size"`
	Total      int64       `json:"total"`
	TotalPages int         `json:"total_pages"`
	Data       interface{} `json:"data"`
}

// HealthCheckResponse 健康检查响应
type HealthCheckResponse struct {
	Status    string                 `json:"status"`
	Timestamp time.Time              `json:"timestamp"`
	Version   string                 `json:"version"`
	Services  map[string]interface{} `json:"services"`
}

