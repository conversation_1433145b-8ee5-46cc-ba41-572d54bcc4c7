package middleware

import (
	"context"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/httpsok/internal/auth"
	"github.com/httpsok/internal/errors"
	"github.com/httpsok/internal/logger"
	"go.uber.org/zap"
	"golang.org/x/time/rate"
)

// Logger 日志中间件
func Logger(log logger.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		path := c.Request.URL.Path
		raw := c.Request.URL.RawQuery

		// 处理请求
		c.Next()

		// 记录日志
		latency := time.Since(start)
		clientIP := c.ClientIP()
		method := c.Request.Method
		statusCode := c.Writer.Status()
		bodySize := c.Writer.Size()
		userAgent := c.Request.UserAgent()

		if raw != "" {
			path = path + "?" + raw
		}

		fields := []zap.Field{
			zap.String("client_ip", clientIP),
			zap.String("method", method),
			zap.String("path", path),
			zap.Int("status_code", statusCode),
			zap.Int("body_size", bodySize),
			zap.Duration("latency", latency),
			zap.String("user_agent", userAgent),
		}

		// 添加用户信息（如果存在）
		if userID, exists := c.Get("user_id"); exists {
			fields = append(fields, zap.Any("user_id", userID))
		}
		if username, exists := c.Get("username"); exists {
			fields = append(fields, zap.String("username", username.(string)))
		}

		// 根据状态码选择日志级别
		switch {
		case statusCode >= 500:
			log.Error("HTTP request", fields...)
		case statusCode >= 400:
			log.Warn("HTTP request", fields...)
		default:
			log.Info("HTTP request", fields...)
		}
	}
}

// Recovery 恢复中间件
func Recovery(log logger.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		defer func() {
			if err := recover(); err != nil {
				log.Error("Panic recovered",
					zap.Any("error", err),
					zap.String("path", c.Request.URL.Path),
					zap.String("method", c.Request.Method),
					zap.String("client_ip", c.ClientIP()),
				)

				appErr := errors.ErrInternal
				c.JSON(appErr.HTTPStatus, gin.H{
					"error": gin.H{
						"code":    appErr.Code,
						"message": appErr.Message,
					},
				})
				c.Abort()
			}
		}()
		c.Next()
	}
}

// ErrorHandler 错误处理中间件
func ErrorHandler(log logger.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Next()

		// 处理错误
		if len(c.Errors) > 0 {
			err := c.Errors.Last().Err
			appErr := errors.GetAppError(err)

			// 记录错误日志
			fields := []zap.Field{
				zap.String("error_code", string(appErr.Code)),
				zap.String("path", c.Request.URL.Path),
				zap.String("method", c.Request.Method),
				zap.String("client_ip", c.ClientIP()),
			}

			if appErr.Internal != nil {
				fields = append(fields, zap.Error(appErr.Internal))
			}

			if appErr.HTTPStatus >= 500 {
				log.Error("Request error", fields...)
			} else {
				log.Warn("Request error", fields...)
			}

			// 返回错误响应
			response := gin.H{
				"error": gin.H{
					"code":    appErr.Code,
					"message": appErr.Message,
				},
			}

			if appErr.Details != "" {
				response["error"].(gin.H)["details"] = appErr.Details
			}

			c.JSON(appErr.HTTPStatus, response)
			c.Abort()
		}
	}
}

// Auth JWT认证中间件
func Auth(jwtManager *auth.JWTManager) gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.Error(errors.ErrUnauthorized)
			c.Abort()
			return
		}

		// 检查Bearer前缀
		parts := strings.SplitN(authHeader, " ", 2)
		if len(parts) != 2 || parts[0] != "Bearer" {
			c.Error(errors.NewAppError(
				errors.ErrCodeTokenInvalid,
				"Invalid authorization header format",
				http.StatusUnauthorized,
			))
			c.Abort()
			return
		}

		// 验证令牌
		claims, err := jwtManager.ValidateToken(parts[1], auth.AccessToken)
		if err != nil {
			c.Error(errors.NewAppError(
				errors.ErrCodeTokenInvalid,
				"Invalid or expired token",
				http.StatusUnauthorized,
			))
			c.Abort()
			return
		}

		// 设置用户信息到上下文
		c.Set("user_id", claims.UserID)
		c.Set("username", claims.Username)
		c.Set("user_role", claims.Role)

		c.Next()
	}
}

// AdminOnly 管理员权限中间件
func AdminOnly() gin.HandlerFunc {
	return func(c *gin.Context) {
		role, exists := c.Get("user_role")
		if !exists || role != "admin" {
			c.Error(errors.ErrForbidden)
			c.Abort()
			return
		}
		c.Next()
	}
}

// RateLimit 速率限制中间件
func RateLimit(requestsPerMin, burstSize int) gin.HandlerFunc {
	limiter := rate.NewLimiter(rate.Limit(requestsPerMin)/60, burstSize)

	return func(c *gin.Context) {
		if !limiter.Allow() {
			c.Error(errors.NewAppError(
				errors.ErrCodeValidation,
				"Rate limit exceeded",
				http.StatusTooManyRequests,
			))
			c.Abort()
			return
		}
		c.Next()
	}
}

// IPRateLimit IP级别的速率限制中间件
func IPRateLimit(requestsPerMin, burstSize int) gin.HandlerFunc {
	limiters := make(map[string]*rate.Limiter)

	return func(c *gin.Context) {
		ip := c.ClientIP()
		
		limiter, exists := limiters[ip]
		if !exists {
			limiter = rate.NewLimiter(rate.Limit(requestsPerMin)/60, burstSize)
			limiters[ip] = limiter
		}

		if !limiter.Allow() {
			c.Error(errors.NewAppError(
				errors.ErrCodeValidation,
				"Rate limit exceeded for IP",
				http.StatusTooManyRequests,
			))
			c.Abort()
			return
		}
		c.Next()
	}
}

// CORS 跨域中间件
func CORS() gin.HandlerFunc {
	return func(c *gin.Context) {
		origin := c.Request.Header.Get("Origin")
		
		// 设置CORS头
		c.Header("Access-Control-Allow-Origin", origin)
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization")
		c.Header("Access-Control-Expose-Headers", "Content-Length")
		c.Header("Access-Control-Allow-Credentials", "true")
		c.Header("Access-Control-Max-Age", "86400")

		// 处理预检请求
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	}
}

// Security 安全头中间件
func Security() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 设置安全头
		c.Header("X-Content-Type-Options", "nosniff")
		c.Header("X-Frame-Options", "DENY")
		c.Header("X-XSS-Protection", "1; mode=block")
		c.Header("Referrer-Policy", "strict-origin-when-cross-origin")
		c.Header("Content-Security-Policy", "default-src 'self'")
		
		// 如果是HTTPS，添加HSTS头
		if c.Request.TLS != nil {
			c.Header("Strict-Transport-Security", "max-age=31536000; includeSubDomains")
		}

		c.Next()
	}
}

// RequestID 请求ID中间件
func RequestID() gin.HandlerFunc {
	return func(c *gin.Context) {
		requestID := c.GetHeader("X-Request-ID")
		if requestID == "" {
			requestID = generateRequestID()
		}
		
		c.Set("request_id", requestID)
		c.Header("X-Request-ID", requestID)
		
		c.Next()
	}
}

// generateRequestID 生成请求ID
func generateRequestID() string {
	token, _ := auth.GenerateSecureToken(16)
	return token
}

// Timeout 超时中间件
func Timeout(timeout time.Duration) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 设置超时
		c.Request = c.Request.WithContext(c.Request.Context())
		
		// 创建带超时的上下文
		ctx, cancel := context.WithTimeout(c.Request.Context(), timeout)
		defer cancel()
		
		c.Request = c.Request.WithContext(ctx)
		
		c.Next()
	}
}

// ValidateContentType 验证Content-Type中间件
func ValidateContentType(allowedTypes ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		if c.Request.Method == "POST" || c.Request.Method == "PUT" || c.Request.Method == "PATCH" {
			contentType := c.GetHeader("Content-Type")
			
			// 检查是否为允许的Content-Type
			allowed := false
			for _, allowedType := range allowedTypes {
				if strings.Contains(contentType, allowedType) {
					allowed = true
					break
				}
			}
			
			if !allowed {
				c.Error(errors.NewAppError(
					errors.ErrCodeInvalidRequest,
					"Unsupported content type",
					http.StatusUnsupportedMediaType,
				))
				c.Abort()
				return
			}
		}
		
		c.Next()
	}
}

