package config

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strconv"
	"strings"

	"github.com/joho/godotenv"
)

// Config 表示应用程序的配置
type Config struct {
	// 服务器配置
	Server struct {
		Address     string `json:"address"`
		Port        int    `json:"port"`
		TLSEnabled  bool   `json:"tls_enabled"`
		TLSCertFile string `json:"tls_cert_file"`
		TLSKeyFile  string `json:"tls_key_file"`
		ReadTimeout int    `json:"read_timeout"`  // 秒
		WriteTimeout int   `json:"write_timeout"` // 秒
		IdleTimeout  int   `json:"idle_timeout"`  // 秒
	} `json:"server"`

	// 数据库配置
	Database struct {
		Host            string `json:"host"`
		Port            int    `json:"port"`
		User            string `json:"user"`
		Password        string `json:"password"`
		DBName          string `json:"dbname"`
		SSLMode         string `json:"sslmode"`
		MaxOpenConns    int    `json:"max_open_conns"`
		MaxIdleConns    int    `json:"max_idle_conns"`
		ConnMaxLifetime int    `json:"conn_max_lifetime"` // 分钟
	} `json:"database"`

	// Redis配置
	Redis struct {
		Enabled  bool   `json:"enabled"`
		Host     string `json:"host"`
		Port     int    `json:"port"`
		Password string `json:"password"`
		DB       int    `json:"db"`
	} `json:"redis"`

	// JWT配置
	JWT struct {
		SecretKey            string `json:"secret_key"`
		AccessTokenExpiry    int    `json:"access_token_expiry"`  // 小时
		RefreshTokenExpiry   int    `json:"refresh_token_expiry"` // 天
		Issuer               string `json:"issuer"`
		Algorithm            string `json:"algorithm"`
	} `json:"jwt"`

	// 日志配置
	Log struct {
		Level      string `json:"level"`
		Path       string `json:"path"`
		MaxSize    int    `json:"max_size"`    // MB
		MaxBackups int    `json:"max_backups"`
		MaxAge     int    `json:"max_age"`     // 天
		Compress   bool   `json:"compress"`
	} `json:"log"`

	// ACME配置
	ACME struct {
		DefaultCA         string `json:"default_ca"`
		DefaultEncryption string `json:"default_encryption"`
		RenewBeforeDays   int    `json:"renew_before_days"`
		ScriptPath        string `json:"script_path"`
		WorkDir           string `json:"work_dir"`
		ConfigDir         string `json:"config_dir"`
	} `json:"acme"`

	// 告警配置
	Alert struct {
		DefaultExpiryAlertDays []int `json:"default_expiry_alert_days"`
		EmailEnabled           bool  `json:"email_enabled"`
		SMTPHost               string `json:"smtp_host"`
		SMTPPort               int    `json:"smtp_port"`
		SMTPUser               string `json:"smtp_user"`
		SMTPPassword           string `json:"smtp_password"`
		FromEmail              string `json:"from_email"`
	} `json:"alert"`

	// 速率限制配置
	RateLimit struct {
		Enabled         bool `json:"enabled"`
		RequestsPerMin  int  `json:"requests_per_min"`
		BurstSize       int  `json:"burst_size"`
		AuthRequestsPerMin int `json:"auth_requests_per_min"`
	} `json:"rate_limit"`

	// 安全配置
	Security struct {
		PasswordMinLength int    `json:"password_min_length"`
		PasswordRequireSpecial bool `json:"password_require_special"`
		PasswordRequireNumber  bool `json:"password_require_number"`
		PasswordRequireUpper   bool `json:"password_require_upper"`
		PasswordRequireLower   bool `json:"password_require_lower"`
		MaxLoginAttempts       int  `json:"max_login_attempts"`
		LockoutDuration        int  `json:"lockout_duration"` // 分钟
		SessionTimeout         int  `json:"session_timeout"`  // 分钟
	} `json:"security"`
}

// Load 从配置文件和环境变量加载配置
func Load() (*Config, error) {
	// 加载.env文件（如果存在）
	_ = godotenv.Load()

	// 默认配置
	cfg := &Config{}
	setDefaults(cfg)

	// 尝试从配置文件加载
	configPath := getEnv("CONFIG_PATH", "configs/config.json")
	
	// 确保配置目录存在
	configDir := filepath.Dir(configPath)
	if err := os.MkdirAll(configDir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create config directory: %v", err)
	}

	// 如果配置文件不存在，创建默认配置
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		if err := saveDefaultConfig(cfg, configPath); err != nil {
			return nil, err
		}
	} else if err == nil {
		// 配置文件存在，读取配置
		if err := loadFromFile(cfg, configPath); err != nil {
			return nil, err
		}
	}

	// 从环境变量覆盖配置
	loadFromEnv(cfg)

	// 验证配置
	if err := validateConfig(cfg); err != nil {
		return nil, fmt.Errorf("config validation failed: %v", err)
	}

	return cfg, nil
}

// setDefaults 设置默认配置
func setDefaults(cfg *Config) {
	// 服务器默认配置
	cfg.Server.Address = "0.0.0.0"
	cfg.Server.Port = 8080
	cfg.Server.ReadTimeout = 15
	cfg.Server.WriteTimeout = 15
	cfg.Server.IdleTimeout = 60

	// 数据库默认配置
	cfg.Database.Host = "localhost"
	cfg.Database.Port = 3306
	cfg.Database.User = "root"
	cfg.Database.DBName = "httpsok"
	cfg.Database.MaxOpenConns = 25
	cfg.Database.MaxIdleConns = 5
	cfg.Database.ConnMaxLifetime = 5

	// Redis默认配置
	cfg.Redis.Enabled = false
	cfg.Redis.Host = "localhost"
	cfg.Redis.Port = 6379
	cfg.Redis.DB = 0

	// JWT默认配置
	cfg.JWT.AccessTokenExpiry = 24
	cfg.JWT.RefreshTokenExpiry = 7
	cfg.JWT.Issuer = "httpsok"
	cfg.JWT.Algorithm = "HS256"

	// 日志默认配置
	cfg.Log.Level = "info"
	cfg.Log.Path = "logs"
	cfg.Log.MaxSize = 100
	cfg.Log.MaxBackups = 3
	cfg.Log.MaxAge = 28
	cfg.Log.Compress = true

	// ACME默认配置
	cfg.ACME.DefaultCA = "letsencrypt"
	cfg.ACME.DefaultEncryption = "ECC"
	cfg.ACME.RenewBeforeDays = 30
	cfg.ACME.ScriptPath = "/usr/local/bin/acme.sh"
	cfg.ACME.WorkDir = "/var/lib/acme"
	cfg.ACME.ConfigDir = "/etc/acme"

	// 告警默认配置
	cfg.Alert.DefaultExpiryAlertDays = []int{30, 14, 7, 3, 1}
	cfg.Alert.EmailEnabled = false
	cfg.Alert.SMTPPort = 587

	// 速率限制默认配置
	cfg.RateLimit.Enabled = true
	cfg.RateLimit.RequestsPerMin = 100
	cfg.RateLimit.BurstSize = 20
	cfg.RateLimit.AuthRequestsPerMin = 10

	// 安全默认配置
	cfg.Security.PasswordMinLength = 8
	cfg.Security.PasswordRequireSpecial = true
	cfg.Security.PasswordRequireNumber = true
	cfg.Security.PasswordRequireUpper = true
	cfg.Security.PasswordRequireLower = true
	cfg.Security.MaxLoginAttempts = 5
	cfg.Security.LockoutDuration = 15
	cfg.Security.SessionTimeout = 30
}

// saveDefaultConfig 保存默认配置到文件
func saveDefaultConfig(cfg *Config, path string) error {
	data, err := json.MarshalIndent(cfg, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal default config: %v", err)
	}
	return os.WriteFile(path, data, 0644)
}

// loadFromFile 从文件加载配置
func loadFromFile(cfg *Config, path string) error {
	data, err := os.ReadFile(path)
	if err != nil {
		return fmt.Errorf("failed to read config file: %v", err)
	}
	if err := json.Unmarshal(data, cfg); err != nil {
		return fmt.Errorf("failed to parse config file: %v", err)
	}
	return nil
}

// loadFromEnv 从环境变量加载配置
func loadFromEnv(cfg *Config) {
	// 服务器配置
	if addr := getEnv("SERVER_ADDRESS", ""); addr != "" {
		cfg.Server.Address = addr
	}
	if port := getEnvInt("SERVER_PORT", 0); port != 0 {
		cfg.Server.Port = port
	}
	if tls := getEnvBool("SERVER_TLS_ENABLED", false); tls {
		cfg.Server.TLSEnabled = tls
	}
	if certFile := getEnv("SERVER_TLS_CERT_FILE", ""); certFile != "" {
		cfg.Server.TLSCertFile = certFile
	}
	if keyFile := getEnv("SERVER_TLS_KEY_FILE", ""); keyFile != "" {
		cfg.Server.TLSKeyFile = keyFile
	}

	// 数据库配置
	if host := getEnv("DB_HOST", ""); host != "" {
		cfg.Database.Host = host
	}
	if port := getEnvInt("DB_PORT", 0); port != 0 {
		cfg.Database.Port = port
	}
	if user := getEnv("DB_USER", ""); user != "" {
		cfg.Database.User = user
	}
	if password := getEnv("DB_PASSWORD", ""); password != "" {
		cfg.Database.Password = password
	}
	if dbname := getEnv("DB_NAME", ""); dbname != "" {
		cfg.Database.DBName = dbname
	}
	if sslmode := getEnv("DB_SSL_MODE", ""); sslmode != "" {
		cfg.Database.SSLMode = sslmode
	}

	// Redis配置
	if enabled := getEnvBool("REDIS_ENABLED", false); enabled {
		cfg.Redis.Enabled = enabled
	}
	if host := getEnv("REDIS_HOST", ""); host != "" {
		cfg.Redis.Host = host
	}
	if port := getEnvInt("REDIS_PORT", 0); port != 0 {
		cfg.Redis.Port = port
	}
	if password := getEnv("REDIS_PASSWORD", ""); password != "" {
		cfg.Redis.Password = password
	}
	if db := getEnvInt("REDIS_DB", -1); db >= 0 {
		cfg.Redis.DB = db
	}

	// JWT配置
	if secret := getEnv("JWT_SECRET_KEY", ""); secret != "" {
		cfg.JWT.SecretKey = secret
	}
	if issuer := getEnv("JWT_ISSUER", ""); issuer != "" {
		cfg.JWT.Issuer = issuer
	}

	// 日志配置
	if level := getEnv("LOG_LEVEL", ""); level != "" {
		cfg.Log.Level = level
	}
	if path := getEnv("LOG_PATH", ""); path != "" {
		cfg.Log.Path = path
	}

	// ACME配置
	if scriptPath := getEnv("ACME_SCRIPT_PATH", ""); scriptPath != "" {
		cfg.ACME.ScriptPath = scriptPath
	}
	if workDir := getEnv("ACME_WORK_DIR", ""); workDir != "" {
		cfg.ACME.WorkDir = workDir
	}
	if configDir := getEnv("ACME_CONFIG_DIR", ""); configDir != "" {
		cfg.ACME.ConfigDir = configDir
	}
}

// validateConfig 验证配置
func validateConfig(cfg *Config) error {
	// 验证JWT密钥
	if cfg.JWT.SecretKey == "" {
		return fmt.Errorf("JWT secret key is required")
	}
	if len(cfg.JWT.SecretKey) < 32 {
		return fmt.Errorf("JWT secret key must be at least 32 characters")
	}

	// 验证数据库配置
	if cfg.Database.Host == "" {
		return fmt.Errorf("database host is required")
	}
	if cfg.Database.User == "" {
		return fmt.Errorf("database user is required")
	}
	if cfg.Database.DBName == "" {
		return fmt.Errorf("database name is required")
	}

	// 验证ACME脚本路径
	if _, err := os.Stat(cfg.ACME.ScriptPath); os.IsNotExist(err) {
		return fmt.Errorf("ACME script not found at %s", cfg.ACME.ScriptPath)
	}

	// 验证TLS配置
	if cfg.Server.TLSEnabled {
		if cfg.Server.TLSCertFile == "" || cfg.Server.TLSKeyFile == "" {
			return fmt.Errorf("TLS cert and key files are required when TLS is enabled")
		}
	}

	return nil
}

// getEnv 获取环境变量，如果不存在则返回默认值
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// getEnvInt 获取整数类型的环境变量
func getEnvInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

// getEnvBool 获取布尔类型的环境变量
func getEnvBool(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		return strings.ToLower(value) == "true" || value == "1"
	}
	return defaultValue
}

