package errors

import (
	"fmt"
	"net/http"
)

// ErrorCode 错误码类型
type ErrorCode string

// 定义错误码常量
const (
	// 通用错误
	ErrCodeInternal       ErrorCode = "INTERNAL_ERROR"
	ErrCodeInvalidRequest ErrorCode = "INVALID_REQUEST"
	ErrCodeNotFound       ErrorCode = "NOT_FOUND"
	ErrCodeUnauthorized   ErrorCode = "UNAUTHORIZED"
	ErrCodeForbidden      ErrorCode = "FORBIDDEN"
	ErrCodeConflict       ErrorCode = "CONFLICT"
	ErrCodeValidation     ErrorCode = "VALIDATION_ERROR"

	// 认证相关错误
	ErrCodeInvalidCredentials ErrorCode = "INVALID_CREDENTIALS"
	ErrCodeTokenExpired       ErrorCode = "TOKEN_EXPIRED"
	ErrCodeTokenInvalid       ErrorCode = "TOKEN_INVALID"
	ErrCodeAccountLocked      ErrorCode = "ACCOUNT_LOCKED"
	ErrCodePasswordWeak       ErrorCode = "PASSWORD_WEAK"

	// 证书相关错误
	ErrCodeCertNotFound       ErrorCode = "CERTIFICATE_NOT_FOUND"
	ErrCodeCertInvalid        ErrorCode = "CERTIFICATE_INVALID"
	ErrCodeCertExpired        ErrorCode = "CERTIFICATE_EXPIRED"
	ErrCodeDNSVerifyFailed    ErrorCode = "DNS_VERIFICATION_FAILED"
	ErrCodeCertIssueFailed    ErrorCode = "CERTIFICATE_ISSUE_FAILED"
	ErrCodeCertDeployFailed   ErrorCode = "CERTIFICATE_DEPLOY_FAILED"

	// 服务器相关错误
	ErrCodeServerNotFound     ErrorCode = "SERVER_NOT_FOUND"
	ErrCodeServerUnreachable  ErrorCode = "SERVER_UNREACHABLE"
	ErrCodeServerConfigError  ErrorCode = "SERVER_CONFIG_ERROR"

	// 监控相关错误
	ErrCodeMonitorNotFound    ErrorCode = "MONITOR_NOT_FOUND"
	ErrCodeMonitorCheckFailed ErrorCode = "MONITOR_CHECK_FAILED"

	// 数据库相关错误
	ErrCodeDatabaseError      ErrorCode = "DATABASE_ERROR"
	ErrCodeDuplicateEntry     ErrorCode = "DUPLICATE_ENTRY"

	// 外部服务错误
	ErrCodeACMEScriptError    ErrorCode = "ACME_SCRIPT_ERROR"
	ErrCodeEmailSendFailed    ErrorCode = "EMAIL_SEND_FAILED"
)

// AppError 应用错误结构
type AppError struct {
	Code       ErrorCode `json:"code"`
	Message    string    `json:"message"`
	Details    string    `json:"details,omitempty"`
	HTTPStatus int       `json:"-"`
	Internal   error     `json:"-"`
}

// Error 实现error接口
func (e *AppError) Error() string {
	if e.Internal != nil {
		return fmt.Sprintf("%s: %s (internal: %v)", e.Code, e.Message, e.Internal)
	}
	return fmt.Sprintf("%s: %s", e.Code, e.Message)
}

// Unwrap 实现errors.Unwrap接口
func (e *AppError) Unwrap() error {
	return e.Internal
}

// NewAppError 创建新的应用错误
func NewAppError(code ErrorCode, message string, httpStatus int) *AppError {
	return &AppError{
		Code:       code,
		Message:    message,
		HTTPStatus: httpStatus,
	}
}

// NewAppErrorWithDetails 创建带详细信息的应用错误
func NewAppErrorWithDetails(code ErrorCode, message, details string, httpStatus int) *AppError {
	return &AppError{
		Code:       code,
		Message:    message,
		Details:    details,
		HTTPStatus: httpStatus,
	}
}

// WrapError 包装内部错误
func WrapError(code ErrorCode, message string, httpStatus int, internal error) *AppError {
	return &AppError{
		Code:       code,
		Message:    message,
		HTTPStatus: httpStatus,
		Internal:   internal,
	}
}

// 预定义的常用错误
var (
	ErrInternal = NewAppError(
		ErrCodeInternal,
		"Internal server error",
		http.StatusInternalServerError,
	)

	ErrInvalidRequest = NewAppError(
		ErrCodeInvalidRequest,
		"Invalid request",
		http.StatusBadRequest,
	)

	ErrNotFound = NewAppError(
		ErrCodeNotFound,
		"Resource not found",
		http.StatusNotFound,
	)

	ErrUnauthorized = NewAppError(
		ErrCodeUnauthorized,
		"Unauthorized",
		http.StatusUnauthorized,
	)

	ErrForbidden = NewAppError(
		ErrCodeForbidden,
		"Forbidden",
		http.StatusForbidden,
	)

	ErrConflict = NewAppError(
		ErrCodeConflict,
		"Resource conflict",
		http.StatusConflict,
	)

	ErrValidation = NewAppError(
		ErrCodeValidation,
		"Validation failed",
		http.StatusBadRequest,
	)

	ErrInvalidCredentials = NewAppError(
		ErrCodeInvalidCredentials,
		"Invalid username or password",
		http.StatusUnauthorized,
	)

	ErrTokenExpired = NewAppError(
		ErrCodeTokenExpired,
		"Token has expired",
		http.StatusUnauthorized,
	)

	ErrTokenInvalid = NewAppError(
		ErrCodeTokenInvalid,
		"Invalid token",
		http.StatusUnauthorized,
	)

	ErrAccountLocked = NewAppError(
		ErrCodeAccountLocked,
		"Account is locked due to too many failed login attempts",
		http.StatusUnauthorized,
	)

	ErrPasswordWeak = NewAppError(
		ErrCodePasswordWeak,
		"Password does not meet security requirements",
		http.StatusBadRequest,
	)

	ErrCertNotFound = NewAppError(
		ErrCodeCertNotFound,
		"Certificate not found",
		http.StatusNotFound,
	)

	ErrServerNotFound = NewAppError(
		ErrCodeServerNotFound,
		"Server not found",
		http.StatusNotFound,
	)

	ErrMonitorNotFound = NewAppError(
		ErrCodeMonitorNotFound,
		"Monitor not found",
		http.StatusNotFound,
	)

	ErrDatabaseError = NewAppError(
		ErrCodeDatabaseError,
		"Database operation failed",
		http.StatusInternalServerError,
	)
)

// IsAppError 检查是否为应用错误
func IsAppError(err error) bool {
	_, ok := err.(*AppError)
	return ok
}

// GetAppError 获取应用错误，如果不是则包装为内部错误
func GetAppError(err error) *AppError {
	if appErr, ok := err.(*AppError); ok {
		return appErr
	}
	return WrapError(ErrCodeInternal, "Internal server error", http.StatusInternalServerError, err)
}

